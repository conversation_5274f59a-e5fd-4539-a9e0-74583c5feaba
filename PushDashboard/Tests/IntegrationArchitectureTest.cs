using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using PushDashboard.Data;
using PushDashboard.Services.Integrations;
using PushDashboard.Services.Integrations.Common;

namespace PushDashboard.Tests;

/// <summary>
/// Entegrasyon mimarisinin doğru ç<PERSON>ıştığını test eden sınıf
/// </summary>
public static class IntegrationArchitectureTest
{
    /// <summary>
    /// Entegrasyon mimarisini test eder
    /// </summary>
    public static async Task TestIntegrationArchitecture(IServiceProvider services)
    {
        var logger = services.GetRequiredService<ILogger<ApplicationDbContext>>();

        try
        {
            logger.LogInformation("🧪 Entegrasyon mimarisi testi başlatılıyor...");

            // 1. Servis kayıtlarını kontrol et
            await TestServiceRegistrations(services, logger);

            // 2. Factory pattern'i test et
            await TestFactoryPattern(services, logger);

            // 3. E-commerce service'i test et
            await TestEcommerceService(services, logger);

            // 4. Entegrasyon akışını test et
            await TestIntegrationFlow(services, logger);

            logger.LogInformation("✅ Entegrasyon mimarisi testi başarıyla tamamlandı!");
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "❌ Entegrasyon mimarisi testi başarısız oldu");
            throw;
        }
    }

    private static async Task TestServiceRegistrations(IServiceProvider services, ILogger logger)
    {
        logger.LogInformation("🔍 Servis kayıtları kontrol ediliyor...");

        // Factory servisini kontrol et
        var factory = services.GetService<IEcommerceServiceFactory>();
        if (factory == null)
            throw new InvalidOperationException("IEcommerceServiceFactory kayıtlı değil");

        // E-commerce service'i kontrol et
        var ecommerceService = services.GetService<IEcommerceService>();
        if (ecommerceService == null)
            throw new InvalidOperationException("IEcommerceService kayıtlı değil");

        logger.LogInformation("✅ Servis kayıtları doğru");
    }

    private static async Task TestFactoryPattern(IServiceProvider services, ILogger logger)
    {
        logger.LogInformation("🏭 Factory pattern test ediliyor...");

        var factory = services.GetRequiredService<IEcommerceServiceFactory>();

        // Test company ID (gerçek bir company olması gerekmez, sadece factory logic'i test ediyoruz)
        var testCompanyId = Guid.NewGuid();

        try
        {
            var ecommerceService = await factory.GetEcommerceServiceAsync(testCompanyId);

            // Servis null olabilir (entegrasyon yoksa), bu normal
            logger.LogInformation("✅ Factory pattern çalışıyor");
        }
        catch (Exception ex)
        {
            logger.LogWarning(ex, "⚠️ Factory pattern test edilirken hata (bu normal olabilir)");
        }
    }

    private static async Task TestEcommerceService(IServiceProvider services, ILogger logger)
    {
        logger.LogInformation("🔧 E-commerce service test ediliyor...");

        var factory = services.GetRequiredService<IEcommerceServiceFactory>();
        var testCompanyId = Guid.NewGuid();

        try
        {
            var ecommerceService = await factory.GetEcommerceServiceAsync(testCompanyId);

            if (ecommerceService != null)
            {
                // PlatformName property'sini kontrol et
                var platformName = ecommerceService.PlatformName;
                if (string.IsNullOrEmpty(platformName))
                    throw new InvalidOperationException("E-commerce service PlatformName boş");

                logger.LogInformation("✅ E-commerce service platform: {PlatformName}", platformName);
            }

            logger.LogInformation("✅ E-commerce service çalışıyor");
        }
        catch (Exception ex)
        {
            logger.LogWarning(ex, "⚠️ E-commerce service test edilirken hata (bu normal olabilir)");
        }
    }

    private static async Task TestIntegrationFlow(IServiceProvider services, ILogger logger)
    {
        logger.LogInformation("🔄 Entegrasyon akışı test ediliyor...");

        var factory = services.GetRequiredService<IEcommerceServiceFactory>();
        var testCompanyId = Guid.NewGuid();

        try
        {
            var ecommerceService = await factory.GetEcommerceServiceAsync(testCompanyId);

            if (ecommerceService != null)
            {
                // Connection test metodunu test et
                var result = await ecommerceService.TestConnectionAsync(testCompanyId);
                logger.LogInformation("✅ Connection test result: {Success}", result.Success);
            }

            logger.LogInformation("✅ Entegrasyon akışı çalışıyor");
        }
        catch (Exception ex)
        {
            logger.LogWarning(ex, "⚠️ Entegrasyon akışı test edilirken hata (bu normal olabilir)");
        }
    }
}
