<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Exit Survey Widget Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-gray-100">
    <div class="min-h-screen py-8">
        <div class="max-w-4xl mx-auto px-4">
            <!-- Header -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-8">
                <div class="px-6 py-4">
                    <h1 class="text-2xl font-bold text-gray-900 flex items-center">
                        <i class="fas fa-poll-h text-blue-600 mr-3"></i>
                        Exit Survey Widget Test
                    </h1>
                    <p class="text-gray-600 mt-2">Bu sayfa yeni modüler exit survey widget'ını test etmek için oluşturulmuştur.</p>
                </div>
            </div>

            <!-- Test Controls -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-8">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h2 class="text-lg font-semibold text-gray-900">Test Kontrolleri</h2>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <button id="triggerExitSurvey" class="inline-flex items-center justify-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-md transition-colors duration-200">
                            <i class="fas fa-play mr-2"></i>
                            Anketi Göster
                        </button>
                        <button id="simulatePageExit" class="inline-flex items-center justify-center px-4 py-2 bg-yellow-600 hover:bg-yellow-700 text-white font-medium rounded-md transition-colors duration-200">
                            <i class="fas fa-mouse-pointer mr-2"></i>
                            Sayfa Çıkışı Simüle Et
                        </button>
                        <button id="clearLocalStorage" class="inline-flex items-center justify-center px-4 py-2 bg-red-600 hover:bg-red-700 text-white font-medium rounded-md transition-colors duration-200">
                            <i class="fas fa-trash mr-2"></i>
                            LocalStorage Temizle
                        </button>
                    </div>
                    
                    <div class="mt-6 p-4 bg-gray-50 rounded-lg">
                        <h3 class="text-sm font-medium text-gray-900 mb-2">Test Talimatları:</h3>
                        <ul class="text-sm text-gray-600 space-y-1">
                            <li>• <strong>Anketi Göster:</strong> Yeni widget'ı manuel olarak tetikler (mouse leave simülasyonu)</li>
                            <li>• <strong>Sayfa Çıkışı Simüle Et:</strong> Mouse leave event'ini simüle eder</li>
                            <li>• <strong>LocalStorage Temizle:</strong> Widget gösterim geçmişini sıfırlar (company-based keys)</li>
                            <li>• Gerçek test için sayfayı kapatmaya çalışın veya mouse'u sayfa üstünden çıkarın</li>
                            <li>• Widget artık modüler yapıda: CSS ve JS dosyaları ayrı yükleniyor</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Sample Content -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-8">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h2 class="text-lg font-semibold text-gray-900">Örnek E-ticaret Sitesi</h2>
                </div>
                <div class="p-6">
                    <div class="prose max-w-none">
                        <p class="text-gray-600 mb-4">
                            Bu bölüm normal bir e-ticaret sitesi içeriğini simüle eder. Kullanıcılar bu içeriği gördükten sonra 
                            sayfayı terk etmeye çalıştığında çıkış anketi gösterilecektir.
                        </p>
                        
                        <h3 class="text-lg font-semibold text-gray-900 mb-3">Popüler Ürünler</h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-6">
                            <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                                <div class="bg-blue-100 h-32 rounded-lg mb-3 flex items-center justify-center">
                                    <i class="fas fa-laptop text-blue-600 text-3xl"></i>
                                </div>
                                <h4 class="font-semibold text-gray-900 mb-2">Gaming Laptop</h4>
                                <p class="text-gray-600 text-sm mb-2">Yüksek performanslı gaming laptop</p>
                                <p class="text-lg font-bold text-blue-600">₺25.999</p>
                                <button class="w-full mt-3 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">
                                    Sepete Ekle
                                </button>
                            </div>
                            
                            <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                                <div class="bg-green-100 h-32 rounded-lg mb-3 flex items-center justify-center">
                                    <i class="fas fa-mobile-alt text-green-600 text-3xl"></i>
                                </div>
                                <h4 class="font-semibold text-gray-900 mb-2">iPhone 15 Pro</h4>
                                <p class="text-gray-600 text-sm mb-2">Son teknoloji akıllı telefon</p>
                                <p class="text-lg font-bold text-green-600">₺45.999</p>
                                <button class="w-full mt-3 px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors">
                                    Sepete Ekle
                                </button>
                            </div>
                            
                            <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                                <div class="bg-purple-100 h-32 rounded-lg mb-3 flex items-center justify-center">
                                    <i class="fas fa-headphones text-purple-600 text-3xl"></i>
                                </div>
                                <h4 class="font-semibold text-gray-900 mb-2">AirPods Pro</h4>
                                <p class="text-gray-600 text-sm mb-2">Kablosuz kulak içi kulaklık</p>
                                <p class="text-lg font-bold text-purple-600">₺3.999</p>
                                <button class="w-full mt-3 px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 transition-colors">
                                    Sepete Ekle
                                </button>
                            </div>
                        </div>
                        
                        <h3 class="text-lg font-semibold text-gray-900 mb-3">Neden Bizi Tercih Etmelisiniz?</h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                            <div class="flex items-start space-x-3">
                                <div class="flex-shrink-0">
                                    <i class="fas fa-shipping-fast text-blue-600 text-xl"></i>
                                </div>
                                <div>
                                    <h4 class="font-semibold text-gray-900 mb-1">Hızlı Kargo</h4>
                                    <p class="text-gray-600 text-sm">Aynı gün kargo imkanı</p>
                                </div>
                            </div>
                            
                            <div class="flex items-start space-x-3">
                                <div class="flex-shrink-0">
                                    <i class="fas fa-shield-alt text-green-600 text-xl"></i>
                                </div>
                                <div>
                                    <h4 class="font-semibold text-gray-900 mb-1">Güvenli Alışveriş</h4>
                                    <p class="text-gray-600 text-sm">256-bit SSL şifreleme</p>
                                </div>
                            </div>
                            
                            <div class="flex items-start space-x-3">
                                <div class="flex-shrink-0">
                                    <i class="fas fa-undo text-orange-600 text-xl"></i>
                                </div>
                                <div>
                                    <h4 class="font-semibold text-gray-900 mb-1">Kolay İade</h4>
                                    <p class="text-gray-600 text-sm">14 gün içinde ücretsiz iade</p>
                                </div>
                            </div>
                            
                            <div class="flex items-start space-x-3">
                                <div class="flex-shrink-0">
                                    <i class="fas fa-headset text-purple-600 text-xl"></i>
                                </div>
                                <div>
                                    <h4 class="font-semibold text-gray-900 mb-1">7/24 Destek</h4>
                                    <p class="text-gray-600 text-sm">Canlı destek hizmeti</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Debug Info -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h2 class="text-lg font-semibold text-gray-900">Debug Bilgileri</h2>
                </div>
                <div class="p-6">
                    <div id="debugInfo" class="bg-gray-50 p-4 rounded-lg font-mono text-sm">
                        <div>Sayfa yüklendi: <span id="loadTime"></span></div>
                        <div>LocalStorage durumu: <span id="localStorageStatus"></span></div>
                        <div>Widget durumu: <span id="widgetStatus"></span></div>
                        <div>Son anket gösterim: <span id="lastShown"></span></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Test Script -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Dinamik olarak script yükle
            loadExitSurveyScript();
            updateDebugInfo();
            
            // Test buttons
            document.getElementById('triggerExitSurvey').addEventListener('click', function() {
                if (window.initExitSurvey) {
                    // Manuel tetikleme için showSurveyModal fonksiyonunu çağır
                    const event = new Event('mouseleave');
                    event.clientY = -10; // Sayfanın üstünden çıkış simüle et
                    document.dispatchEvent(event);
                } else {
                    alert('Exit Survey widget yüklenmedi!');
                }
            });
            
            document.getElementById('simulatePageExit').addEventListener('click', function() {
                // Mouse leave event simülasyonu
                const event = new Event('mouseleave');
                document.dispatchEvent(event);
            });
            
            document.getElementById('clearLocalStorage').addEventListener('click', function() {
                // Yeni localStorage key'lerini temizle
                localStorage.removeItem('exitSurvey_dd0ed437-18b2-47d5-8c53-67feb207737c_shown');
                localStorage.removeItem('exitSurvey_550e8400-e29b-41d4-a716-446655440000_shown');
                localStorage.removeItem('exitSurvey_bcd6bad9-8dfc-4ed4-924f-59a1bd0bd294_shown');
                // Eski key'i de temizle
                localStorage.removeItem('exitSurveyLastShown');
                updateDebugInfo();
                alert('LocalStorage temizlendi!');
            });
            
            // Debug info güncelleme
            function updateDebugInfo() {
                document.getElementById('loadTime').textContent = new Date().toLocaleString('tr-TR');

                // Yeni localStorage key'ini kontrol et
                const lastShown = localStorage.getItem('exitSurvey_dd0ed437-18b2-47d5-8c53-67feb207737c_shown') ||
                                localStorage.getItem('exitSurvey_550e8400-e29b-41d4-a716-446655440000_shown');
                document.getElementById('lastShown').textContent = lastShown ? 'Gösterildi' : 'Hiç gösterilmedi';

                document.getElementById('localStorageStatus').textContent = lastShown ? 'Var' : 'Yok';

                // Yeni widget yapısını kontrol et
                const widgetStatus = window.initExitSurvey ? 'Yüklendi ✅' : 'Yüklenmedi ❌';
                document.getElementById('widgetStatus').textContent = widgetStatus;
            }
            
            // Periyodik güncelleme
            setInterval(updateDebugInfo, 1000);

            // Script yükleme fonksiyonu
            function loadExitSurveyScript() {
                // Test için bilinen company ID'leri dene
                const companyIds = [
                    'dd0ed437-18b2-47d5-8c53-67feb207737c', // Admin company (gerçek)
                    '550e8400-e29b-41d4-a716-446655440000', // Test company 1
                    'bcd6bad9-8dfc-4ed4-924f-59a1bd0bd294'  // Test company 2
                ];

                // İlk company ID ile dene
                const scriptUrl = `/api/ExitSurveyApi/widget/${companyIds[0]}`;
                const script = document.createElement('script');
                script.src = scriptUrl;
                script.onload = function() {
                    console.log('Exit Survey widget loaded successfully');
                    updateDebugInfo();

                    // Widget yüklendikten sonra kısa bir süre bekle
                    setTimeout(() => {
                        if (window.initExitSurvey) {
                            console.log('Exit Survey widget ready');
                            updateDebugInfo();
                        }
                    }, 1000);
                };
                script.onerror = function() {
                    console.log('Failed to load widget with first company ID, trying second...');
                    // İkinci company ID ile dene
                    const script2 = document.createElement('script');
                    script2.src = `/api/ExitSurveyApi/widget/${companyIds[1]}`;
                    script2.onload = function() {
                        console.log('Exit Survey widget loaded with second company ID');
                        updateDebugInfo();

                        setTimeout(() => {
                            if (window.initExitSurvey) {
                                console.log('Exit Survey widget ready');
                                updateDebugInfo();
                            }
                        }, 1000);
                    };
                    script2.onerror = function() {
                        console.log('Failed to load Exit Survey widget');
                        updateDebugInfo();
                    };
                    document.head.appendChild(script2);
                };
                document.head.appendChild(script);
            }
        });
    </script>
</body>
</html>
