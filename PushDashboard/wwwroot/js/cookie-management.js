// Cookie Management JavaScript
document.addEventListener('DOMContentLoaded', function() {
    // Initialize
    initializeCookieManagement();
    loadPreview();
    
    // Event listeners
    document.getElementById('saveSettings').addEventListener('click', saveSettings);
    document.getElementById('refreshPreview').addEventListener('click', loadPreview);
    document.getElementById('copyScript').addEventListener('click', copyScriptUrl);
    
    // Real-time preview updates
    const inputs = document.querySelectorAll('input, textarea, select');
    inputs.forEach(input => {
        input.addEventListener('input', debounce(updatePreview, 500));
        input.addEventListener('change', debounce(updatePreview, 500));
    });
});

function initializeCookieManagement() {
    console.log('Cookie Management initialized');
}

function saveSettings() {
    const saveButton = document.getElementById('saveSettings');
    const originalText = saveButton.textContent;
    
    // Disable button and show loading
    saveButton.disabled = true;
    saveButton.textContent = 'Kaydediliyor...';
    
    // Collect form data
    const formData = {
        bannerTitle: document.getElementById('bannerTitle').value,
        bannerDescription: document.getElementById('bannerDescription').value,
        acceptButtonText: document.getElementById('acceptButtonText').value,
        rejectButtonText: document.getElementById('rejectButtonText').value,
        settingsButtonText: document.getElementById('settingsButtonText').value,
        saveButtonText: document.getElementById('saveButtonText').value,
        bannerPosition: document.getElementById('bannerPosition').value,
        bannerBackgroundColor: document.getElementById('bannerBackgroundColor').value,
        bannerTextColor: document.getElementById('bannerTextColor').value,
        acceptButtonColor: document.getElementById('acceptButtonColor').value,
        rejectButtonColor: document.getElementById('rejectButtonColor').value,
        settingsButtonColor: document.getElementById('settingsButtonColor').value,
        borderRadius: document.getElementById('borderRadius').value,
        showSettingsButton: document.getElementById('showSettingsButton').checked,
        enableAnimation: document.getElementById('enableAnimation').checked,
        isActive: true, // Modüle sahipse her zaman aktif
        cookieExpiryDays: parseInt(document.getElementById('cookieExpiryDays').value),
        categories: collectCategories()
    };
    
    // Get anti-forgery token
    const token = document.querySelector('input[name="__RequestVerificationToken"]').value;
    
    // Send AJAX request
    fetch('/CookieManagement/SaveSettings', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'RequestVerificationToken': token
        },
        body: JSON.stringify(formData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification(data.message, 'success');
            loadPreview(); // Refresh preview
        } else {
            showNotification(data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('Bir hata oluştu. Lütfen tekrar deneyin.', 'error');
    })
    .finally(() => {
        // Re-enable button
        saveButton.disabled = false;
        saveButton.textContent = originalText;
    });
}

function collectCategories() {
    const categories = [];
    const categoryElements = document.querySelectorAll('[data-category-id]');
    
    categoryElements.forEach(element => {
        const categoryId = element.getAttribute('data-category-id');
        const name = element.querySelector('h4').textContent;
        const description = element.querySelector('.category-description').value;
        const isRequired = element.querySelector('input[type="checkbox"]').disabled;
        const isEnabled = element.querySelector('input[type="checkbox"]').checked;
        
        categories.push({
            id: categoryId,
            name: name,
            description: description,
            isRequired: isRequired,
            isEnabled: isEnabled
        });
    });
    
    return categories;
}

function loadPreview() {
    const previewContainer = document.getElementById('previewContainer');
    previewContainer.innerHTML = '<div class="text-center text-gray-500 py-8"><p>Önizleme yükleniyor...</p></div>';
    
    fetch('/CookieManagement/GetPreviewData')
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            generatePreview(data.data);
        } else {
            previewContainer.innerHTML = '<div class="text-center text-red-500 py-8"><p>Önizleme yüklenemedi</p></div>';
        }
    })
    .catch(error => {
        console.error('Error loading preview:', error);
        previewContainer.innerHTML = '<div class="text-center text-red-500 py-8"><p>Önizleme yüklenemedi</p></div>';
    });
}

function updatePreview() {
    // Get current form values and generate preview
    const config = {
        isActive: true, // Modüle sahipse her zaman aktif
        bannerTitle: document.getElementById('bannerTitle').value,
        bannerDescription: document.getElementById('bannerDescription').value,
        acceptButtonText: document.getElementById('acceptButtonText').value,
        rejectButtonText: document.getElementById('rejectButtonText').value,
        settingsButtonText: document.getElementById('settingsButtonText').value,
        bannerPosition: document.getElementById('bannerPosition').value,
        bannerBackgroundColor: document.getElementById('bannerBackgroundColor').value,
        bannerTextColor: document.getElementById('bannerTextColor').value,
        acceptButtonColor: document.getElementById('acceptButtonColor').value,
        rejectButtonColor: document.getElementById('rejectButtonColor').value,
        settingsButtonColor: document.getElementById('settingsButtonColor').value,
        borderRadius: document.getElementById('borderRadius').value,
        showSettingsButton: document.getElementById('showSettingsButton').checked,
        enableAnimation: document.getElementById('enableAnimation').checked,
        categories: collectCategories()
    };
    
    generatePreview(config);
}

function generatePreview(config) {
    const previewContainer = document.getElementById('previewContainer');
    
    if (!config.isActive) {
        previewContainer.innerHTML = '<div class="text-center text-gray-500 py-8"><p>Çerez yönetimi deaktif</p></div>';
        return;
    }
    
    const positionStyles = getPreviewPositionStyles(config.bannerPosition);
    
    const previewHtml = `
        <div class="cookie-banner-preview" style="
            position: absolute;
            background: ${config.bannerBackgroundColor};
            color: ${config.bannerTextColor};
            padding: 16px;
            border-radius: ${config.borderRadius};
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            font-size: 13px;
            line-height: 1.4;
            max-width: 320px;
            ${positionStyles}
        ">
            <h3 style="margin: 0 0 8px 0; font-size: 14px; font-weight: 600;">${config.bannerTitle}</h3>
            <p style="margin: 0 0 12px 0; opacity: 0.9;">${config.bannerDescription}</p>
            <div style="display: flex; gap: 8px; flex-wrap: wrap;">
                <button style="
                    padding: 6px 12px;
                    border: none;
                    border-radius: 4px;
                    background: ${config.acceptButtonColor};
                    color: white;
                    font-size: 12px;
                    font-weight: 500;
                    cursor: pointer;
                ">${config.acceptButtonText}</button>
                <button style="
                    padding: 6px 12px;
                    border: none;
                    border-radius: 4px;
                    background: ${config.rejectButtonColor};
                    color: white;
                    font-size: 12px;
                    font-weight: 500;
                    cursor: pointer;
                ">${config.rejectButtonText}</button>
                ${config.showSettingsButton ? `
                <button style="
                    padding: 6px 12px;
                    border: none;
                    border-radius: 4px;
                    background: ${config.settingsButtonColor};
                    color: white;
                    font-size: 12px;
                    font-weight: 500;
                    cursor: pointer;
                ">${config.settingsButtonText}</button>
                ` : ''}
            </div>
        </div>
    `;
    
    previewContainer.innerHTML = previewHtml;
}

function getPreviewPositionStyles(position) {
    switch(position) {
        case 'top':
            return 'top: 10px; left: 10px;';
        case 'left':
            return 'left: 10px; top: 50%; transform: translateY(-50%);';
        case 'right':
            return 'right: 10px; top: 50%; transform: translateY(-50%);';
        default: // bottom
            return 'bottom: 10px; left: 10px;';
    }
}

function copyScriptUrl() {
    const scriptUrl = document.getElementById('scriptUrl');
    scriptUrl.select();
    scriptUrl.setSelectionRange(0, 99999); // For mobile devices
    
    navigator.clipboard.writeText(scriptUrl.value).then(function() {
        showNotification('Script URL kopyalandı!', 'success');
    }, function(err) {
        console.error('Could not copy text: ', err);
        showNotification('Kopyalama başarısız', 'error');
    });
}

// Utility function for debouncing
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Notification function
function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg max-w-sm transition-all duration-300 transform translate-x-full`;

    // Set colors based on type
    switch(type) {
        case 'success':
            notification.className += ' bg-green-500 text-white';
            break;
        case 'error':
            notification.className += ' bg-red-500 text-white';
            break;
        case 'warning':
            notification.className += ' bg-yellow-500 text-white';
            break;
        default:
            notification.className += ' bg-blue-500 text-white';
    }

    notification.innerHTML = `
        <div class="flex items-center justify-between">
            <span>${message}</span>
            <button onclick="this.parentElement.parentElement.remove()" class="ml-4 text-white hover:text-gray-200">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>
    `;

    document.body.appendChild(notification);

    // Animate in
    setTimeout(() => {
        notification.classList.remove('translate-x-full');
    }, 100);

    // Auto remove after 5 seconds
    setTimeout(() => {
        notification.classList.add('translate-x-full');
        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, 300);
    }, 5000);
}
