/**
 * Exit Survey Widget
 * Dinamik çıkış anketi widget'ı
 */
(function() {
    'use strict';

    // Widget konfigürasyonu (backend'den gelecek)
    let config = null;
    let hasShown = false;

    // Widget'ı başlat
    function initExitSurvey(widgetConfig) {
        config = widgetConfig;
        
        if (!config || !config.isActive || !config.questions || config.questions.length === 0) {
            return;
        }

        // LocalStorage kontrolü
        const storageKey = `exitSurvey_${config.companyId}_shown`;
        if (localStorage.getItem(storageKey)) {
            return; // Daha önce gösterilmiş
        }

        // Mouse leave event'i ekle
        document.addEventListener('mouseleave', handleMouseLeave);
        
        // Sayfa kapatma event'i ekle
        window.addEventListener('beforeunload', handleBeforeUnload);
    }

    // Mouse leave handler
    function handleMouseLeave(e) {
        if (hasShown) return;
        
        // Mouse'un sayfanın üst kısmından çıkıp çıkmadığını kontrol et
        if (e.clientY <= 0) {
            showSurveyModal();
        }
    }

    // Sayfa kapatma handler
    function handleBeforeUnload(e) {
        if (hasShown) return;
        
        showSurveyModal();
        // Tarayıcı confirmation dialog'u göstermek için
        e.preventDefault();
        e.returnValue = '';
    }

    // Anket modalını göster
    function showSurveyModal() {
        if (hasShown) return;
        hasShown = true;

        // Modal HTML'ini oluştur
        const modalHtml = createModalHtml();
        
        // Modal'ı DOM'a ekle
        document.body.insertAdjacentHTML('beforeend', modalHtml);
        
        // Event listener'ları ekle
        attachEventListeners();
        
        // Modal'ı göster
        const modal = document.getElementById('exitSurveyModal');
        modal.style.display = 'flex';
        
        // Animasyon için timeout
        setTimeout(() => {
            modal.classList.add('show');
        }, 10);
    }

    // Modal HTML'ini oluştur
    function createModalHtml() {
        const questionsHtml = config.questions.map((question, index) => {
            return createQuestionHtml(question, index);
        }).join('');

        return `
            <div id="exitSurveyModal" class="exit-survey-modal">
                <div class="exit-survey-overlay"></div>
                <div class="exit-survey-content">
                    <div class="exit-survey-header">
                        <h3>${config.title || 'Anket'}</h3>
                        <button type="button" class="exit-survey-close" id="exitSurveyClose">×</button>
                    </div>
                    <div class="exit-survey-body">
                        <p>${config.description || 'Lütfen aşağıdaki soruları cevaplayın.'}</p>
                        <form id="exitSurveyForm">
                            ${questionsHtml}
                            <div class="exit-survey-actions">
                                <button type="submit" id="exitSurveySubmitBtn">${config.submitButtonText || 'Gönder'}</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        `;
    }

    // Soru HTML'ini oluştur
    function createQuestionHtml(question, index) {
        const isRequired = question.isRequired ? 'required' : '';
        const requiredMark = question.isRequired ? '<span class="required">*</span>' : '';
        
        let inputHtml = '';
        
        switch (question.questionType) {
            case 0: // MultipleChoice
                const options = question.options || [];
                inputHtml = `<div class="question-input">${options.map(option => `
                    <label class="radio-option">
                        <input type="radio" name="question_${question.id}" value="${option}" ${isRequired}>
                        <span>${option}</span>
                    </label>
                `).join('')}</div>`;
                break;

            case 1: // Text
                inputHtml = `<div class="question-input"><textarea name="question_${question.id}" placeholder="Cevabınızı yazın..." ${isRequired}></textarea></div>`;
                break;
                
            case 2: // YesNo
                inputHtml = `
                    <div class="question-input">
                        <label class="radio-option">
                            <input type="radio" name="question_${question.id}" value="Evet" ${isRequired}>
                            <span>Evet</span>
                        </label>
                        <label class="radio-option">
                            <input type="radio" name="question_${question.id}" value="Hayır" ${isRequired}>
                            <span>Hayır</span>
                        </label>
                    </div>
                `;
                break;
                
            case 3: // Rating
                inputHtml = `
                    <div class="question-input">
                        <div class="rating-container">
                            ${[1,2,3,4,5].map(rating => `
                                <span class="rating-star" data-rating="${rating}">★</span>
                            `).join('')}
                            <input type="hidden" name="question_${question.id}" ${isRequired}>
                        </div>
                    </div>
                `;
                break;
        }
        
        return `
            <div class="question-container" data-question-id="${question.id}">
                <label class="question-label">
                    ${question.questionText} ${requiredMark}
                </label>
                ${inputHtml}
            </div>
        `;
    }

    // Event listener'ları ekle
    function attachEventListeners() {
        // Kapatma butonu
        document.getElementById('exitSurveyClose').addEventListener('click', closeModal);
        
        // Overlay tıklama
        document.querySelector('.exit-survey-overlay').addEventListener('click', closeModal);
        
        // Rating yıldızları
        document.querySelectorAll('.rating-star').forEach(star => {
            star.addEventListener('click', handleRatingClick);
            star.addEventListener('mouseover', handleRatingHover);
        });
        
        // Rating container mouse leave
        document.querySelectorAll('.rating-container').forEach(container => {
            container.addEventListener('mouseleave', handleRatingMouseLeave);
        });
        
        // Form submit
        document.getElementById('exitSurveyForm').addEventListener('submit', handleFormSubmit);
    }

    // Rating yıldız tıklama
    function handleRatingClick(e) {
        const rating = parseInt(e.target.dataset.rating);
        const container = e.target.closest('.rating-container');
        const hiddenInput = container.querySelector('input[type="hidden"]');
        
        hiddenInput.value = rating;
        
        // Yıldızları güncelle
        updateRatingStars(container, rating);
    }

    // Rating yıldız hover
    function handleRatingHover(e) {
        const rating = parseInt(e.target.dataset.rating);
        const container = e.target.closest('.rating-container');
        
        updateRatingStars(container, rating, true);
    }

    // Rating mouse leave
    function handleRatingMouseLeave(e) {
        const container = e.target;
        const hiddenInput = container.querySelector('input[type="hidden"]');
        const currentRating = parseInt(hiddenInput.value) || 0;
        
        updateRatingStars(container, currentRating);
    }

    // Rating yıldızlarını güncelle
    function updateRatingStars(container, rating, isHover = false) {
        const stars = container.querySelectorAll('.rating-star');
        
        stars.forEach((star, index) => {
            const starRating = index + 1;
            star.classList.remove('active', 'hover');
            
            if (starRating <= rating) {
                star.classList.add(isHover ? 'hover' : 'active');
            }
        });
    }

    // Form submit
    function handleFormSubmit(e) {
        e.preventDefault();
        
        // Zorunlu alanları kontrol et
        const requiredFields = document.querySelectorAll('#exitSurveyForm [required]');
        let hasError = false;
        
        // Önceki hata stillerini temizle
        document.querySelectorAll('.error-message').forEach(msg => msg.remove());
        document.querySelectorAll('.error').forEach(el => el.classList.remove('error'));
        
        requiredFields.forEach(field => {
            const isRadio = field.type === 'radio';
            const isValid = isRadio ? 
                document.querySelector(`input[name="${field.name}"]:checked`) : 
                field.value.trim() !== '';
            
            if (!isValid) {
                hasError = true;
                const container = field.closest('.question-container');
                container.classList.add('error');
                
                // Hata mesajı ekle
                const errorMsg = document.createElement('div');
                errorMsg.className = 'error-message';
                errorMsg.textContent = 'Bu alan zorunludur.';
                container.appendChild(errorMsg);
            }
        });
        
        if (hasError) {
            // İlk hataya scroll yap
            const firstError = document.querySelector('.error');
            if (firstError) {
                firstError.scrollIntoView({ behavior: 'smooth', block: 'center' });
            }
            return;
        }
        
        // Form verilerini topla ve gönder
        submitSurvey();
    }

    // Anketi gönder
    function submitSurvey() {
        const formData = new FormData(document.getElementById('exitSurveyForm'));
        const responses = [];
        
        config.questions.forEach(question => {
            const value = formData.get(`question_${question.id}`);
            if (value) {
                responses.push({
                    questionId: question.id,
                    responseText: value,
                    ratingValue: question.questionType === 3 ? parseInt(value) : null
                });
            }
        });
        
        const submitData = {
            companyId: config.companyId,
            sessionId: generateSessionId(),
            answers: responses
        };

        // AJAX ile gönder
        fetch('/api/ExitSurveyApi/submit', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(submitData)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showThankYouMessage();
                markAsShown();
            } else {
                showErrorMessage();
            }
        })
        .catch(() => {
            showErrorMessage();
        });
    }

    // Teşekkür mesajını göster
    function showThankYouMessage() {
        const modal = document.getElementById('exitSurveyModal');
        const content = modal.querySelector('.exit-survey-content');
        
        content.innerHTML = `
            <div class="exit-survey-header">
                <h3>Teşekkürler!</h3>
            </div>
            <div class="exit-survey-body">
                <p>${config.thankYouMessage || 'Geri bildiriminiz için teşekkür ederiz.'}</p>
                <button type="button" onclick="closeExitSurveyModal()">Kapat</button>
            </div>
        `;
        
        // 3 saniye sonra otomatik kapat
        setTimeout(() => {
            closeModal();
        }, 3000);
    }

    // Hata mesajını göster
    function showErrorMessage() {
        alert('Anket gönderilirken bir hata oluştu. Lütfen tekrar deneyin.');
    }

    // Modal'ı kapat
    function closeModal() {
        const modal = document.getElementById('exitSurveyModal');
        if (modal) {
            modal.classList.remove('show');
            setTimeout(() => {
                modal.remove();
            }, 300);
        }
        markAsShown();
    }

    // Gösterildi olarak işaretle
    function markAsShown() {
        const storageKey = `exitSurvey_${config.companyId}_shown`;
        localStorage.setItem(storageKey, 'true');
    }

    // Session ID oluştur
    function generateSessionId() {
        return 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    // Global fonksiyonlar
    window.initExitSurvey = initExitSurvey;
    window.closeExitSurveyModal = closeModal;
})();
