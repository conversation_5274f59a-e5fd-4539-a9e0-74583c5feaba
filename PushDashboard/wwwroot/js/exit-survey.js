// Exit Survey Management JavaScript

$(document).ready(function() {
    // Initialize
    loadScriptCode();
    
    // Form submissions
    $('#exitSurveySettingsForm').on('submit', function(e) {
        e.preventDefault();
        saveSettings();
    });

    $('#saveQuestionBtn').on('click', function() {
        saveQuestion();
    });

    // Button clicks
    $('#addQuestionBtn').on('click', function() {
        openQuestionModal();
    });

    $('#previewBtn').on('click', function() {
        previewSurvey();
    });

    $('#copyScriptBtn').on('click', function() {
        copyScriptCode();
    });

    // Question management
    $(document).on('click', '.edit-question-btn', function() {
        const questionId = $(this).data('question-id');
        editQuestion(questionId);
    });

    $(document).on('click', '.delete-question-btn', function() {
        const questionId = $(this).data('question-id');
        deleteQuestion(questionId);
    });

    // Question type change
    $('#questionType').on('change', function() {
        toggleOptionsContainer();
    });

    // Add option button
    $('#addOptionBtn').on('click', function() {
        addOption();
    });

    // Remove option
    $(document).on('click', '.remove-option-btn', function() {
        $(this).closest('.option-item').remove();
    });

    // Modal close events
    $('#closeModalBtn, #cancelModalBtn').on('click', function() {
        closeModal();
    });

    // Close modal when clicking outside
    $('#questionModal').on('click', function(e) {
        if (e.target === this) {
            closeModal();
        }
    });
});

// Save settings
function saveSettings() {
    const formData = {
        title: $('#title').val(),
        description: $('#description').val(),
        submitButtonText: $('#submitButtonText').val(),
        cancelButtonText: $('#cancelButtonText').val(),
        thankYouMessage: $('#thankYouMessage').val(),
        backgroundColor: $('#backgroundColor').val(),
        textColor: $('#textColor').val(),
        submitButtonColor: $('#submitButtonColor').val(),
        cancelButtonColor: $('#cancelButtonColor').val(),
        borderRadius: '8px',
        enableAnimation: $('#enableAnimation').is(':checked'),
        showOnPageExit: $('#showOnPageExit').is(':checked'),
        showOnTabClose: $('#showOnTabClose').is(':checked'),
        delayBeforeShow: parseInt($('#delayBeforeShow').val()) || 0,
        showFrequencyDays: parseInt($('#showFrequencyDays').val()) || 30,
        showOnMobile: $('#showOnMobile').is(':checked'),
        showOnDesktop: true,
        isActive: $('#isActive').val() === 'true'
    };

    $.ajax({
        url: '/ExitSurvey/SaveSettings',
        type: 'POST',
        contentType: 'application/json',
        data: JSON.stringify(formData),
        success: function(response) {
            if (response.success) {
                showToast('Ayarlar başarıyla kaydedildi!', 'success');
                loadScriptCode();
            } else {
                showToast(response.message || 'Bir hata oluştu!', 'error');
            }
        },
        error: function() {
            showToast('Bir hata oluştu!', 'error');
        }
    });
}

// Load script code
function loadScriptCode() {
    $.ajax({
        url: '/ExitSurvey/GetScriptUrl',
        type: 'GET',
        success: function(response) {
            if (response.success) {
                const scriptCode = `<script src="${response.scriptUrl}"></script>`;
                $('#scriptCode').val(scriptCode);
            }
        }
    });
}

// Copy script code
function copyScriptCode() {
    const scriptCode = $('#scriptCode')[0];
    scriptCode.select();
    scriptCode.setSelectionRange(0, 99999);
    
    try {
        document.execCommand('copy');
        showToast('Script kodu kopyalandı!', 'success');
    } catch (err) {
        showToast('Kopyalama başarısız!', 'error');
    }
}

// Question Modal Management
function openQuestionModal(questionData = null) {
    const isEdit = questionData && questionData.id > 0;

    $('#questionId').val(questionData ? questionData.id : 0);
    $('#questionText').val(questionData ? questionData.questionText : '');
    $('#questionType').val(questionData ? questionData.questionType : 0);
    $('#isRequired').prop('checked', questionData ? questionData.isRequired : false);

    // Modal başlığını güncelle
    $('#questionModalTitle').text(isEdit ? 'Soru Düzenle' : 'Yeni Soru Ekle');
    $('#saveQuestionBtn').text(isEdit ? 'Güncelle' : 'Kaydet');

    // Clear options
    $('#optionsList').empty();

    if (questionData && questionData.options) {
        questionData.options.forEach(option => {
            addOption(option);
        });
    }

    toggleOptionsContainer();
    showModal();
}

function showModal() {
    $('#questionModal').removeClass('hidden');
}

function closeModal() {
    $('#questionModal').addClass('hidden');
}

function toggleOptionsContainer() {
    const questionType = parseInt($('#questionType').val());
    const optionsContainer = $('#optionsContainer');

    if (questionType === 0) { // Multiple Choice
        optionsContainer.removeClass('hidden');
        if ($('#optionsList').children().length === 0) {
            addOption();
            addOption();
        }
    } else {
        optionsContainer.addClass('hidden');
    }
}

function addOption(value = '') {
    const optionHtml = `
        <div class="option-item flex space-x-2">
            <input type="text" class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent option-input"
                   value="${value}" placeholder="Seçenek metni">
            <button type="button" class="px-3 py-2 bg-red-100 hover:bg-red-200 text-red-700 rounded-md transition-colors duration-200 remove-option-btn">
                <i class="fas fa-times"></i>
            </button>
        </div>
    `;
    $('#optionsList').append(optionHtml);
}

function saveQuestion() {
    const questionType = parseInt($('#questionType').val());
    const options = [];
    
    if (questionType === 0) { // Multiple Choice
        $('.option-input').each(function() {
            const value = $(this).val().trim();
            if (value) {
                options.push(value);
            }
        });
        
        if (options.length < 2) {
            showToast('Çoktan seçmeli sorular için en az 2 seçenek gereklidir!', 'error');
            return;
        }
    }
    
    const questionId = parseInt($('#questionId').val()) || 0;
    const isEdit = questionId > 0;

    const questionData = {
        id: questionId,
        questionText: $('#questionText').val(),
        questionType: questionType,
        isRequired: $('#isRequired').is(':checked'),
        isActive: true,
        sortOrder: $('.question-item').length + 1,
        options: options
    };

    console.log('Saving question:', questionData, 'isEdit:', isEdit); // Debug için

    $.ajax({
        url: isEdit ? '/ExitSurvey/UpdateQuestion' : '/ExitSurvey/SaveQuestion',
        type: 'POST',
        contentType: 'application/json',
        data: JSON.stringify(questionData),
        success: function(response) {
            if (response.success) {
                showToast('Soru başarıyla kaydedildi!', 'success');
                closeModal();
                location.reload(); // Refresh to show updated questions
            } else {
                showToast(response.message || 'Bir hata oluştu!', 'error');
            }
        },
        error: function() {
            showToast('Bir hata oluştu!', 'error');
        }
    });
}

function editQuestion(questionId) {
    $.ajax({
        url: '/ExitSurvey/GetQuestion',
        type: 'GET',
        data: { questionId: questionId },
        success: function(response) {
            if (response.success) {
                const question = response.question;
                console.log('Question data received:', question); // Debug için
                const questionData = {
                    id: question.id || question.Id,
                    questionText: question.questionText || question.QuestionText,
                    questionType: question.questionType || question.QuestionType,
                    isRequired: question.isRequired || question.IsRequired,
                    options: question.options ? (typeof question.options === 'string' ? JSON.parse(question.options) : question.options) :
                            (question.Options ? (typeof question.Options === 'string' ? JSON.parse(question.Options) : question.Options) : [])
                };
                console.log('Processed question data:', questionData); // Debug için
                openQuestionModal(questionData);
            } else {
                showToast(response.message || 'Soru bilgileri alınamadı!', 'error');
            }
        },
        error: function() {
            showToast('Soru bilgileri alınırken bir hata oluştu!', 'error');
        }
    });
}

function deleteQuestion(questionId) {
    if (!confirm('Bu soruyu silmek istediğinizden emin misiniz?')) {
        return;
    }
    
    $.ajax({
        url: '/ExitSurvey/DeleteQuestion',
        type: 'POST',
        data: { questionId: questionId },
        success: function(response) {
            if (response.success) {
                showToast('Soru başarıyla silindi!', 'success');
                $(`.question-item[data-question-id="${questionId}"]`).remove();
            } else {
                showToast(response.message || 'Bir hata oluştu!', 'error');
            }
        },
        error: function() {
            showToast('Bir hata oluştu!', 'error');
        }
    });
}

function previewSurvey() {
    $.ajax({
        url: '/ExitSurvey/Preview',
        type: 'GET',
        success: function(response) {
            if (response.success && response.config) {
                if (response.config.questions && response.config.questions.length > 0) {
                    showPreviewModal(response.config);
                } else {
                    showToast('Önizleme için en az bir soru eklemelisiniz.', 'warning');
                }
            } else {
                showToast('Önizleme için önce anket ayarlarını kaydedin.', 'warning');
            }
        },
        error: function() {
            showToast('Önizleme yüklenirken bir hata oluştu!', 'error');
        }
    });
}

function showPreviewModal(config) {
    // Create preview modal
    const previewModalHtml = `
        <div class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50" id="previewModal">
            <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-2/3 lg:w-1/2 shadow-lg rounded-md bg-white">
                <div class="mt-3">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-lg font-semibold text-gray-900">Çıkış Anketi Önizlemesi</h3>
                        <button type="button" class="text-gray-400 hover:text-gray-600" id="closePreviewBtn">
                            <i class="fas fa-times text-xl"></i>
                        </button>
                    </div>

                    <div class="bg-gray-100 p-4 rounded-lg mb-4">
                        <p class="text-sm text-gray-600 mb-2">Bu önizleme, anketin gerçek görünümünü simüle eder:</p>
                        <div id="previewContent"></div>
                    </div>

                    <div class="flex justify-end">
                        <button type="button" class="px-4 py-2 bg-gray-300 hover:bg-gray-400 text-gray-800 font-medium rounded-md transition-colors duration-200" id="closePreviewBtn2">
                            Kapat
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // Remove existing preview modal if any
    $('#previewModal').remove();

    // Add to body
    $('body').append(previewModalHtml);

    // Generate preview content
    generatePreviewContent(config);

    // Add event listeners
    $('#closePreviewBtn, #closePreviewBtn2').on('click', function() {
        $('#previewModal').remove();
    });

    // Close on outside click
    $('#previewModal').on('click', function(e) {
        if (e.target === this) {
            $('#previewModal').remove();
        }
    });
}

function generatePreviewContent(config) {
    let html = `
        <div style="background: ${config.backgroundColor}; color: ${config.textColor}; border-radius: ${config.borderRadius}; padding: 24px; max-width: 500px; margin: 0 auto; box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);">
            <div style="text-align: center; margin-bottom: 20px;">
                <h3 style="margin: 0 0 10px 0; font-size: 20px; font-weight: 600;">${config.title}</h3>
                <p style="margin: 0; font-size: 14px; opacity: 0.8;">${config.description}</p>
            </div>
            <form>
    `;

    config.questions.forEach((question, index) => {
        html += `<div style="margin-bottom: 20px;">`;
        html += `<label style="display: block; margin-bottom: 8px; font-weight: 500; font-size: 14px;">${question.questionText}${question.isRequired ? ' *' : ''}</label>`;

        switch (question.questionType) {
            case 0: // MultipleChoice
                question.options.forEach((option, optionIndex) => {
                    html += `
                        <label style="display: block; margin-bottom: 6px; font-weight: normal; cursor: pointer;">
                            <input type="radio" name="preview_question_${question.id}" value="${option}" style="margin-right: 8px;" disabled>
                            ${option}
                        </label>
                    `;
                });
                break;
            case 1: // Text
                html += `<textarea name="preview_question_${question.id}" disabled style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; font-family: inherit; resize: vertical; min-height: 60px;" placeholder="Cevabınızı yazın..."></textarea>`;
                break;
            case 2: // YesNo
                html += `
                    <label style="display: block; margin-bottom: 6px; font-weight: normal; cursor: pointer;">
                        <input type="radio" name="preview_question_${question.id}" value="Evet" style="margin-right: 8px;" disabled>
                        Evet
                    </label>
                    <label style="display: block; margin-bottom: 6px; font-weight: normal; cursor: pointer;">
                        <input type="radio" name="preview_question_${question.id}" value="Hayır" style="margin-right: 8px;" disabled>
                        Hayır
                    </label>
                `;
                break;
            case 3: // Rating
                html += `<div style="display: flex; gap: 8px; justify-content: center;">`;
                for (let i = 1; i <= 5; i++) {
                    html += `
                        <span style="font-size: 24px; color: #ddd; cursor: pointer;">★</span>
                    `;
                }
                html += `</div>`;
                break;
        }

        html += `</div>`;
    });

    html += `
                <div style="display: flex; gap: 12px; justify-content: center; margin-top: 24px;">
                    <button type="button" style="
                        padding: 10px 20px;
                        border: none;
                        border-radius: 4px;
                        background: ${config.cancelButtonColor};
                        color: white;
                        cursor: not-allowed;
                        font-size: 14px;
                        opacity: 0.7;
                    " disabled>${config.cancelButtonText}</button>
                    <button type="button" style="
                        padding: 10px 20px;
                        border: none;
                        border-radius: 4px;
                        background: ${config.submitButtonColor};
                        color: white;
                        cursor: not-allowed;
                        font-size: 14px;
                        opacity: 0.7;
                    " disabled>${config.submitButtonText}</button>
                </div>
            </form>
        </div>
    `;

    $('#previewContent').html(html);
}

// Utility function for toast notifications
function showToast(message, type = 'info') {
    // Create toast element
    const toastId = 'toast-' + Date.now();
    const bgClass = type === 'success' ? 'bg-green-500' :
                   type === 'error' ? 'bg-red-500' :
                   type === 'warning' ? 'bg-yellow-500' : 'bg-blue-500';

    const iconClass = type === 'success' ? 'fas fa-check-circle' :
                     type === 'error' ? 'fas fa-exclamation-circle' :
                     type === 'warning' ? 'fas fa-exclamation-triangle' : 'fas fa-info-circle';

    const toastHtml = `
        <div id="${toastId}" class="flex items-center w-full max-w-xs p-4 mb-4 text-white ${bgClass} rounded-lg shadow-lg transform transition-all duration-300 ease-in-out translate-x-full opacity-0">
            <div class="inline-flex items-center justify-center flex-shrink-0 w-8 h-8 rounded-lg">
                <i class="${iconClass}"></i>
            </div>
            <div class="ml-3 text-sm font-normal">${message}</div>
            <button type="button" class="ml-auto -mx-1.5 -my-1.5 text-white hover:text-gray-200 rounded-lg focus:ring-2 focus:ring-gray-300 p-1.5 inline-flex h-8 w-8" onclick="removeToast('${toastId}')">
                <i class="fas fa-times"></i>
            </button>
        </div>
    `;

    // Add to toast container (create if doesn't exist)
    if ($('#toastContainer').length === 0) {
        $('body').append('<div id="toastContainer" class="fixed top-4 right-4 z-50"></div>');
    }

    $('#toastContainer').append(toastHtml);

    // Animate in
    setTimeout(() => {
        $(`#${toastId}`).removeClass('translate-x-full opacity-0');
    }, 100);

    // Auto remove after 5 seconds
    setTimeout(() => {
        removeToast(toastId);
    }, 5000);
}

function removeToast(toastId) {
    const toast = $(`#${toastId}`);
    toast.addClass('translate-x-full opacity-0');
    setTimeout(() => {
        toast.remove();
    }, 300);
}
