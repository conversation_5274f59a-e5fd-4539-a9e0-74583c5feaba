/**
 * Trendyol Product Reviews Integration Script
 * Modern ve kullanışlı tasarım ile yorum entegrasyonu
 * PushDashboard yorum taşıma sistemi ile entegre çalışır
 */

(function() {
    'use strict';

    // Konfigürasyon
    const CONFIG = {
        // API URL - kullanıcı tarafından ayarlanacak
        apiBaseUrl: 'https://pub-2bba9adab676432eb161192ccf1b6f98.r2.dev/customer_cst_12345/prd_64042818513_20250606_003007_comments.json',
        
        // Hedef CSS sınıfları
        targetClasses: {
            ratingElement: '.Formline.puanVer',
            commentsContainer: '.commentTiciNewComment',
            productDetailMainRow: '.ProductDetailMainRow'
        },
        
        // Ürün ID'si çıkarma - önce urunKartID değişkenini kontrol et, sonra URL'den çıkar
        getProductId: function() {
            // Önce sayfadaki urunKartID değişkenini kontrol et
            if (typeof window.urunKartID !== 'undefined' && window.urunKartID) {
                console.log('Ürün ID urunKartID değişkeninden alındı:', window.urunKartID);
                return window.urunKartID.toString();
            }

            // urunKartID bulunamazsa URL'den çıkarmaya çalış
            const url = window.location.href;
            const match = url.match(/\/([^\/]+)-p-(\d+)/);
            if (match && match[2]) {
                console.log('Ürün ID URL\'den çıkarıldı:', match[2]);
                return match[2];
            }

            // Son çare olarak data attribute'larını kontrol et
            const productElement = document.querySelector('[data-product-id]');
            if (productElement && productElement.dataset.productId) {
                console.log('Ürün ID data-product-id\'den alındı:', productElement.dataset.productId);
                return productElement.dataset.productId;
            }

            console.warn('Ürün ID bulunamadı! urunKartID değişkeni, URL pattern veya data-product-id attribute\'u kontrol edin.');
            return null;
        }
    };

    // Global değişkenler
    let reviewsData = null;
    let isInitialized = false;
    let allComments = [];
    let filteredComments = [];
    let currentFilters = {
        rating: 'all',
        sortBy: 'newest'
    };
    let currentSlide = 0;
    let commentsPerSlide = 6;

    /**
     * Sistemi başlat
     */
    function init() {
        if (isInitialized) return;

        console.log('Trendyol Yorum Entegrasyonu başlatılıyor...');

        // DOM hazır olana kadar bekle
        function waitForDOM() {
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', function() {
                    setTimeout(waitForDOM, 200);
                });
                return;
            }

            // DOM hazır, ama elementlerin yüklenmesi için biraz daha bekle
            setTimeout(function() {
                if (!isInitialized) {
                    isInitialized = true;
                    setupReviewsIntegration();
                }
            }, 500); // 0.5 saniye bekle
        }

        waitForDOM();
    }

    /**
     * Ana kurulum fonksiyonu
     */
    function setupReviewsIntegration() {
        try {
            const productId = CONFIG.getProductId();
            if (!productId) {
                console.warn('URL\'den ürün ID\'si çıkarılamadı');
                return;
            }

            console.log('Ürün ID:', productId);

            // CSS stillerini ekle
            addModernStyles();

            // Elementlerin hazır olması için bekle
            setTimeout(function() {
                // Ortalama puan gösterimini kur
                setupAverageRatingDisplay();

                // Modern yorum konteynerini kur
                setupModernCommentsContainer();

                // Yorumları getirmeye başla
                setTimeout(function() {
                    fetchReviews(productId);
                }, 300); // API çağrısı için ek bekleme
            }, 200);

        } catch (error) {
            console.error('Yorum entegrasyonu kurulumunda hata:', error);
            // Hata durumunda hiçbir değişiklik yapma
        }
    }

    /**
     * Ortalama puan gösterimini kur
     */
    function setupAverageRatingDisplay() {
        const targetElement = document.querySelector(CONFIG.targetClasses.ratingElement);
        if (!targetElement) {
            console.warn('Puan hedef elementi bulunamadı:', CONFIG.targetClasses.ratingElement);
            return;
        }

        // Orijinal elementi gizle
        targetElement.style.display = 'none';

        // Özel puan gösterimini oluştur
        const ratingContainer = document.createElement('div');
        ratingContainer.className = 'custom-rating-display';
        ratingContainer.innerHTML = `
            <div class="rating-summary" id="ratingSummary">
                <div class="rating-loading">
                    <span>⭐ Yorumlar yükleniyor...</span>
                </div>
            </div>
        `;

        // Orijinal elementin yanına ekle
        targetElement.parentNode.insertBefore(ratingContainer, targetElement.nextSibling);
    }

    /**
     * Modern yorum konteynerini ProductDetailMainRow altında kur
     */
    function setupModernCommentsContainer() {
        try {
            // Element bulma işlemini retry mekanizması ile yap
            function findAndSetupContainer(retryCount = 0) {
                const productDetailMainRow = document.querySelector(CONFIG.targetClasses.productDetailMainRow);

                if (!productDetailMainRow && retryCount < 5) {
                    console.log(`ProductDetailMainRow bulunamadı, tekrar deneniyor... (${retryCount + 1}/5)`);
                    setTimeout(() => findAndSetupContainer(retryCount + 1), 500);
                    return;
                }

                if (!productDetailMainRow) {
                    console.warn('ProductDetailMainRow elementi bulunamadı:', CONFIG.targetClasses.productDetailMainRow);
                    return;
                }

                // Zaten oluşturulmuş mu kontrol et
                if (document.getElementById('modernReviewsContainer')) {
                    console.log('Modern container zaten mevcut');
                    return;
                }

                createModernContainer(productDetailMainRow);
            }

            findAndSetupContainer();

        } catch (error) {
            console.error('Modern yorum konteyneri kurulumunda hata:', error);
        }
    }

    /**
     * Modern container'ı oluştur
     */
    function createModernContainer(productDetailMainRow) {
        try {
            console.log('Modern container oluşturuluyor...');

            // Modern yorum konteynerini oluştur
            const modernContainer = document.createElement('div');
            modernContainer.id = 'modernReviewsContainer';
            modernContainer.className = 'modern-reviews-container';
            modernContainer.innerHTML = `
                <div class="reviews-header">
                    <h2 class="reviews-title">
                        <span class="reviews-icon">💬</span>
                        Müşteri Yorumları
                        <span class="reviews-count" id="reviewsCount">Yükleniyor...</span>
                    </h2>
                </div>

                <div class="reviews-filters-minimal" id="reviewsFilters" style="display: none;">
                    <select class="filter-select-minimal" id="ratingFilter">
                        <option value="all">Tüm Puanlar</option>
                        <option value="5">⭐⭐⭐⭐⭐</option>
                        <option value="4">⭐⭐⭐⭐</option>
                        <option value="3">⭐⭐⭐</option>
                        <option value="2">⭐⭐</option>
                        <option value="1">⭐</option>
                    </select>

                    <select class="filter-select-minimal" id="sortFilter">
                        <option value="newest">En Yeni</option>
                        <option value="oldest">En Eski</option>
                        <option value="highest">En Yüksek Puan</option>
                        <option value="lowest">En Düşük Puan</option>
                        <option value="most_liked">En Beğenilen</option>
                    </select>

                    <button type="button" class="filter-reset-btn-minimal" id="resetFilters">
                        Temizle
                    </button>
                </div>

                <div class="reviews-content-slider" id="reviewsContent">
                    <div class="reviews-loading">
                        <div class="loading-spinner"></div>
                        <span>Yorumlar yükleniyor...</span>
                    </div>
                </div>

                <div class="slider-controls" id="sliderControls" style="display: none;">
                    <button type="button" class="slider-btn slider-prev" id="prevSlide" onclick="goToPrevSlide(event)">‹</button>
                    <div class="slider-dots" id="sliderDots"></div>
                    <button type="button" class="slider-btn slider-next" id="nextSlide" onclick="goToNextSlide(event)">›</button>
                </div>
            `;

            // ProductDetailMainRow'un ALTINA ekle (nextSibling kullan)
            if (productDetailMainRow.nextSibling) {
                productDetailMainRow.parentNode.insertBefore(modernContainer, productDetailMainRow.nextSibling);
            } else {
                productDetailMainRow.parentNode.appendChild(modernContainer);
            }

            console.log('Modern yorum konteyneri DOM\'a eklendi');

            // Event listener'ları ekle - biraz bekleyerek
            setTimeout(function() {
                setupFilterEventListeners();
                // setupSliderEventListeners(); // Removed - using direct function calls instead
                console.log('Event listener\'lar kuruldu');
            }, 100);

        } catch (error) {
            console.error('Modern container oluşturma hatası:', error);
        }
    }

    /**
     * Eski yorum konteynerini kur (fallback)
     */
    function setupCommentsContainer() {
        try {
            const commentsElement = document.querySelector(CONFIG.targetClasses.commentsContainer);
            if (!commentsElement) {
                console.warn('Yorumlar hedef elementi bulunamadı:', CONFIG.targetClasses.commentsContainer);
                return;
            }

            // Mevcut içeriği temizle
            commentsElement.innerHTML = `
                <div class="custom-comments-container" id="customCommentsContainer">
                    <div class="comments-loading">
                        <span>💬 Yorumlar yükleniyor...</span>
                    </div>
                </div>
            `;
        } catch (error) {
            console.error('Yorum konteyneri kurulumunda hata:', error);
            // Hata durumunda hiçbir değişiklik yapma
        }
    }

    /**
     * Yorum verisini normalize et (güvenlik kontrolü)
     */
    function normalizeComment(comment) {
        try {
            // API'den gelen veri zaten doğru formatta, sadece güvenlik kontrolü yap
            return {
                rating: typeof comment.rating === 'number' ? comment.rating : 0,
                user: comment.user || 'Anonim',
                date: comment.date || 'Tarih bilinmiyor',
                elit_customer: !!comment.elit_customer,
                comment: comment.comment || 'Yorum metni bulunamadı',
                photos: Array.isArray(comment.photos) ? comment.photos : [],
                like_count: typeof comment.like_count === 'number' ? comment.like_count : 0
            };
        } catch (error) {
            console.error('Yorum normalize edilirken hata:', error, comment);
            return {
                rating: 0,
                user: 'Anonim',
                date: 'Tarih bilinmiyor',
                elit_customer: false,
                comment: 'Yorum metni bulunamadı',
                photos: [],
                like_count: 0
            };
        }
    }

    /**
     * API'den yorumları getir
     */
    /**
     * Filter event listener'larını kur
     */
    function setupFilterEventListeners() {
        try {
            const ratingFilter = document.getElementById('ratingFilter');
            const sortFilter = document.getElementById('sortFilter');
            const resetFilters = document.getElementById('resetFilters');

            if (ratingFilter) {
                ratingFilter.addEventListener('change', function() {
                    currentFilters.rating = this.value;
                    applyFilters();
                });
            }

            if (sortFilter) {
                sortFilter.addEventListener('change', function() {
                    currentFilters.sortBy = this.value;
                    applyFilters();
                });
            }

            if (resetFilters) {
                resetFilters.addEventListener('click', function(event) {
                    // Sayfa yenilenmesini engelle
                    event.preventDefault();
                    event.stopPropagation();

                    // Filtreleri sıfırla
                    currentFilters.rating = 'all';
                    currentFilters.sortBy = 'newest';
                    if (ratingFilter) ratingFilter.value = 'all';
                    if (sortFilter) sortFilter.value = 'newest';

                    // Slider'ı da sıfırla
                    currentSlide = 0;

                    // Filtreleri uygula
                    applyFilters();
                });
            }
        } catch (error) {
            console.error('Filter event listener kurulumunda hata:', error);
        }
    }

    /**
     * Previous slide function - direct function call instead of event listener
     */
    window.goToPrevSlide = function(event) {
        try {
            if (event) {
                event.preventDefault();
                event.stopPropagation();
            }
            if (currentSlide > 0) {
                currentSlide--;
                updateSliderDisplay();
            }
        } catch (error) {
            console.error('Previous slide hatası:', error);
        }
    };

    /**
     * Next slide function - direct function call instead of event listener
     */
    window.goToNextSlide = function(event) {
        try {
            if (event) {
                event.preventDefault();
                event.stopPropagation();
            }
            const maxSlides = Math.ceil(filteredComments.length / commentsPerSlide);
            if (currentSlide < maxSlides - 1) {
                currentSlide++;
                updateSliderDisplay();
            }
        } catch (error) {
            console.error('Next slide hatası:', error);
        }
    };

    /**
     * Filtreleri uygula
     */
    function applyFilters() {
        try {
            if (!allComments || allComments.length === 0) return;

            // Rating filtresi uygula
            filteredComments = allComments.filter(comment => {
                if (currentFilters.rating === 'all') return true;
                return Math.round(comment.rating) === parseInt(currentFilters.rating);
            });

            // Sıralama uygula
            filteredComments.sort((a, b) => {
                switch (currentFilters.sortBy) {
                    case 'newest':
                        return new Date(b.date) - new Date(a.date);
                    case 'oldest':
                        return new Date(a.date) - new Date(b.date);
                    case 'highest':
                        return b.rating - a.rating;
                    case 'lowest':
                        return a.rating - b.rating;
                    case 'most_liked':
                        return b.like_count - a.like_count;
                    default:
                        return 0;
                }
            });

            // Slider'ı sıfırla ve filtrelenmiş yorumları göster
            currentSlide = 0;
            displayFilteredComments();
        } catch (error) {
            console.error('Filtre uygulamada hata:', error);
        }
    }

    function fetchReviews(productId) {
        try {
            // Kullanıcı tarafından ayarlanacak URL
            const apiUrl = `${CONFIG.apiBaseUrl}`;

            console.log('Yorumlar getiriliyor:', apiUrl);

            updateModernLoadingState('🔄 Yorumlar getiriliyor...');

            fetch(apiUrl)
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP hatası! durum: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    console.log('Yorum verisi alındı:', data);
                    reviewsData = data;
                    displayReviews(data);
                })
                .catch(error => {
                    console.error('Yorumlar getirilirken hata:', error);
                    handleFetchError(error);
                });
        } catch (error) {
            console.error('Yorum getirme işleminde hata:', error);
            // Hata durumunda hiçbir değişiklik yapma
        }
    }

    /**
     * Yorumları UI'da göster
     */
    function displayReviews(data) {
        try {
            console.log('displayReviews çağrıldı, data:', data);

            // API'den direkt array dönüyor, Comments property'si yok
            if (!data || !Array.isArray(data) || data.length === 0) {
                console.log('Yorum verisi boş veya array değil, showNoReviewsMessage çağrılıyor');
                showNoReviewsMessage();
                return;
            }

            // Veri tiplerini normalize et
            const normalizedComments = data.map(comment => normalizeComment(comment));
            console.log('Yorumlar normalize edildi:', normalizedComments);

            // Global değişkenleri güncelle
            allComments = normalizedComments;
            filteredComments = [...allComments];
            console.log('Global değişkenler güncellendi, allComments:', allComments.length);

            // Ortalama puan gösterimini güncelle (normalize edilmiş veri ile)
            updateAverageRatingDisplay({Comments: normalizedComments});

            // Modern container'ın hazır olmasını bekle
            setTimeout(function() {
                const modernContainer = document.getElementById('modernReviewsContainer');
                console.log('Modern container kontrol ediliyor:', !!modernContainer);

                if (modernContainer) {
                    updateModernCommentsDisplay();
                } else {
                    console.log('Modern container bulunamadı, eski sistem kullanılıyor');
                    updateCommentsDisplay(normalizedComments);
                }
            }, 200);

        } catch (error) {
            console.error('Yorumları gösterirken hata:', error);
        }
    }

    /**
     * Modern yorum container'ını güncelle
     */
    function updateModernCommentsDisplay() {
        try {
            console.log('updateModernCommentsDisplay çalışıyor, allComments:', allComments);

            // Element bulma işlemini retry ile yap
            function updateWithRetry(retryCount = 0) {
                const modernContainer = document.getElementById('modernReviewsContainer');
                const reviewsCount = document.getElementById('reviewsCount');
                const reviewsFilters = document.getElementById('reviewsFilters');
                const reviewsContent = document.getElementById('reviewsContent');

                console.log(`Güncelleme denemesi ${retryCount + 1}, elementler:`, {
                    modernContainer: !!modernContainer,
                    reviewsCount: !!reviewsCount,
                    reviewsFilters: !!reviewsFilters,
                    reviewsContent: !!reviewsContent
                });

                // Tüm elementler hazır değilse tekrar dene
                if ((!modernContainer || !reviewsCount || !reviewsFilters || !reviewsContent) && retryCount < 3) {
                    setTimeout(() => updateWithRetry(retryCount + 1), 300);
                    return;
                }

                if (!modernContainer) {
                    console.warn('Modern container bulunamadı!');
                    return;
                }

                // Elementleri güncelle
                if (reviewsCount) {
                    reviewsCount.textContent = `(${allComments.length})`;
                    console.log('Yorum sayısı güncellendi:', allComments.length);
                }

                if (reviewsFilters) {
                    reviewsFilters.style.display = allComments.length > 0 ? 'flex' : 'none';
                    console.log('Filtreler gösterildi:', allComments.length > 0);
                }

                if (reviewsContent) {
                    console.log('displayFilteredComments çağrılıyor...');
                    displayFilteredComments();
                } else {
                    console.error('reviewsContent elementi bulunamadı!');
                }
            }

            updateWithRetry();

        } catch (error) {
            console.error('Modern yorum container güncellemede hata:', error);
        }
    }

    /**
     * Filtrelenmiş yorumları slider ile göster
     */
    function displayFilteredComments() {
        try {
            console.log('displayFilteredComments çalışıyor...');

            const reviewsContent = document.getElementById('reviewsContent');
            const sliderControls = document.getElementById('sliderControls');

            if (!reviewsContent) {
                console.error('reviewsContent elementi bulunamadı!');
                return;
            }

            console.log('filteredComments:', filteredComments);

            if (!filteredComments || filteredComments.length === 0) {
                console.log('Filtrelenmiş yorum yok, no-results gösteriliyor');
                reviewsContent.innerHTML = `
                    <div class="no-reviews-filtered">
                        <div class="no-reviews-icon">🔍</div>
                        <h3>Filtreye uygun yorum bulunamadı</h3>
                        <p>Farklı filtre seçeneklerini deneyebilirsiniz.</p>
                        <button type="button" class="clear-filters-btn" onclick="clearAllFilters(event)">
                            Filtreleri Temizle
                        </button>
                    </div>
                `;
                if (sliderControls) sliderControls.style.display = 'none';
                return;
            }

            console.log('Yorumlar gösteriliyor, toplam:', filteredComments.length);

            // Slider için yorumları göster
            updateSliderDisplay();

            // Slider kontrollerini göster
            if (sliderControls) {
                sliderControls.style.display = filteredComments.length > commentsPerSlide ? 'flex' : 'none';
            }

            console.log('Slider güncellendi');
        } catch (error) {
            console.error('Filtrelenmiş yorumları gösterirken hata:', error);
        }
    }

    /**
     * Slider görünümünü güncelle
     */
    function updateSliderDisplay() {
        try {
            const reviewsContent = document.getElementById('reviewsContent');
            if (!reviewsContent) return;

            const startIndex = currentSlide * commentsPerSlide;
            const endIndex = startIndex + commentsPerSlide;
            const currentComments = filteredComments.slice(startIndex, endIndex);

            const commentsHTML = currentComments.map(comment => generateTrendyolCommentHTML(comment)).join('');

            reviewsContent.innerHTML = `
                <div class="trendyol-comments-slider">
                    ${commentsHTML}
                </div>
            `;

            // Slider kontrollerini güncelle
            updateSliderControls();

        } catch (error) {
            console.error('Slider görünümü güncellenirken hata:', error);
        }
    }

    /**
     * Slider kontrollerini güncelle
     */
    function updateSliderControls() {
        try {
            const prevBtn = document.getElementById('prevSlide');
            const nextBtn = document.getElementById('nextSlide');
            const dotsContainer = document.getElementById('sliderDots');

            const maxSlides = Math.ceil(filteredComments.length / commentsPerSlide);

            // Butonları güncelle
            if (prevBtn) {
                prevBtn.disabled = currentSlide === 0;
                prevBtn.style.opacity = currentSlide === 0 ? '0.5' : '1';
            }

            if (nextBtn) {
                nextBtn.disabled = currentSlide >= maxSlides - 1;
                nextBtn.style.opacity = currentSlide >= maxSlides - 1 ? '0.5' : '1';
            }

            // Dots'ları güncelle
            if (dotsContainer && maxSlides > 1) {
                let dotsHTML = '';
                for (let i = 0; i < maxSlides; i++) {
                    dotsHTML += `<span class="slider-dot ${i === currentSlide ? 'active' : ''}" onclick="goToSlide(${i}, event)"></span>`;
                }
                dotsContainer.innerHTML = dotsHTML;
            }

        } catch (error) {
            console.error('Slider kontrolleri güncellenirken hata:', error);
        }
    }

    /**
     * Trendyol tarzı yorum kartı HTML'i oluştur
     */
    function generateTrendyolCommentHTML(comment) {
        try {
            console.log('generateTrendyolCommentHTML çalışıyor, comment:', comment);

            if (!comment) {
                console.error('Comment verisi boş!');
                return '';
            }

            const photosHTML = comment.photos && comment.photos.length > 0 ?
                `<div class="trendyol-comment-photos">
                    ${comment.photos.slice(0, 2).map(photo =>
                        `<img src="${photo}" alt="Yorum fotoğrafı" class="trendyol-comment-photo" onclick="openPhotoModal('${photo}')">`
                    ).join('')}
                    ${comment.photos.length > 2 ? `<div class="more-photos-trendyol">+${comment.photos.length - 2}</div>` : ''}
                </div>` : '';

            const eliteBadge = comment.elit_customer ? '<span class="trendyol-elite-badge">Elite</span>' : '';

            const html = `
                <div class="trendyol-comment-card">
                    <div class="trendyol-comment-header">
                        <div class="trendyol-user-info">
                            <div class="trendyol-user-avatar">${comment.user ? comment.user.charAt(0).toUpperCase() : 'U'}</div>
                            <div class="trendyol-user-details">
                                <span class="trendyol-user-name">${comment.user || 'Anonim'}</span>
                                ${eliteBadge}
                                <div class="trendyol-comment-rating">
                                    ${generateStarsHTML(comment.rating || 0, true)}
                                </div>
                            </div>
                        </div>
                        <div class="trendyol-comment-date">
                            ${comment.date || 'Tarih bilinmiyor'}
                        </div>
                    </div>
                    <div class="trendyol-comment-body">
                        <p class="trendyol-comment-text">${comment.comment || 'Yorum metni bulunamadı'}</p>
                        ${photosHTML}
                    </div>
                    <div class="trendyol-comment-footer">
                        <div class="trendyol-comment-likes">
                            <span class="like-icon">👍</span>
                            <span class="like-count">${comment.like_count || 0}</span>
                        </div>
                    </div>
                </div>
            `;

            console.log('Trendyol HTML oluşturuldu:', html.substring(0, 100) + '...');
            return html;
        } catch (error) {
            console.error('Trendyol yorum kartı oluşturulurken hata:', error, comment);
            return `<div class="trendyol-comment-card error">Yorum yüklenirken hata oluştu</div>`;
        }
    }

    /**
     * Modern yorum kartı HTML'i oluştur (eski versiyon)
     */
    function generateModernCommentHTML(comment) {
        try {
            console.log('generateModernCommentHTML çalışıyor, comment:', comment);

            if (!comment) {
                console.error('Comment verisi boş!');
                return '';
            }

            const photosHTML = comment.photos && comment.photos.length > 0 ?
                `<div class="modern-comment-photos">
                    ${comment.photos.slice(0, 3).map(photo =>
                        `<img src="${photo}" alt="Yorum fotoğrafı" class="modern-comment-photo" onclick="openPhotoModal('${photo}')">`
                    ).join('')}
                    ${comment.photos.length > 3 ? `<div class="more-photos">+${comment.photos.length - 3}</div>` : ''}
                </div>` : '';

            const eliteBadge = comment.elit_customer ? '<span class="modern-elite-badge">👑 Elite</span>' : '';

            const html = `
                <div class="modern-comment-card">
                    <div class="modern-comment-header">
                        <div class="modern-comment-user">
                            <div class="user-avatar">${comment.user ? comment.user.charAt(0).toUpperCase() : 'U'}</div>
                            <div class="user-info">
                                <span class="user-name">${comment.user || 'Anonim'}</span>
                                ${eliteBadge}
                            </div>
                        </div>
                        <div class="modern-comment-rating">
                            ${generateStarsHTML(comment.rating || 0, true)}
                            <span class="rating-number">${(comment.rating || 0).toFixed(1)}</span>
                        </div>
                    </div>
                    <div class="modern-comment-body">
                        <p class="modern-comment-text">${comment.comment || 'Yorum metni bulunamadı'}</p>
                        ${photosHTML}
                    </div>
                    <div class="modern-comment-footer">
                        <span class="modern-comment-date">📅 ${comment.date || 'Tarih bilinmiyor'}</span>
                        <span class="modern-comment-likes">
                            <span class="like-icon">❤️</span>
                            <span class="like-count">${comment.like_count || 0}</span>
                        </span>
                    </div>
                </div>
            `;

            console.log('HTML oluşturuldu:', html.substring(0, 100) + '...');
            return html;
        } catch (error) {
            console.error('Modern yorum kartı oluşturulurken hata:', error, comment);
            return `<div class="modern-comment-card error">Yorum yüklenirken hata oluştu</div>`;
        }
    }

    /**
     * Tüm filtreleri temizle
     */
    window.clearAllFilters = function(event) {
        try {
            if (event) {
                event.preventDefault();
                event.stopPropagation();
            }

            // Filtreleri sıfırla
            currentFilters.rating = 'all';
            currentFilters.sortBy = 'newest';

            // Form elementlerini sıfırla
            const ratingFilter = document.getElementById('ratingFilter');
            const sortFilter = document.getElementById('sortFilter');

            if (ratingFilter) ratingFilter.value = 'all';
            if (sortFilter) sortFilter.value = 'newest';

            // Slider'ı da sıfırla
            currentSlide = 0;

            // Filtreleri uygula
            applyFilters();
        } catch (error) {
            console.error('Filtreleri temizlerken hata:', error);
        }
    };

    /**
     * Modern loading state güncelle
     */
    function updateModernLoadingState(message) {
        try {
            const reviewsContent = document.getElementById('reviewsContent');
            if (reviewsContent) {
                reviewsContent.innerHTML = `
                    <div class="modern-reviews-loading">
                        <div class="modern-loading-spinner"></div>
                        <span>${message}</span>
                    </div>
                `;
            }

            // Fallback: eski sistem
            updateLoadingState(message);
        } catch (error) {
            console.error('Modern loading state güncellemede hata:', error);
        }
    }

    /**
     * Ortalama puan gösterimini güncelle
     */
    function updateAverageRatingDisplay(data) {
        const ratingSummary = document.getElementById('ratingSummary');
        if (!ratingSummary) return;

        const comments = data.Comments;
        const totalComments = comments.length;
        const averageRating = totalComments > 0 ? 
            comments.reduce((sum, comment) => sum + comment.rating, 0) / totalComments : 0;

        // Puan dağılımını hesapla
        const ratingBreakdown = {1: 0, 2: 0, 3: 0, 4: 0, 5: 0};
        comments.forEach(comment => {
            const rating = Math.round(comment.rating);
            if (rating >= 1 && rating <= 5) {
                ratingBreakdown[rating]++;
            }
        });

        ratingSummary.innerHTML = `
            <div class="rating-display-clean" onclick="scrollToComments()">
                <span class="rating-score-clean">${averageRating.toFixed(1)}</span>
                <div class="rating-stars-clean">
                    ${generateStarsHTML(averageRating)}
                </div>
                <span class="rating-count-clean" onmouseenter="showRatingTooltip(event)" onmouseleave="hideRatingTooltip()">(${totalComments} yorum)</span>
                <div class="rating-tooltip-clean" id="ratingTooltipClean" style="display: none;">
                    <div class="tooltip-header">Puan Dağılımı</div>
                    ${Object.keys(ratingBreakdown).reverse().map(rating => `
                        <div class="rating-breakdown-row">
                            <span class="rating-stars-tooltip">${generateStarsHTML(parseInt(rating), true)}</span>
                            <span class="rating-count-tooltip">${ratingBreakdown[rating]}</span>
                        </div>
                    `).join('')}
                </div>
            </div>
        `;

        // Store rating breakdown for tooltip
        window.currentRatingBreakdown = ratingBreakdown;
    }

    /**
     * Show rating tooltip
     */
    window.showRatingTooltip = function(event) {
        try {
            const tooltip = document.getElementById('ratingTooltipClean');
            if (tooltip) {
                tooltip.style.display = 'block';

                // Position tooltip below the rating count
                const rect = event.target.getBoundingClientRect();
                tooltip.style.left = rect.left + 'px';
                tooltip.style.top = (rect.bottom + 5) + 'px';
            }
        } catch (error) {
            console.error('Tooltip gösterme hatası:', error);
        }
    };

    /**
     * Hide rating tooltip
     */
    window.hideRatingTooltip = function() {
        try {
            const tooltip = document.getElementById('ratingTooltipClean');
            if (tooltip) {
                tooltip.style.display = 'none';
            }
        } catch (error) {
            console.error('Tooltip gizleme hatası:', error);
        }
    };

    /**
     * Yorumlar gösterimini yatay kaydırma ile güncelle
     */
    function updateCommentsDisplay(comments) {
        const container = document.getElementById('customCommentsContainer');
        if (!container) return;

        container.innerHTML = `
            <div class="comments-header">
                <h3>💬 Müşteri Yorumları (${comments.length})</h3>
            </div>
            <div class="comments-scroll-container">
                <div class="comments-list" id="commentsList">
                    ${comments.map(comment => generateCommentHTML(comment)).join('')}
                </div>
            </div>
            <div class="scroll-controls">
                <button class="scroll-btn scroll-left" onclick="scrollComments('left')" title="Sola kaydır">‹</button>
                <button class="scroll-btn scroll-right" onclick="scrollComments('right')" title="Sağa kaydır">›</button>
            </div>
        `;
    }

    /**
     * Tek bir yorum için HTML oluştur
     */
    function generateCommentHTML(comment) {
        const photosHTML = comment.photos && comment.photos.length > 0 ? 
            `<div class="comment-photos">
                ${comment.photos.map(photo => `<img src="${photo}" alt="Yorum fotoğrafı" class="comment-photo" onclick="openPhotoModal('${photo}')">`).join('')}
            </div>` : '';

        const eliteBadge = comment.elit_customer ? '<span class="elite-badge">Elite</span>' : '';

        return `
            <div class="comment-card">
                <div class="comment-header">
                    <div class="comment-user">
                        <span class="user-name">${comment.user}</span>
                        ${eliteBadge}
                    </div>
                    <div class="comment-rating">
                        ${generateStarsHTML(comment.rating, true)}
                    </div>
                </div>
                <div class="comment-body">
                    <p class="comment-text">${comment.comment}</p>
                    ${photosHTML}
                </div>
                <div class="comment-footer">
                    <span class="comment-date">📅 ${comment.date}</span>
                    <span class="comment-likes">❤️ ${comment.like_count}</span>
                </div>
            </div>
        `;
    }

    /**
     * Yıldız HTML'i oluştur
     */
    function generateStarsHTML(rating, small = false) {
        const fullStars = Math.floor(rating);
        const hasHalfStar = rating % 1 >= 0.5;
        const emptyStars = 5 - fullStars - (hasHalfStar ? 1 : 0);
        
        const starClass = small ? 'star-small' : 'star';
        
        let starsHTML = '';
        
        // Dolu yıldızlar
        for (let i = 0; i < fullStars; i++) {
            starsHTML += `<span class="${starClass} star-filled">★</span>`;
        }
        
        // Yarım yıldız
        if (hasHalfStar) {
            starsHTML += `<span class="${starClass} star-half">★</span>`;
        }
        
        // Boş yıldızlar
        for (let i = 0; i < emptyStars; i++) {
            starsHTML += `<span class="${starClass} star-empty">★</span>`;
        }
        
        return starsHTML;
    }

    /**
     * Belirli slide'a git
     */
    window.goToSlide = function(slideIndex, event) {
        try {
            if (event) {
                event.preventDefault();
                event.stopPropagation();
            }
            const maxSlides = Math.ceil(filteredComments.length / commentsPerSlide);
            if (slideIndex >= 0 && slideIndex < maxSlides) {
                currentSlide = slideIndex;
                updateSliderDisplay();
            }
        } catch (error) {
            console.error('Slide değiştirirken hata:', error);
        }
    };

    /**
     * Yorumlar bölümüne kaydır
     */
    window.scrollToComments = function() {
        const modernContainer = document.getElementById('modernReviewsContainer');
        const oldContainer = document.getElementById('customCommentsContainer');

        if (modernContainer) {
            modernContainer.scrollIntoView({ behavior: 'smooth', block: 'start' });
        } else if (oldContainer) {
            oldContainer.scrollIntoView({ behavior: 'smooth', block: 'start' });
        }
    };

    /**
     * Yorumları yatay olarak kaydır
     */
    window.scrollComments = function(direction) {
        const commentsList = document.getElementById('commentsList');
        if (!commentsList) return;

        const scrollAmount = 370; // Kart genişliği + gap
        const currentScroll = commentsList.scrollLeft;

        if (direction === 'left') {
            commentsList.scrollTo({
                left: currentScroll - scrollAmount,
                behavior: 'smooth'
            });
        } else {
            commentsList.scrollTo({
                left: currentScroll + scrollAmount,
                behavior: 'smooth'
            });
        }
    };

    /**
     * Fotoğraf modalını aç
     */
    window.openPhotoModal = function(photoUrl) {
        const modal = document.createElement('div');
        modal.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.9);
            z-index: 10000;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
        `;

        const img = document.createElement('img');
        img.src = photoUrl;
        img.style.cssText = `
            max-width: 90%;
            max-height: 90%;
            border-radius: 12px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.5);
        `;

        modal.appendChild(img);
        document.body.appendChild(modal);

        modal.addEventListener('click', () => {
            document.body.removeChild(modal);
        });
    };

    /**
     * Fetch hatalarını işle - Hata durumunda hiçbir değişiklik yapma
     */
    function handleFetchError(error) {
        try {
            console.error('Yorum getirme hatası:', error);

            // Modern container varsa onu gizle
            const modernContainer = document.getElementById('modernReviewsContainer');
            if (modernContainer) {
                modernContainer.style.display = 'none';
            }

            // Eski container'ı da gizle
            const oldContainer = document.getElementById('customCommentsContainer');
            if (oldContainer) {
                oldContainer.style.display = 'none';
            }

            // Rating display'i de gizle
            const ratingDisplay = document.querySelector('.custom-rating-display');
            if (ratingDisplay) {
                ratingDisplay.style.display = 'none';
            }

            // Hiçbir görsel değişiklik yapma - sessizce başarısız ol
        } catch (error) {
            console.error('Hata yönetiminde hata:', error);
        }
    }

    /**
     * Yorum yok mesajını göster
     */
    function showNoReviewsMessage() {
        try {
            const modernContainer = document.getElementById('modernReviewsContainer');
            const oldContainer = document.getElementById('customCommentsContainer');

            if (modernContainer) {
                const reviewsContent = document.getElementById('reviewsContent');
                if (reviewsContent) {
                    reviewsContent.innerHTML = `
                        <div class="no-reviews-modern">
                            <div class="no-reviews-icon">💬</div>
                            <h3>Henüz yorum bulunmuyor</h3>
                            <p>Bu ürün için ilk yorumu siz yazabilirsiniz!</p>
                        </div>
                    `;
                }

                // Filtreleri gizle
                const reviewsFilters = document.getElementById('reviewsFilters');
                if (reviewsFilters) {
                    reviewsFilters.style.display = 'none';
                }
            } else if (oldContainer) {
                // Fallback: eski sistem
                oldContainer.innerHTML = `
                    <div class="no-comments">
                        <p>📝 Bu ürün için henüz yorum bulunmuyor.</p>
                        <p style="font-size: 14px; margin-top: 10px;">İlk yorumu siz yazabilirsiniz!</p>
                    </div>
                `;
            }
        } catch (error) {
            console.error('Yorum yok mesajı gösterilirken hata:', error);
        }
    }

    /**
     * Yükleme durumunu güncelle
     */
    function updateLoadingState(message) {
        const ratingSummary = document.getElementById('ratingSummary');
        if (ratingSummary) {
            ratingSummary.innerHTML = `<div class="rating-loading"><span>${message}</span></div>`;
        }

        const container = document.getElementById('customCommentsContainer');
        if (container) {
            container.innerHTML = `<div class="comments-loading"><span>${message}</span></div>`;
        }
    }

    /**
     * Modern CSS stillerini ekle
     */
    function addModernStyles() {
        if (document.getElementById('trendyol-modern-styles')) return;

        const style = document.createElement('style');
        style.id = 'trendyol-modern-styles';
        style.textContent = `
            /* Clean Rating Display Styles */
            .custom-rating-display {
                margin: 15px 0;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            }

            .rating-display-clean {
                display: inline-flex;
                align-items: center;
                gap: 8px;
                padding: 12px 16px;
                background: #ffffff;
                border: 1px solid #e0e0e0;
                border-radius: 8px;
                cursor: pointer;
                transition: all 0.3s ease;
                color: #333;
                font-weight: 500;
                font-size: 14px;
                box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                position: relative;
            }

            .rating-display-clean:hover {
                border-color: #ffc107;
                box-shadow: 0 4px 8px rgba(0,0,0,0.15);
                transform: translateY(-1px);
            }

            .rating-score-clean {
                font-weight: 700;
                font-size: 18px;
                color: #333;
                margin-right: 4px;
            }

            .rating-stars-clean {
                display: flex;
                gap: 2px;
                margin-right: 4px;
            }

            .rating-count-clean {
                color: #666;
                font-size: 14px;
                cursor: pointer;
                padding: 2px 4px;
                border-radius: 4px;
                transition: background-color 0.2s ease;
            }

            .rating-count-clean:hover {
                background-color: #f5f5f5;
                color: #333;
            }

            .rating-tooltip-clean {
                position: fixed;
                background: white;
                border: 1px solid #ddd;
                border-radius: 8px;
                padding: 12px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                z-index: 1000;
                min-width: 180px;
                font-size: 13px;
            }

            .tooltip-header {
                font-weight: 600;
                margin-bottom: 8px;
                color: #333;
                border-bottom: 1px solid #eee;
                padding-bottom: 4px;
            }

            .rating-breakdown-row {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 4px 0;
            }

            .rating-stars-tooltip {
                display: flex;
                gap: 1px;
            }

            .rating-count-tooltip {
                font-weight: 500;
                color: #666;
            }

            /* Minimal Filters Styles */
            .reviews-filters-minimal {
                display: flex;
                align-items: center;
                gap: 12px;
                margin: 20px 0;
                padding: 12px 20px;
                background: linear-gradient(135deg, #fff5e6 0%, #fff 100%);
                border-radius: 15px;
                border: 1px solid #ffe0b3;
                flex-wrap: wrap;
            }

            .filter-select-minimal {
                padding: 6px 12px;
                border: 1px solid #ff9933;
                border-radius: 8px;
                background: white;
                color: #ff6000;
                font-size: 13px;
                font-weight: 500;
                cursor: pointer;
                transition: all 0.3s ease;
                outline: none;
            }

            .filter-select-minimal:hover {
                border-color: #ff6000;
                box-shadow: 0 2px 8px rgba(255, 96, 0, 0.2);
            }

            .filter-select-minimal:focus {
                border-color: #ff6000;
                box-shadow: 0 0 0 2px rgba(255, 96, 0, 0.2);
            }

            .filter-reset-btn-minimal {
                padding: 6px 12px;
                background: linear-gradient(135deg, #ff6000 0%, #ff8533 100%);
                color: white;
                border: none;
                border-radius: 8px;
                font-size: 13px;
                font-weight: 600;
                cursor: pointer;
                transition: all 0.3s ease;
            }

            .filter-reset-btn-minimal:hover {
                background: linear-gradient(135deg, #e55a00 0%, #ff6000 100%);
                transform: translateY(-1px);
                box-shadow: 0 2px 8px rgba(255, 96, 0, 0.3);
            }

            /* Trendyol Slider Styles */
            .reviews-content-slider {
                margin: 20px 0;
                min-height: 200px;
            }

            .trendyol-comments-slider {
                display: grid;
                grid-template-columns: repeat(3, 1fr);
                gap: 16px;
                padding: 20px 0;
            }

            @media (max-width: 1200px) {
                .trendyol-comments-slider {
                    grid-template-columns: repeat(2, 1fr);
                }
            }

            @media (max-width: 768px) {
                .trendyol-comments-slider {
                    grid-template-columns: 1fr;
                }
            }

            .trendyol-comment-card {
                background: white;
                border: 1px solid #e6e6e6;
                border-radius: 12px;
                padding: 16px;
                transition: all 0.3s ease;
                box-shadow: 0 2px 8px rgba(0,0,0,0.08);
            }

            .trendyol-comment-card:hover {
                border-color: #ff6000;
                box-shadow: 0 4px 16px rgba(255, 96, 0, 0.15);
                transform: translateY(-2px);
            }

            .trendyol-comment-header {
                display: flex;
                justify-content: space-between;
                align-items: flex-start;
                margin-bottom: 12px;
            }

            .trendyol-user-info {
                display: flex;
                align-items: flex-start;
                gap: 12px;
            }

            .trendyol-user-avatar {
                width: 40px;
                height: 40px;
                border-radius: 50%;
                background: linear-gradient(135deg, #ff6000 0%, #ff8533 100%);
                color: white;
                display: flex;
                align-items: center;
                justify-content: center;
                font-weight: 700;
                font-size: 16px;
            }

            .trendyol-user-details {
                flex: 1;
            }

            .trendyol-user-name {
                font-weight: 600;
                color: #333;
                font-size: 14px;
                display: block;
                margin-bottom: 4px;
            }

            .trendyol-elite-badge {
                background: linear-gradient(135deg, #ff6000 0%, #ff8533 100%);
                color: white;
                padding: 2px 8px;
                border-radius: 10px;
                font-size: 10px;
                font-weight: 600;
                text-transform: uppercase;
                letter-spacing: 0.5px;
                margin-bottom: 4px;
                display: inline-block;
            }

            .trendyol-comment-rating {
                display: flex;
                gap: 2px;
                margin-top: 2px;
            }

            .trendyol-comment-date {
                font-size: 12px;
                color: #999;
                text-align: right;
            }

            .trendyol-comment-body {
                margin-bottom: 12px;
            }

            .trendyol-comment-text {
                font-size: 14px;
                line-height: 1.5;
                color: #333;
                margin: 0 0 12px 0;
                display: -webkit-box;
                -webkit-line-clamp: 3;
                -webkit-box-orient: vertical;
                overflow: hidden;
            }

            .trendyol-comment-photos {
                display: flex;
                gap: 8px;
                margin-top: 8px;
            }

            .trendyol-comment-photo {
                width: 50px;
                height: 50px;
                object-fit: cover;
                border-radius: 8px;
                cursor: pointer;
                transition: all 0.3s ease;
                border: 2px solid transparent;
            }

            .trendyol-comment-photo:hover {
                transform: scale(1.1);
                border-color: #ff6000;
            }

            .more-photos-trendyol {
                width: 50px;
                height: 50px;
                background: #f5f5f5;
                border-radius: 8px;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 12px;
                color: #666;
                font-weight: 600;
            }

            .trendyol-comment-footer {
                display: flex;
                justify-content: flex-end;
                align-items: center;
                border-top: 1px solid #f5f5f5;
                padding-top: 8px;
            }

            .trendyol-comment-likes {
                display: flex;
                align-items: center;
                gap: 4px;
                font-size: 12px;
                color: #666;
            }

            .like-icon {
                font-size: 14px;
            }

            /* Slider Controls */
            .slider-controls {
                display: flex;
                justify-content: center;
                align-items: center;
                gap: 20px;
                margin-top: 20px;
            }

            .slider-btn {
                width: 40px;
                height: 40px;
                border: none;
                border-radius: 50%;
                background: linear-gradient(135deg, #ff6000 0%, #ff8533 100%);
                color: white;
                font-size: 18px;
                font-weight: bold;
                cursor: pointer;
                transition: all 0.3s ease;
                display: flex;
                align-items: center;
                justify-content: center;
            }

            .slider-btn:hover:not(:disabled) {
                background: linear-gradient(135deg, #e55a00 0%, #ff6000 100%);
                transform: scale(1.1);
            }

            .slider-btn:disabled {
                opacity: 0.5;
                cursor: not-allowed;
                transform: none !important;
            }

            .slider-dots {
                display: flex;
                gap: 8px;
            }

            .slider-dot {
                width: 8px;
                height: 8px;
                border-radius: 50%;
                background: #ddd;
                cursor: pointer;
                transition: all 0.3s ease;
            }

            .slider-dot.active {
                background: #ff6000;
                transform: scale(1.2);
            }

            .slider-dot:hover {
                background: #ff8533;
            }

            .star {
                font-size: 16px;
                color: #ddd;
                transition: all 0.3s ease;
            }

            .star-small {
                font-size: 14px;
                color: #ddd;
            }

            .star-filled {
                color: #ffc107 !important;
                text-shadow: 0 1px 2px rgba(255, 193, 7, 0.3);
            }

            .star-half {
                color: #ffc107 !important;
                opacity: 0.7;
                text-shadow: 0 1px 2px rgba(255, 193, 7, 0.3);
            }

            .star-empty {
                color: #e0e0e0 !important;
            }

            /* Modern Reviews Container */
            .modern-reviews-container {
                margin: 30px 0;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
                background: white;
                border-radius: 16px;
                box-shadow: 0 4px 20px rgba(0,0,0,0.08);
                overflow: hidden;
            }

            .reviews-header {
                padding: 20px 24px;
                background: linear-gradient(135deg, #fff5e6 0%, #fff 100%);
                border-bottom: 1px solid #ffe0b3;
            }

            .reviews-title {
                margin: 0;
                font-size: 20px;
                font-weight: 700;
                color: #ff6000;
                display: flex;
                align-items: center;
                gap: 8px;
            }

            .reviews-icon {
                font-size: 18px;
            }

            .reviews-count {
                font-size: 16px;
                color: #ff8533;
                font-weight: 600;
            }

            /* Comments Container Styles */
            .custom-comments-container {
                margin: 30px 0;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            }

            .comments-header {
                margin-bottom: 24px;
                text-align: center;
            }

            .comments-header h3 {
                margin: 0;
                font-size: 24px;
                font-weight: 700;
                color: #212529;
                background: linear-gradient(135deg, #ff6000 0%, #ff8533 100%);
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
                background-clip: text;
            }

            .comments-scroll-container {
                position: relative;
                overflow: hidden;
                border-radius: 20px;
                background: linear-gradient(135deg, #f8f9fa 0%, #fff 100%);
                padding: 20px;
                box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            }

            .comments-list {
                display: flex;
                gap: 20px;
                overflow-x: auto;
                padding: 20px 0;
                scroll-behavior: smooth;
                scrollbar-width: none;
                -ms-overflow-style: none;
            }

            .comments-list::-webkit-scrollbar {
                display: none;
            }

            .comment-card {
                flex: 0 0 350px;
                background: white;
                border: 2px solid #e9ecef;
                border-radius: 20px;
                padding: 24px;
                box-shadow: 0 8px 32px rgba(0,0,0,0.1);
                transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
                position: relative;
                overflow: hidden;
            }

            .comment-card::before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                height: 4px;
                background: linear-gradient(135deg, #ff6000 0%, #ff8533 100%);
                transform: scaleX(0);
                transition: transform 0.3s ease;
            }

            .comment-card:hover::before {
                transform: scaleX(1);
            }

            .comment-card:hover {
                transform: translateY(-8px) scale(1.02);
                box-shadow: 0 20px 60px rgba(0,0,0,0.15);
                border-color: #ff6000;
            }

            .comment-header {
                display: flex;
                justify-content: space-between;
                align-items: flex-start;
                margin-bottom: 16px;
                padding-bottom: 12px;
                border-bottom: 1px solid #f8f9fa;
            }

            .comment-user {
                display: flex;
                align-items: center;
                gap: 10px;
            }

            .user-name {
                font-weight: 700;
                color: #212529;
                font-size: 16px;
            }

            .elite-badge {
                background: linear-gradient(135deg, #fbbf24, #f59e0b);
                color: white;
                padding: 4px 10px;
                border-radius: 12px;
                font-size: 11px;
                font-weight: 700;
                text-transform: uppercase;
                letter-spacing: 0.5px;
                box-shadow: 0 2px 8px rgba(251, 191, 36, 0.3);
            }

            .comment-rating {
                display: flex;
                gap: 2px;
            }

            .comment-body {
                margin-bottom: 16px;
            }

            .comment-text {
                font-size: 15px;
                line-height: 1.6;
                color: #495057;
                margin: 0 0 16px 0;
                display: -webkit-box;
                -webkit-line-clamp: 4;
                -webkit-box-orient: vertical;
                overflow: hidden;
            }

            .comment-photos {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(60px, 1fr));
                gap: 8px;
                margin-top: 12px;
            }

            .comment-photo {
                width: 100%;
                height: 60px;
                object-fit: cover;
                border-radius: 12px;
                cursor: pointer;
                transition: all 0.3s ease;
                border: 2px solid transparent;
            }

            .comment-photo:hover {
                transform: scale(1.1);
                border-color: #ff6000;
                box-shadow: 0 4px 16px rgba(255, 96, 0, 0.3);
            }

            .comment-footer {
                display: flex;
                justify-content: space-between;
                align-items: center;
                font-size: 13px;
                color: #6c757d;
                border-top: 1px solid #f8f9fa;
                padding-top: 12px;
                font-weight: 500;
            }

            .comment-date {
                font-style: italic;
            }

            .comment-likes {
                display: flex;
                align-items: center;
                gap: 6px;
                background: #f8f9fa;
                padding: 4px 8px;
                border-radius: 12px;
            }

            .scroll-controls {
                display: flex;
                justify-content: center;
                gap: 16px;
                margin-top: 24px;
            }

            .scroll-btn {
                background: linear-gradient(135deg, #ff6000 0%, #ff8533 100%);
                color: white;
                border: none;
                border-radius: 50%;
                width: 50px;
                height: 50px;
                cursor: pointer;
                font-size: 18px;
                font-weight: bold;
                transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                display: flex;
                align-items: center;
                justify-content: center;
                box-shadow: 0 4px 16px rgba(255, 96, 0, 0.3);
            }

            .scroll-btn:hover {
                background: linear-gradient(135deg, #e55a00 0%, #ff6000 100%);
                transform: scale(1.1);
                box-shadow: 0 8px 24px rgba(255, 96, 0, 0.4);
            }

            .scroll-btn:active {
                transform: scale(0.95);
            }

            /* Loading and No Reviews Styles */
            .reviews-loading, .modern-reviews-loading {
                text-align: center;
                padding: 40px;
                color: #ff6000;
                font-size: 16px;
                font-weight: 500;
            }

            .loading-spinner, .modern-loading-spinner {
                width: 20px;
                height: 20px;
                border: 2px solid #ffe0b3;
                border-top: 2px solid #ff6000;
                border-radius: 50%;
                animation: spin 1s linear infinite;
                display: inline-block;
                margin-right: 8px;
            }

            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }

            .no-reviews-filtered, .no-reviews-modern {
                text-align: center;
                padding: 40px 20px;
                color: #666;
                background: linear-gradient(135deg, #fff5e6 0%, #fff 100%);
                border-radius: 12px;
                border: 1px solid #ffe0b3;
            }

            .no-reviews-icon {
                font-size: 48px;
                margin-bottom: 16px;
                opacity: 0.7;
            }

            .no-reviews-filtered h3, .no-reviews-modern h3 {
                margin: 0 0 8px 0;
                color: #ff6000;
                font-size: 18px;
                font-weight: 600;
            }

            .no-reviews-filtered p, .no-reviews-modern p {
                margin: 0 0 16px 0;
                color: #666;
                font-size: 14px;
            }

            .clear-filters-btn {
                background: linear-gradient(135deg, #ff6000 0%, #ff8533 100%);
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 8px;
                font-size: 14px;
                font-weight: 600;
                cursor: pointer;
                transition: all 0.3s ease;
            }

            .clear-filters-btn:hover {
                background: linear-gradient(135deg, #e55a00 0%, #ff6000 100%);
                transform: translateY(-1px);
            }

            /* Responsive Design */
            @media (max-width: 768px) {
                .comment-card {
                    flex: 0 0 280px;
                }

                .rating-display {
                    flex-direction: column;
                    align-items: center;
                    gap: 16px;
                    text-align: center;
                }

                .rating-tooltip {
                    position: fixed;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    max-width: 90vw;
                }

                .comments-scroll-container {
                    padding: 15px;
                }

                .scroll-controls {
                    gap: 12px;
                }

                .scroll-btn {
                    width: 45px;
                    height: 45px;
                    font-size: 16px;
                }
            }

            /* Modern Reviews Container Styles - Ruiada Inspired */
            .modern-reviews-container {
                margin: 40px 0;
                padding: 25px;
                background: #ffffff;
                border: 1px solid #e0e0e0;
                border-radius: 12px;
                box-shadow: 0 2px 12px rgba(0,0,0,0.08);
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
                position: relative;
                z-index: 1;
                width: 100%;
                box-sizing: border-box;
                clear: both;
            }

            .reviews-header {
                text-align: left;
                margin-bottom: 25px;
                padding-bottom: 15px;
                border-bottom: 1px solid #e0e0e0;
            }

            .reviews-title {
                margin: 0;
                font-size: 22px;
                font-weight: 600;
                color: #333333;
                display: flex;
                align-items: center;
                gap: 8px;
            }

            .reviews-icon {
                font-size: 24px;
                color: #666666;
            }

            .reviews-count {
                background: #f5f5f5;
                color: #666666;
                padding: 4px 12px;
                border-radius: 16px;
                font-size: 14px;
                font-weight: 500;
                margin-left: 8px;
                border: 1px solid #e0e0e0;
            }

            .reviews-filters {
                display: flex;
                justify-content: center;
                align-items: center;
                gap: 20px;
                margin-bottom: 30px;
                padding: 20px;
                background: white;
                border-radius: 16px;
                box-shadow: 0 4px 16px rgba(0,0,0,0.05);
                flex-wrap: wrap;
            }

            .filter-group {
                display: flex;
                align-items: center;
                gap: 8px;
            }

            .filter-label {
                font-weight: 600;
                color: #495057;
                font-size: 14px;
            }

            .filter-select {
                padding: 8px 16px;
                border: 2px solid #e9ecef;
                border-radius: 12px;
                background: white;
                font-size: 14px;
                font-weight: 500;
                color: #495057;
                cursor: pointer;
                transition: all 0.3s ease;
                min-width: 120px;
            }

            .filter-select:focus {
                outline: none;
                border-color: #ff6000;
                box-shadow: 0 0 0 3px rgba(255, 96, 0, 0.1);
            }

            .filter-select:hover {
                border-color: #ff6000;
            }

            .filter-reset-btn {
                background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 12px;
                font-size: 14px;
                font-weight: 600;
                cursor: pointer;
                transition: all 0.3s ease;
                display: flex;
                align-items: center;
                gap: 6px;
            }

            .filter-reset-btn:hover {
                background: linear-gradient(135deg, #495057 0%, #343a40 100%);
                transform: translateY(-2px);
                box-shadow: 0 4px 12px rgba(108, 117, 125, 0.3);
            }

            .modern-comments-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
                gap: 16px;
                margin-bottom: 25px;
            }

            .modern-comment-card {
                background: white;
                border: 1px solid #e0e0e0;
                border-radius: 8px;
                padding: 20px;
                transition: all 0.2s ease;
                position: relative;
                box-shadow: 0 1px 4px rgba(0,0,0,0.1);
            }

            .modern-comment-card:hover {
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                border-color: #cccccc;
            }

            .modern-comment-header {
                display: flex;
                justify-content: space-between;
                align-items: flex-start;
                margin-bottom: 12px;
                padding-bottom: 10px;
                border-bottom: 1px solid #f0f0f0;
            }

            .modern-comment-user {
                display: flex;
                align-items: center;
                gap: 10px;
            }

            .user-avatar {
                width: 40px;
                height: 40px;
                background: #007bff;
                color: white;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 16px;
                font-weight: 600;
            }

            .user-info {
                display: flex;
                flex-direction: column;
                gap: 2px;
            }

            .user-name {
                font-size: 14px;
                font-weight: 600;
                color: #333333;
            }

            .modern-elite-badge {
                background: #28a745;
                color: white;
                padding: 2px 6px;
                border-radius: 4px;
                font-size: 9px;
                font-weight: 600;
                text-transform: uppercase;
            }

            .modern-comment-rating {
                display: flex;
                align-items: center;
                gap: 6px;
            }

            .rating-number {
                font-size: 13px;
                font-weight: 600;
                color: #666666;
                background: #f5f5f5;
                padding: 3px 6px;
                border-radius: 4px;
            }

            .modern-comment-text {
                font-size: 14px;
                line-height: 1.5;
                color: #555555;
                margin: 0 0 12px 0;
            }

            .modern-comment-photos {
                display: flex;
                gap: 6px;
                margin-top: 10px;
                position: relative;
            }

            .modern-comment-photo {
                width: 50px;
                height: 50px;
                object-fit: cover;
                border-radius: 6px;
                cursor: pointer;
                transition: all 0.2s ease;
                border: 1px solid #e0e0e0;
            }

            .modern-comment-photo:hover {
                transform: scale(1.05);
                border-color: #007bff;
            }

            .more-photos {
                width: 50px;
                height: 50px;
                background: rgba(0,0,0,0.6);
                color: white;
                border-radius: 6px;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 11px;
                font-weight: 600;
                cursor: pointer;
            }

            .modern-comment-footer {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-top: 12px;
                padding-top: 10px;
                border-top: 1px solid #f0f0f0;
                font-size: 12px;
                color: #888888;
            }

            .modern-comment-date {
                font-style: italic;
            }

            .modern-comment-likes {
                display: flex;
                align-items: center;
                gap: 4px;
                background: #f5f5f5;
                padding: 4px 8px;
                border-radius: 12px;
                transition: all 0.2s ease;
            }

            .modern-comment-likes:hover {
                background: #e9ecef;
            }

            .like-icon {
                font-size: 12px;
            }

            .like-count {
                font-weight: 600;
                color: #666666;
            }

            .modern-reviews-loading {
                text-align: center;
                padding: 60px 20px;
                color: #6c757d;
            }

            .modern-loading-spinner {
                width: 40px;
                height: 40px;
                border: 4px solid #f8f9fa;
                border-top: 4px solid #ff6000;
                border-radius: 50%;
                animation: spin 1s linear infinite;
                margin: 0 auto 20px;
            }

            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }

            .no-reviews-modern, .no-reviews-filtered {
                text-align: center;
                padding: 60px 20px;
                color: #6c757d;
            }

            .no-reviews-icon {
                font-size: 48px;
                margin-bottom: 16px;
                opacity: 0.7;
            }

            .no-reviews-modern h3, .no-reviews-filtered h3 {
                margin: 0 0 12px 0;
                font-size: 20px;
                font-weight: 600;
                color: #495057;
            }

            .no-reviews-modern p, .no-reviews-filtered p {
                margin: 0;
                font-size: 16px;
                opacity: 0.8;
            }

            .clear-filters-btn {
                background: linear-gradient(135deg, #ff6000 0%, #ff8533 100%);
                color: white;
                border: none;
                padding: 12px 24px;
                border-radius: 12px;
                font-size: 14px;
                font-weight: 600;
                cursor: pointer;
                transition: all 0.3s ease;
                margin-top: 16px;
            }

            .clear-filters-btn:hover {
                background: linear-gradient(135deg, #e55a00 0%, #ff6000 100%);
                transform: translateY(-2px);
                box-shadow: 0 6px 20px rgba(255, 96, 0, 0.4);
            }

            /* Responsive Design for Modern Container */
            @media (max-width: 768px) {
                .modern-reviews-container {
                    margin: 20px 0;
                    padding: 20px;
                }

                .reviews-title {
                    font-size: 22px;
                    flex-direction: column;
                    gap: 8px;
                }

                .reviews-filters {
                    flex-direction: column;
                    gap: 12px;
                }

                .modern-comments-grid {
                    grid-template-columns: 1fr;
                    gap: 16px;
                }

                .modern-comment-card {
                    padding: 16px;
                }

                .user-avatar {
                    width: 40px;
                    height: 40px;
                    font-size: 16px;
                }
            }
        `;

        document.head.appendChild(style);
    }

    /**
     * Public API for external configuration
     */
    window.TrendyolReviews = {
        init: init,
        configure: function(options) {
            Object.assign(CONFIG, options);
        },
        setApiUrl: function(url) {
            CONFIG.apiBaseUrl = url;
        },
        setProductId: function(productId) {
            CONFIG.getProductId = function() { return productId; };
        },
        refresh: function() {
            const productId = CONFIG.getProductId();
            if (productId) {
                fetchReviews(productId);
            }
        }
    };

    // Auto-initialize when script loads with improved timing
    function autoInit() {
        console.log('Auto-init başlatılıyor, document.readyState:', document.readyState);

        if (document.readyState === 'loading') {
            // Sayfa hala yükleniyor, DOMContentLoaded'ı bekle
            document.addEventListener('DOMContentLoaded', function() {
                console.log('DOMContentLoaded tetiklendi');
                setTimeout(function() {
                    console.log('Auto-init gecikme sonrası başlatılıyor');
                    init();
                }, 500);
            });
        } else {
            // Sayfa zaten yüklenmiş, biraz bekleyerek başlat
            console.log('Sayfa zaten yüklenmiş, gecikme ile başlatılıyor');
            setTimeout(function() {
                console.log('Auto-init gecikme sonrası başlatılıyor');
                init();
            }, 500);
        }
    }

    // Başlat
    autoInit();

})();
