// Product Slider Admin Panel JavaScript

// Global variables
let currentSliderId = null;

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    initializeProductSliderAdmin();
});

function initializeProductSliderAdmin() {
    // Initialize form handlers
    initializeCreateSliderForm();
    
    // Initialize tooltips and other UI components
    console.log('Product Slider Admin initialized');
}

// Create Slider Modal Functions
function openCreateSliderModal() {
    document.getElementById('createSliderModal').classList.remove('hidden');
    document.getElementById('sliderName').focus();
}

function closeCreateSliderModal() {
    document.getElementById('createSliderModal').classList.add('hidden');
    document.getElementById('createSliderForm').reset();
}

function initializeCreateSliderForm() {
    const form = document.getElementById('createSliderForm');
    if (form) {
        form.addEventListener('submit', handleCreateSlider);
    }
}

async function handleCreateSlider(event) {
    event.preventDefault();
    
    const formData = new FormData(event.target);
    const data = {
        name: formData.get('name'),
        description: formData.get('description'),
        displayType: formData.get('displayType')
    };
    
    try {
        const response = await fetch('/ProductSlider/CreateSlider', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'RequestVerificationToken': getAntiForgeryToken()
            },
            body: JSON.stringify(data)
        });
        
        const result = await response.json();
        
        if (result.success) {
            showNotification('Slider başarıyla oluşturuldu!', 'success');
            closeCreateSliderModal();
            
            // Redirect to edit page
            if (result.sliderId) {
                window.location.href = `/ProductSlider/Edit/${result.sliderId}`;
            } else {
                // Refresh page to show new slider
                window.location.reload();
            }
        } else {
            showNotification(result.message || 'Slider oluşturulurken bir hata oluştu.', 'error');
        }
    } catch (error) {
        console.error('Error creating slider:', error);
        showNotification('Bir hata oluştu. Lütfen tekrar deneyin.', 'error');
    }
}

// Slider Management Functions
async function toggleSliderStatus(sliderId) {
    if (!confirm('Slider durumunu değiştirmek istediğinizden emin misiniz?')) {
        return;
    }

    try {
        const formData = new FormData();
        formData.append('id', sliderId);
        formData.append('__RequestVerificationToken', getAntiForgeryToken());

        const response = await fetch('/ProductSlider/ToggleSliderStatus', {
            method: 'POST',
            body: formData
        });
        
        const result = await response.json();
        
        if (result.success) {
            showNotification(result.message, 'success');
            window.location.reload();
        } else {
            showNotification(result.message || 'Durum değiştirilirken bir hata oluştu.', 'error');
        }
    } catch (error) {
        console.error('Error toggling slider status:', error);
        showNotification('Bir hata oluştu. Lütfen tekrar deneyin.', 'error');
    }
}

async function deleteSlider(sliderId, sliderName) {
    if (!confirm(`"${sliderName}" slider'ını silmek istediğinizden emin misiniz? Bu işlem geri alınamaz.`)) {
        return;
    }

    try {
        const formData = new FormData();
        formData.append('id', sliderId);
        formData.append('__RequestVerificationToken', getAntiForgeryToken());

        const response = await fetch('/ProductSlider/DeleteSlider', {
            method: 'POST',
            body: formData
        });
        
        const result = await response.json();
        
        if (result.success) {
            showNotification(result.message, 'success');
            window.location.reload();
        } else {
            showNotification(result.message || 'Slider silinirken bir hata oluştu.', 'error');
        }
    } catch (error) {
        console.error('Error deleting slider:', error);
        showNotification('Bir hata oluştu. Lütfen tekrar deneyin.', 'error');
    }
}

// DEPRECATED: Eski embed code sistemi - artık kullanılmıyor
function showEmbedCode(sliderId, sliderName) {
    showNotification('Bu özellik artık kullanılmıyor. Lütfen "R2 Embed Kodu Al" butonunu kullanın.', 'warning');
    return;
}

function closeEmbedCodeModal() {
    document.getElementById('embedCodeModal').classList.add('hidden');
    currentSliderId = null;
}

function copyEmbedCode() {
    const textarea = document.getElementById('embedCodeText');
    textarea.select();
    textarea.setSelectionRange(0, 99999); // For mobile devices
    
    try {
        document.execCommand('copy');
        showNotification('Embed kodu kopyalandı!', 'success');
    } catch (err) {
        console.error('Error copying embed code:', err);
        showNotification('Kopyalama başarısız. Lütfen manuel olarak kopyalayın.', 'error');
    }
}

// Utility Functions
function getAntiForgeryToken() {
    const token = document.querySelector('input[name="__RequestVerificationToken"]');
    return token ? token.value : '';
}

function getCompanyIdFromUrl() {
    // This should be implemented based on your URL structure or passed from server
    // For now, return null and handle it in the backend
    return null;
}

function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg max-w-sm ${getNotificationClasses(type)}`;
    notification.innerHTML = `
        <div class="flex items-center">
            <div class="flex-shrink-0">
                ${getNotificationIcon(type)}
            </div>
            <div class="ml-3">
                <p class="text-sm font-medium">${message}</p>
            </div>
            <div class="ml-auto pl-3">
                <button onclick="this.parentElement.parentElement.parentElement.remove()" class="text-gray-400 hover:text-gray-600">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
        </div>
    `;
    
    document.body.appendChild(notification);
    
    // Auto remove after 5 seconds
    setTimeout(() => {
        if (notification.parentElement) {
            notification.remove();
        }
    }, 5000);
}

function getNotificationClasses(type) {
    switch (type) {
        case 'success':
            return 'bg-green-50 border border-green-200 text-green-800';
        case 'error':
            return 'bg-red-50 border border-red-200 text-red-800';
        case 'warning':
            return 'bg-yellow-50 border border-yellow-200 text-yellow-800';
        default:
            return 'bg-blue-50 border border-blue-200 text-blue-800';
    }
}

function getNotificationIcon(type) {
    switch (type) {
        case 'success':
            return `<svg class="w-5 h-5 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>`;
        case 'error':
            return `<svg class="w-5 h-5 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>`;
        case 'warning':
            return `<svg class="w-5 h-5 text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                    </svg>`;
        default:
            return `<svg class="w-5 h-5 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>`;
    }
}

// Close modals when clicking outside
document.addEventListener('click', function(event) {
    const createModal = document.getElementById('createSliderModal');
    const embedModal = document.getElementById('embedCodeModal');
    
    if (event.target === createModal) {
        closeCreateSliderModal();
    }
    
    if (event.target === embedModal) {
        closeEmbedCodeModal();
    }
});

// R2 Embed Code Functions
async function showR2EmbedCode() {
    try {
        const response = await fetch('/ProductSlider/GetR2EmbedCode', {
            method: 'GET',
            headers: {
                'RequestVerificationToken': getAntiForgeryToken()
            }
        });

        const result = await response.json();

        if (result.success) {
            document.getElementById('r2EmbedCodeText').value = result.embedCode;
            document.getElementById('r2EmbedCodeModal').classList.remove('hidden');
        } else {
            showNotification(result.message || 'Embed kodu alınırken hata oluştu.', 'error');
        }
    } catch (error) {
        console.error('Error getting R2 embed code:', error);
        showNotification('Bir hata oluştu. Lütfen tekrar deneyin.', 'error');
    }
}

function closeR2EmbedCodeModal() {
    document.getElementById('r2EmbedCodeModal').classList.add('hidden');
}

function copyR2EmbedCode() {
    const textarea = document.getElementById('r2EmbedCodeText');
    textarea.select();
    textarea.setSelectionRange(0, 99999); // For mobile devices

    try {
        document.execCommand('copy');
        showNotification('Embed kodu kopyalandı!', 'success');
    } catch (err) {
        console.error('Copy failed:', err);
        showNotification('Kopyalama başarısız. Lütfen manuel olarak kopyalayın.', 'error');
    }
}

// R2 Export Function
async function exportToR2() {
    if (!confirm('Tüm slider\'lar R2\'ye export edilecek. Devam etmek istiyor musunuz?')) {
        return;
    }

    try {
        const formData = new FormData();
        formData.append('__RequestVerificationToken', getAntiForgeryToken());

        const response = await fetch('/ProductSlider/ExportToR2', {
            method: 'POST',
            body: formData
        });

        const result = await response.json();

        if (result.success) {
            showNotification('Slider\'lar başarıyla R2\'ye export edildi!', 'success');
            console.log('JSON URL:', result.jsonUrl);
            console.log('Script URL:', result.scriptUrl);
        } else {
            showNotification(result.message || 'R2 export işlemi başarısız.', 'error');
        }
    } catch (error) {
        console.error('Error exporting to R2:', error);
        showNotification('Bir hata oluştu. Lütfen tekrar deneyin.', 'error');
    }
}

// Close modals with Escape key
document.addEventListener('keydown', function(event) {
    if (event.key === 'Escape') {
        closeCreateSliderModal();
        closeEmbedCodeModal();
        closeR2EmbedCodeModal();
    }
});

// Close R2 modal when clicking outside
document.addEventListener('click', function(event) {
    const r2Modal = document.getElementById('r2EmbedCodeModal');
    if (event.target === r2Modal) {
        closeR2EmbedCodeModal();
    }
});
