/**
 * Pushonica Gift Wheel Module
 * Hediye çarkı modülü - Anasayfada otomatik yüklenir
 * Modül ayarlarından tasarım ve içerik çeker
 */

(function() {
    'use strict';

    // Gift Wheel Module
    class GiftWheelModule {
        constructor() {
            console.log('[GiftWheel] 🎯 Constructor başlatılıyor...');

            this.companyId = this.getCompanyId();
            this.apiBaseUrl = this.getApiBaseUrl();
            this.config = null;
            this.isInitialized = false;
            this.wheelContainer = null;
            this.canvas = null;
            this.ctx = null;
            this.isSpinning = false;
            this.currentRotation = 0;
            this.cookieKey = `pushonica_gift_wheel_${this.companyId}`;

            console.log('[GiftWheel] 📋 Başlangıç değerleri:', {
                companyId: this.companyId,
                apiBaseUrl: this.apiBaseUrl,
                cookieKey: this.cookieKey
            });

            if (this.companyId) {
                console.log('[GiftWheel] ✅ Company ID bulundu, modül yükleniyor...');
                this.init();
            } else {
                console.warn('[GiftWheel] ❌ Company ID bulunamadı - modül yüklenemeyecek');
            }
        }

        async init() {
            console.log('[GiftWheel] 🚀 Init metodu başlatılıyor...');

            if (this.isInitialized) {
                console.log('[GiftWheel] ⚠️ Modül zaten başlatılmış, tekrar başlatılmayacak');
                return;
            }

            try {
                console.log('[GiftWheel] 📡 1. Adım: Konfigürasyon yükleniyor...');
                await this.loadConfig();

                if (!this.config) {
                    console.log('[GiftWheel] ❌ Konfigürasyon bulunamadı - modül sonlandırılıyor');
                    return;
                }
                console.log('[GiftWheel] ✅ Konfigürasyon başarıyla yüklendi');

                console.log('[GiftWheel] 🔍 2. Adım: Uygunluk kontrolü yapılıyor...');
                const eligibility = await this.checkEligibility();
                if (!eligibility.canSpin) {
                    console.log('[GiftWheel] ❌ Uygunluk kontrolü başarısız:', eligibility.message);
                    return;
                }
                console.log('[GiftWheel] ✅ Uygunluk kontrolü başarılı');

                console.log('[GiftWheel] 🍪 3. Adım: Cookie limit kontrolü yapılıyor...');
                if (!this.canSpinBasedOnCookie()) {
                    console.log('[GiftWheel] ❌ Cookie limit aşıldı - çark gösterilmeyecek');
                    return;
                }
                console.log('[GiftWheel] ✅ Cookie kontrolü başarılı');

                console.log('[GiftWheel] 🎨 4. Adım: Çark render ediliyor...');
                this.renderWheel();
                this.isInitialized = true;

                console.log('[GiftWheel] 🎉 Modül başarıyla başlatıldı!');
            } catch (error) {
                console.error('[GiftWheel] ❌ Başlatma hatası:', error);
            }
        }

        async loadConfig() {
            console.log('[GiftWheel] 📡 Config API çağrısı yapılıyor...');
            const configUrl = `${this.apiBaseUrl}/api/gift-wheel/${this.companyId}/config`;
            console.log('[GiftWheel] 🔗 Config URL:', configUrl);

            try {
                const response = await fetch(configUrl);
                console.log('[GiftWheel] 📨 API yanıtı alındı:', {
                    status: response.status,
                    statusText: response.statusText,
                    ok: response.ok
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                this.config = await response.json();
                console.log('[GiftWheel] ✅ Config başarıyla parse edildi:', {
                    hasConfig: !!this.config,
                    configKeys: this.config ? Object.keys(this.config) : [],
                    prizesCount: this.config?.prizes?.length || 0
                });
            } catch (error) {
                console.warn('[GiftWheel] ❌ Config yükleme hatası:', error.message);
                this.config = null;
            }
        }

        async checkEligibility() {
            console.log('[GiftWheel] 🔍 Uygunluk kontrolü API çağrısı yapılıyor...');
            const eligibilityUrl = `${this.apiBaseUrl}/api/gift-wheel/${this.companyId}/eligibility`;
            console.log('[GiftWheel] 🔗 Eligibility URL:', eligibilityUrl);

            try {
                const response = await fetch(eligibilityUrl);
                console.log('[GiftWheel] 📨 Eligibility API yanıtı:', {
                    status: response.status,
                    statusText: response.statusText,
                    ok: response.ok
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const result = await response.json();
                console.log('[GiftWheel] ✅ Uygunluk sonucu:', result);
                return result;
            } catch (error) {
                console.warn('[GiftWheel] ❌ Uygunluk kontrolü hatası:', error.message);
                return { canSpin: false, message: 'Eligibility check failed' };
            }
        }

        canSpinBasedOnCookie() {
            console.log('[GiftWheel] 🍪 Cookie tabanlı limit kontrolü başlatılıyor...');

            try {
                const cookieData = this.getCookieData();
                console.log('[GiftWheel] 📊 Mevcut cookie verisi:', cookieData);

                if (!cookieData) {
                    console.log('[GiftWheel] ✅ İlk kez kullanım - izin veriliyor');
                    return true;
                }

                const today = new Date().toDateString();
                const lastSpinDate = new Date(cookieData.lastSpinDate).toDateString();

                console.log('[GiftWheel] 📅 Tarih karşılaştırması:', {
                    today,
                    lastSpinDate,
                    isSameDay: today === lastSpinDate
                });

                // Farklı gün ise sıfırla
                if (today !== lastSpinDate) {
                    console.log('[GiftWheel] ✅ Farklı gün - limit sıfırlandı');
                    return true;
                }

                // Aynı gün ise limit kontrolü
                const maxSpins = this.config?.maxSpinsPerDay || 1;
                const canSpin = cookieData.spinCount < maxSpins;

                console.log('[GiftWheel] 🎯 Limit kontrolü:', {
                    currentSpins: cookieData.spinCount,
                    maxSpins,
                    canSpin
                });

                return canSpin;
            } catch (error) {
                console.warn('[GiftWheel] ❌ Cookie kontrolü hatası:', error);
                return true; // Hata durumunda izin ver
            }
        }

        getCookieData() {
            try {
                const cookieValue = this.getCookie(this.cookieKey);
                return cookieValue ? JSON.parse(cookieValue) : null;
            } catch (error) {
                return null;
            }
        }

        updateCookieData() {
            try {
                const today = new Date().toDateString();
                const existingData = this.getCookieData();
                
                let newData;
                if (!existingData || new Date(existingData.lastSpinDate).toDateString() !== today) {
                    // Yeni gün veya ilk kez
                    newData = {
                        lastSpinDate: today,
                        spinCount: 1,
                        maxSpinsPerDay: this.config?.maxSpinsPerDay || 1
                    };
                } else {
                    // Aynı gün, sayacı artır
                    newData = {
                        ...existingData,
                        spinCount: existingData.spinCount + 1
                    };
                }

                this.setCookie(this.cookieKey, JSON.stringify(newData), 365);
            } catch (error) {
                console.warn('[GiftWheel] Failed to update cookie:', error);
            }
        }

        renderWheel() {
            console.log('[GiftWheel] 🎨 Çark render işlemi başlatılıyor...');
            this.createFloatingButton();
        }

        createFloatingButton() {
            console.log('[GiftWheel] 🔘 Floating button oluşturuluyor...');

            // Mevcut button varsa kaldır
            const existingButton = document.getElementById('pushonica-gift-wheel-btn');
            if (existingButton) {
                console.log('[GiftWheel] 🗑️ Mevcut button kaldırılıyor');
                existingButton.remove();
            }

            const buttonText = this.config?.buttonText || 'Hediye Çarkı';
            console.log('[GiftWheel] 📝 Button metni:', buttonText);

            const button = document.createElement('div');
            button.id = 'pushonica-gift-wheel-btn';
            button.innerHTML = `
                <div class="wheel-icon">🎁</div>
                <span class="wheel-text">${buttonText}</span>
            `;

            console.log('[GiftWheel] 🎨 Stil dosyası enjekte ediliyor...');
            this.injectStyles();

            console.log('[GiftWheel] 👆 Click event listener ekleniyor...');
            button.addEventListener('click', () => {
                console.log('[GiftWheel] 🖱️ Button tıklandı!');
                this.showWheelModal();
            });

            console.log('[GiftWheel] 📄 Button DOM\'a ekleniyor...');
            document.body.appendChild(button);

            console.log('[GiftWheel] ✨ Animasyonlu giriş başlatılıyor...');
            setTimeout(() => {
                button.classList.add('show');
                console.log('[GiftWheel] 🎉 Button başarıyla gösterildi!');
            }, 1000);
        }

        showWheelModal() {
            console.log('[GiftWheel] 🪟 Modal gösterme işlemi başlatılıyor...');
            this.createWheelModal();
        }

        createWheelModal() {
            console.log('[GiftWheel] 🏗️ Modal oluşturuluyor...');

            // Mevcut modal varsa kaldır
            const existingModal = document.getElementById('pushonica-gift-wheel-modal');
            if (existingModal) {
                console.log('[GiftWheel] 🗑️ Mevcut modal kaldırılıyor');
                existingModal.remove();
            }

            console.log('[GiftWheel] 📄 Modal HTML oluşturuluyor...');
            const modal = document.createElement('div');
            modal.id = 'pushonica-gift-wheel-modal';
            modal.innerHTML = this.getModalHTML();

            console.log('[GiftWheel] 📄 Modal DOM\'a ekleniyor...');
            document.body.appendChild(modal);

            console.log('[GiftWheel] 👆 Modal event listener\'ları kuruluyor...');
            this.setupModalEvents(modal);

            console.log('[GiftWheel] 🎨 Canvas başlatılıyor...');
            this.initCanvas();

            console.log('[GiftWheel] ✨ Modal animasyonu başlatılıyor...');
            setTimeout(() => {
                modal.classList.add('show');
                console.log('[GiftWheel] 🎉 Modal başarıyla gösterildi!');
            }, 10);
        }

        getModalHTML() {
            const prizes = this.config?.prizes || [];
            const activePrizes = prizes.filter(p => p.isActive);

            return `
                <div class="wheel-modal-overlay">
                    <div class="wheel-modal-content">
                        <button class="wheel-close-btn">&times;</button>
                        
                        <div class="wheel-header">
                            <h2 class="wheel-title">${this.config?.title || 'Hediye Çarkı'}</h2>
                            <p class="wheel-subtitle">${this.config?.subtitle || 'Şansını dene!'}</p>
                        </div>

                        <div class="wheel-container">
                            <canvas id="gift-wheel-canvas" width="300" height="300"></canvas>
                            <div class="wheel-pointer"></div>
                            <button id="spin-button" class="spin-button">
                                ${this.config?.buttonText || 'Çarkı Çevir'}
                            </button>
                        </div>

                        <div class="wheel-form">
                            <input type="text" id="customer-name" placeholder="Adınız Soyadınız" required>
                            <input type="tel" id="customer-phone" placeholder="Telefon Numaranız" required>
                            ${this.config?.requireEmail ? 
                                '<input type="email" id="customer-email" placeholder="E-posta Adresiniz" required>' : 
                                ''
                            }
                        </div>

                        <div class="wheel-message" id="wheel-message"></div>
                    </div>
                </div>
            `;
        }

        setupModalEvents(modal) {
            // Kapat butonu
            const closeBtn = modal.querySelector('.wheel-close-btn');
            closeBtn.addEventListener('click', () => this.closeModal());

            // Overlay tıklama
            const overlay = modal.querySelector('.wheel-modal-overlay');
            overlay.addEventListener('click', (e) => {
                if (e.target === overlay) {
                    this.closeModal();
                }
            });

            // Spin butonu
            const spinBtn = modal.querySelector('#spin-button');
            spinBtn.addEventListener('click', () => this.handleSpin());

            // ESC tuşu
            document.addEventListener('keydown', (e) => {
                if (e.key === 'Escape') {
                    this.closeModal();
                }
            });
        }

        closeModal() {
            const modal = document.getElementById('pushonica-gift-wheel-modal');
            if (modal) {
                modal.classList.remove('show');
                setTimeout(() => {
                    modal.remove();
                }, 300);
            }
        }

        // Utility methods
        getCompanyId() {
            console.log('[GiftWheel] 🔍 Company ID aranıyor...');

            // Script tag'den company ID'yi çek
            console.log('[GiftWheel] 📜 Script tag\'lerinde aranıyor...');
            const scripts = document.querySelectorAll('script[src*="pushonica-modules.js"]');
            console.log('[GiftWheel] 📊 Bulunan script sayısı:', scripts.length);

            for (const script of scripts) {
                const src = script.src;
                console.log('[GiftWheel] 🔗 Script src kontrol ediliyor:', src);
                const match = src.match(/[?&]company[_-]?id=([^&]+)/i);
                if (match) {
                    console.log('[GiftWheel] ✅ Script tag\'den company ID bulundu:', match[1]);
                    return match[1];
                }
            }

            // Meta tag'den dene
            console.log('[GiftWheel] 🏷️ Meta tag\'de aranıyor...');
            const metaTag = document.querySelector('meta[name="pushonica-company-id"]');
            if (metaTag) {
                const companyId = metaTag.getAttribute('content');
                console.log('[GiftWheel] ✅ Meta tag\'den company ID bulundu:', companyId);
                return companyId;
            }

            // Global değişkenden dene
            console.log('[GiftWheel] 🌐 Global değişkende aranıyor...');
            if (window.PUSHONICA_COMPANY_ID) {
                console.log('[GiftWheel] ✅ Global değişkenden company ID bulundu:', window.PUSHONICA_COMPANY_ID);
                return window.PUSHONICA_COMPANY_ID;
            }

            console.log('[GiftWheel] ❌ Company ID hiçbir yerde bulunamadı');
            return null;
        }

        getApiBaseUrl() {
            const baseUrl = window.location.origin;
            console.log('[GiftWheel] 🌐 API Base URL belirlendi:', baseUrl);
            return baseUrl;
        }

        getCookie(name) {
            const value = `; ${document.cookie}`;
            const parts = value.split(`; ${name}=`);
            if (parts.length === 2) return parts.pop().split(';').shift();
            return null;
        }

        setCookie(name, value, days) {
            const expires = new Date();
            expires.setTime(expires.getTime() + (days * 24 * 60 * 60 * 1000));
            document.cookie = `${name}=${value};expires=${expires.toUTCString()};path=/`;
        }

        // Canvas ve çark çizimi
        initCanvas() {
            console.log('[GiftWheel] 🎨 Canvas başlatılıyor...');
            this.canvas = document.getElementById('gift-wheel-canvas');

            if (!this.canvas) {
                console.error('[GiftWheel] ❌ Canvas elementi bulunamadı!');
                return;
            }
            console.log('[GiftWheel] ✅ Canvas elementi bulundu');

            this.ctx = this.canvas.getContext('2d');
            console.log('[GiftWheel] 🖼️ 2D context alındı');

            console.log('[GiftWheel] 🎯 Çark çizimi başlatılıyor...');
            this.drawWheel();
        }

        drawWheel() {
            console.log('[GiftWheel] 🎨 Çark çizimi başlatılıyor...');

            if (!this.ctx || !this.config) {
                console.error('[GiftWheel] ❌ Context veya config eksik:', {
                    hasCtx: !!this.ctx,
                    hasConfig: !!this.config
                });
                return;
            }

            const prizes = this.config.prizes?.filter(p => p.isActive) || [];
            console.log('[GiftWheel] 🏆 Aktif ödül sayısı:', prizes.length);

            if (prizes.length === 0) {
                console.warn('[GiftWheel] ⚠️ Aktif ödül bulunamadı, çark çizilemeyecek');
                return;
            }

            const centerX = this.canvas.width / 2;
            const centerY = this.canvas.height / 2;
            const radius = Math.min(centerX, centerY) - 10;

            console.log('[GiftWheel] 📐 Çark boyutları:', {
                centerX,
                centerY,
                radius,
                canvasSize: `${this.canvas.width}x${this.canvas.height}`
            });

            // Temizle
            this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);

            // Çark dilimlerini çiz
            const anglePerSlice = (2 * Math.PI) / prizes.length;

            prizes.forEach((prize, index) => {
                const startAngle = index * anglePerSlice + this.currentRotation;
                const endAngle = startAngle + anglePerSlice;

                // Dilim çiz
                this.ctx.beginPath();
                this.ctx.moveTo(centerX, centerY);
                this.ctx.arc(centerX, centerY, radius, startAngle, endAngle);
                this.ctx.closePath();
                this.ctx.fillStyle = prize.color || '#3B82F6';
                this.ctx.fill();
                this.ctx.strokeStyle = '#ffffff';
                this.ctx.lineWidth = 2;
                this.ctx.stroke();

                // Metin çiz
                this.ctx.save();
                this.ctx.translate(centerX, centerY);
                this.ctx.rotate(startAngle + anglePerSlice / 2);
                this.ctx.textAlign = 'center';
                this.ctx.fillStyle = '#ffffff';
                this.ctx.font = 'bold 12px Arial';
                this.ctx.fillText(prize.name, radius * 0.7, 5);
                this.ctx.restore();
            });

            // Merkez daire
            this.ctx.beginPath();
            this.ctx.arc(centerX, centerY, 20, 0, 2 * Math.PI);
            this.ctx.fillStyle = this.config.primaryColor || '#6366f1';
            this.ctx.fill();
            this.ctx.strokeStyle = '#ffffff';
            this.ctx.lineWidth = 3;
            this.ctx.stroke();
        }

        async handleSpin() {
            console.log('[GiftWheel] 🎯 Çark çevirme işlemi başlatılıyor...');

            if (this.isSpinning) {
                console.log('[GiftWheel] ⚠️ Çark zaten dönüyor, işlem iptal edildi');
                return;
            }

            console.log('[GiftWheel] 📝 Form verilerini alınıyor...');
            const customerData = this.getCustomerData();
            console.log('[GiftWheel] 📊 Müşteri verisi:', customerData);

            if (!this.validateCustomerData(customerData)) {
                console.log('[GiftWheel] ❌ Form validasyonu başarısız');
                return;
            }
            console.log('[GiftWheel] ✅ Form validasyonu başarılı');

            this.isSpinning = true;
            console.log('[GiftWheel] 🔒 Spin durumu kilitlendi');
            this.showMessage('Çark çevriliyor...', 'info');

            try {
                console.log('[GiftWheel] 🎨 Çark animasyonu başlatılıyor...');
                this.startSpinAnimation();

                console.log('[GiftWheel] 📡 Spin API çağrısı yapılıyor...');
                const spinUrl = `${this.apiBaseUrl}/api/gift-wheel/${this.companyId}/spin`;
                console.log('[GiftWheel] 🔗 Spin URL:', spinUrl);

                const response = await fetch(spinUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(customerData)
                });

                console.log('[GiftWheel] 📨 Spin API yanıtı:', {
                    status: response.status,
                    statusText: response.statusText,
                    ok: response.ok
                });

                const result = await response.json();
                console.log('[GiftWheel] 🎲 Spin sonucu:', result);

                console.log('[GiftWheel] ⏳ Animasyon bitmesi bekleniyor...');
                await this.waitForSpinAnimation();

                if (result.success) {
                    console.log('[GiftWheel] 🎉 Başarılı spin - sonuç işleniyor...');
                    this.handleWinResult(result);
                    console.log('[GiftWheel] 🍪 Cookie güncelleniyor...');
                    this.updateCookieData();
                } else {
                    console.log('[GiftWheel] ❌ Spin başarısız:', result.message);
                    this.showMessage(result.message, 'error');
                }

            } catch (error) {
                console.error('[GiftWheel] ❌ Spin hatası:', error);
                this.showMessage('Bir hata oluştu. Lütfen tekrar deneyin.', 'error');
            } finally {
                this.isSpinning = false;
                console.log('[GiftWheel] 🔓 Spin durumu serbest bırakıldı');
            }
        }

        getCustomerData() {
            return {
                customerName: document.getElementById('customer-name')?.value || '',
                customerPhone: document.getElementById('customer-phone')?.value || '',
                customerEmail: document.getElementById('customer-email')?.value || ''
            };
        }

        validateCustomerData(data) {
            if (!data.customerName.trim()) {
                this.showMessage('Lütfen adınızı giriniz.', 'error');
                return false;
            }

            if (!data.customerPhone.trim()) {
                this.showMessage('Lütfen telefon numaranızı giriniz.', 'error');
                return false;
            }

            if (this.config?.requireEmail && !data.customerEmail.trim()) {
                this.showMessage('Lütfen e-posta adresinizi giriniz.', 'error');
                return false;
            }

            return true;
        }

        startSpinAnimation() {
            console.log('[GiftWheel] 🌀 Spin animasyonu başlatılıyor...');

            const spinDuration = 3000; // 3 saniye
            const spinRotations = 5 + Math.random() * 3; // 5-8 tur
            const targetRotation = this.currentRotation + (spinRotations * 2 * Math.PI);

            console.log('[GiftWheel] 📊 Animasyon parametreleri:', {
                duration: spinDuration + 'ms',
                rotations: spinRotations.toFixed(2),
                startRotation: this.currentRotation.toFixed(2),
                targetRotation: targetRotation.toFixed(2)
            });

            const startTime = Date.now();
            const startRotation = this.currentRotation;
            let frameCount = 0;

            const animate = () => {
                frameCount++;
                const elapsed = Date.now() - startTime;
                const progress = Math.min(elapsed / spinDuration, 1);

                // Easing function (ease-out)
                const easeOut = 1 - Math.pow(1 - progress, 3);

                this.currentRotation = startRotation + (targetRotation - startRotation) * easeOut;
                this.drawWheel();

                // Her 30 frame'de bir log
                if (frameCount % 30 === 0) {
                    console.log('[GiftWheel] 🎬 Animasyon ilerlemesi:', {
                        progress: (progress * 100).toFixed(1) + '%',
                        currentRotation: this.currentRotation.toFixed(2),
                        frame: frameCount
                    });
                }

                if (progress < 1) {
                    requestAnimationFrame(animate);
                } else {
                    console.log('[GiftWheel] ✅ Animasyon tamamlandı!', {
                        totalFrames: frameCount,
                        finalRotation: this.currentRotation.toFixed(2)
                    });
                }
            };

            animate();
        }

        waitForSpinAnimation() {
            return new Promise(resolve => {
                setTimeout(resolve, 3000);
            });
        }

        handleWinResult(result) {
            if (result.prize) {
                let message = this.config?.winMessage || 'Tebrikler! {prize} kazandınız!';
                message = message.replace('{prize}', result.prize.name);

                if (result.voucherCode) {
                    message += ` Kupon kodunuz: ${result.voucherCode}`;
                }

                this.showMessage(message, 'success');

                // Konfeti efekti
                if (this.config?.showConfetti) {
                    this.showConfetti();
                }
            } else {
                const message = this.config?.loseMessage || 'Bu sefer olmadı, tekrar deneyin!';
                this.showMessage(message, 'info');
            }
        }

        showMessage(message, type = 'info') {
            const messageEl = document.getElementById('wheel-message');
            if (!messageEl) return;

            messageEl.textContent = message;
            messageEl.className = `wheel-message ${type}`;
            messageEl.style.display = 'block';
        }

        showConfetti() {
            // Basit konfeti efekti
            const colors = ['#ff0000', '#00ff00', '#0000ff', '#ffff00', '#ff00ff', '#00ffff'];

            for (let i = 0; i < 50; i++) {
                setTimeout(() => {
                    const confetti = document.createElement('div');
                    confetti.style.cssText = `
                        position: fixed;
                        top: -10px;
                        left: ${Math.random() * 100}%;
                        width: 10px;
                        height: 10px;
                        background: ${colors[Math.floor(Math.random() * colors.length)]};
                        z-index: 10001;
                        animation: confetti-fall 3s linear forwards;
                        pointer-events: none;
                    `;

                    document.body.appendChild(confetti);

                    setTimeout(() => {
                        confetti.remove();
                    }, 3000);
                }, i * 50);
            }
        }

        injectStyles() {
            if (document.getElementById('pushonica-gift-wheel-styles')) return;

            const styles = document.createElement('style');
            styles.id = 'pushonica-gift-wheel-styles';
            styles.textContent = `
                /* Gift Wheel Floating Button */
                #pushonica-gift-wheel-btn {
                    position: fixed;
                    bottom: 20px;
                    right: 20px;
                    background: linear-gradient(135deg, ${this.config?.primaryColor || '#6366f1'}, ${this.config?.secondaryColor || '#8b5cf6'});
                    color: white;
                    border: none;
                    border-radius: 50px;
                    padding: 15px 20px;
                    cursor: pointer;
                    box-shadow: 0 4px 20px rgba(0,0,0,0.3);
                    display: flex;
                    align-items: center;
                    gap: 10px;
                    font-family: Arial, sans-serif;
                    font-weight: bold;
                    z-index: 9999;
                    transform: translateY(100px);
                    opacity: 0;
                    transition: all 0.3s ease;
                }

                #pushonica-gift-wheel-btn.show {
                    transform: translateY(0);
                    opacity: 1;
                }

                #pushonica-gift-wheel-btn:hover {
                    transform: translateY(-2px);
                    box-shadow: 0 6px 25px rgba(0,0,0,0.4);
                }

                .wheel-icon {
                    font-size: 20px;
                    animation: bounce 2s infinite;
                }

                @keyframes bounce {
                    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
                    40% { transform: translateY(-5px); }
                    60% { transform: translateY(-3px); }
                }

                /* Gift Wheel Modal */
                #pushonica-gift-wheel-modal {
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    z-index: 10000;
                    opacity: 0;
                    visibility: hidden;
                    transition: all 0.3s ease;
                }

                #pushonica-gift-wheel-modal.show {
                    opacity: 1;
                    visibility: visible;
                }

                .wheel-modal-overlay {
                    position: absolute;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background: rgba(0,0,0,0.8);
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    padding: 20px;
                }

                .wheel-modal-content {
                    background: white;
                    border-radius: 20px;
                    padding: 30px;
                    max-width: 500px;
                    width: 100%;
                    max-height: 90vh;
                    overflow-y: auto;
                    position: relative;
                    text-align: center;
                    font-family: Arial, sans-serif;
                }

                .wheel-close-btn {
                    position: absolute;
                    top: 15px;
                    right: 20px;
                    background: none;
                    border: none;
                    font-size: 30px;
                    cursor: pointer;
                    color: #666;
                }

                .wheel-header {
                    margin-bottom: 30px;
                }

                .wheel-title {
                    font-size: 24px;
                    font-weight: bold;
                    color: ${this.config?.primaryColor || '#6366f1'};
                    margin: 0 0 10px 0;
                }

                .wheel-subtitle {
                    font-size: 16px;
                    color: #666;
                    margin: 0;
                }

                .wheel-container {
                    position: relative;
                    display: inline-block;
                    margin-bottom: 30px;
                }

                #gift-wheel-canvas {
                    border-radius: 50%;
                    box-shadow: 0 0 20px rgba(0,0,0,0.2);
                }

                .wheel-pointer {
                    position: absolute;
                    top: -5px;
                    left: 50%;
                    transform: translateX(-50%);
                    width: 0;
                    height: 0;
                    border-left: 15px solid transparent;
                    border-right: 15px solid transparent;
                    border-top: 25px solid #ff4444;
                    z-index: 10;
                }

                .spin-button {
                    position: absolute;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    background: ${this.config?.primaryColor || '#6366f1'};
                    color: white;
                    border: none;
                    border-radius: 50%;
                    width: 80px;
                    height: 80px;
                    font-size: 12px;
                    font-weight: bold;
                    cursor: pointer;
                    z-index: 10;
                    transition: all 0.3s ease;
                }

                .spin-button:hover {
                    background: ${this.config?.secondaryColor || '#8b5cf6'};
                    transform: translate(-50%, -50%) scale(1.1);
                }

                .wheel-form {
                    display: flex;
                    flex-direction: column;
                    gap: 15px;
                    margin-bottom: 20px;
                }

                .wheel-form input {
                    padding: 12px;
                    border: 2px solid #e5e7eb;
                    border-radius: 8px;
                    font-size: 16px;
                    transition: border-color 0.3s ease;
                }

                .wheel-form input:focus {
                    outline: none;
                    border-color: ${this.config?.primaryColor || '#6366f1'};
                }

                .wheel-message {
                    padding: 15px;
                    border-radius: 8px;
                    margin-top: 20px;
                    display: none;
                }

                .wheel-message.success {
                    background: #d1fae5;
                    color: #065f46;
                    border: 1px solid #a7f3d0;
                }

                .wheel-message.error {
                    background: #fee2e2;
                    color: #991b1b;
                    border: 1px solid #fca5a5;
                }

                .wheel-message.info {
                    background: #dbeafe;
                    color: #1e40af;
                    border: 1px solid #93c5fd;
                }

                @keyframes confetti-fall {
                    to {
                        transform: translateY(100vh) rotate(360deg);
                    }
                }

                /* Mobile responsive */
                @media (max-width: 768px) {
                    .wheel-modal-content {
                        padding: 20px;
                        margin: 10px;
                    }

                    .wheel-title {
                        font-size: 20px;
                    }

                    #gift-wheel-canvas {
                        width: 250px;
                        height: 250px;
                    }

                    #pushonica-gift-wheel-btn {
                        bottom: 15px;
                        right: 15px;
                        padding: 12px 16px;
                    }

                    .wheel-text {
                        display: none;
                    }
                }
            `;

            document.head.appendChild(styles);
        }
    }

    // Global instance oluştur
    console.log('[GiftWheel] 🌟 Global instance oluşturuluyor...');
    window.pushonicaGiftWheel = new GiftWheelModule();
    console.log('[GiftWheel] ✅ Modül tamamen yüklendi ve hazır!');

})();
