/**
 * Simple Product Slider
 * Auto-detects company ID and loads sliders from R2
 */

(function() {
    'use strict';

    // Simple ProductSlider
    class ProductSlider {
        constructor() {
            this.companyId = this.getCompanyId();
            this.r2BaseUrl = '{{R2_BASE_URL}}';
            this.sliders = [];

            if (this.companyId) {
                console.log('[ProductSlider] Loading for company:', this.companyId);
                // Bind this context properly
                setTimeout(() => this.init(), 0);
            } else {
                console.warn('[ProductSlider] Company ID not found');
            }
        }

        getCompanyId() {
            return 'fbca2f27-b5ce-4e43-9da8-256d3b75c701';
            // Try to get from script tag
            const scripts = document.querySelectorAll('script[data-company-id]');
            for (let script of scripts) {
                const id = script.getAttribute('data-company-id');
                if (id) return id;
            }

            // Try to get from template
            const templateId = '{{COMPANY_ID}}';
            if (templateId !== '{{COMPANY_ID}}') return templateId;

            return null;
        }
        
        async init() {
            try {
                if (document.readyState === 'loading') {
                    document.addEventListener('DOMContentLoaded', () => {
                        this.loadSliders().catch(error => {
                            console.error('[ProductSlider] Load error:', error);
                        });
                    });
                } else {
                    await this.loadSliders();
                }
            } catch (error) {
                console.error('[ProductSlider] Init error:', error);
            }
        }

        async loadSliders() {
            try {
                const slidersData = await this.fetchSlidersData();

                if (!slidersData || !slidersData.sliders) {
                    console.log('[ProductSlider] No sliders found');
                    return;
                }

                // Handle new structure: sliders organized by ID
                let activeSliders = [];

                if (Array.isArray(slidersData.sliders)) {
                    // Old structure compatibility
                    activeSliders = slidersData.sliders.filter(slider =>
                        slider.items && slider.items.length > 0
                    );
                } else if (typeof slidersData.sliders === 'object') {
                    // New structure: sliders by ID
                    activeSliders = Object.keys(slidersData.sliders)
                        .map(sliderId => slidersData.sliders[sliderId])
                        .filter(slider => slider.items && slider.items.length > 0);
                }

                if (activeSliders.length === 0) {
                    console.log('[ProductSlider] No active sliders with items');
                    return;
                }

                // Inject base CSS once
                this.injectStyles();

                for (const slider of activeSliders) {
                    console.log('[ProductSlider] Rendering slider:', slider.sliderId, slider.name);
                    // Inject slider-specific styles
                    this.injectStyles(slider);
                    this.renderSlider(slider);
                }

                console.log(`[ProductSlider] Loaded ${activeSliders.length} sliders`);

            } catch (error) {
                console.error('[ProductSlider] Error:', error);
            }
        }
        
        async fetchSlidersData() {
            try {
                const url = `${this.r2BaseUrl}/${this.companyId}/productSliders.json`;
                const response = await fetch(url);

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}`);
                }

                return await response.json();
            } catch (error) {
                console.warn('[ProductSlider] R2 fetch failed:', error.message);
                return null;
            }
        }
        
        renderSlider(sliderConfig) {
            // Create floating button with proper positioning for multiple sliders
            const button = this.createFloatingButton(sliderConfig, this.sliders.length);
            this.sliders.push({
                config: sliderConfig,
                button: button
            });

            console.log('[ProductSlider] Floating button rendered for:', sliderConfig.name, 'Position:', this.sliders.length);
        }
        
        createSliderContainer(config) {
            const sliderId = config.sliderId || config.id;
            const container = document.createElement('div');
            container.className = `pushonica-product-slider pushonica-slider-${sliderId}`;
            container.innerHTML = this.generateSliderHTML(config);
            return container;
        }
        
        generateSliderHTML(config) {
            const settings = config.settings || {};
            const items = config.items || [];
            
            let itemsHTML = '';
            items.forEach((item, index) => {
                itemsHTML += `
                    <div class="product-item" data-index="${index}">
                        ${settings.showProductImage !== false ? `
                            <div class="product-image">
                                <img src="${item.productImage || ''}" alt="${item.productTitle || ''}" loading="lazy" onerror="this.style.display='none'" style="aspect-ratio: ${this.getAspectRatio(settings.imageAspectRatio)}">
                            </div>
                        ` : ''}
                        <div class="product-title">${item.productTitle || ''}</div>
                        ${settings.showProductDescription && item.productDescription ? `
                            <div class="product-description">${item.productDescription}</div>
                        ` : ''}
                        ${settings.showProductPrice !== false && item.productPrice ? `
                            <div class="product-price">${item.productPrice} ${item.currency || 'TRY'}</div>
                        ` : ''}
                        <div class="product-button">
                            <a href="${item.productUrl || '#'}" target="_blank" rel="noopener" style="color: inherit; text-decoration: none;">
                                Görüntüle
                            </a>
                        </div>
                    </div>
                `;
            });
            
            return `
                <div class="slider-wrapper">
                    <div class="slider-header">
                        <h2 class="slider-title">${config.name || 'Önerilen Ürünler'}</h2>
                    </div>
                    <div class="slider-container">
                        ${settings.showArrows !== false ? '<button class="slider-arrow slider-prev" aria-label="Önceki">&lt;</button>' : ''}
                        <div class="slider-track">
                            ${itemsHTML}
                        </div>
                        ${settings.showArrows !== false ? '<button class="slider-arrow slider-next" aria-label="Sonraki">&gt;</button>' : ''}
                    </div>
                    ${settings.showDots !== false ? '<div class="slider-dots" role="tablist"></div>' : ''}
                </div>
            `;
        }

        generateThemeCSS(settings, sliderId) {
            const theme = settings.theme || 'default';
            const primaryColor = settings.primaryColor || '#667eea';
            const secondaryColor = settings.secondaryColor || '#764ba2';
            const backgroundColor = settings.backgroundColor || '#ffffff';

            switch (theme) {
                case 'modern':
                    return `
                        .pushonica-slider-${sliderId} .pushonica-product-slider {
                            background: linear-gradient(135deg, ${backgroundColor} 0%, ${primaryColor}10 100%) !important;
                            border: none !important;
                            border-radius: 12px !important;
                        }
                        .pushonica-slider-${sliderId} .product-item {
                            border-radius: 10px !important;
                            backdrop-filter: blur(10px) !important;
                        }
                    `;
                case 'minimal':
                    return `
                        .pushonica-slider-${sliderId} .pushonica-product-slider {
                            border: 1px solid #e5e5e5 !important;
                            box-shadow: none !important;
                        }
                        .pushonica-slider-${sliderId} .product-item {
                            border: none !important;
                            border-bottom: 1px solid #f0f0f0 !important;
                            border-radius: 0 !important;
                        }
                    `;
                case 'colorful':
                    return `
                        .pushonica-slider-${sliderId} .pushonica-product-slider {
                            background: linear-gradient(45deg, ${primaryColor}20, ${secondaryColor}20) !important;
                            border: 2px solid ${primaryColor} !important;
                        }
                        .pushonica-slider-${sliderId} .product-item {
                            background: linear-gradient(135deg, ${backgroundColor} 0%, ${primaryColor}05 100%) !important;
                        }
                    `;
                default:
                    return '';
            }
        }
        
        createFloatingButton(sliderConfig, buttonIndex = 0) {
            const settings = sliderConfig.settings || {};

            const button = document.createElement('div');
            button.className = 'pushonica-floating-button';
            button.setAttribute('data-slider-id', sliderConfig.sliderId);
            button.innerHTML = `
                <div class="floating-btn-icon">🛍️</div>
                <div class="floating-btn-text">${sliderConfig.name || 'Önerilen Ürünler'}</div>
            `;

            // Calculate position for multiple buttons (stack vertically)
            const buttonHeight = 60; // Approximate button height
            const buttonSpacing = 10; // Space between buttons
            const totalButtonHeight = buttonHeight + buttonSpacing;
            const topOffset = buttonIndex * totalButtonHeight;

            // Apply dynamic styling based on settings
            button.style.position = 'fixed';
            button.style.right = '20px';
            button.style.top = `calc(50% + ${topOffset}px)`;
            button.style.transform = 'translateY(-50%)';
            button.style.zIndex = `${999999 - buttonIndex}`; // Higher buttons have higher z-index
            button.style.cursor = 'pointer';
            button.style.background = `linear-gradient(135deg, ${settings.primaryColor || '#667eea'} 0%, ${settings.secondaryColor || '#764ba2'} 100%)`;
            button.style.color = 'white';
            button.style.padding = '15px 20px';
            button.style.borderRadius = '50px';
            button.style.boxShadow = `0 4px 20px ${settings.primaryColor || '#667eea'}40`;
            button.style.fontFamily = '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif';
            button.style.fontWeight = '600';
            button.style.fontSize = '14px';
            button.style.display = 'flex';
            button.style.alignItems = 'center';
            button.style.gap = '10px';
            button.style.transition = 'all 0.3s ease';
            button.style.userSelect = 'none';

            // Hover effect
            button.addEventListener('mouseenter', () => {
                button.style.transform = 'translateY(-50%) scale(1.05)';
                button.style.boxShadow = `0 6px 25px ${settings.primaryColor || '#667eea'}60`;
            });

            button.addEventListener('mouseleave', () => {
                button.style.transform = 'translateY(-50%) scale(1)';
                button.style.boxShadow = `0 4px 20px ${settings.primaryColor || '#667eea'}40`;
            });

            // Add click event
            button.addEventListener('click', () => {
                this.showSliderOverlay(sliderConfig);
            });

            document.body.appendChild(button);
            console.log('[ProductSlider] Dynamic floating button created with settings:', settings);

            return button;
        }

        createSliderOverlay(sliderConfig) {
            const settings = sliderConfig.settings || {};
            const sliderId = sliderConfig.sliderId || sliderConfig.id;

            const popup = document.createElement('div');
            popup.className = `pushonica-slider-popup pushonica-slider-popup-${sliderId} pushonica-slider-${sliderId}`;
            popup.style.display = 'none';

            // Simple popup - no backdrop, no blur
            popup.innerHTML = `
                <div class="popup-header">
                    <h3>${sliderConfig.name || 'Önerilen Ürünler'}</h3>
                    <button class="popup-close">&times;</button>
                </div>
                <div class="popup-slider-container">
                    ${this.generateSliderHTML(sliderConfig)}
                </div>
            `;

            // Apply dynamic styling
            popup.style.position = 'fixed';
            popup.style.top = '20px';
            popup.style.right = '20px';
            popup.style.width = '400px';
            popup.style.maxHeight = '80vh';
            popup.style.backgroundColor = settings.backgroundColor || '#ffffff';
            popup.style.borderRadius = '12px';
            popup.style.boxShadow = '0 10px 30px rgba(0, 0, 0, 0.2)';
            popup.style.zIndex = '1000000';
            popup.style.overflow = 'hidden';
            popup.style.fontFamily = '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif';

            // Apply dynamic header styling
            const header = popup.querySelector('.popup-header');
            if (header) {
                header.style.background = `linear-gradient(135deg, ${settings.primaryColor || '#667eea'} 0%, ${settings.secondaryColor || '#764ba2'} 100%)`;
                header.style.color = 'white';
                header.style.padding = '15px 20px';
                header.style.display = 'flex';
                header.style.justifyContent = 'space-between';
                header.style.alignItems = 'center';
            }

            // Style close button
            const closeBtn = popup.querySelector('.popup-close');
            if (closeBtn) {
                closeBtn.style.background = 'none';
                closeBtn.style.border = 'none';
                closeBtn.style.color = 'white';
                closeBtn.style.fontSize = '24px';
                closeBtn.style.cursor = 'pointer';
                closeBtn.style.padding = '0';
                closeBtn.style.width = '30px';
                closeBtn.style.height = '30px';
                closeBtn.style.borderRadius = '50%';
                closeBtn.style.display = 'flex';
                closeBtn.style.alignItems = 'center';
                closeBtn.style.justifyContent = 'center';
                closeBtn.style.transition = 'background 0.3s ease';

                closeBtn.addEventListener('mouseenter', () => {
                    closeBtn.style.background = 'rgba(255, 255, 255, 0.2)';
                });
                closeBtn.addEventListener('mouseleave', () => {
                    closeBtn.style.background = 'none';
                });
            }

            // Style slider container
            const sliderContainer = popup.querySelector('.popup-slider-container');
            if (sliderContainer) {
                sliderContainer.style.padding = '20px';
                sliderContainer.style.maxHeight = 'calc(80vh - 60px)';
                sliderContainer.style.overflowY = 'auto';
            }

            // Close event
            closeBtn.addEventListener('click', () => this.hideSliderOverlay());

            document.body.appendChild(popup);

            // No slider functionality needed for popup - using vertical layout
            console.log('[ProductSlider] Simple popup created - vertical layout, no slider functionality');

            return popup;
        }

        showSliderOverlay(sliderConfig) {
            const sliderId = sliderConfig.sliderId || sliderConfig.id;
            console.log('[ProductSlider] Opening overlay for slider:', sliderId);

            // Close any other open popups first
            this.hideAllSliderOverlays();

            // Look for specific slider popup
            let popup = document.querySelector(`.pushonica-slider-popup-${sliderId}`);

            if (!popup) {
                console.log('[ProductSlider] Creating new overlay for slider:', sliderId);
                popup = this.createSliderOverlay(sliderConfig);
            } else {
                console.log('[ProductSlider] Using existing overlay for slider:', sliderId);
            }

            popup.style.display = 'block';

            // Simple fade in animation
            popup.style.opacity = '0';
            popup.style.transform = 'translateY(-10px)';
            popup.style.transition = 'all 0.3s ease-out';

            setTimeout(() => {
                popup.style.opacity = '1';
                popup.style.transform = 'translateY(0)';
            }, 10);

            console.log('[ProductSlider] Popup shown for slider:', sliderId);
        }

        hideSliderOverlay(sliderId = null) {
            if (sliderId) {
                const popup = document.querySelector(`.pushonica-slider-popup-${sliderId}`);
                if (popup) {
                    popup.style.opacity = '0';
                    popup.style.transform = 'translateY(-10px)';

                    setTimeout(() => {
                        popup.style.display = 'none';
                    }, 300);

                    console.log('[ProductSlider] Popup hidden for slider:', sliderId);
                }
            } else {
                this.hideAllSliderOverlays();
            }
        }

        hideAllSliderOverlays() {
            const popups = document.querySelectorAll('.pushonica-slider-popup');
            popups.forEach(popup => {
                popup.style.opacity = '0';
                popup.style.transform = 'translateY(-10px)';

                setTimeout(() => {
                    popup.style.display = 'none';
                }, 300);
            });

            console.log('[ProductSlider] All popups hidden');
        }

        initializeSliderFunctionality(container, config) {
            // Check if this is a popup - if so, skip slider functionality (vertical layout)
            if (container.closest('.pushonica-slider-popup')) {
                console.log('[ProductSlider] Popup detected - using vertical layout, skipping slider functionality');
                return;
            }

            const settings = config.settings || {};
            const track = container.querySelector('.slider-track');
            const items = container.querySelectorAll('.slider-item');
            const prevBtn = container.querySelector('.slider-prev');
            const nextBtn = container.querySelector('.slider-next');
            const dotsContainer = container.querySelector('.slider-dots');

            if (!track || items.length === 0) {
                console.log('[ProductSlider] No track or items found, skipping functionality');
                return;
            }

            let currentIndex = 0;
            const itemsPerView = this.getItemsPerView(settings);
            const maxIndex = Math.max(0, items.length - itemsPerView);
/*
            log('Initializing slider functionality:', {
                itemsCount: items.length,
                itemsPerView: itemsPerView,
                maxIndex: maxIndex
            });*/

            // Create dots
            if (dotsContainer && settings.showDots !== false) {
                for (let i = 0; i <= maxIndex; i++) {
                    const dot = document.createElement('button');
                    dot.className = 'slider-dot';
                    dot.setAttribute('data-index', i);
                    dot.setAttribute('aria-label', `Slide ${i + 1}`);
                    if (i === 0) dot.classList.add('active');
                    dotsContainer.appendChild(dot);
                }
                //log('Dots created:', maxIndex + 1);
            }

            // Update slider position
            const updateSlider = () => {
                const translateX = -(currentIndex * (100 / itemsPerView));
                track.style.transform = `translateX(${translateX}%)`;

                // Update dots
                const dots = container.querySelectorAll('.slider-dot');
                dots.forEach((dot, index) => {
                    dot.classList.toggle('active', index === currentIndex);
                });

                // Update arrow states
                if (prevBtn) prevBtn.disabled = currentIndex === 0;
                if (nextBtn) nextBtn.disabled = currentIndex >= maxIndex;

                //log('Slider updated to index:', currentIndex);
            };

            // Previous button
            if (prevBtn) {
                prevBtn.addEventListener('click', () => {
                    if (currentIndex > 0) {
                        currentIndex--;
                        updateSlider();
                        //log('Previous clicked, new index:', currentIndex);
                    }
                });
            }

            // Next button
            if (nextBtn) {
                nextBtn.addEventListener('click', () => {
                    if (currentIndex < maxIndex) {
                        currentIndex++;
                        updateSlider();
                        //log('Next clicked, new index:', currentIndex);
                    }
                });
            }

            // Dot clicks
            if (dotsContainer) {
                dotsContainer.addEventListener('click', (e) => {
                    if (e.target.classList.contains('slider-dot')) {
                        currentIndex = parseInt(e.target.getAttribute('data-index'));
                        updateSlider();
                        //log('Dot clicked, new index:', currentIndex);
                    }
                });
            }

            // Touch/swipe support
            this.addTouchSupport(track, () => {
                if (currentIndex < maxIndex) {
                    currentIndex++;
                    updateSlider();
                    //log('Swipe left, new index:', currentIndex);
                }
            }, () => {
                if (currentIndex > 0) {
                    currentIndex--;
                    updateSlider();
                    //log('Swipe right, new index:', currentIndex);
                }
            });

            // Auto play
            if (settings.autoPlay !== false && maxIndex > 0) {
                const interval = settings.autoPlayInterval || 5000;
                setInterval(() => {
                    if (currentIndex < maxIndex) {
                        currentIndex++;
                    } else {
                        currentIndex = 0;
                    }
                    updateSlider();
                    //log('Auto play, new index:', currentIndex);
                }, interval);
                //log('Auto play enabled with interval:', interval);
            }

            // Initial update
            updateSlider();
            //log('Slider functionality initialized successfully');
        }

        getItemsPerView(settings) {
            const width = window.innerWidth;
            if (width < 768) {
                return settings.itemsPerViewMobile || 1;
            } else if (width < 1024) {
                return settings.itemsPerViewTablet || 2;
            } else {
                return settings.itemsPerView || 4;
            }
        }

        addTouchSupport(element, onSwipeLeft, onSwipeRight) {
            let startX = 0;
            let startY = 0;

            element.addEventListener('touchstart', (e) => {
                startX = e.touches[0].clientX;
                startY = e.touches[0].clientY;
            });

            element.addEventListener('touchend', (e) => {
                if (!startX || !startY) return;

                const endX = e.changedTouches[0].clientX;
                const endY = e.changedTouches[0].clientY;

                const diffX = startX - endX;
                const diffY = startY - endY;

                // Check if it's a horizontal swipe
                if (Math.abs(diffX) > Math.abs(diffY) && Math.abs(diffX) > 50) {
                    if (diffX > 0) {
                        onSwipeLeft();
                    } else {
                        onSwipeRight();
                    }
                }

                startX = 0;
                startY = 0;
            });
        }

        getAspectRatio(ratio) {
            switch (ratio) {
                case 'landscape': return '16/9';
                case 'portrait': return '3/4';
                case 'square':
                default: return '1/1';
            }
        }

        injectStyles(sliderConfig = null) {
            // Base styles - inject once
            if (!document.getElementById('pushonica-slider-base-styles')) {
                this.injectBaseStyles();
            }

            // Dynamic styles for specific slider
            if (sliderConfig) {
                this.injectSliderSpecificStyles(sliderConfig);
            }
        }

        injectBaseStyles() {
            const baseStyles = document.createElement('style');
            baseStyles.id = 'pushonica-slider-base-styles';
            baseStyles.textContent = `
                /* Base responsive and layout styles */
                .pushonica-product-slider {
                    margin: 20px auto;
                    padding: 20px;
                    border-radius: 8px;
                    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                    max-width: 1200px;
                    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                }
                .slider-title {
                    font-size: 24px;
                    font-weight: bold;
                    margin: 0 0 20px 0;
                    text-align: center;
                }
                .slider-container { position: relative; overflow: hidden; }
                .slider-track { display: flex; transition: transform 0.3s ease; }
                .slider-item { flex: 0 0 25%; padding: 0 10px; box-sizing: border-box; }
                @media (max-width: 1023px) { .slider-item { flex: 0 0 50%; } }
                @media (max-width: 767px) { .slider-item { flex: 0 0 100%; } }

                /* Product item styles - matching preview exactly */
                .product-item {
                    border-radius: 6px;
                    padding: 15px;
                    margin-bottom: 10px;
                    text-align: center;
                    transition: all 0.3s ease;
                    width: 100%;
                }
                .product-image {
                    margin-bottom: 10px;
                }
                .product-image img {
                    width: 100%;
                    height: 120px;
                    object-fit: cover;
                    border-radius: 6px;
                }
                .product-title {
                    font-size: 16px;
                    font-weight: 600;
                    margin-bottom: 8px;
                    transition: all 0.3s ease;
                }
                .product-description {
                    font-size: 12px;
                    margin-bottom: 8px;
                    line-height: 1.3;
                }
                .product-price {
                    font-size: 18px;
                    font-weight: bold;
                    margin-bottom: 10px;
                    transition: all 0.3s ease;
                }
                .product-button {
                    padding: 8px 16px;
                    border-radius: 4px;
                    font-size: 14px;
                    font-weight: 500;
                    text-align: center;
                    cursor: pointer;
                    transition: all 0.3s ease;
                    display: inline-block;
                }
                .slider-arrow {
                    position: absolute;
                    top: 50%;
                    transform: translateY(-50%);
                    border: none;
                    width: 40px;
                    height: 40px;
                    border-radius: 50%;
                    cursor: pointer;
                    font-size: 18px;
                    z-index: 10;
                    transition: all 0.3s ease;
                }
                .slider-arrow:disabled { opacity: 0.3; cursor: not-allowed; }
                .slider-prev { left: -20px; }
                .slider-next { right: -20px; }
                .slider-dots {
                    display: flex;
                    justify-content: center;
                    gap: 8px;
                    margin-top: 20px;
                }
                .slider-dot {
                    width: 12px;
                    height: 12px;
                    border-radius: 50%;
                    border: none;
                    cursor: pointer;
                    transition: all 0.3s ease;
                }

            `;

            document.head.appendChild(baseStyles);
        }

        injectSliderSpecificStyles(sliderConfig) {
            const settings = sliderConfig.settings || {};
            const sliderId = sliderConfig.sliderId || sliderConfig.id;

            // Remove existing styles for this slider
            const existingStyle = document.getElementById(`pushonica-slider-${sliderId}-styles`);
            if (existingStyle) {
                existingStyle.remove();
            }

            const dynamicStyles = document.createElement('style');
            dynamicStyles.id = `pushonica-slider-${sliderId}-styles`;
            dynamicStyles.textContent = this.generateDynamicCSS(settings, sliderId);

            document.head.appendChild(dynamicStyles);
        }

        generateDynamicCSS(settings, sliderId) {
            // Get colors with fallbacks
            const primaryColor = settings.primaryColor || '#667eea';
            const secondaryColor = settings.secondaryColor || '#764ba2';
            const backgroundColor = settings.backgroundColor || '#ffffff';
            const textColor = settings.textColor || '#333333';

            // Animation settings
            const transitionDuration = (settings.transitionDuration || '300') + 'ms';
            const enableAnimations = settings.enableAnimations !== false;

            // Display settings
            const showArrows = settings.showArrows !== false;
            const showDots = settings.showDots !== false;
            const showProductImage = settings.showProductImage !== false;
            const showProductPrice = settings.showProductPrice !== false;
            const showProductDescription = settings.showProductDescription || false;

            return `
                /* Dynamic styles for slider ${sliderId} */
                .pushonica-slider-${sliderId} .pushonica-product-slider {
                    background: ${backgroundColor} !important;
                    border: 2px solid ${primaryColor} !important;
                    transition: all ${transitionDuration} !important;
                }

                .pushonica-slider-${sliderId} .slider-title {
                    color: ${textColor} !important;
                }

                .pushonica-slider-${sliderId} .product-item {
                    background: ${backgroundColor} !important;
                    border: 1px solid ${primaryColor} !important;
                    border-radius: 6px !important;
                    padding: 15px !important;
                    margin-bottom: 10px !important;
                    transition: all ${transitionDuration} !important;
                }

                .pushonica-slider-${sliderId} .product-item:hover {
                    background: ${primaryColor}15 !important;
                    ${enableAnimations ? `
                        transform: translateY(-2px) !important;
                        box-shadow: 0 4px 12px rgba(0,0,0,0.15) !important;
                    ` : ''}
                }

                .pushonica-slider-${sliderId} .product-title {
                    color: ${textColor} !important;
                    font-size: 16px !important;
                    font-weight: 600 !important;
                    margin-bottom: 8px !important;
                    transition: all ${transitionDuration} !important;
                }

                .pushonica-slider-${sliderId} .product-description {
                    color: #666 !important;
                    font-size: 12px !important;
                    margin-bottom: 8px !important;
                    line-height: 1.3 !important;
                    ${!showProductDescription ? 'display: none !important;' : ''}
                }

                .pushonica-slider-${sliderId} .product-price {
                    color: ${primaryColor} !important;
                    font-size: 18px !important;
                    font-weight: bold !important;
                    margin-bottom: 10px !important;
                    transition: all ${transitionDuration} !important;
                    ${!showProductPrice ? 'display: none !important;' : ''}
                }

                .pushonica-slider-${sliderId} .product-image {
                    margin-bottom: 10px !important;
                    ${!showProductImage ? 'display: none !important;' : ''}
                }

                .pushonica-slider-${sliderId} .product-image img {
                    width: 100% !important;
                    height: 120px !important;
                    object-fit: cover !important;
                    border-radius: 6px !important;
                }

                .pushonica-slider-${sliderId} .product-button {
                    background: ${primaryColor} !important;
                    color: white !important;
                    padding: 8px 16px !important;
                    border-radius: 4px !important;
                    font-size: 14px !important;
                    font-weight: 500 !important;
                    text-align: center !important;
                    cursor: pointer !important;
                    transition: all ${transitionDuration} !important;
                }

                .pushonica-slider-${sliderId} .product-button:hover {
                    background: ${secondaryColor} !important;
                    ${enableAnimations ? `
                        transform: translateY(-1px) !important;
                        box-shadow: 0 4px 12px rgba(0,0,0,0.2) !important;
                    ` : ''}
                }

                /* Alt alta sıralama - her zaman dikey liste */
                .pushonica-slider-${sliderId} .pushonica-product-slider {
                    display: flex !important;
                    flex-direction: column !important;
                    gap: 10px !important;
                }

                .pushonica-slider-${sliderId} .slider-track {
                    display: flex !important;
                    flex-direction: column !important;
                    gap: 10px !important;
                    transition: none !important;
                    transform: none !important;
                }

                .pushonica-slider-${sliderId} .product-item {
                    width: 100% !important;
                }

                /* Hide arrows and dots - not needed for vertical layout */
                .pushonica-slider-${sliderId} .slider-arrow {
                    display: none !important;
                }

                .pushonica-slider-${sliderId} .slider-dots {
                    display: none !important;
                }



                ${this.generateThemeCSS(settings, sliderId)}

                /* Floating Button Styles */
                .pushonica-floating-button {
                    position: fixed;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    padding: 15px 20px;
                    border-radius: 50px;
                    box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);
                    cursor: pointer;
                    transition: all 0.3s ease;
                    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                    font-weight: 600;
                    font-size: 14px;
                    display: flex;
                    align-items: center;
                    gap: 10px;
                    z-index: 999999;
                    user-select: none;
                }
                .pushonica-floating-button:hover {
                    transform: scale(1.05);
                    box-shadow: 0 6px 25px rgba(102, 126, 234, 0.4);
                }
                .floating-btn-icon { font-size: 18px; }
                .floating-btn-text { white-space: nowrap; }

                /* Simple Popup Styles - NO backdrop, NO blur */
                .pushonica-slider-popup {
                    /* All styling is done inline in JavaScript for dynamic colors */
                }

                /* Popup content adjustments - Vertical Layout */
                .pushonica-slider-popup .pushonica-product-slider {
                    margin: 0 !important;
                    padding: 0 !important;
                    background: transparent !important;
                    border-radius: 0 !important;
                    box-shadow: none !important;
                    max-width: none !important;
                    border: none !important;
                }

                .pushonica-slider-popup .slider-title {
                    display: none !important;
                }

                .pushonica-slider-popup .slider-container {
                    margin: 0 !important;
                    overflow: visible !important;
                }

                .pushonica-slider-popup .slider-track {
                    display: flex !important;
                    flex-direction: column !important;
                    gap: 10px !important;
                    transition: none !important;
                    transform: none !important;
                }

                .pushonica-slider-popup .product-item {
                    width: 100% !important;
                    padding: 15px !important;
                    margin-bottom: 10px !important;
                    border-radius: 8px !important;
                    transition: all 0.3s ease !important;
                    display: block !important;
                }

                .pushonica-slider-popup .product-image {
                    margin-bottom: 10px !important;
                }

                .pushonica-slider-popup .product-image img {
                    height: 120px !important;
                    width: 100% !important;
                    object-fit: cover !important;
                    border-radius: 6px !important;
                }

                .pushonica-slider-popup .product-title {
                    font-size: 14px !important;
                    font-weight: 600 !important;
                    margin: 8px 0 !important;
                    line-height: 1.3 !important;
                }

                .pushonica-slider-popup .product-description {
                    font-size: 12px !important;
                    margin: 6px 0 !important;
                    line-height: 1.4 !important;
                }

                .pushonica-slider-popup .product-price {
                    font-size: 16px !important;
                    font-weight: 700 !important;
                    margin: 8px 0 !important;
                }

                .pushonica-slider-popup .product-button {
                    padding: 8px 16px !important;
                    font-size: 12px !important;
                    border-radius: 6px !important;
                    display: inline-block !important;
                    margin-top: 8px !important;
                }

                /* Hide arrows and dots in popup - not needed for vertical layout */
                .pushonica-slider-popup .slider-arrow {
                    display: none !important;
                }

                .pushonica-slider-popup .slider-dots {
                    display: none !important;
                }

                /* Mobile responsive */
                @media (max-width: 768px) {
                    .pushonica-slider-popup {
                        width: calc(100vw - 40px) !important;
                        right: 20px !important;
                    }
                }
            `;

            document.head.appendChild(styles);
        }
    }

    // Initialize
    const productSlider = new ProductSlider();
    window.productSlider = productSlider;

})();
