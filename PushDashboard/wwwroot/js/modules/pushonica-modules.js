/**
 * <PERSON>ush<PERSON><PERSON> Modules - <PERSON>
 * Tüm Pushonica modüllerini yöneten ana script
 * Mevcut modülleri dinamik olarak yükler ve yönetir
 */

(function() {
    'use strict';

    // Global değişkenler
    const PUSHONICA_VERSION = '1.0.0';
    const R2_BASE_URL = 'https://pub-99389ce4adeb4856bc9e14750f2fa16f.r2.dev';

    // Ana modül yöneticisi
    class PushonicaModules {
        constructor() {
            this.companyId = this.getCompanyId();
            this.modules = {};
            this.loadedScripts = new Set();
            this.initialized = false;
            this.debug = this.getDebugMode();
            this.domAnalyzer = null; // Lazy load edilecek

            this.log('Pushonica Modules initializing...', { companyId: this.companyId });
        }

        getCompanyId() {
            return 'fbca2f27-b5ce-4e43-9da8-256d3b75c701';
            // Script tag'inden company ID'yi al
            const scripts = document.querySelectorAll('script[data-company-id]');
            for (let script of scripts) {
                const companyId = script.getAttribute('data-company-id');
                if (companyId) return companyId;
            }

            // Template'den al (R2 deployment için)
            const templateId = '{{COMPANY_ID}}';
            if (templateId !== '{{COMPANY_ID}}') return templateId;

            // URL'den al (fallback)
            const urlParams = new URLSearchParams(window.location.search);
            return urlParams.get('pushonicaCompanyId') || null;
        }

        getDebugMode() {
            return window.location.search.includes('pushonicaDebug=true');
        }

        log(message, data = null) {
            if (this.debug) {
                console.log(`[Pushonica] ${message}`, data || '');
            }
        }

        async init() {
            if (this.initialized) return;

            if (!this.companyId) {
                this.log('Company ID not found. Pushonica modules will not load.');
                return;
            }

            try {
                // DOM hazır olana kadar bekle
                if (document.readyState === 'loading') {
                    document.addEventListener('DOMContentLoaded', () => this.loadModules());
                } else {
                    await this.loadModules();
                }

                this.initialized = true;
                this.log('Pushonica Modules initialized successfully');
            } catch (error) {
                this.log('Pushonica Modules initialization failed:', error);
            }
        }
        
        async loadModules() {
            try {
                // DOM Analyzer'ı lazy load et
                await this.loadDOMAnalyzer();

                // Sayfa tipini analiz et
                const pageType = this.domAnalyzer.analyzePageType();
                const pageInfo = this.domAnalyzer.getPageInfo();
                this.log('Detected page info:', pageInfo);

                // Modül listesini R2'den çek
                const modulesList = await this.fetchModulesList();
                if (!modulesList || !modulesList.modules) {
                    this.log('No modules list found');
                    return;
                }

                // Her modül için kontrol et ve yükle
                for (const moduleConfig of modulesList.modules) {
                    if (this.shouldLoadModule(moduleConfig, pageType)) {
                        await this.tryLoadModule(moduleConfig.name);
                    }
                }

                this.log('All modules processed successfully');
            } catch (error) {
                this.log('Failed to load modules:', error);
            }
        }

        async loadDOMAnalyzer() {
            // TicimaxDOMAnalyzer zaten mevcut mu kontrol et
            if (typeof TicimaxDOMAnalyzer !== 'undefined') {
                this.domAnalyzer = new TicimaxDOMAnalyzer();
                this.log('DOM Analyzer loaded from existing script');
                return;
            }

            // DOM Analyzer'ı R2'den dinamik olarak yükle
            try {
                // R2'den ticimax-dom-analyzer.js'i yükle
                await this.loadScript('ticimax-dom-analyzer.js');

                // Script yüklendikten sonra class'ın mevcut olup olmadığını kontrol et
                if (typeof TicimaxDOMAnalyzer !== 'undefined') {
                    this.domAnalyzer = new TicimaxDOMAnalyzer();
                    this.log('DOM Analyzer loaded from R2');
                } else {
                    throw new Error('TicimaxDOMAnalyzer class not found after loading script');
                }
            } catch (error) {
                this.log('Failed to load DOM Analyzer from R2:', error);
                throw error;
            }
        }

        async fetchModulesList() {
            try {
                // Önce R2'den dene
                let url = `${R2_BASE_URL}/modules.json`;
                this.log('Fetching modules list from:', url);

                let response = await fetch(url);

                // R2'de yoksa local'den dene (test için)
                if (!response.ok) {
                    url = './modules.json';
                    this.log('Trying local modules list from:', url);
                    response = await fetch(url);
                }

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();
                this.log('Modules list fetched successfully:', data);
                return data;
            } catch (error) {
                this.log('Failed to fetch modules list:', error);
                return null;
            }
        }

        shouldLoadModule(moduleConfig, pageType) {
            const moduleName = moduleConfig.name;

            // Bu sayfa tipinde çalışacak mı?
            if (moduleConfig.pages && !moduleConfig.pages.includes(pageType) && !moduleConfig.pages.includes('all')) {
                this.log(`Module ${moduleName} not allowed on page type: ${pageType}`);
                return false;
            }

            this.log(`Module ${moduleName} should be loaded on ${pageType}`);
            return true;
        }

        async tryLoadModule(moduleName) {
            try {
                this.log(`Trying to load module: ${moduleName}`);

                // Modül script'inin URL'ini oluştur
                const scriptUrl = `${R2_BASE_URL}/${this.companyId}/${moduleName}.js`;

                // Script var mı kontrol et
                const response = await fetch(scriptUrl, { method: 'HEAD' });
                if (!response.ok) {
                    this.log(`Module ${moduleName} not found for company ${this.companyId}`);
                    return;
                }

                // Script'i yükle
                await this.loadScript(`${this.companyId}/${moduleName}.js`);

                this.log(`Module ${moduleName} loaded successfully`);
                this.modules[moduleName] = {
                    loaded: true,
                    loadedAt: new Date().toISOString()
                };

            } catch (error) {
                this.log(`Failed to load module ${moduleName}:`, error);
            }
        }

        async loadScript(scriptPath) {
            // Zaten yüklendi mi?
            if (this.loadedScripts.has(scriptPath)) {
                this.log(`Script already loaded: ${scriptPath}`);
                return;
            }

            return new Promise((resolve, reject) => {
                const script = document.createElement('script');

                // Script URL'ini belirle
                let scriptUrl;
                if (scriptPath.startsWith('./')) {
                    // Local dosya
                    scriptUrl = scriptPath;
                } else if (scriptPath.includes('/')) {
                    // Company specific dosya (örn: "companyId/productSlider.js")
                    scriptUrl = `${R2_BASE_URL}/${scriptPath}`;
                } else {
                    // Global dosya (örn: "ticimax-dom-analyzer.js")
                    scriptUrl = `${R2_BASE_URL}/${scriptPath}`;
                }

                script.src = scriptUrl;

                script.onload = () => {
                    this.loadedScripts.add(scriptPath);
                    this.log(`Script loaded: ${scriptUrl}`);
                    resolve();
                };
                script.onerror = () => {
                    this.log(`Failed to load script: ${scriptUrl}`);
                    reject(new Error(`Failed to load script: ${scriptUrl}`));
                };
                document.head.appendChild(script);
            });
        }

        // Utility methods
        getModule(moduleName) {
            return this.modules[moduleName] || null;
        }

        isModuleLoaded(moduleName) {
            return this.modules[moduleName] && this.modules[moduleName].loaded;
        }

        // Modül durumu kontrol etme
        isModuleAvailable(moduleName) {
            return this.modules[moduleName] && this.modules[moduleName].loaded;
        }

        // Debug bilgileri
        getDebugInfo() {
            return {
                version: PUSHONICA_VERSION,
                companyId: this.companyId,
                initialized: this.initialized,
                loadedScripts: Array.from(this.loadedScripts),
                modules: Object.keys(this.modules),
                pageType: this.domAnalyzer ? this.domAnalyzer.analyzePageType() : 'unknown'
            };
        }
    }

    // Global instance oluştur
    window.pushonicaModules = new PushonicaModules();

    // Debug için global erişim
    if (window.pushonicaModules.debug) {
        window.PushonicaDebug = {
            getInfo: () => window.pushonicaModules.getDebugInfo(),
            getModule: (name) => window.pushonicaModules.getModule(name),
            reload: () => {
                window.pushonicaModules.initialized = false;
                window.pushonicaModules.init();
            }
        };
    }

    // Otomatik başlatma
    window.pushonicaModules.init();

})();
