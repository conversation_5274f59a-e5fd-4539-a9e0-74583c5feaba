/**
 * Gift Wheel Admin JavaScript
 * Handles all admin interface interactions for the Gift Wheel module
 */

class GiftWheelAdmin {
    constructor() {
        this.currentWheelId = null;
        this.currentPrizes = [];
        this.isLoading = false;
        
        this.init();
    }
    
    init() {
        this.setupEventListeners();
        this.setupTabNavigation();
        this.loadInitialData();
    }
    
    setupEventListeners() {
        // Wheel management
        document.getElementById('createWheelBtn')?.addEventListener('click', () => this.createWheel());
        document.getElementById('toggleWheelStatus')?.addEventListener('click', (e) => this.toggleWheelStatus(e));

        // Prize management
        document.getElementById('addPrizeBtn')?.addEventListener('click', () => this.openPrizeModal());
        document.getElementById('addFirstPrizeBtn')?.addEventListener('click', () => this.openPrizeModal());

        // Settings
        document.getElementById('wheelSettingsForm')?.addEventListener('submit', (e) => this.saveSettings(e));
        document.getElementById('previewWheelBtn')?.addEventListener('click', () => this.openPreviewModal());

        // Prize template buttons (setup after DOM is ready)
        setTimeout(() => {
            document.querySelectorAll('.prize-template-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    try {
                        const template = JSON.parse(this.dataset.template);
                        window.addPrizeFromTemplate(template);
                    } catch (error) {
                        console.error('Error parsing template data:', error);
                    }
                });
            });
        }, 500);
        
        // Statistics
        document.getElementById('refreshStatsBtn')?.addEventListener('click', () => this.refreshStatistics());
        
        // Script generation
        document.getElementById('refreshScriptBtn')?.addEventListener('click', () => this.loadEmbedScript());
        document.getElementById('fullPreviewBtn')?.addEventListener('click', () => this.openFullPreview());

        // Settings
        document.getElementById('resetSettingsBtn')?.addEventListener('click', () => this.resetSettings());

        // Prize actions (setup after DOM is ready)
        setTimeout(() => {
            this.setupPrizeActions();
        }, 500);

        // Global error handler
        window.addEventListener('unhandledrejection', (e) => {
            console.error('Unhandled promise rejection:', e.reason);
            this.showNotification('Bir hata oluştu. Lütfen sayfayı yenileyin.', 'error');
        });
    }
    
    setupTabNavigation() {
        const tabButtons = document.querySelectorAll('.tab-button');
        const tabContents = document.querySelectorAll('.tab-content');
        
        tabButtons.forEach(button => {
            button.addEventListener('click', () => {
                const targetTab = button.dataset.tab;
                
                // Update button states
                tabButtons.forEach(btn => {
                    btn.classList.remove('active', 'border-indigo-500', 'text-indigo-600');
                    btn.classList.add('border-transparent', 'text-gray-500');
                });
                
                button.classList.add('active', 'border-indigo-500', 'text-indigo-600');
                button.classList.remove('border-transparent', 'text-gray-500');
                
                // Update content visibility
                tabContents.forEach(content => {
                    content.classList.add('hidden');
                    content.style.display = 'none';
                });

                const targetContent = document.getElementById(`${targetTab}-tab`);
                if (targetContent) {
                    targetContent.classList.remove('hidden');
                    targetContent.style.display = 'block';
                    console.log('Tab content shown for:', targetTab, targetContent);
                } else {
                    console.error('Tab content not found for:', targetTab);
                }
                
                // Load tab-specific data
                this.onTabChange(targetTab);
            });
        });

        // Show first tab by default
        if (tabButtons.length > 0) {
            const firstTab = tabButtons[0].dataset.tab;
            const firstContent = document.getElementById(`${firstTab}-tab`);
            if (firstContent) {
                // Hide all tabs first
                tabContents.forEach(content => {
                    content.classList.add('hidden');
                    content.style.display = 'none';
                });

                // Show first tab
                firstContent.classList.remove('hidden');
                firstContent.style.display = 'block';
                console.log('First tab shown:', firstTab, firstContent);
            }
        }
    }
    
    onTabChange(tabName) {
        switch (tabName) {
            case 'prizes':
                this.loadPrizes();
                break;
            case 'statistics':
                this.loadStatistics();
                break;
            case 'script':
                this.loadEmbedScript();
                break;
        }
    }
    
    async loadInitialData() {
        try {
            this.showLoading(true);
            
            // Load basic wheel data if exists
            const response = await fetch('/GiftWheel/GetWheelConfig');
            if (response.ok) {
                const result = await response.json();
                if (result.success && result.data) {
                    this.currentWheelId = result.data.wheelId;
                }
            }
            
        } catch (error) {
            console.error('Error loading initial data:', error);
        } finally {
            this.showLoading(false);
        }
    }
    
    // Wheel Management
    async createWheel() {
        try {
            const response = await fetch('/GiftWheel/CreateOrUpdateWheel', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'RequestVerificationToken': this.getAntiForgeryToken()
                },
                body: JSON.stringify({
                    name: 'Hediye Çarkı'
                })
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.showNotification(result.message, 'success');
                setTimeout(() => {
                    window.location.reload();
                }, 1500);
            } else {
                this.showNotification(result.message, 'error');
            }
            
        } catch (error) {
            console.error('Error creating wheel:', error);
            this.showNotification('Çark oluşturulurken hata oluştu.', 'error');
        }
    }
    
    async toggleWheelStatus(event) {
        const button = event.target.closest('button');
        const isActive = button.dataset.active === 'true';
        const newStatus = !isActive;
        
        try {
            const response = await fetch('/GiftWheel/ToggleWheelStatus', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'RequestVerificationToken': this.getAntiForgeryToken()
                },
                body: JSON.stringify({
                    isActive: newStatus
                })
            });
            
            const result = await response.json();
            
            if (result.success) {
                // Update UI
                button.dataset.active = newStatus.toString();
                
                if (newStatus) {
                    button.classList.remove('bg-gray-200');
                    button.classList.add('bg-green-600');
                    button.querySelector('span').classList.remove('translate-x-1');
                    button.querySelector('span').classList.add('translate-x-6');
                    button.nextElementSibling.textContent = 'Aktif';
                    button.nextElementSibling.classList.remove('text-gray-500');
                    button.nextElementSibling.classList.add('text-green-600');
                } else {
                    button.classList.remove('bg-green-600');
                    button.classList.add('bg-gray-200');
                    button.querySelector('span').classList.remove('translate-x-6');
                    button.querySelector('span').classList.add('translate-x-1');
                    button.nextElementSibling.textContent = 'Pasif';
                    button.nextElementSibling.classList.remove('text-green-600');
                    button.nextElementSibling.classList.add('text-gray-500');
                }
                
                this.showNotification(result.message, 'success');
            } else {
                this.showNotification(result.message, 'error');
            }
            
        } catch (error) {
            console.error('Error toggling wheel status:', error);
            this.showNotification('Durum değiştirilirken hata oluştu.', 'error');
        }
    }
    
    // Prize Management
    async loadPrizes() {
        try {
            const response = await fetch('/GiftWheel/GetPrizes');
            const result = await response.json();
            
            if (result.success) {
                this.currentPrizes = result.data;
                this.updatePrizesList();
            }
            
        } catch (error) {
            console.error('Error loading prizes:', error);
        }
    }
    
    updatePrizesList() {
        // This would update the prizes list in the UI
        // Implementation depends on the specific UI structure
        console.log('Updating prizes list:', this.currentPrizes);
    }

    // Prize Form Submission
    async submitPrizeForm(formData) {
        try {
            this.showLoading(true);

            // Get prize ID to determine if this is add or update
            const prizeId = formData.get('prizeId');
            const isUpdate = prizeId && prizeId !== '';

            // Prepare request data
            const requestData = {
                name: formData.get('prizeName'),
                prizeType: formData.get('prizeType'),
                discountAmount: formData.get('discountAmount') ? parseFloat(formData.get('discountAmount')) : null,
                discountType: formData.get('discountType') ? parseInt(formData.get('discountType')) : 1,
                validityDays: formData.get('validityDays') ? parseInt(formData.get('validityDays')) : 30,
                probability: parseInt(formData.get('probability')),
                color: formData.get('prizeColor'),
                sortOrder: formData.get('sortOrder') ? parseInt(formData.get('sortOrder')) : 1,
                isActive: formData.has('isActive')
            };

            // Make API call
            const url = isUpdate ? `/GiftWheel/UpdatePrize/${prizeId}` : '/GiftWheel/AddPrize';
            const method = isUpdate ? 'PUT' : 'POST';

            const response = await fetch(url, {
                method: method,
                headers: {
                    'Content-Type': 'application/json',
                    'RequestVerificationToken': this.getAntiForgeryToken()
                },
                body: JSON.stringify(requestData)
            });

            const result = await response.json();

            if (result.success) {
                this.showNotification(result.message, 'success');

                // Close modal
                document.getElementById('prizeModal').classList.add('hidden');

                // Reload prizes list
                await this.loadPrizes();

                // Reload page to update UI
                setTimeout(() => {
                    window.location.reload();
                }, 1500);

            } else {
                this.showNotification(result.message, 'error');
            }

        } catch (error) {
            console.error('Error submitting prize form:', error);
            this.showNotification('Ödül kaydedilirken hata oluştu.', 'error');
        } finally {
            this.showLoading(false);
        }
    }
    
    openPrizeModal(prizeId = null) {
        const modal = document.getElementById('prizeModal');
        const title = document.getElementById('prizeModalTitle');
        
        if (prizeId) {
            title.innerHTML = '<i class="fas fa-edit mr-2 text-yellow-500"></i>Ödül Düzenle';
            this.loadPrizeForEdit(prizeId);
        } else {
            title.innerHTML = '<i class="fas fa-plus mr-2 text-green-500"></i>Ödül Ekle';
            document.getElementById('prizeForm').reset();
        }
        
        modal.classList.remove('hidden');
    }
    
    async loadPrizeForEdit(prizeId) {
        const prize = this.currentPrizes.find(p => p.id === prizeId);
        if (prize) {
            document.getElementById('prizeId').value = prize.id;
            document.getElementById('prizeName').value = prize.name;
            document.getElementById('prizeType').value = prize.prizeType;
            document.getElementById('discountAmount').value = prize.discountAmount || '';
            document.getElementById('discountType').value = prize.discountType || 1;
            document.getElementById('validityDays').value = prize.validityDays || 30;
            document.getElementById('probability').value = prize.probability;
            document.getElementById('probabilitySlider').value = prize.probability;
            document.getElementById('prizeColor').value = prize.color;
            document.getElementById('prizeColorText').value = prize.color;
            document.getElementById('sortOrder').value = prize.sortOrder;
            document.getElementById('isActive').checked = prize.isActive;
        }
    }
    
    // Settings Management
    async saveSettings(event) {
        event.preventDefault();

        try {
            this.showLoading(true);

            const form = event.target;
            const formData = new FormData(form);

            const settings = {
                wheelTitle: formData.get('wheelTitle'),
                wheelSubtitle: formData.get('wheelSubtitle'),
                buttonText: formData.get('buttonText'),
                winMessage: formData.get('winMessage'),
                loseMessage: formData.get('loseMessage'),
                maxSpinsPerDay: parseInt(formData.get('maxSpinsPerDay')),
                requirePhone: formData.has('requirePhone'),
                requireEmail: formData.has('requireEmail'),
                notificationTemplate: formData.get('notificationTemplate'),
                primaryColor: formData.get('primaryColor'),
                secondaryColor: formData.get('secondaryColor'),
                wheelSize: parseInt(formData.get('wheelSize')),
                showConfetti: document.getElementById('showConfettiToggle')?.dataset.enabled === 'true'
            };

            const response = await fetch('/GiftWheel/UpdateSettings', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'RequestVerificationToken': this.getAntiForgeryToken()
                },
                body: JSON.stringify(settings)
            });

            const result = await response.json();

            if (result.success) {
                this.showNotification(result.message, 'success');
            } else {
                this.showNotification(result.message, 'error');
            }

        } catch (error) {
            console.error('Error saving settings:', error);
            this.showNotification('Ayarlar kaydedilirken hata oluştu.', 'error');
        } finally {
            this.showLoading(false);
        }
    }
    
    // Statistics
    async loadStatistics() {
        try {
            const response = await fetch('/GiftWheel/GetStats');
            const result = await response.json();
            
            if (result.success) {
                this.updateStatisticsDisplay(result.data);
            }
            
        } catch (error) {
            console.error('Error loading statistics:', error);
        }
    }
    
    updateStatisticsDisplay(stats) {
        // Update statistics display
        document.getElementById('totalSpinsDisplay').textContent = stats.totalSpins;
        document.getElementById('totalVouchersDisplay').textContent = stats.totalVouchersCreated;
        document.getElementById('totalNotificationsDisplay').textContent = stats.totalNotificationsSent;
        document.getElementById('totalCostDisplay').textContent = new Intl.NumberFormat('tr-TR', {
            style: 'currency',
            currency: 'TRY'
        }).format(stats.totalCost);
    }
    
    async refreshStatistics() {
        await this.loadStatistics();
        this.showNotification('İstatistikler güncellendi.', 'success');
    }
    
    // Script Generation
    async loadEmbedScript() {
        try {
            const response = await fetch('/GiftWheel/GetEmbedScript');
            const script = await response.text();

            document.getElementById('jsCodeContent').textContent = script;

        } catch (error) {
            console.error('Error loading embed script:', error);
            document.getElementById('jsCodeContent').textContent = '// Script yüklenirken hata oluştu.';
        }
    }

    // Full Preview
    async openFullPreview() {
        try {
            // Get current wheel configuration
            const data = await this.loadWheelPreviewData();
            if (!data) {
                this.showNotification('Önizleme verileri yüklenemedi.', 'error');
                return;
            }

            // Open in new window/tab
            const previewWindow = window.open('', '_blank', 'width=800,height=600,scrollbars=yes,resizable=yes');

            if (!previewWindow) {
                this.showNotification('Pop-up engelleyici nedeniyle önizleme açılamadı.', 'error');
                return;
            }

            // Generate full preview HTML
            const previewHtml = this.generateFullPreviewHtml(data);
            previewWindow.document.write(previewHtml);
            previewWindow.document.close();

        } catch (error) {
            console.error('Error opening full preview:', error);
            this.showNotification('Tam ekran önizleme açılırken hata oluştu.', 'error');
        }
    }

    generateFullPreviewHtml(data) {
        return `
            <!DOCTYPE html>
            <html lang="tr">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>Hediye Çarkı - Tam Ekran Önizleme</title>
                <style>
                    body { margin: 0; padding: 20px; font-family: Arial, sans-serif; background: #f5f5f5; }
                    .preview-container { max-width: 600px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); }
                    .wheel-container { text-align: center; margin: 20px 0; }
                    .wheel { width: 300px; height: 300px; border: 5px solid #333; border-radius: 50%; margin: 0 auto; background: conic-gradient(${this.generateWheelGradient(data.prizes)}); }
                    .form-container { margin: 20px 0; }
                    .form-input { width: 100%; padding: 10px; margin: 5px 0; border: 1px solid #ddd; border-radius: 5px; }
                    .spin-button { width: 100%; padding: 15px; background: ${data.primaryColor || '#4f46e5'}; color: white; border: none; border-radius: 5px; font-size: 16px; cursor: pointer; }
                    .spin-button:hover { opacity: 0.9; }
                    .title { font-size: 24px; font-weight: bold; margin-bottom: 10px; }
                    .subtitle { color: #666; margin-bottom: 20px; }
                </style>
            </head>
            <body>
                <div class="preview-container">
                    <h1 class="title">${data.title || 'Çarkı Çevir, Hediyeni Kazan!'}</h1>
                    <p class="subtitle">${data.subtitle || 'Şansını dene ve harika hediyeler kazan'}</p>

                    <div class="wheel-container">
                        <div class="wheel"></div>
                    </div>

                    <div class="form-container">
                        <input type="text" class="form-input" placeholder="Adınız" required>
                        <input type="tel" class="form-input" placeholder="Telefon Numaranız" required>
                        ${data.requireEmail ? '<input type="email" class="form-input" placeholder="E-posta Adresiniz" required>' : ''}
                        <button class="spin-button">${data.buttonText || 'Çarkı Çevir'}</button>
                    </div>

                    <div style="margin-top: 20px; padding: 15px; background: #f0f9ff; border-radius: 5px; text-align: center;">
                        <small style="color: #0369a1;">Bu bir önizlemedir. Gerçek çark işlevselliği için kodu sitenize entegre edin.</small>
                    </div>
                </div>
            </body>
            </html>
        `;
    }

    generateWheelGradient(prizes) {
        if (!prizes || prizes.length === 0) return '#f3f4f6';

        const activePrizes = prizes.filter(p => p.isActive);
        if (activePrizes.length === 0) return '#f3f4f6';

        const segmentSize = 100 / activePrizes.length;
        let gradient = '';

        activePrizes.forEach((prize, index) => {
            const start = index * segmentSize;
            const end = (index + 1) * segmentSize;
            gradient += `${prize.color || '#6b7280'} ${start}% ${end}%`;
            if (index < activePrizes.length - 1) gradient += ', ';
        });

        return gradient;
    }

    // Settings Reset
    async resetSettings() {
        if (!confirm('Tüm ayarları varsayılan değerlere sıfırlamak istediğinizden emin misiniz?')) {
            return;
        }

        try {
            this.showLoading(true);

            // Reset form to default values
            const form = document.getElementById('wheelSettingsForm');
            if (form) {
                // Reset to default values
                document.getElementById('wheelTitle').value = 'Çarkı Çevir, Hediyeni Kazan!';
                document.getElementById('wheelSubtitle').value = 'Şansını dene ve harika hediyeler kazan';
                document.getElementById('buttonText').value = 'Çarkı Çevir';
                document.getElementById('winMessage').value = 'Tebrikler! {prize} kazandınız!';
                document.getElementById('loseMessage').value = 'Bu sefer olmadı, tekrar deneyin!';
                document.getElementById('maxSpinsPerDay').value = '1';
                document.getElementById('requirePhone').checked = true;
                document.getElementById('requireEmail').checked = false;
                document.getElementById('notificationTemplate').value = '🎉 Tebrikler {name}! {prize} kazandınız. Hemen alışverişe başlayın: {siteUrl}';
                document.getElementById('primaryColor').value = '#4f46e5';
                document.getElementById('secondaryColor').value = '#f3f4f6';
                document.getElementById('wheelSize').value = '300';

                // Reset toggle
                const confettiToggle = document.getElementById('showConfettiToggle');
                if (confettiToggle) {
                    confettiToggle.dataset.enabled = 'true';
                    confettiToggle.classList.remove('bg-gray-200');
                    confettiToggle.classList.add('bg-green-600');
                    const toggleSpan = confettiToggle.querySelector('span');
                    if (toggleSpan) {
                        toggleSpan.classList.remove('translate-x-1');
                        toggleSpan.classList.add('translate-x-5');
                    }
                }
            }

            this.showNotification('Ayarlar sıfırlandı. Kaydetmeyi unutmayın.', 'success');

        } catch (error) {
            console.error('Error resetting settings:', error);
            this.showNotification('Ayarlar sıfırlanırken hata oluştu.', 'error');
        } finally {
            this.showLoading(false);
        }
    }

    // Prize Actions Setup
    setupPrizeActions() {
        // Edit prize buttons
        document.querySelectorAll('.edit-prize-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const prizeId = parseInt(e.currentTarget.dataset.prizeId);
                this.openPrizeModal(prizeId);
            });
        });

        // Delete prize buttons
        document.querySelectorAll('.delete-prize-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const prizeId = parseInt(e.currentTarget.dataset.prizeId);
                this.deletePrize(prizeId);
            });
        });

        // Toggle prize status buttons
        document.querySelectorAll('.toggle-prize-status').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const prizeId = parseInt(e.currentTarget.dataset.prizeId);
                const isActive = e.currentTarget.dataset.active === 'true';
                this.togglePrizeStatus(prizeId, !isActive);
            });
        });

        // Enable all prizes
        document.getElementById('enableAllPrizes')?.addEventListener('click', () => {
            this.toggleAllPrizes(true);
        });

        // Disable all prizes
        document.getElementById('disableAllPrizes')?.addEventListener('click', () => {
            this.toggleAllPrizes(false);
        });
    }

    // Prize Management Actions
    async deletePrize(prizeId) {
        if (!confirm('Bu ödülü silmek istediğinizden emin misiniz?')) {
            return;
        }

        try {
            this.showLoading(true);

            const response = await fetch(`/GiftWheel/DeletePrize/${prizeId}`, {
                method: 'DELETE',
                headers: {
                    'RequestVerificationToken': this.getAntiForgeryToken()
                }
            });

            const result = await response.json();

            if (result.success) {
                this.showNotification(result.message, 'success');
                setTimeout(() => {
                    window.location.reload();
                }, 1500);
            } else {
                this.showNotification(result.message, 'error');
            }

        } catch (error) {
            console.error('Error deleting prize:', error);
            this.showNotification('Ödül silinirken hata oluştu.', 'error');
        } finally {
            this.showLoading(false);
        }
    }

    async togglePrizeStatus(prizeId, isActive) {
        try {
            const response = await fetch(`/GiftWheel/TogglePrizeStatus/${prizeId}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'RequestVerificationToken': this.getAntiForgeryToken()
                },
                body: JSON.stringify({ isActive })
            });

            const result = await response.json();

            if (result.success) {
                // Update UI immediately
                const toggleBtn = document.querySelector(`[data-prize-id="${prizeId}"]`);
                if (toggleBtn) {
                    toggleBtn.dataset.active = isActive.toString();
                    if (isActive) {
                        toggleBtn.classList.remove('bg-gray-200');
                        toggleBtn.classList.add('bg-green-600');
                        const span = toggleBtn.querySelector('span');
                        if (span) {
                            span.classList.remove('translate-x-1');
                            span.classList.add('translate-x-5');
                        }
                    } else {
                        toggleBtn.classList.remove('bg-green-600');
                        toggleBtn.classList.add('bg-gray-200');
                        const span = toggleBtn.querySelector('span');
                        if (span) {
                            span.classList.remove('translate-x-5');
                            span.classList.add('translate-x-1');
                        }
                    }
                }

                this.showNotification(result.message, 'success');
            } else {
                this.showNotification(result.message, 'error');
            }

        } catch (error) {
            console.error('Error toggling prize status:', error);
            this.showNotification('Ödül durumu değiştirilirken hata oluştu.', 'error');
        }
    }

    async toggleAllPrizes(isActive) {
        const action = isActive ? 'aktif' : 'pasif';
        if (!confirm(`Tüm ödülleri ${action} yapmak istediğinizden emin misiniz?`)) {
            return;
        }

        try {
            this.showLoading(true);

            const response = await fetch('/GiftWheel/ToggleAllPrizes', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'RequestVerificationToken': this.getAntiForgeryToken()
                },
                body: JSON.stringify({ isActive })
            });

            const result = await response.json();

            if (result.success) {
                this.showNotification(result.message, 'success');
                setTimeout(() => {
                    window.location.reload();
                }, 1500);
            } else {
                this.showNotification(result.message, 'error');
            }

        } catch (error) {
            console.error('Error toggling all prizes:', error);
            this.showNotification('Ödül durumları değiştirilirken hata oluştu.', 'error');
        } finally {
            this.showLoading(false);
        }
    }

    // Preview Modal
    async openPreviewModal() {
        console.log('Opening preview modal...');
        const modal = document.getElementById('previewModal');
        if (!modal) {
            console.error('Preview modal not found');
            this.showNotification('Önizleme modalı bulunamadı.', 'error');
            return;
        }

        modal.classList.remove('hidden');

        // Wait a bit for modal to be visible and scripts to load
        setTimeout(async () => {
            try {
                // Load preview data
                const data = await this.loadWheelPreviewData();
                console.log('Preview data loaded:', data);

                if (!data) {
                    console.error('No preview data available');
                    this.showNotification('Önizleme verileri yüklenemedi.', 'error');
                    return;
                }

                // Try multiple methods to ensure the preview is updated
                let updateSuccess = false;

                // Method 1: Direct function call
                if (window.updatePreview && typeof window.updatePreview === 'function') {
                    console.log('Calling updatePreview directly');
                    try {
                        window.updatePreview(data);
                        updateSuccess = true;
                    } catch (error) {
                        console.error('Error calling updatePreview directly:', error);
                    }
                }

                // Method 2: Custom event (fallback)
                if (!updateSuccess) {
                    console.log('Using fallback updatePreview event');
                    const updatePreviewEvent = new CustomEvent('updatePreview', { detail: data });
                    document.dispatchEvent(updatePreviewEvent);
                }

                // Method 3: Force update after a delay (last resort)
                setTimeout(() => {
                    if (window.updatePreview && typeof window.updatePreview === 'function') {
                        console.log('Force calling updatePreview after delay');
                        window.updatePreview(data);
                    }
                }, 500);

            } catch (error) {
                console.error('Error in openPreviewModal:', error);
                this.showNotification('Önizleme açılırken hata oluştu.', 'error');
            }
        }, 300);
    }
    
    async loadWheelPreviewData() {
        try {
            console.log('Loading wheel preview data...');
            const response = await fetch('/GiftWheel/GetWheelConfig');

            if (!response.ok) {
                console.error('Response not ok:', response.status, response.statusText);
                return null;
            }

            const result = await response.json();
            console.log('Raw response:', result);

            if (result.success && result.data) {
                console.log('Preview data loaded successfully:', result.data);
                console.log('Prizes count:', result.data.prizes ? result.data.prizes.length : 0);
                return result.data;
            } else {
                console.error('Response indicates failure:', result);
                return null;
            }

        } catch (error) {
            console.error('Error loading preview data:', error);
        }

        return null;
    }
    
    // Utility Methods
    getAntiForgeryToken() {
        return document.querySelector('input[name="__RequestVerificationToken"]')?.value || '';
    }
    
    showLoading(show) {
        this.isLoading = show;

        // Show/hide loading overlay
        let loadingOverlay = document.getElementById('loadingOverlay');
        if (!loadingOverlay) {
            loadingOverlay = document.createElement('div');
            loadingOverlay.id = 'loadingOverlay';
            loadingOverlay.className = 'fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50';
            loadingOverlay.innerHTML = `
                <div class="bg-white rounded-lg p-6 flex items-center space-x-3">
                    <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-indigo-600"></div>
                    <span class="text-gray-700">İşlem yapılıyor...</span>
                </div>
            `;
            document.body.appendChild(loadingOverlay);
        }

        if (show) {
            loadingOverlay.classList.remove('hidden');
        } else {
            loadingOverlay.classList.add('hidden');
        }
    }
    
    showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg max-w-sm ${
            type === 'success' ? 'bg-green-500 text-white' :
            type === 'error' ? 'bg-red-500 text-white' :
            type === 'warning' ? 'bg-yellow-500 text-white' :
            'bg-blue-500 text-white'
        }`;
        
        notification.innerHTML = `
            <div class="flex items-center">
                <i class="fas ${
                    type === 'success' ? 'fa-check-circle' :
                    type === 'error' ? 'fa-exclamation-circle' :
                    type === 'warning' ? 'fa-exclamation-triangle' :
                    'fa-info-circle'
                } mr-2"></i>
                <span>${message}</span>
                <button class="ml-4 text-white hover:text-gray-200" onclick="this.parentElement.parentElement.remove()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;
        
        document.body.appendChild(notification);
        
        // Auto remove after 5 seconds
        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, 5000);
    }
}

// Global functions for use in partials
window.addPrizeFromTemplate = function(template) {
    const admin = window.giftWheelAdmin;
    if (admin) {
        admin.openPrizeModal();

        // Fill form with template data
        setTimeout(() => {
            document.getElementById('prizeName').value = template.name;
            document.getElementById('prizeType').value = template.prizeType;
            document.getElementById('discountAmount').value = template.discountAmount || '';
            document.getElementById('discountType').value = template.discountType || 1;
            document.getElementById('probability').value = template.probability;
            document.getElementById('probabilitySlider').value = template.probability;
            document.getElementById('prizeColor').value = template.color;
            document.getElementById('prizeColorText').value = template.color;

            // Update color preview
            document.getElementById('prizePreviewColor').style.backgroundColor = template.color;

            // Trigger change events
            document.getElementById('prizeType').dispatchEvent(new Event('change'));
            document.getElementById('probability').dispatchEvent(new Event('input'));
            document.getElementById('prizeName').dispatchEvent(new Event('input'));
        }, 100);
    }
};

// Global function for prize form submission
window.submitPrizeForm = async function(formData) {
    const admin = window.giftWheelAdmin;
    if (admin) {
        await admin.submitPrizeForm(formData);
    }
};

window.refreshStatistics = function() {
    const admin = window.giftWheelAdmin;
    if (admin) {
        admin.refreshStatistics();
    }
};

window.loadEmbedScript = function() {
    const admin = window.giftWheelAdmin;
    if (admin) {
        admin.loadEmbedScript();
    }
};

window.loadWheelPreviewData = function() {
    const admin = window.giftWheelAdmin;
    if (admin) {
        return admin.loadWheelPreviewData();
    }
    return Promise.resolve(null);
};

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    window.giftWheelAdmin = new GiftWheelAdmin();
});
