using System.ComponentModel.DataAnnotations.Schema;

namespace PushDashboard.Models;

public class UserSession
{
    public int Id { get; set; }
    
    public string UserId { get; set; } = string.Empty;
    
    public string SessionId { get; set; } = string.Empty;
    
    public string? DeviceInfo { get; set; }
    
    public string? Browser { get; set; }
    
    public string? OperatingSystem { get; set; }
    
    public string? IpAddress { get; set; }
    
    public string? Location { get; set; }
    
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    
    public DateTime LastActivityAt { get; set; } = DateTime.UtcNow;
    
    public DateTime? EndedAt { get; set; }
    
    public bool IsActive { get; set; } = true;
    
    public string? DeviceType { get; set; } // Desktop, Mobile, Tablet
    
    public bool IsCurrent { get; set; } = false;
    
    // Navigation property
    public virtual ApplicationUser User { get; set; } = null!;
    
    // Helper properties
    [NotMapped]
    public string FormattedDeviceInfo => $"{Browser} - {OperatingSystem}";
    
    [NotMapped]
    public string FormattedLocation => !string.IsNullOrEmpty(Location) ? Location : "Bilinmeyen Konum";
    
    [NotMapped]
    public string FormattedLastActivity
    {
        get
        {
            var timeSpan = DateTime.UtcNow - LastActivityAt;
            
            if (timeSpan.TotalMinutes < 1)
                return "Şu an aktif";
            else if (timeSpan.TotalMinutes < 60)
                return $"{(int)timeSpan.TotalMinutes} dakika önce";
            else if (timeSpan.TotalHours < 24)
                return $"{(int)timeSpan.TotalHours} saat önce";
            else
                return $"{(int)timeSpan.TotalDays} gün önce";
        }
    }
    
    [NotMapped]
    public string StatusBadge => IsCurrent ? "Mevcut Oturum" : (IsActive ? "Aktif" : "Sonlandırıldı");
    
    [NotMapped]
    public string StatusBadgeClass => IsCurrent ? "text-green-600" : (IsActive ? "text-blue-600" : "text-gray-500");
}
