using Microsoft.AspNetCore.Identity;
using System.ComponentModel.DataAnnotations.Schema;

namespace PushDashboard.Models;

public class ApplicationUser : IdentityUser
{
    public string? FirstName { get; set; }

    public string? LastName { get; set; }

    // Foreign key for Company
    public Guid? CompanyId { get; set; }

    // Navigation property for Company
    public virtual Company? Company { get; set; }

    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    public DateTime? LastLoginAt { get; set; }

    // Two Factor Authentication
    public string? TwoFactorSecretKey { get; set; }

    // Credit Balance
    public decimal CreditBalance { get; set; } = 0.00m;

    // Helper property to get full name
    [NotMapped]
    public string FullName => $"{FirstName} {LastName}".Trim();

    // Navigation property for notification preferences
    public virtual NotificationPreferences? NotificationPreferences { get; set; }

    // Navigation property for user sessions
    public virtual ICollection<UserSession> UserSessions { get; set; } = new List<UserSession>();
}
