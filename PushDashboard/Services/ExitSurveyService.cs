using Microsoft.EntityFrameworkCore;
using PushDashboard.Data;
using PushDashboard.Models;
using PushDashboard.Models.ViewModels;
using System.Text.Json;

namespace PushDashboard.Services;

public interface IExitSurveyService
{
    Task<bool> HasExitSurveyModuleAsync(Guid companyId);
    Task<ExitSurvey?> GetExitSurveyAsync(Guid companyId);
    Task<bool> SaveExitSurveyAsync(ExitSurvey exitSurvey);
    Task<ExitSurvey> CreateDefaultSettingsAsync(Guid companyId, string userId);
    Task<ExitSurveyScriptConfig> GetScriptConfigAsync(Guid companyId);
    Task<List<ExitSurveyQuestion>> GetQuestionsAsync(int exitSurveyId);
    Task<bool> SaveQuestionAsync(ExitSurveyQuestion question);
    Task<bool> DeleteQuestionAsync(int questionId);
    Task<bool> ReorderQuestionsAsync(List<int> questionIds);
    Task<bool> SubmitResponseAsync(SubmitExitSurveyResponseViewModel model, string? ipAddress, string? userAgent);
    Task<ExitSurveyStatsViewModel> GetStatsAsync(Guid companyId);
    Task<List<ExitSurveyResponseViewModel>> GetResponsesAsync(Guid companyId, int page = 1, int pageSize = 50);
    Task<object?> GetQuestionAsync(int questionId, Guid companyId);
    Task<AllResponsesViewModel> GetAllResponsesViewModelAsync(Guid companyId, int page = 1, int pageSize = 10);
    Task<bool> UpdateQuestionAsync(ExitSurveyQuestionViewModel model, Guid companyId);
}

public class ExitSurveyService : IExitSurveyService
{
    private readonly ApplicationDbContext _context;
    private readonly ILogger<ExitSurveyService> _logger;

    public ExitSurveyService(ApplicationDbContext context, ILogger<ExitSurveyService> logger)
    {
        _context = context;
        _logger = logger;
    }

    public async Task<bool> HasExitSurveyModuleAsync(Guid companyId)
    {
        try
        {
            var hasModule = await _context.CompanyModules
                .AnyAsync(cm => cm.CompanyId == companyId &&
                               cm.Module.Name == "Çıkış Anketi" &&
                               cm.IsActive);

            return hasModule;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking exit survey module for company {CompanyId}", companyId);
            return false;
        }
    }

    public async Task<ExitSurvey?> GetExitSurveyAsync(Guid companyId)
    {
        try
        {
            return await _context.ExitSurveys
                .Include(e => e.Questions.Where(q => q.IsActive))
                .FirstOrDefaultAsync(e => e.CompanyId == companyId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting exit survey for company {CompanyId}", companyId);
            return null;
        }
    }

    public async Task<bool> SaveExitSurveyAsync(ExitSurvey exitSurvey)
    {
        try
        {
            var existing = await _context.ExitSurveys
                .FirstOrDefaultAsync(e => e.CompanyId == exitSurvey.CompanyId);

            if (existing != null)
            {
                // Update existing
                existing.Title = exitSurvey.Title;
                existing.Description = exitSurvey.Description;
                existing.SubmitButtonText = exitSurvey.SubmitButtonText;
                existing.CancelButtonText = exitSurvey.CancelButtonText;
                existing.ThankYouMessage = exitSurvey.ThankYouMessage;
                existing.BackgroundColor = exitSurvey.BackgroundColor;
                existing.TextColor = exitSurvey.TextColor;
                existing.SubmitButtonColor = exitSurvey.SubmitButtonColor;
                existing.CancelButtonColor = exitSurvey.CancelButtonColor;
                existing.BorderRadius = exitSurvey.BorderRadius;
                existing.EnableAnimation = exitSurvey.EnableAnimation;
                existing.ShowOnPageExit = exitSurvey.ShowOnPageExit;
                existing.ShowOnTabClose = exitSurvey.ShowOnTabClose;
                existing.DelayBeforeShow = exitSurvey.DelayBeforeShow;
                existing.ShowFrequencyDays = exitSurvey.ShowFrequencyDays;
                existing.ShowOnMobile = exitSurvey.ShowOnMobile;
                existing.ShowOnDesktop = exitSurvey.ShowOnDesktop;
                existing.IsActive = exitSurvey.IsActive;
                existing.UpdatedAt = DateTime.UtcNow;
                existing.UpdatedByUserId = exitSurvey.UpdatedByUserId;

                _context.ExitSurveys.Update(existing);
            }
            else
            {
                // Create new
                _context.ExitSurveys.Add(exitSurvey);
            }

            await _context.SaveChangesAsync();
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error saving exit survey for company {CompanyId}", exitSurvey.CompanyId);
            return false;
        }
    }

    public async Task<ExitSurvey> CreateDefaultSettingsAsync(Guid companyId, string userId)
    {
        var defaultSettings = new ExitSurvey
        {
            CompanyId = companyId,
            Title = "Görüşünüz Bizim İçin Önemli",
            Description = "Sitemizi terk etmeden önce deneyiminizi bizimle paylaşır mısınız? Bu sadece birkaç saniye sürecek.",
            SubmitButtonText = "Gönder",
            CancelButtonText = "Kapat",
            ThankYouMessage = "Geri bildiriminiz için teşekkür ederiz!",
            BackgroundColor = "#ffffff",
            TextColor = "#333333",
            SubmitButtonColor = "#4CAF50",
            CancelButtonColor = "#6c757d",
            BorderRadius = "8px",
            EnableAnimation = true,
            ShowOnPageExit = true,
            ShowOnTabClose = true,
            DelayBeforeShow = 0,
            ShowFrequencyDays = 30,
            ShowOnMobile = true,
            ShowOnDesktop = true,
            IsActive = true,
            UpdatedByUserId = userId
        };

        _context.ExitSurveys.Add(defaultSettings);
        await _context.SaveChangesAsync();

        return defaultSettings;
    }

    public async Task<ExitSurveyScriptConfig> GetScriptConfigAsync(Guid companyId)
    {
        try
        {
            // Önce modül kontrolü yap
            var hasModule = await HasExitSurveyModuleAsync(companyId);
            if (!hasModule)
            {
                return new ExitSurveyScriptConfig { CompanyId = companyId, IsActive = false };
            }

            var exitSurvey = await GetExitSurveyAsync(companyId);
            if (exitSurvey == null || !exitSurvey.IsActive)
            {
                return new ExitSurveyScriptConfig { CompanyId = companyId, IsActive = false };
            }

            return new ExitSurveyScriptConfig
            {
                CompanyId = companyId,
                IsActive = exitSurvey.IsActive,
                Title = exitSurvey.Title,
                Description = exitSurvey.Description,
                SubmitButtonText = exitSurvey.SubmitButtonText,
                CancelButtonText = exitSurvey.CancelButtonText,
                ThankYouMessage = exitSurvey.ThankYouMessage,
                BackgroundColor = exitSurvey.BackgroundColor,
                TextColor = exitSurvey.TextColor,
                SubmitButtonColor = exitSurvey.SubmitButtonColor,
                CancelButtonColor = exitSurvey.CancelButtonColor,
                BorderRadius = exitSurvey.BorderRadius,
                EnableAnimation = exitSurvey.EnableAnimation,
                ShowOnPageExit = exitSurvey.ShowOnPageExit,
                ShowOnTabClose = exitSurvey.ShowOnTabClose,
                DelayBeforeShow = exitSurvey.DelayBeforeShow,
                ShowFrequencyDays = exitSurvey.ShowFrequencyDays,
                ShowOnMobile = exitSurvey.ShowOnMobile,
                ShowOnDesktop = exitSurvey.ShowOnDesktop,
                Questions = exitSurvey.Questions
                    .Where(q => q.IsActive)
                    .OrderBy(q => q.SortOrder)
                    .Select(q => new ExitSurveyQuestionConfig
                    {
                        Id = q.Id,
                        QuestionText = q.QuestionText,
                        QuestionType = q.QuestionType,
                        IsRequired = q.IsRequired,
                        Options = q.Options
                    }).ToList()
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting script config for company {CompanyId}", companyId);
            return new ExitSurveyScriptConfig { CompanyId = companyId, IsActive = false };
        }
    }

    public async Task<List<ExitSurveyQuestion>> GetQuestionsAsync(int exitSurveyId)
    {
        try
        {
            return await _context.ExitSurveyQuestions
                .Where(q => q.ExitSurveyId == exitSurveyId && q.IsActive)
                .OrderBy(q => q.SortOrder)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting questions for exit survey {ExitSurveyId}", exitSurveyId);
            return new List<ExitSurveyQuestion>();
        }
    }

    public async Task<bool> SaveQuestionAsync(ExitSurveyQuestion question)
    {
        try
        {
            if (question.Id == 0)
            {
                // New question
                question.CreatedAt = DateTime.UtcNow;
                _context.ExitSurveyQuestions.Add(question);
            }
            else
            {
                // Update existing
                _context.ExitSurveyQuestions.Update(question);
            }

            await _context.SaveChangesAsync();
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error saving question {QuestionId}", question.Id);
            return false;
        }
    }

    public async Task<bool> DeleteQuestionAsync(int questionId)
    {
        try
        {
            var question = await _context.ExitSurveyQuestions.FindAsync(questionId);
            if (question != null)
            {
                question.IsActive = false;
                _context.ExitSurveyQuestions.Update(question);
                await _context.SaveChangesAsync();
                return true;
            }
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting question {QuestionId}", questionId);
            return false;
        }
    }

    public async Task<bool> ReorderQuestionsAsync(List<int> questionIds)
    {
        try
        {
            for (int i = 0; i < questionIds.Count; i++)
            {
                var question = await _context.ExitSurveyQuestions.FindAsync(questionIds[i]);
                if (question != null)
                {
                    question.SortOrder = i + 1;
                    _context.ExitSurveyQuestions.Update(question);
                }
            }

            await _context.SaveChangesAsync();
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error reordering questions");
            return false;
        }
    }

    public async Task<bool> SubmitResponseAsync(SubmitExitSurveyResponseViewModel model, string? ipAddress, string? userAgent)
    {
        try
        {
            // Modül kontrolü
            var hasModule = await HasExitSurveyModuleAsync(model.CompanyId);
            if (!hasModule)
            {
                return false;
            }

            var exitSurvey = await GetExitSurveyAsync(model.CompanyId);
            if (exitSurvey == null || !exitSurvey.IsActive)
            {
                return false;
            }

            // Cevapları kaydet
            foreach (var answer in model.Answers)
            {
                var response = new ExitSurveyResponse
                {
                    ExitSurveyId = exitSurvey.Id,
                    QuestionId = answer.QuestionId,
                    ResponseText = answer.ResponseText,
                    RatingValue = answer.RatingValue,
                    IpAddress = ipAddress,
                    UserAgent = userAgent,
                    SessionId = model.SessionId,
                    CreatedAt = DateTime.UtcNow
                };

                _context.ExitSurveyResponses.Add(response);
            }

            await _context.SaveChangesAsync();
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error submitting response for company {CompanyId}", model.CompanyId);
            return false;
        }
    }

    public async Task<ExitSurveyStatsViewModel> GetStatsAsync(Guid companyId)
    {
        try
        {
            var exitSurvey = await GetExitSurveyAsync(companyId);
            if (exitSurvey == null)
            {
                return new ExitSurveyStatsViewModel();
            }

            var now = DateTime.UtcNow;
            var startOfMonth = new DateTime(now.Year, now.Month, 1);
            var startOfWeek = now.AddDays(-(int)now.DayOfWeek);

            var stats = new ExitSurveyStatsViewModel
            {
                TotalResponses = await _context.ExitSurveyResponses
                    .CountAsync(r => r.ExitSurveyId == exitSurvey.Id),
                TotalQuestions = exitSurvey.Questions.Count(q => q.IsActive),
                ResponsesThisMonth = await _context.ExitSurveyResponses
                    .CountAsync(r => r.ExitSurveyId == exitSurvey.Id && r.CreatedAt >= startOfMonth),
                ResponsesThisWeek = await _context.ExitSurveyResponses
                    .CountAsync(r => r.ExitSurveyId == exitSurvey.Id && r.CreatedAt >= startOfWeek)
            };

            // Ortalama rating hesapla
            var ratingResponses = await _context.ExitSurveyResponses
                .Where(r => r.ExitSurveyId == exitSurvey.Id && r.RatingValue.HasValue)
                .Select(r => r.RatingValue!.Value)
                .ToListAsync();

            if (ratingResponses.Any())
            {
                stats.AverageRating = ratingResponses.Average();
            }

            // Soru bazlı istatistikleri hesapla
            stats.QuestionStats = await CalculateQuestionStatsAsync(exitSurvey.Id);

            return stats;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting stats for company {CompanyId}", companyId);
            return new ExitSurveyStatsViewModel();
        }
    }

    public async Task<List<ExitSurveyResponseViewModel>> GetResponsesAsync(Guid companyId, int page = 1, int pageSize = 50)
    {
        try
        {
            var exitSurvey = await GetExitSurveyAsync(companyId);
            if (exitSurvey == null)
            {
                return new List<ExitSurveyResponseViewModel>();
            }

            var responses = await _context.ExitSurveyResponses
                .Include(r => r.Question)
                .Where(r => r.ExitSurveyId == exitSurvey.Id)
                .OrderByDescending(r => r.CreatedAt)
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .Select(r => new ExitSurveyResponseViewModel
                {
                    Id = r.Id,
                    QuestionText = r.Question.QuestionText,
                    QuestionType = r.Question.QuestionType,
                    ResponseText = r.ResponseText,
                    RatingValue = r.RatingValue,
                    IpAddress = r.IpAddress,
                    CreatedAt = r.CreatedAt
                })
                .ToListAsync();

            return responses;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting responses for company {CompanyId}", companyId);
            return new List<ExitSurveyResponseViewModel>();
        }
    }

    public async Task<object?> GetQuestionAsync(int questionId, Guid companyId)
    {
        try
        {
            var question = await _context.ExitSurveyQuestions
                .Where(q => q.Id == questionId)
                .Where(q => _context.ExitSurveys.Any(es => es.Id == q.ExitSurveyId && es.CompanyId == companyId))
                .Select(q => new
                {
                    Id = q.Id,
                    QuestionText = q.QuestionText,
                    QuestionType = q.QuestionType,
                    IsRequired = q.IsRequired,
                    Options = q.Options,
                    SortOrder = q.SortOrder
                })
                .FirstOrDefaultAsync();

            return question;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting question {QuestionId} for company {CompanyId}", questionId, companyId);
            return null;
        }
    }

    public async Task<AllResponsesViewModel> GetAllResponsesViewModelAsync(Guid companyId, int page = 1, int pageSize = 10)
    {
        try
        {
            var exitSurvey = await GetExitSurveyAsync(companyId);
            if (exitSurvey == null)
            {
                exitSurvey = await CreateDefaultSettingsAsync(companyId, "system");
            }

            var questions = await _context.ExitSurveyQuestions
                .Where(q => q.ExitSurveyId == exitSurvey.Id && q.IsActive)
                .OrderBy(q => q.SortOrder)
                .ToListAsync();

            // Önce tüm session'ları al (sayfalama için)
            var allSessionIds = await _context.ExitSurveyResponses
                .Include(r => r.Question)
                .Where(r => r.Question.ExitSurveyId == exitSurvey.Id)
                .Select(r => r.SessionId)
                .Distinct()
                .ToListAsync();

            var totalSessions = allSessionIds.Count;
            var totalPages = (int)Math.Ceiling((double)totalSessions / pageSize);

            // Sayfalama uygula
            var pagedSessionIds = allSessionIds
                .OrderByDescending(s => s) // Session ID'ye göre sırala (yeni olanlar üstte)
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .ToList();

            // Sadece sayfalanmış session'ların response'larını al
            var responses = await _context.ExitSurveyResponses
                .Include(r => r.Question)
                .Where(r => r.Question.ExitSurveyId == exitSurvey.Id && pagedSessionIds.Contains(r.SessionId))
                .OrderByDescending(r => r.CreatedAt)
                .ToListAsync();

            // Session'lara göre grupla
            var sessionGroups = responses
                .GroupBy(r => r.SessionId)
                .Select(g => new ResponseSessionGroup
                {
                    SessionId = g.Key,
                    CreatedAt = g.Min(r => r.CreatedAt),
                    IpAddress = g.First().IpAddress,
                    UserAgent = g.First().UserAgent,
                    Responses = g.ToList()
                })
                .OrderByDescending(g => g.CreatedAt)
                .ToList();

            // Toplam response sayısını al
            var totalResponses = await _context.ExitSurveyResponses
                .Include(r => r.Question)
                .Where(r => r.Question.ExitSurveyId == exitSurvey.Id)
                .CountAsync();

            return new AllResponsesViewModel
            {
                Questions = questions,
                SessionGroups = sessionGroups,
                TotalSessions = totalSessions,
                TotalResponses = totalResponses,
                CurrentPage = page,
                PageSize = pageSize,
                TotalPages = totalPages
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting all responses for company {CompanyId}", companyId);
            return new AllResponsesViewModel
            {
                Questions = new List<ExitSurveyQuestion>(),
                SessionGroups = new List<ResponseSessionGroup>(),
                TotalSessions = 0,
                TotalResponses = 0
            };
        }
    }

    public async Task<bool> UpdateQuestionAsync(ExitSurveyQuestionViewModel model, Guid companyId)
    {
        try
        {
            var question = await _context.ExitSurveyQuestions
                .Include(q => q.ExitSurvey)
                .FirstOrDefaultAsync(q => q.Id == model.Id && q.ExitSurvey.CompanyId == companyId);

            if (question == null)
                return false;

            question.QuestionText = model.QuestionText;
            question.QuestionType = model.QuestionType;
            question.IsRequired = model.IsRequired;
            question.Options = model.Options;

            await _context.SaveChangesAsync();
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating question {QuestionId} for company {CompanyId}", model.Id, companyId);
            return false;
        }
    }

    private async Task<List<QuestionStatsViewModel>> CalculateQuestionStatsAsync(int exitSurveyId)
    {
        var questions = await _context.ExitSurveyQuestions
            .Where(q => q.ExitSurveyId == exitSurveyId && q.IsActive)
            .OrderBy(q => q.SortOrder)
            .ToListAsync();

        var questionStats = new List<QuestionStatsViewModel>();

        foreach (var question in questions)
        {
            var responses = await _context.ExitSurveyResponses
                .Where(r => r.QuestionId == question.Id)
                .ToListAsync();

            var questionStat = new QuestionStatsViewModel
            {
                QuestionId = question.Id,
                QuestionText = question.QuestionText,
                QuestionType = question.QuestionType,
                ResponseCount = responses.Count
            };

            switch (question.QuestionType)
            {
                case ExitSurveyQuestionType.Text:
                    // Metin cevapları
                    questionStat.TextResponses = responses
                        .Where(r => !string.IsNullOrEmpty(r.ResponseText))
                        .Select(r => r.ResponseText)
                        .ToList();
                    break;

                case ExitSurveyQuestionType.MultipleChoice:
                    // Çoktan seçmeli cevaplar
                    questionStat.OptionStats = CalculateOptionStats(responses, question.Options);
                    break;

                case ExitSurveyQuestionType.YesNo:
                    // Evet/Hayır cevapları
                    var yesNoOptions = new List<string> { "Evet", "Hayır" };
                    questionStat.OptionStats = CalculateOptionStats(responses, yesNoOptions);
                    break;

                case ExitSurveyQuestionType.Rating:
                    // Puanlama cevapları
                    var ratingResponses = responses
                        .Where(r => r.RatingValue.HasValue)
                        .Select(r => r.RatingValue.Value)
                        .ToList();

                    if (ratingResponses.Any())
                    {
                        questionStat.AverageRating = ratingResponses.Average();

                        // Rating dağılımını da göster
                        var ratingOptions = new List<string> { "1", "2", "3", "4", "5" };
                        var ratingStats = new List<OptionStatsViewModel>();

                        foreach (var rating in ratingOptions)
                        {
                            var count = ratingResponses.Count(r => r.ToString() == rating);
                            var percentage = responses.Count > 0 ? (double)count / responses.Count * 100 : 0;

                            ratingStats.Add(new OptionStatsViewModel
                            {
                                OptionText = $"{rating} Yıldız",
                                Count = count,
                                Percentage = percentage
                            });
                        }

                        questionStat.OptionStats = ratingStats;
                    }
                    break;
            }

            questionStats.Add(questionStat);
        }

        return questionStats;
    }

    private List<OptionStatsViewModel> CalculateOptionStats(List<ExitSurveyResponse> responses, List<string> options)
    {
        var optionStats = new List<OptionStatsViewModel>();
        var totalResponses = responses.Count;

        foreach (var option in options)
        {
            var count = responses.Count(r => r.ResponseText == option);
            var percentage = totalResponses > 0 ? (double)count / totalResponses * 100 : 0;

            optionStats.Add(new OptionStatsViewModel
            {
                OptionText = option,
                Count = count,
                Percentage = percentage
            });
        }

        return optionStats;
    }
}
