using Microsoft.EntityFrameworkCore;
using PushDashboard.Data;
using PushDashboard.ViewModels;
using PushDashboard.Models;

namespace PushDashboard.Services;

public class DashboardService : IDashboardService
{
    private readonly ApplicationDbContext _context;
    private readonly IModuleUsageService _moduleUsageService;
    private readonly ILogger<DashboardService> _logger;

    public DashboardService(
        ApplicationDbContext context,
        IModuleUsageService moduleUsageService,
        ILogger<DashboardService> logger)
    {
        _context = context;
        _moduleUsageService = moduleUsageService;
        _logger = logger;
    }

    public async Task<DashboardIndexViewModel> GetDashboardDataAsync(Guid companyId)
    {
        try
        {
            var stats = await GetDashboardStatsAsync(companyId);
            var moduleUsage = await GetModuleUsageOverviewAsync(companyId);
            var integrationStatus = await GetIntegrationStatusAsync(companyId);
            var recentActivities = await GetRecentActivitiesAsync(companyId);
            var notificationMetrics = await GetNotificationMetricsAsync(companyId);
            var charts = await GetDashboardChartsAsync(companyId);
            var quickActions = await GetQuickActionsAsync(companyId);

            return new DashboardIndexViewModel
            {
                Stats = stats,
                ModuleUsage = moduleUsage,
                IntegrationStatus = integrationStatus,
                RecentActivities = recentActivities,
                NotificationMetrics = notificationMetrics,
                Charts = charts,
                QuickActions = quickActions
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting dashboard data for company {CompanyId}", companyId);
            return new DashboardIndexViewModel();
        }
    }

    public async Task<DashboardStatsViewModel> GetDashboardStatsAsync(Guid companyId)
    {
        var company = await _context.Companies.FindAsync(companyId);
        var activeModules = await _context.CompanyModules
            .Where(cm => cm.CompanyId == companyId && cm.IsActive)
            .CountAsync();

        var totalCustomers = await _context.Customers
            .Where(c => c.CompanyId == companyId)
            .CountAsync();

        var today = DateTime.UtcNow.Date;
        var startOfMonth = new DateTime(today.Year, today.Month, 1);

        var notificationsToday = await _context.ModuleUsageLogs
            .Where(mul => mul.CompanyId == companyId && 
                         mul.CreatedAt.Date == today &&
                         mul.UsageType.Contains("notification"))
            .CountAsync();

        var notificationsThisMonth = await _context.ModuleUsageLogs
            .Where(mul => mul.CompanyId == companyId && 
                         mul.CreatedAt >= startOfMonth &&
                         mul.UsageType.Contains("notification"))
            .CountAsync();

        var monthlySpending = await _context.ModuleUsageLogs
            .Where(mul => mul.CompanyId == companyId && mul.CreatedAt >= startOfMonth)
            .SumAsync(mul => mul.Cost);

        var activeIntegrations = await _context.CompanyIntegrations
            .Where(ci => ci.CompanyId == companyId && ci.IsActive)
            .CountAsync();

        // Calculate average delivery rate
        var deliveryLogs = await _context.ModuleUsageLogs
            .Where(mul => mul.CompanyId == companyId && 
                         mul.CreatedAt >= startOfMonth &&
                         true)
            .ToListAsync();

        double averageDeliveryRate = 0;
        if (deliveryLogs.Any())
        {
            // This would need to be implemented based on how delivery status is stored
            averageDeliveryRate = 85.5; // Placeholder
        }

        var lastActivity = await _context.ModuleUsageLogs
            .Where(mul => mul.CompanyId == companyId)
            .OrderByDescending(mul => mul.CreatedAt)
            .Select(mul => mul.CreatedAt)
            .FirstOrDefaultAsync();

        return new DashboardStatsViewModel
        {
            ActiveModules = activeModules,
            CreditBalance = company?.CreditBalance ?? 0,
            TotalCustomers = totalCustomers,
            NotificationsSentToday = notificationsToday,
            NotificationsSentThisMonth = notificationsThisMonth,
            MonthlySpending = monthlySpending,
            ActiveIntegrations = activeIntegrations,
            AverageDeliveryRate = averageDeliveryRate,
            LastActivity = lastActivity
        };
    }

    public async Task<List<ModuleUsageOverviewViewModel>> GetModuleUsageOverviewAsync(Guid companyId)
    {
        var startOfMonth = new DateTime(DateTime.UtcNow.Year, DateTime.UtcNow.Month, 1);

        var companyModules = await _context.CompanyModules
            .Include(cm => cm.Module)
            .Where(cm => cm.CompanyId == companyId)
            .ToListAsync();

        var moduleUsageStats = await _context.ModuleUsageLogs
            .Where(mul => mul.CompanyId == companyId && mul.CreatedAt >= startOfMonth)
            .GroupBy(mul => mul.ModuleId)
            .Select(g => new
            {
                ModuleId = g.Key,
                UsageCount = g.Count(),
                TotalCost = g.Sum(mul => mul.Cost),
                LastUsed = g.Max(mul => mul.CreatedAt)
            })
            .ToListAsync();

        var result = new List<ModuleUsageOverviewViewModel>();

        foreach (var companyModule in companyModules)
        {
            var stats = moduleUsageStats.FirstOrDefault(s => s.ModuleId == companyModule.ModuleId);
            
            result.Add(new ModuleUsageOverviewViewModel
            {
                ModuleId = companyModule.ModuleId,
                ModuleName = companyModule.Module.Name,
                ModuleIcon = companyModule.Module.IconClass,
                ModuleColor = companyModule.Module.BackgroundColor,
                IsActive = companyModule.IsActive,
                LastUsed = stats?.LastUsed,
                UsageCountThisMonth = stats?.UsageCount ?? 0,
                CostThisMonth = stats?.TotalCost ?? 0,
                SuccessRate = 92.5 // Placeholder - would need to calculate from actual delivery data
            });
        }

        return result.OrderByDescending(m => m.IsActive).ThenBy(m => m.ModuleName).ToList();
    }

    public async Task<List<IntegrationStatusViewModel>> GetIntegrationStatusAsync(Guid companyId)
    {
        var companyIntegrations = await _context.CompanyIntegrations
            .Include(ci => ci.Integration)
            .Where(ci => ci.CompanyId == companyId)
            .ToListAsync();

        var result = new List<IntegrationStatusViewModel>();

        foreach (var integration in companyIntegrations)
        {
            // Get recent sync logs for this integration
            var recentSync = await _context.SyncLogs
                .Where(sl => sl.CompanyId == companyId && 
                           sl.SyncType == integration.Integration.Name)
                .OrderByDescending(sl => sl.SyncStartTime)
                .FirstOrDefaultAsync();

            var today = DateTime.UtcNow.Date;
            var todayLogs = await _context.SyncLogs
                .Where(sl => sl.CompanyId == companyId && 
                           sl.SyncType == integration.Integration.Name &&
                           sl.SyncStartTime.Date == today)
                .ToListAsync();

            result.Add(new IntegrationStatusViewModel
            {
                IntegrationId = integration.IntegrationId,
                IntegrationName = integration.Integration.Name,
                IntegrationType = integration.Integration.Category,
                IsConnected = integration.Settings != null && integration.Settings.Any(),
                IsActive = integration.IsActive,
                LastSync = recentSync?.SyncEndTime,
                LastError = recentSync?.IsSuccessful == false ? recentSync.ErrorMessage : null,
                SuccessfulOperationsToday = todayLogs.Count(l => l.IsSuccessful),
                FailedOperationsToday = todayLogs.Count(l => !l.IsSuccessful)
            });
        }

        return result.OrderByDescending(i => i.IsActive).ThenBy(i => i.IntegrationName).ToList();
    }

    public async Task<List<RecentActivityViewModel>> GetRecentActivitiesAsync(Guid companyId, int limit = 10)
    {
        var recentLogs = await _context.ModuleUsageLogs
            .Include(mul => mul.Module)
            .Where(mul => mul.CompanyId == companyId)
            .OrderByDescending(mul => mul.CreatedAt)
            .Take(limit)
            .ToListAsync();

        var activities = new List<RecentActivityViewModel>();

        foreach (var log in recentLogs)
        {
            var activityType = DetermineActivityType(log.UsageType);
            var description = GenerateActivityDescription(log);

            activities.Add(new RecentActivityViewModel
            {
                ActivityType = activityType,
                Description = description,
                Timestamp = log.CreatedAt,
                ModuleName = log.Module?.Name,
                Status = "completed", // Could be derived from log data
                Cost = log.Cost > 0 ? log.Cost : null
            });
        }

        return activities;
    }

    public async Task<List<NotificationMetricsViewModel>> GetNotificationMetricsAsync(Guid companyId)
    {
        var today = DateTime.UtcNow.Date;
        var startOfWeek = today.AddDays(-(int)today.DayOfWeek);
        var startOfMonth = new DateTime(today.Year, today.Month, 1);

        // This would need to be implemented based on how notification channels are tracked
        // For now, returning sample data structure
        var metrics = new List<NotificationMetricsViewModel>
        {
            new NotificationMetricsViewModel
            {
                ChannelType = "email",
                ChannelName = "E-posta",
                SentToday = 45,
                SentThisWeek = 234,
                SentThisMonth = 1250,
                DeliveredToday = 42,
                FailedToday = 3,
                CostToday = 4.50m,
                CostThisMonth = 125.00m
            }
        };

        return metrics;
    }

    public async Task<DashboardChartsViewModel> GetDashboardChartsAsync(Guid companyId)
    {
        var last7Days = Enumerable.Range(0, 7)
            .Select(i => DateTime.UtcNow.Date.AddDays(-i))
            .Reverse()
            .ToList();

        var usageTrend = new List<ChartDataPoint>();
        var costTrend = new List<ChartDataPoint>();

        foreach (var date in last7Days)
        {
            var dayUsage = await _context.ModuleUsageLogs
                .Where(mul => mul.CompanyId == companyId && mul.CreatedAt.Date == date)
                .CountAsync();

            var dayCost = await _context.ModuleUsageLogs
                .Where(mul => mul.CompanyId == companyId && mul.CreatedAt.Date == date)
                .SumAsync(mul => mul.Cost);

            usageTrend.Add(new ChartDataPoint
            {
                Label = date.ToString("dd MMM"),
                Value = dayUsage,
                Date = date
            });

            costTrend.Add(new ChartDataPoint
            {
                Label = date.ToString("dd MMM"),
                Value = dayCost,
                Date = date
            });
        }

        return new DashboardChartsViewModel
        {
            UsageTrend = usageTrend,
            CostTrend = costTrend,
            DeliveryRates = new List<ChartDataPoint>(),
            ModuleDistribution = new List<PieChartDataPoint>(),
            ChannelDistribution = new List<PieChartDataPoint>()
        };
    }

    public async Task<List<QuickActionViewModel>> GetQuickActionsAsync(Guid companyId)
    {
        var companyModules = await _context.CompanyModules
            .Include(cm => cm.Module)
            .Where(cm => cm.CompanyId == companyId && cm.IsActive)
            .ToListAsync();

        var actions = new List<QuickActionViewModel>();

        // Birthday notifications
        if (companyModules.Any(cm => cm.Module.Name == "Doğum Günü Hatırlatıcısı"))
        {
            actions.Add(new QuickActionViewModel
            {
                Title = "Doğum Günü Bildirimleri",
                Description = "Bugün doğum günü olan müşterilere kutlama mesajı gönderin",
                Icon = "gift",
                Color = "pink",
                ActionUrl = "/Birthday/SendNotifications",
                ActionType = "button"
            });
        }

        // Store access
        actions.Add(new QuickActionViewModel
        {
            Title = "Mağaza",
            Description = "Yeni modüller keşfedin ve satın alın",
            Icon = "shopping-bag",
            Color = "blue",
            ActionUrl = "/Store",
            ActionType = "link"
        });

        // Usage history
        actions.Add(new QuickActionViewModel
        {
            Title = "Harcama Geçmişi",
            Description = "Modül kullanım maliyetlerinizi görüntüleyin",
            Icon = "bar-chart",
            Color = "purple",
            ActionUrl = "/UsageHistory",
            ActionType = "link"
        });

        // Settings
        actions.Add(new QuickActionViewModel
        {
            Title = "Ayarlar",
            Description = "Hesap ve entegrasyon ayarlarınızı yönetin",
            Icon = "settings",
            Color = "gray",
            ActionUrl = "/Settings",
            ActionType = "link"
        });

        return actions;
    }

    public async Task<object> GetRealTimeDataAsync(Guid companyId)
    {
        var stats = await GetDashboardStatsAsync(companyId);
        
        return new
        {
            creditBalance = stats.FormattedCreditBalance,
            notificationsToday = stats.NotificationsSentToday,
            activeModules = stats.ActiveModules,
            lastActivity = stats.FormattedLastActivity
        };
    }

    private string DetermineActivityType(string usageType)
    {
        if (usageType.Contains("notification")) return "notification";
        if (usageType.Contains("sync")) return "sync";
        if (usageType.Contains("purchase")) return "module_purchase";
        if (usageType.Contains("integration")) return "integration";
        return "activity";
    }

    private string GenerateActivityDescription(ModuleUsageLog log)
    {
        var moduleName = log.Module?.Name ?? "Bilinmeyen Modül";
        
        return log.UsageType switch
        {
            var type when type.Contains("notification") => $"{moduleName} - Bildirim gönderildi",
            var type when type.Contains("sync") => $"{moduleName} - Veri senkronizasyonu",
            var type when type.Contains("purchase") => $"{moduleName} - Modül satın alındı",
            _ => $"{moduleName} - {log.Description}"
        };
    }
}
