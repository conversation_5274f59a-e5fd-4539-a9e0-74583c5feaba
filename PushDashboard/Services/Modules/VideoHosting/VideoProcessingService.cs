using FFMpegCore;
using FFMpegCore.Enums;

namespace PushDashboard.Services.Modules.VideoHosting;

public class VideoProcessingService : IVideoProcessingService
{
    private readonly ILogger<VideoProcessingService> _logger;
    private readonly string[] _supportedFormats = { ".mp4", ".avi", ".mov", ".wmv", ".flv", ".webm", ".mkv" };

    public VideoProcessingService(ILogger<VideoProcessingService> logger)
    {
        _logger = logger;
    }

    #region Video Analysis

    public async Task<(bool Success, VideoMetadata Metadata, string Message)> AnalyzeVideoAsync(IFormFile videoFile)
    {
        try
        {
            using var stream = videoFile.OpenReadStream();
            return await AnalyzeVideoAsync(stream, videoFile.FileName);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error analyzing video file: {FileName}", videoFile.FileName);
            return (false, new VideoMetadata(), "Video analizi sırasında hata oluştu.");
        }
    }

    public async Task<(bool Success, VideoMetadata Metadata, string Message)> AnalyzeVideoAsync(Stream inputVideoStream, string fileName)
    {
        try
        {
            // Create a temporary file for FFMpeg analysis
            var tempFilePath = Path.GetTempFileName();
            var tempVideoPath = Path.ChangeExtension(tempFilePath, Path.GetExtension(fileName));

            try
            {
                // Copy stream to temporary file
                using (var fileStream = File.Create(tempVideoPath))
                {
                    inputVideoStream.Position = 0;
                    await inputVideoStream.CopyToAsync(fileStream);
                }

                // Analyze video with FFMpeg
                var mediaInfo = await FFProbe.AnalyseAsync(tempVideoPath);

                var videoStreamInfo = mediaInfo.VideoStreams.FirstOrDefault();
                var audioStreamInfo = mediaInfo.AudioStreams.FirstOrDefault();

                if (videoStreamInfo == null)
                {
                    return (false, new VideoMetadata(), "Video stream bulunamadı.");
                }

                var metadata = new VideoMetadata
                {
                    Duration = mediaInfo.Duration,
                    Width = videoStreamInfo.Width,
                    Height = videoStreamInfo.Height,
                    FrameRate = videoStreamInfo.FrameRate,
                    VideoCodec = videoStreamInfo.CodecName ?? "unknown",
                    AudioCodec = audioStreamInfo?.CodecName ?? "none",
                    BitRate = videoStreamInfo.BitRate,
                    Format = mediaInfo.Format.FormatName ?? "unknown",
                    HasAudio = audioStreamInfo != null,
                    HasVideo = videoStreamInfo != null
                };

                _logger.LogInformation("Video analyzed successfully: {FileName}, Duration: {Duration}, Resolution: {Width}x{Height}",
                    fileName, metadata.Duration, metadata.Width, metadata.Height);

                return (true, metadata, "Video başarıyla analiz edildi.");
            }
            finally
            {
                // Clean up temporary files
                if (File.Exists(tempFilePath))
                    File.Delete(tempFilePath);
                if (File.Exists(tempVideoPath))
                    File.Delete(tempVideoPath);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error analyzing video stream: {FileName}", fileName);
            return (false, new VideoMetadata(), "Video analizi sırasında hata oluştu.");
        }
    }

    #endregion

    #region Thumbnail Generation

    public async Task<(bool Success, Stream ThumbnailStream, string Message)> GenerateThumbnailAsync(
        IFormFile videoFile, 
        TimeSpan timeOffset = default)
    {
        try
        {
            using var stream = videoFile.OpenReadStream();
            return await GenerateThumbnailAsync(stream, videoFile.FileName, timeOffset);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating thumbnail for video file: {FileName}", videoFile.FileName);
            return (false, Stream.Null, "Thumbnail oluşturma sırasında hata oluştu.");
        }
    }

    public async Task<(bool Success, Stream ThumbnailStream, string Message)> GenerateThumbnailAsync(
        Stream inputVideoStream,
        string fileName,
        TimeSpan timeOffset = default)
    {
        try
        {
            // Create temporary files
            var tempVideoPath = Path.GetTempFileName() + Path.GetExtension(fileName);
            var tempThumbnailPath = Path.GetTempFileName() + ".jpg";

            try
            {
                // Copy stream to temporary file
                using (var fileStream = File.Create(tempVideoPath))
                {
                    inputVideoStream.Position = 0;
                    await inputVideoStream.CopyToAsync(fileStream);
                }

                // If no time offset specified, use 10% of video duration or 5 seconds, whichever is smaller
                if (timeOffset == default)
                {
                    var mediaInfo = await FFProbe.AnalyseAsync(tempVideoPath);
                    var tenPercent = TimeSpan.FromSeconds(mediaInfo.Duration.TotalSeconds * 0.1);
                    timeOffset = tenPercent > TimeSpan.FromSeconds(5) ? TimeSpan.FromSeconds(5) : tenPercent;
                }

                // Generate thumbnail
                await FFMpeg.SnapshotAsync(tempVideoPath, tempThumbnailPath, null, timeOffset);

                // Read thumbnail into memory stream
                var thumbnailBytes = await File.ReadAllBytesAsync(tempThumbnailPath);
                var thumbnailStream = new MemoryStream(thumbnailBytes);

                _logger.LogInformation("Thumbnail generated successfully for video: {FileName} at {TimeOffset}",
                    fileName, timeOffset);

                return (true, thumbnailStream, "Thumbnail başarıyla oluşturuldu.");
            }
            finally
            {
                // Clean up temporary files
                if (File.Exists(tempVideoPath))
                    File.Delete(tempVideoPath);
                if (File.Exists(tempThumbnailPath))
                    File.Delete(tempThumbnailPath);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating thumbnail for video: {FileName}", fileName);
            return (false, Stream.Null, "Thumbnail oluşturma sırasında hata oluştu.");
        }
    }

    #endregion

    #region Validation

    public async Task<(bool IsValid, string Message)> ValidateVideoFileAsync(IFormFile videoFile)
    {
        try
        {
            // Check file extension
            var extension = Path.GetExtension(videoFile.FileName).ToLowerInvariant();
            if (!_supportedFormats.Contains(extension))
            {
                return (false, $"Desteklenmeyen video formatı: {extension}. Desteklenen formatlar: {string.Join(", ", _supportedFormats)}");
            }

            // Check content type
            if (!videoFile.ContentType.StartsWith("video/"))
            {
                return (false, "Dosya video formatında değil.");
            }

            // Try to analyze the video to ensure it's valid
            var analysisResult = await AnalyzeVideoAsync(videoFile);
            if (!analysisResult.Success)
            {
                return (false, "Video dosyası geçersiz veya bozuk.");
            }

            return (true, "Video dosyası geçerli.");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating video file: {FileName}", videoFile.FileName);
            return (false, "Video dosyası doğrulama sırasında hata oluştu.");
        }
    }

    public bool IsValidDuration(TimeSpan duration, int maxDurationMinutes)
    {
        return duration.TotalMinutes <= maxDurationMinutes && duration.TotalSeconds > 0;
    }

    public bool IsValidFileSize(long fileSizeBytes, long maxFileSizeBytes)
    {
        return fileSizeBytes <= maxFileSizeBytes && fileSizeBytes > 0;
    }

    #endregion

    #region Utility

    public string FormatFileSize(long bytes)
    {
        string[] sizes = { "B", "KB", "MB", "GB", "TB" };
        double len = bytes;
        int order = 0;
        while (len >= 1024 && order < sizes.Length - 1)
        {
            order++;
            len = len / 1024;
        }
        return $"{len:0.##} {sizes[order]}";
    }

    public string FormatDuration(TimeSpan duration)
    {
        if (duration.TotalHours >= 1)
        {
            return $"{(int)duration.TotalHours}:{duration.Minutes:D2}:{duration.Seconds:D2}";
        }
        else
        {
            return $"{duration.Minutes}:{duration.Seconds:D2}";
        }
    }

    public string[] GetSupportedVideoFormats()
    {
        return _supportedFormats;
    }

    #endregion
}
