using PushDashboard.DTOs;

namespace PushDashboard.Services.Modules.VideoHosting;

public interface IVideoHostingService
{
    #region CRUD Operations

    /// <summary>
    /// Uploads a new video and processes it
    /// </summary>
    Task<(bool Success, string Message, int? VideoId)> UploadVideoAsync(
        Guid companyId, 
        VideoUploadRequestDto request, 
        string userId);

    /// <summary>
    /// Gets all videos for a company
    /// </summary>
    Task<List<VideoListDto>> GetVideosAsync(Guid companyId);

    /// <summary>
    /// Gets a specific video by ID
    /// </summary>
    Task<VideoResponseDto?> GetVideoByIdAsync(Guid companyId, int videoId);

    /// <summary>
    /// Updates video information
    /// </summary>
    Task<(bool Success, string Message)> UpdateVideoAsync(
        Guid companyId, 
        int videoId, 
        VideoUpdateRequestDto request, 
        string userId);

    /// <summary>
    /// Deletes a video
    /// </summary>
    Task<(bool Success, string Message)> DeleteVideoAsync(Guid companyId, int videoId, string userId);

    #endregion

    #region Player Operations

    /// <summary>
    /// Gets video player data for viewing
    /// </summary>
    Task<VideoPlayerDto?> GetVideoPlayerDataAsync(Guid companyId, int videoId);

    /// <summary>
    /// Gets video player data for public viewing (with access token)
    /// </summary>
    Task<VideoPlayerDto?> GetPublicVideoPlayerDataAsync(int videoId, string? accessToken = null);

    /// <summary>
    /// Records a video view for analytics
    /// </summary>
    Task<(bool Success, string Message)> RecordVideoViewAsync(
        int videoId, 
        string? viewerIpAddress, 
        string? viewerUserAgent, 
        string? referrerUrl,
        string? embedUrl = null);

    #endregion

    #region Embed Operations

    /// <summary>
    /// Generates embed code for a video
    /// </summary>
    Task<VideoEmbedDto?> GenerateEmbedCodeAsync(Guid companyId, int videoId, int width = 640, int height = 360);

    /// <summary>
    /// Gets embed player data (for iframe)
    /// </summary>
    Task<VideoPlayerDto?> GetEmbedPlayerDataAsync(int videoId, string? domain = null);

    #endregion

    #region Statistics

    /// <summary>
    /// Gets video hosting statistics for a company
    /// </summary>
    Task<VideoStatsDto> GetVideoStatsAsync(Guid companyId);

    /// <summary>
    /// Gets view analytics for a specific video
    /// </summary>
    Task<List<VideoViewAnalyticsDto>> GetVideoViewAnalyticsAsync(Guid companyId, int videoId, DateTime? startDate = null, DateTime? endDate = null);

    #endregion

    #region Settings

    /// <summary>
    /// Gets video hosting settings for a company
    /// </summary>
    Task<VideoSettingsDto?> GetSettingsAsync(Guid companyId);

    /// <summary>
    /// Updates video hosting settings for a company
    /// </summary>
    Task<(bool Success, string Message)> UpdateSettingsAsync(
        Guid companyId, 
        VideoSettingsRequestDto request, 
        string userId);

    #endregion

    #region Validation

    /// <summary>
    /// Validates if company can upload a new video
    /// </summary>
    Task<(bool CanUpload, string Message, decimal RequiredCredits)> ValidateVideoUploadAsync(
        Guid companyId, 
        IFormFile videoFile);

    /// <summary>
    /// Calculates upload cost based on video duration
    /// </summary>
    Task<decimal> CalculateUploadCostAsync(Guid companyId, TimeSpan videoDuration);

    #endregion
}

/// <summary>
/// Video view analytics DTO
/// </summary>
public class VideoViewAnalyticsDto
{
    public DateTime Date { get; set; }
    public int ViewCount { get; set; }
    public int UniqueViewers { get; set; }
    public TimeSpan AverageWatchDuration { get; set; }
    public int CompletedViews { get; set; }
    public string TopReferrer { get; set; } = string.Empty;
    public string TopCountry { get; set; } = string.Empty;
}
