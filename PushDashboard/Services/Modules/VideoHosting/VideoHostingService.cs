using Microsoft.EntityFrameworkCore;
using PushDashboard.Data;
using PushDashboard.DTOs;
using PushDashboard.Models;

namespace PushDashboard.Services.Modules.VideoHosting;

public class VideoHostingService : IVideoHostingService
{
    private readonly ApplicationDbContext _context;
    private readonly IS3VideoService _s3VideoService;
    private readonly IVideoProcessingService _videoProcessingService;
    private readonly IModuleUsageService _moduleUsageService;
    private readonly ILogger<VideoHostingService> _logger;

    public VideoHostingService(
        ApplicationDbContext context,
        IS3VideoService s3VideoService,
        IVideoProcessingService videoProcessingService,
        IModuleUsageService moduleUsageService,
        ILogger<VideoHostingService> logger)
    {
        _context = context;
        _s3VideoService = s3VideoService;
        _videoProcessingService = videoProcessingService;
        _moduleUsageService = moduleUsageService;
        _logger = logger;
    }

    #region CRUD Operations

    public async Task<(bool Success, string Message, int? VideoId)> UploadVideoAsync(
        Guid companyId, 
        VideoUploadRequestDto request, 
        string userId)
    {
        try
        {
            // Validate video upload
            var validationResult = await ValidateVideoUploadAsync(companyId, request.VideoFile);
            if (!validationResult.CanUpload)
            {
                return (false, validationResult.Message, null);
            }

            // Analyze video
            var analysisResult = await _videoProcessingService.AnalyzeVideoAsync(request.VideoFile);
            if (!analysisResult.Success)
            {
                return (false, analysisResult.Message, null);
            }

            var metadata = analysisResult.Metadata;

            // Calculate upload cost
            var uploadCost = await CalculateUploadCostAsync(companyId, metadata.Duration);

            // Check if company has enough credits
            var company = await _context.Companies.FirstOrDefaultAsync(c => c.Id == companyId);
            if (company == null || company.CreditBalance < uploadCost)
            {
                return (false, $"Yetersiz kredi bakiyesi. Gerekli: {uploadCost:F2}, Mevcut: {company?.CreditBalance:F2}", null);
            }

            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                // Create video entity
                var video = new Models.VideoHosting
                {
                    CompanyId = companyId,
                    Title = request.Title,
                    Description = request.Description,
                    OriginalFileName = request.VideoFile.FileName,
                    FileSizeBytes = request.VideoFile.Length,
                    ContentType = request.VideoFile.ContentType,
                    Duration = metadata.Duration,
                    Width = metadata.Width,
                    Height = metadata.Height,
                    VideoCodec = metadata.VideoCodec,
                    AudioCodec = metadata.AudioCodec,
                    FrameRate = metadata.FrameRate,
                    UploadCost = uploadCost,
                    Status = VideoStatus.Uploading,
                    IsPublic = request.IsPublic,
                    AccessToken = request.IsPublic ? null : Guid.NewGuid().ToString("N"),
                    UploadedByUserId = userId,
                    CreatedAt = DateTime.UtcNow
                };

                _context.VideoHostings.Add(video);
                await _context.SaveChangesAsync();

                // Upload to S3
                var uploadResult = await _s3VideoService.UploadVideoAsync(
                    companyId, 
                    request.VideoFile, 
                    $"{video.Id}_{request.VideoFile.FileName}");

                if (!uploadResult.Success)
                {
                    video.Status = VideoStatus.Failed;
                    video.ProcessingError = uploadResult.Message;
                    await _context.SaveChangesAsync();
                    await transaction.RollbackAsync();
                    return (false, uploadResult.Message, null);
                }

                // Update video with S3 information
                video.S3Key = uploadResult.S3Key;
                video.S3Url = uploadResult.S3Url;
                video.Status = VideoStatus.Processing;

                // Generate thumbnail
                var thumbnailResult = await _videoProcessingService.GenerateThumbnailAsync(request.VideoFile);
                if (thumbnailResult.Success)
                {
                    var thumbnailUploadResult = await _s3VideoService.UploadThumbnailAsync(
                        companyId,
                        thumbnailResult.ThumbnailStream,
                        $"{video.Id}_thumbnail.jpg");

                    if (thumbnailUploadResult.Success)
                    {
                        video.ThumbnailS3Key = thumbnailUploadResult.S3Key;
                        video.ThumbnailS3Url = thumbnailUploadResult.S3Url;
                    }

                    thumbnailResult.ThumbnailStream.Dispose();
                }

                // Calculate storage cost
                var settings = await GetOrCreateSettingsAsync(companyId, userId);
                video.StorageCostPerMonth = (decimal)(video.FileSizeBytes / (1024.0 * 1024.0 * 1024.0)) * settings.StorageCostPerGBPerMonth;

                video.Status = VideoStatus.Ready;
                video.UpdatedAt = DateTime.UtcNow;

                await _context.SaveChangesAsync();

                // Deduct credits
                var videoHostingModule = await _context.Modules
                    .FirstOrDefaultAsync(m => m.Name == "Video Hosting" && m.IsActive);

                if (videoHostingModule == null)
                {
                    await transaction.RollbackAsync();
                    return (false, "Video Hosting modülü bulunamadı.", null);
                }

                var usageResult = await _moduleUsageService.DeductUsageCostAsync(
                    companyId,
                    videoHostingModule.Id,
                    "video_upload",
                    uploadCost,
                    $"Video yükleme: {request.Title} ({_videoProcessingService.FormatDuration(metadata.Duration)})",
                    userId,
                    video.Id.ToString());

                if (!usageResult.Success)
                {
                    await transaction.RollbackAsync();
                    return (false, usageResult.Message, null);
                }

                await transaction.CommitAsync();

                _logger.LogInformation("Video uploaded successfully. Company: {CompanyId}, VideoId: {VideoId}, Cost: {Cost}",
                    companyId, video.Id, uploadCost);

                return (true, "Video başarıyla yüklendi ve işlendi.", video.Id);
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                _logger.LogError(ex, "Error during video upload transaction. Company: {CompanyId}", companyId);
                return (false, "Video yükleme sırasında hata oluştu.", null);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error uploading video. Company: {CompanyId}", companyId);
            return (false, "Video yükleme sırasında hata oluştu.", null);
        }
    }

    public async Task<List<VideoListDto>> GetVideosAsync(Guid companyId)
    {
        try
        {
            var videos = await _context.VideoHostings
                .Where(v => v.CompanyId == companyId)
                .OrderByDescending(v => v.CreatedAt)
                .AsNoTracking()
                .ToListAsync();

            return videos.Select(v => new VideoListDto
            {
                Id = v.Id,
                Title = v.Title,
                OriginalFileName = v.OriginalFileName,
                FileSizeFormatted = _videoProcessingService.FormatFileSize(v.FileSizeBytes),
                DurationFormatted = _videoProcessingService.FormatDuration(v.Duration),
                Resolution = $"{v.Width}x{v.Height}",
                Status = v.Status,
                StatusText = GetStatusText(v.Status),
                IsPublic = v.IsPublic,
                ViewCount = v.ViewCount,
                UploadCost = v.UploadCost,
                CreatedAt = v.CreatedAt,
                ThumbnailUrl = v.ThumbnailS3Url
            }).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting videos for company {CompanyId}", companyId);
            return new List<VideoListDto>();
        }
    }

    public async Task<VideoResponseDto?> GetVideoByIdAsync(Guid companyId, int videoId)
    {
        try
        {
            var video = await _context.VideoHostings
                .Include(v => v.UploadedByUser)
                .Where(v => v.CompanyId == companyId && v.Id == videoId)
                .AsNoTracking()
                .FirstOrDefaultAsync();

            if (video == null)
                return null;

            // Generate presigned URL for video access
            string? videoUrl = null;
            if (video.Status == VideoStatus.Ready)
            {
                var urlResult = await _s3VideoService.GeneratePresignedUrlAsync(video.S3Key, TimeSpan.FromHours(1));
                if (urlResult.Success)
                {
                    videoUrl = urlResult.Url;
                }
            }

            // Generate embed code
            var embedCode = GenerateEmbedCode(video.Id, 640, 360);

            return new VideoResponseDto
            {
                Id = video.Id,
                Title = video.Title,
                Description = video.Description,
                OriginalFileName = video.OriginalFileName,
                FileSizeBytes = video.FileSizeBytes,
                FileSizeFormatted = _videoProcessingService.FormatFileSize(video.FileSizeBytes),
                Duration = video.Duration,
                DurationFormatted = _videoProcessingService.FormatDuration(video.Duration),
                Width = video.Width,
                Height = video.Height,
                Resolution = $"{video.Width}x{video.Height}",
                Status = video.Status,
                StatusText = GetStatusText(video.Status),
                IsPublic = video.IsPublic,
                ViewCount = video.ViewCount,
                LastViewedAt = video.LastViewedAt,
                UploadCost = video.UploadCost,
                StorageCostPerMonth = video.StorageCostPerMonth,
                CreatedAt = video.CreatedAt,
                UploadedByUserName = video.UploadedByUser?.UserName ?? "Unknown",
                ThumbnailUrl = video.ThumbnailS3Url,
                VideoUrl = videoUrl,
                EmbedCode = embedCode,
                ProcessingError = video.ProcessingError
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting video {VideoId} for company {CompanyId}", videoId, companyId);
            return null;
        }
    }

    public async Task<(bool Success, string Message)> UpdateVideoAsync(
        Guid companyId,
        int videoId,
        VideoUpdateRequestDto request,
        string userId)
    {
        try
        {
            var video = await _context.VideoHostings
                .FirstOrDefaultAsync(v => v.CompanyId == companyId && v.Id == videoId);

            if (video == null)
            {
                return (false, "Video bulunamadı.");
            }

            video.Title = request.Title;
            video.Description = request.Description;
            video.IsPublic = request.IsPublic;
            video.UpdatedAt = DateTime.UtcNow;
            video.UpdatedByUserId = userId;

            // If changing from public to private, generate access token
            if (!request.IsPublic && video.AccessToken == null)
            {
                video.AccessToken = Guid.NewGuid().ToString("N");
            }
            // If changing from private to public, remove access token
            else if (request.IsPublic)
            {
                video.AccessToken = null;
            }

            await _context.SaveChangesAsync();

            _logger.LogInformation("Video updated successfully. Company: {CompanyId}, VideoId: {VideoId}",
                companyId, videoId);

            return (true, "Video başarıyla güncellendi.");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating video {VideoId} for company {CompanyId}", videoId, companyId);
            return (false, "Video güncelleme sırasında hata oluştu.");
        }
    }

    public async Task<(bool Success, string Message)> DeleteVideoAsync(Guid companyId, int videoId, string userId)
    {
        try
        {
            var video = await _context.VideoHostings
                .FirstOrDefaultAsync(v => v.CompanyId == companyId && v.Id == videoId);

            if (video == null)
            {
                return (false, "Video bulunamadı.");
            }

            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                // Delete from S3
                if (!string.IsNullOrEmpty(video.S3Key))
                {
                    await _s3VideoService.DeleteVideoAsync(video.S3Key);
                }

                if (!string.IsNullOrEmpty(video.ThumbnailS3Key))
                {
                    await _s3VideoService.DeleteThumbnailAsync(video.ThumbnailS3Key);
                }

                // Mark as deleted in database
                video.Status = VideoStatus.Deleted;
                video.UpdatedAt = DateTime.UtcNow;
                video.UpdatedByUserId = userId;

                await _context.SaveChangesAsync();
                await transaction.CommitAsync();

                _logger.LogInformation("Video deleted successfully. Company: {CompanyId}, VideoId: {VideoId}",
                    companyId, videoId);

                return (true, "Video başarıyla silindi.");
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                _logger.LogError(ex, "Error during video deletion transaction. Company: {CompanyId}, VideoId: {VideoId}",
                    companyId, videoId);
                return (false, "Video silme sırasında hata oluştu.");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting video {VideoId} for company {CompanyId}", videoId, companyId);
            return (false, "Video silme sırasında hata oluştu.");
        }
    }

    #endregion

    #region Player Operations

    public async Task<VideoPlayerDto?> GetVideoPlayerDataAsync(Guid companyId, int videoId)
    {
        try
        {
            var video = await _context.VideoHostings
                .Where(v => v.CompanyId == companyId && v.Id == videoId && v.Status == VideoStatus.Ready)
                .AsNoTracking()
                .FirstOrDefaultAsync();

            if (video == null)
                return null;

            var settings = await _context.VideoHostingSettings
                .Where(s => s.CompanyId == companyId)
                .AsNoTracking()
                .FirstOrDefaultAsync();

            // Generate presigned URL
            var urlResult = await _s3VideoService.GeneratePresignedUrlAsync(video.S3Key, TimeSpan.FromHours(2));
            if (!urlResult.Success)
                return null;

            return new VideoPlayerDto
            {
                Id = video.Id,
                Title = video.Title,
                Description = video.Description,
                VideoUrl = urlResult.Url,
                ThumbnailUrl = video.ThumbnailS3Url,
                Width = video.Width,
                Height = video.Height,
                Duration = video.Duration,
                AutoPlay = settings?.AutoPlay ?? false,
                ShowControls = settings?.ShowControls ?? true,
                AllowDownload = settings?.AllowDownload ?? false,
                PlayerTheme = settings?.PlayerTheme ?? "default",
                CustomPlayerCss = settings?.CustomPlayerCss
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting video player data for video {VideoId}, company {CompanyId}",
                videoId, companyId);
            return null;
        }
    }

    #endregion

    #region Validation

    public async Task<(bool CanUpload, string Message, decimal RequiredCredits)> ValidateVideoUploadAsync(
        Guid companyId,
        IFormFile videoFile)
    {
        try
        {
            // Validate file
            var fileValidation = await _videoProcessingService.ValidateVideoFileAsync(videoFile);
            if (!fileValidation.IsValid)
            {
                return (false, fileValidation.Message, 0);
            }

            // Get settings
            var settings = await _context.VideoHostingSettings
                .FirstOrDefaultAsync(s => s.CompanyId == companyId);

            if (settings != null)
            {
                // Check file size limit
                if (!_videoProcessingService.IsValidFileSize(videoFile.Length, settings.MaxFileSizeBytes))
                {
                    var maxSizeMB = settings.MaxFileSizeBytes / (1024 * 1024);
                    return (false, $"Dosya boyutu çok büyük. Maksimum: {maxSizeMB} MB", 0);
                }

                // Check video count limit
                var currentVideoCount = await _context.VideoHostings
                    .CountAsync(v => v.CompanyId == companyId && v.Status != VideoStatus.Deleted);

                if (currentVideoCount >= settings.MaxVideosPerCompany)
                {
                    return (false, $"Maksimum video sayısına ulaştınız. Limit: {settings.MaxVideosPerCompany}", 0);
                }
            }

            // Analyze video for duration check
            var analysisResult = await _videoProcessingService.AnalyzeVideoAsync(videoFile);
            if (!analysisResult.Success)
            {
                return (false, "Video analizi başarısız.", 0);
            }

            var maxDurationMinutes = settings?.MaxDurationMinutes ?? 60;
            if (!_videoProcessingService.IsValidDuration(analysisResult.Metadata.Duration, maxDurationMinutes))
            {
                return (false, $"Video süresi çok uzun. Maksimum: {maxDurationMinutes} dakika", 0);
            }

            // Calculate required credits
            var requiredCredits = await CalculateUploadCostAsync(companyId, analysisResult.Metadata.Duration);

            return (true, "Video yükleme için uygun.", requiredCredits);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating video upload for company {CompanyId}", companyId);
            return (false, "Video doğrulama sırasında hata oluştu.", 0);
        }
    }

    public async Task<decimal> CalculateUploadCostAsync(Guid companyId, TimeSpan videoDuration)
    {
        try
        {
            var settings = await _context.VideoHostingSettings
                .FirstOrDefaultAsync(s => s.CompanyId == companyId);

            var costPerMinute = settings?.CostPerMinute ?? 0.5m;
            var durationMinutes = (decimal)videoDuration.TotalMinutes;

            return Math.Ceiling(durationMinutes) * costPerMinute;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error calculating upload cost for company {CompanyId}", companyId);
            return 0.5m; // Default cost
        }
    }

    #endregion

    #region Settings

    public async Task<VideoSettingsDto?> GetSettingsAsync(Guid companyId)
    {
        try
        {
            var settings = await _context.VideoHostingSettings
                .Where(s => s.CompanyId == companyId)
                .AsNoTracking()
                .FirstOrDefaultAsync();

            if (settings == null)
                return null;

            return new VideoSettingsDto
            {
                CostPerMinute = settings.CostPerMinute,
                StorageCostPerGBPerMonth = settings.StorageCostPerGBPerMonth,
                MaxFileSizeMB = (int)(settings.MaxFileSizeBytes / (1024 * 1024)),
                MaxDurationMinutes = settings.MaxDurationMinutes,
                MaxVideosPerCompany = settings.MaxVideosPerCompany,
                AutoPlay = settings.AutoPlay,
                ShowControls = settings.ShowControls,
                AllowDownload = settings.AllowDownload,
                PlayerTheme = settings.PlayerTheme,
                CustomPlayerCss = settings.CustomPlayerCss,
                RequireAuthentication = settings.RequireAuthentication,
                AllowEmbedding = settings.AllowEmbedding,
                AllowedDomains = settings.AllowedDomains
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting settings for company {CompanyId}", companyId);
            return null;
        }
    }

    public async Task<(bool Success, string Message)> UpdateSettingsAsync(
        Guid companyId,
        VideoSettingsRequestDto request,
        string userId)
    {
        try
        {
            var settings = await GetOrCreateSettingsAsync(companyId, userId);

            settings.CostPerMinute = request.CostPerMinute;
            settings.StorageCostPerGBPerMonth = request.StorageCostPerGBPerMonth;
            settings.MaxFileSizeBytes = request.MaxFileSizeMB * 1024 * 1024;
            settings.MaxDurationMinutes = request.MaxDurationMinutes;
            settings.MaxVideosPerCompany = request.MaxVideosPerCompany;
            settings.AutoPlay = request.AutoPlay;
            settings.ShowControls = request.ShowControls;
            settings.AllowDownload = request.AllowDownload;
            settings.PlayerTheme = request.PlayerTheme;
            settings.CustomPlayerCss = request.CustomPlayerCss;
            settings.RequireAuthentication = request.RequireAuthentication;
            settings.AllowEmbedding = request.AllowEmbedding;
            settings.AllowedDomains = request.AllowedDomains;
            settings.UpdatedAt = DateTime.UtcNow;
            settings.UpdatedByUserId = userId;

            await _context.SaveChangesAsync();

            _logger.LogInformation("Video hosting settings updated for company {CompanyId}", companyId);

            return (true, "Ayarlar başarıyla güncellendi.");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating settings for company {CompanyId}", companyId);
            return (false, "Ayarlar güncelleme sırasında hata oluştu.");
        }
    }

    #endregion

    #region Statistics and Analytics

    public async Task<VideoStatsDto> GetVideoStatsAsync(Guid companyId)
    {
        try
        {
            var videos = await _context.VideoHostings
                .Where(v => v.CompanyId == companyId && v.Status != VideoStatus.Deleted)
                .AsNoTracking()
                .ToListAsync();

            var totalViews = await _context.VideoHostingViews
                .Where(vv => vv.CompanyId == companyId)
                .CountAsync();

            return new VideoStatsDto
            {
                TotalVideos = videos.Count,
                ReadyVideos = videos.Count(v => v.Status == VideoStatus.Ready),
                ProcessingVideos = videos.Count(v => v.Status == VideoStatus.Processing || v.Status == VideoStatus.Uploading),
                FailedVideos = videos.Count(v => v.Status == VideoStatus.Failed),
                TotalStorageBytes = videos.Sum(v => v.FileSizeBytes),
                TotalStorageFormatted = _videoProcessingService.FormatFileSize(videos.Sum(v => v.FileSizeBytes)),
                TotalDuration = TimeSpan.FromTicks(videos.Sum(v => v.Duration.Ticks)),
                TotalDurationFormatted = _videoProcessingService.FormatDuration(TimeSpan.FromTicks(videos.Sum(v => v.Duration.Ticks))),
                TotalViews = totalViews,
                TotalUploadCosts = videos.Sum(v => v.UploadCost),
                MonthlyStorageCosts = videos.Sum(v => v.StorageCostPerMonth)
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting video stats for company {CompanyId}", companyId);
            return new VideoStatsDto();
        }
    }

    public async Task<List<VideoViewAnalyticsDto>> GetVideoViewAnalyticsAsync(Guid companyId, int videoId, DateTime? startDate = null, DateTime? endDate = null)
    {
        try
        {
            var query = _context.VideoHostingViews
                .Where(vv => vv.CompanyId == companyId && vv.VideoHostingId == videoId);

            if (startDate.HasValue)
                query = query.Where(vv => vv.ViewedAt >= startDate.Value);

            if (endDate.HasValue)
                query = query.Where(vv => vv.ViewedAt <= endDate.Value);

            var views = await query
                .AsNoTracking()
                .ToListAsync();

            var analytics = views
                .GroupBy(v => v.ViewedAt.Date)
                .Select(g => new VideoViewAnalyticsDto
                {
                    Date = g.Key,
                    ViewCount = g.Count(),
                    UniqueViewers = g.GroupBy(v => v.ViewerIpAddress).Count(),
                    AverageWatchDuration = TimeSpan.FromTicks((long)g.Where(v => v.WatchDuration.HasValue).Average(v => v.WatchDuration?.Ticks ?? 0)),
                    CompletedViews = g.Count(v => v.CompletedView),
                    TopReferrer = g.GroupBy(v => v.ReferrerUrl).OrderByDescending(rg => rg.Count()).FirstOrDefault()?.Key ?? "",
                    TopCountry = g.GroupBy(v => v.ViewerCountry).OrderByDescending(cg => cg.Count()).FirstOrDefault()?.Key ?? ""
                })
                .OrderBy(a => a.Date)
                .ToList();

            return analytics;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting video analytics for video {VideoId}, company {CompanyId}", videoId, companyId);
            return new List<VideoViewAnalyticsDto>();
        }
    }

    public async Task<(bool Success, string Message)> RecordVideoViewAsync(
        int videoId,
        string? viewerIpAddress,
        string? viewerUserAgent,
        string? referrerUrl,
        string? embedUrl = null)
    {
        try
        {
            var video = await _context.VideoHostings
                .FirstOrDefaultAsync(v => v.Id == videoId && v.Status == VideoStatus.Ready);

            if (video == null)
            {
                return (false, "Video bulunamadı.");
            }

            var view = new VideoHostingView
            {
                VideoHostingId = videoId,
                CompanyId = video.CompanyId,
                ViewerIpAddress = viewerIpAddress,
                ViewerUserAgent = viewerUserAgent,
                ReferrerUrl = referrerUrl,
                EmbedUrl = embedUrl,
                ViewedAt = DateTime.UtcNow
            };

            _context.VideoHostingViews.Add(view);

            // Update video view count and last viewed date
            video.ViewCount++;
            video.LastViewedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();

            return (true, "Video görüntüleme kaydedildi.");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error recording video view for video {VideoId}", videoId);
            return (false, "Video görüntüleme kaydı sırasında hata oluştu.");
        }
    }

    #endregion

    #region Embed Operations

    public async Task<VideoEmbedDto?> GenerateEmbedCodeAsync(Guid companyId, int videoId, int width = 640, int height = 360)
    {
        try
        {
            var video = await _context.VideoHostings
                .Where(v => v.CompanyId == companyId && v.Id == videoId && v.Status == VideoStatus.Ready)
                .AsNoTracking()
                .FirstOrDefaultAsync();

            if (video == null)
                return null;

            var settings = await _context.VideoHostingSettings
                .Where(s => s.CompanyId == companyId)
                .AsNoTracking()
                .FirstOrDefaultAsync();

            if (settings?.AllowEmbedding == false)
                return null;

            var embedCode = GenerateEmbedCode(videoId, width, height);
            var directUrl = $"https://your-domain.com/VideoHosting/Player/{videoId}"; // This should come from configuration

            return new VideoEmbedDto
            {
                VideoId = videoId,
                EmbedCode = embedCode,
                DirectUrl = directUrl,
                Width = width,
                Height = height
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating embed code for video {VideoId}, company {CompanyId}", videoId, companyId);
            return null;
        }
    }

    public async Task<VideoPlayerDto?> GetEmbedPlayerDataAsync(int videoId, string? domain = null)
    {
        try
        {
            var video = await _context.VideoHostings
                .Where(v => v.Id == videoId && v.Status == VideoStatus.Ready)
                .AsNoTracking()
                .FirstOrDefaultAsync();

            if (video == null)
                return null;

            var settings = await _context.VideoHostingSettings
                .Where(s => s.CompanyId == video.CompanyId)
                .AsNoTracking()
                .FirstOrDefaultAsync();

            // Check if embedding is allowed
            if (settings?.AllowEmbedding == false)
                return null;

            // Check domain restrictions
            if (!string.IsNullOrEmpty(settings?.AllowedDomains) && !string.IsNullOrEmpty(domain))
            {
                var allowedDomains = System.Text.Json.JsonSerializer.Deserialize<string[]>(settings.AllowedDomains);
                if (allowedDomains != null && !allowedDomains.Contains(domain))
                    return null;
            }

            // Check if video is public or has valid access
            if (!video.IsPublic)
                return null;

            // Generate presigned URL
            var urlResult = await _s3VideoService.GeneratePresignedUrlAsync(video.S3Key, TimeSpan.FromHours(2));
            if (!urlResult.Success)
                return null;

            return new VideoPlayerDto
            {
                Id = video.Id,
                Title = video.Title,
                Description = video.Description,
                VideoUrl = urlResult.Url,
                ThumbnailUrl = video.ThumbnailS3Url,
                Width = video.Width,
                Height = video.Height,
                Duration = video.Duration,
                AutoPlay = settings?.AutoPlay ?? false,
                ShowControls = settings?.ShowControls ?? true,
                AllowDownload = settings?.AllowDownload ?? false,
                PlayerTheme = settings?.PlayerTheme ?? "default",
                CustomPlayerCss = settings?.CustomPlayerCss
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting embed player data for video {VideoId}", videoId);
            return null;
        }
    }

    public async Task<VideoPlayerDto?> GetPublicVideoPlayerDataAsync(int videoId, string? accessToken = null)
    {
        try
        {
            var video = await _context.VideoHostings
                .Where(v => v.Id == videoId && v.Status == VideoStatus.Ready)
                .AsNoTracking()
                .FirstOrDefaultAsync();

            if (video == null)
                return null;

            // Check access permissions
            if (!video.IsPublic && (string.IsNullOrEmpty(accessToken) || video.AccessToken != accessToken))
                return null;

            var settings = await _context.VideoHostingSettings
                .Where(s => s.CompanyId == video.CompanyId)
                .AsNoTracking()
                .FirstOrDefaultAsync();

            // Generate presigned URL
            var urlResult = await _s3VideoService.GeneratePresignedUrlAsync(video.S3Key, TimeSpan.FromHours(2));
            if (!urlResult.Success)
                return null;

            return new VideoPlayerDto
            {
                Id = video.Id,
                Title = video.Title,
                Description = video.Description,
                VideoUrl = urlResult.Url,
                ThumbnailUrl = video.ThumbnailS3Url,
                Width = video.Width,
                Height = video.Height,
                Duration = video.Duration,
                AutoPlay = settings?.AutoPlay ?? false,
                ShowControls = settings?.ShowControls ?? true,
                AllowDownload = settings?.AllowDownload ?? false,
                PlayerTheme = settings?.PlayerTheme ?? "default",
                CustomPlayerCss = settings?.CustomPlayerCss
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting public video player data for video {VideoId}", videoId);
            return null;
        }
    }

    #endregion

    #region Helper Methods

    private async Task<VideoHostingSettings> GetOrCreateSettingsAsync(Guid companyId, string userId)
    {
        var settings = await _context.VideoHostingSettings
            .FirstOrDefaultAsync(s => s.CompanyId == companyId);

        if (settings == null)
        {
            settings = new VideoHostingSettings
            {
                CompanyId = companyId,
                UpdatedByUserId = userId
            };
            _context.VideoHostingSettings.Add(settings);
            await _context.SaveChangesAsync();
        }

        return settings;
    }

    private string GetStatusText(VideoStatus status)
    {
        return status switch
        {
            VideoStatus.Uploading => "Yükleniyor",
            VideoStatus.Processing => "İşleniyor",
            VideoStatus.Ready => "Hazır",
            VideoStatus.Failed => "Başarısız",
            VideoStatus.Deleted => "Silindi",
            _ => "Bilinmiyor"
        };
    }

    private string GenerateEmbedCode(int videoId, int width, int height)
    {
        var baseUrl = "https://your-domain.com"; // This should come from configuration
        return $"<iframe src=\"{baseUrl}/VideoHosting/Embed/{videoId}\" width=\"{width}\" height=\"{height}\" frameborder=\"0\" allowfullscreen></iframe>";
    }

    #endregion
}
