using Amazon.S3;
using Amazon.S3.Model;
using Microsoft.Extensions.Options;
using PushDashboard.Configuration;

namespace PushDashboard.Services.Modules.VideoHosting;

public class S3VideoService : IS3VideoService
{
    private readonly IAmazonS3 _s3Client;
    private readonly AwsSettings _awsSettings;
    private readonly ILogger<S3VideoService> _logger;

    public S3VideoService(
        IAmazonS3 s3Client,
        IOptions<AwsSettings> awsSettings,
        ILogger<S3VideoService> logger)
    {
        _s3Client = s3Client;
        _awsSettings = awsSettings.Value;
        _logger = logger;
    }

    #region Upload Operations

    public async Task<(bool Success, string S3Key, string S3Url, string Message)> UploadVideoAsync(
        Guid companyId, 
        IFormFile videoFile, 
        string fileName)
    {
        try
        {
            var s3Key = GenerateVideoS3Key(companyId, fileName);

            var request = new PutObjectRequest
            {
                BucketName = _awsSettings.S3.BucketName,
                Key = s3Key,
                InputStream = videoFile.OpenReadStream(),
                ContentType = videoFile.ContentType,
                ServerSideEncryptionMethod = ServerSideEncryptionMethod.AES256,
                Metadata =
                {
                    ["company-id"] = companyId.ToString(),
                    ["original-filename"] = videoFile.FileName,
                    ["upload-timestamp"] = DateTime.UtcNow.ToString("O")
                }
            };

            var response = await _s3Client.PutObjectAsync(request);

            if (response.HttpStatusCode == System.Net.HttpStatusCode.OK)
            {
                var s3Url = $"https://{_awsSettings.S3.BucketName}.s3.{_awsSettings.Region}.amazonaws.com/{s3Key}";
                
                _logger.LogInformation("Video uploaded successfully to S3. Company: {CompanyId}, S3Key: {S3Key}", 
                    companyId, s3Key);

                return (true, s3Key, s3Url, "Video başarıyla yüklendi.");
            }

            return (false, "", "", "Video yükleme başarısız.");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error uploading video to S3. Company: {CompanyId}, FileName: {FileName}", 
                companyId, fileName);
            return (false, "", "", "Video yükleme sırasında hata oluştu.");
        }
    }

    public async Task<(bool Success, string S3Key, string S3Url, string Message)> UploadThumbnailAsync(
        Guid companyId, 
        Stream thumbnailStream, 
        string fileName)
    {
        try
        {
            var s3Key = GenerateThumbnailS3Key(companyId, fileName);

            var request = new PutObjectRequest
            {
                BucketName = _awsSettings.S3.BucketName,
                Key = s3Key,
                InputStream = thumbnailStream,
                ContentType = "image/jpeg",
                ServerSideEncryptionMethod = ServerSideEncryptionMethod.AES256,
                Metadata =
                {
                    ["company-id"] = companyId.ToString(),
                    ["thumbnail-for"] = fileName,
                    ["upload-timestamp"] = DateTime.UtcNow.ToString("O")
                }
            };

            var response = await _s3Client.PutObjectAsync(request);

            if (response.HttpStatusCode == System.Net.HttpStatusCode.OK)
            {
                var s3Url = $"https://{_awsSettings.S3.BucketName}.s3.{_awsSettings.Region}.amazonaws.com/{s3Key}";
                
                _logger.LogInformation("Thumbnail uploaded successfully to S3. Company: {CompanyId}, S3Key: {S3Key}", 
                    companyId, s3Key);

                return (true, s3Key, s3Url, "Thumbnail başarıyla yüklendi.");
            }

            return (false, "", "", "Thumbnail yükleme başarısız.");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error uploading thumbnail to S3. Company: {CompanyId}, FileName: {FileName}", 
                companyId, fileName);
            return (false, "", "", "Thumbnail yükleme sırasında hata oluştu.");
        }
    }

    #endregion

    #region Download Operations

    public async Task<(bool Success, string Url, string Message)> GeneratePresignedUrlAsync(
        string s3Key, 
        TimeSpan expiration)
    {
        try
        {
            var request = new GetPreSignedUrlRequest
            {
                BucketName = _awsSettings.S3.BucketName,
                Key = s3Key,
                Verb = HttpVerb.GET,
                Expires = DateTime.UtcNow.Add(expiration)
            };

            var url = await _s3Client.GetPreSignedURLAsync(request);
            
            _logger.LogInformation("Presigned URL generated for S3Key: {S3Key}, Expiration: {Expiration}", 
                s3Key, expiration);

            return (true, url, "Presigned URL oluşturuldu.");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating presigned URL for S3Key: {S3Key}", s3Key);
            return (false, "", "Presigned URL oluşturma sırasında hata oluştu.");
        }
    }

    public async Task<(bool Success, string Url, string Message)> GetDownloadUrlAsync(string s3Key)
    {
        try
        {
            // Generate a presigned URL valid for 1 hour
            return await GeneratePresignedUrlAsync(s3Key, TimeSpan.FromHours(1));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting download URL for S3Key: {S3Key}", s3Key);
            return (false, "", "Download URL oluşturma sırasında hata oluştu.");
        }
    }

    #endregion

    #region Delete Operations

    public async Task<(bool Success, string Message)> DeleteVideoAsync(string s3Key)
    {
        try
        {
            var request = new DeleteObjectRequest
            {
                BucketName = _awsSettings.S3.BucketName,
                Key = s3Key
            };

            var response = await _s3Client.DeleteObjectAsync(request);

            if (response.HttpStatusCode == System.Net.HttpStatusCode.NoContent)
            {
                _logger.LogInformation("Video deleted successfully from S3. S3Key: {S3Key}", s3Key);
                return (true, "Video başarıyla silindi.");
            }

            return (false, "Video silme başarısız.");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting video from S3. S3Key: {S3Key}", s3Key);
            return (false, "Video silme sırasında hata oluştu.");
        }
    }

    public async Task<(bool Success, string Message)> DeleteThumbnailAsync(string s3Key)
    {
        try
        {
            var request = new DeleteObjectRequest
            {
                BucketName = _awsSettings.S3.BucketName,
                Key = s3Key
            };

            var response = await _s3Client.DeleteObjectAsync(request);

            if (response.HttpStatusCode == System.Net.HttpStatusCode.NoContent)
            {
                _logger.LogInformation("Thumbnail deleted successfully from S3. S3Key: {S3Key}", s3Key);
                return (true, "Thumbnail başarıyla silindi.");
            }

            return (false, "Thumbnail silme başarısız.");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting thumbnail from S3. S3Key: {S3Key}", s3Key);
            return (false, "Thumbnail silme sırasında hata oluştu.");
        }
    }

    #endregion

    #region Utility Operations

    public async Task<bool> FileExistsAsync(string s3Key)
    {
        try
        {
            var request = new GetObjectMetadataRequest
            {
                BucketName = _awsSettings.S3.BucketName,
                Key = s3Key
            };

            await _s3Client.GetObjectMetadataAsync(request);
            return true;
        }
        catch (AmazonS3Exception ex) when (ex.StatusCode == System.Net.HttpStatusCode.NotFound)
        {
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking file existence in S3. S3Key: {S3Key}", s3Key);
            return false;
        }
    }

    public async Task<(bool Success, long FileSize, DateTime LastModified, string ContentType)> GetFileMetadataAsync(string s3Key)
    {
        try
        {
            var request = new GetObjectMetadataRequest
            {
                BucketName = _awsSettings.S3.BucketName,
                Key = s3Key
            };

            var response = await _s3Client.GetObjectMetadataAsync(request);

            return (true, response.ContentLength, response.LastModified ?? DateTime.UtcNow, response.Headers.ContentType);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting file metadata from S3. S3Key: {S3Key}", s3Key);
            return (false, 0, DateTime.MinValue, "");
        }
    }

    public string GenerateVideoS3Key(Guid companyId, string originalFileName)
    {
        var fileExtension = Path.GetExtension(originalFileName);
        var uniqueFileName = $"{Guid.NewGuid()}{fileExtension}";
        return $"{_awsSettings.S3.VideoFolder}/{companyId}/{uniqueFileName}";
    }

    public string GenerateThumbnailS3Key(Guid companyId, string videoFileName)
    {
        var fileNameWithoutExtension = Path.GetFileNameWithoutExtension(videoFileName);
        var uniqueFileName = $"{fileNameWithoutExtension}_thumb_{Guid.NewGuid()}.jpg";
        return $"{_awsSettings.S3.ThumbnailFolder}/{companyId}/{uniqueFileName}";
    }

    #endregion
}
