namespace PushDashboard.Services.Modules.VideoHosting;

public interface IS3VideoService
{
    #region Upload Operations

    /// <summary>
    /// Uploads a video file to S3 and returns the S3 key and URL
    /// </summary>
    Task<(bool Success, string S3Key, string S3Url, string Message)> UploadVideoAsync(
        Guid companyId, 
        IFormFile videoFile, 
        string fileName);

    /// <summary>
    /// Uploads a thumbnail image to S3
    /// </summary>
    Task<(bool Success, string S3Key, string S3Url, string Message)> UploadThumbnailAsync(
        Guid companyId, 
        Stream thumbnailStream, 
        string fileName);

    #endregion

    #region Download Operations

    /// <summary>
    /// Generates a presigned URL for video access
    /// </summary>
    Task<(bool Success, string Url, string Message)> GeneratePresignedUrlAsync(
        string s3Key, 
        TimeSpan expiration);

    /// <summary>
    /// Gets a direct download URL for a video
    /// </summary>
    Task<(bool Success, string Url, string Message)> GetDownloadUrlAsync(string s3Key);

    #endregion

    #region Delete Operations

    /// <summary>
    /// Deletes a video file from S3
    /// </summary>
    Task<(bool Success, string Message)> DeleteVideoAsync(string s3Key);

    /// <summary>
    /// Deletes a thumbnail from S3
    /// </summary>
    Task<(bool Success, string Message)> DeleteThumbnailAsync(string s3Key);

    #endregion

    #region Utility Operations

    /// <summary>
    /// Checks if a file exists in S3
    /// </summary>
    Task<bool> FileExistsAsync(string s3Key);

    /// <summary>
    /// Gets file metadata from S3
    /// </summary>
    Task<(bool Success, long FileSize, DateTime LastModified, string ContentType)> GetFileMetadataAsync(string s3Key);

    /// <summary>
    /// Generates a unique S3 key for a video file
    /// </summary>
    string GenerateVideoS3Key(Guid companyId, string originalFileName);

    /// <summary>
    /// Generates a unique S3 key for a thumbnail file
    /// </summary>
    string GenerateThumbnailS3Key(Guid companyId, string videoFileName);

    #endregion
}
