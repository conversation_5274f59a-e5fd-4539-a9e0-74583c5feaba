using Microsoft.EntityFrameworkCore;
using PushDashboard.Data;
using PushDashboard.DTOs;
using PushDashboard.Models;
using PushDashboard.Services.Integrations;
using PushDashboard.Services.Integrations.Common.Models;
using PushDashboard.Services.Notifications;
using System.Text;
using System.Text.Json;
using System.Text.Json.Serialization;

namespace PushDashboard.Services.Modules.GiftWheel;

/// <summary>
/// Main service for Gift Wheel functionality
/// </summary>
public class GiftWheelService : IGiftWheelService
{
    private readonly ApplicationDbContext _context;
    private readonly ILogger<GiftWheelService> _logger;
    private readonly EcommerceGiftVoucherFactory _giftVoucherFactory;
    private readonly INotificationChannelFactory _notificationChannelFactory;
    private readonly IModuleUsageService _moduleUsageService;
    private readonly IR2StorageService _r2StorageService;
    private readonly IConfiguration _configuration;

    public GiftWheelService(
        ApplicationDbContext context,
        ILogger<GiftWheelService> logger,
        EcommerceGiftVoucherFactory giftVoucherFactory,
        INotificationChannelFactory notificationChannelFactory,
        IModuleUsageService moduleUsageService,
        IR2StorageService r2StorageService,
        IConfiguration configuration)
    {
        _context = context;
        _logger = logger;
        _giftVoucherFactory = giftVoucherFactory;
        _notificationChannelFactory = notificationChannelFactory;
        _moduleUsageService = moduleUsageService;
        _r2StorageService = r2StorageService;
        _configuration = configuration;
    }

    #region Wheel Management

    public async Task<Models.GiftWheel?> GetCompanyWheelAsync(Guid companyId)
    {
        try
        {
            return await _context.GiftWheels
                .Include(w => w.Prizes.Where(p => p.IsActive))
                .Include(w => w.Settings)
                .FirstOrDefaultAsync(w => w.CompanyId == companyId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting company wheel for {CompanyId}", companyId);
            return null;
        }
    }

    public async Task<(bool Success, string Message)> CreateOrUpdateWheelAsync(Guid companyId, string wheelName, string userId)
    {
        try
        {
            var existingWheel = await _context.GiftWheels
                .FirstOrDefaultAsync(w => w.CompanyId == companyId);

            if (existingWheel != null)
            {
                existingWheel.Name = wheelName;
                existingWheel.UpdatedAt = DateTime.UtcNow;
                _context.GiftWheels.Update(existingWheel);
            }
            else
            {
                var newWheel = new Models.GiftWheel
                {
                    CompanyId = companyId,
                    Name = wheelName,
                    IsActive = true,
                    CreatedByUserId = userId,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                };

                _context.GiftWheels.Add(newWheel);
                await _context.SaveChangesAsync();

                // Create default settings
                var defaultSettings = new GiftWheelSettings
                {
                    GiftWheelId = newWheel.Id,
                    UpdatedByUserId = userId
                };

                _context.GiftWheelSettings.Add(defaultSettings);
            }

            await _context.SaveChangesAsync();
            _logger.LogInformation("Gift wheel created/updated for company {CompanyId}", companyId);
            
            return (true, "Hediye çarkı başarıyla kaydedildi.");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating/updating wheel for company {CompanyId}", companyId);
            return (false, "Hediye çarkı kaydedilirken hata oluştu.");
        }
    }

    public async Task<(bool Success, string Message)> ToggleWheelStatusAsync(Guid companyId, bool isActive, string userId)
    {
        try
        {
            var wheel = await _context.GiftWheels
                .FirstOrDefaultAsync(w => w.CompanyId == companyId);

            if (wheel == null)
            {
                return (false, "Hediye çarkı bulunamadı.");
            }

            wheel.IsActive = isActive;
            wheel.UpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();
            
            var status = isActive ? "aktif" : "pasif";
            _logger.LogInformation("Gift wheel status changed to {Status} for company {CompanyId}", status, companyId);
            
            return (true, $"Hediye çarkı {status} duruma getirildi.");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error toggling wheel status for company {CompanyId}", companyId);
            return (false, "Durum değiştirilirken hata oluştu.");
        }
    }

    #endregion

    #region Prize Management

    public async Task<List<GiftWheelPrizeDto>> GetWheelPrizesAsync(int wheelId)
    {
        try
        {
            var prizes = await _context.GiftWheelPrizes
                .Where(p => p.GiftWheelId == wheelId)
                .OrderBy(p => p.SortOrder)
                .Select(p => new GiftWheelPrizeDto
                {
                    Id = p.Id,
                    Name = p.Name,
                    PrizeType = p.PrizeType,
                    DiscountAmount = p.DiscountAmount,
                    DiscountType = p.DiscountType,
                    ValidityDays = p.ValidityDays,
                    Probability = p.Probability,
                    Color = p.Color,
                    IsActive = p.IsActive,
                    SortOrder = p.SortOrder
                })
                .ToListAsync();

            return prizes;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting prizes for wheel {WheelId}", wheelId);
            return new List<GiftWheelPrizeDto>();
        }
    }

    public async Task<(bool Success, string Message, GiftWheelPrizeDto? Prize)> AddPrizeAsync(int wheelId, GiftWheelPrizeRequest request)
    {
        try
        {
            // Validate wheel exists
            var wheel = await _context.GiftWheels.FindAsync(wheelId);
            if (wheel == null)
            {
                return (false, "Hediye çarkı bulunamadı.", null);
            }

            // Validate probability doesn't exceed 100% total
            var totalProbability = await _context.GiftWheelPrizes
                .Where(p => p.GiftWheelId == wheelId && p.IsActive)
                .SumAsync(p => p.Probability);

            if (totalProbability + request.Probability > 100)
            {
                return (false, $"Toplam olasılık %100'ü geçemez. Mevcut: %{totalProbability}, Eklenmek istenen: %{request.Probability}", null);
            }

            var prize = new GiftWheelPrize
            {
                GiftWheelId = wheelId,
                Name = request.Name,
                PrizeType = request.PrizeType,
                DiscountAmount = request.DiscountAmount,
                DiscountType = request.DiscountType,
                ValidityDays = request.ValidityDays,
                Probability = request.Probability,
                Color = request.Color,
                IsActive = request.IsActive,
                SortOrder = request.SortOrder,
                CreatedAt = DateTime.UtcNow
            };

            _context.GiftWheelPrizes.Add(prize);
            await _context.SaveChangesAsync();

            var prizeDto = new GiftWheelPrizeDto
            {
                Id = prize.Id,
                Name = prize.Name,
                PrizeType = prize.PrizeType,
                DiscountAmount = prize.DiscountAmount,
                DiscountType = prize.DiscountType,
                ValidityDays = prize.ValidityDays,
                Probability = prize.Probability,
                Color = prize.Color,
                IsActive = prize.IsActive,
                SortOrder = prize.SortOrder
            };

            _logger.LogInformation("Prize added to wheel {WheelId}: {PrizeName}", wheelId, request.Name);
            return (true, "Ödül başarıyla eklendi.", prizeDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error adding prize to wheel {WheelId}", wheelId);
            return (false, "Ödül eklenirken hata oluştu.", null);
        }
    }

    public async Task<(bool Success, string Message)> UpdatePrizeAsync(int prizeId, GiftWheelPrizeRequest request)
    {
        try
        {
            var prize = await _context.GiftWheelPrizes.FindAsync(prizeId);
            if (prize == null)
            {
                return (false, "Ödül bulunamadı.");
            }

            // Validate probability doesn't exceed 100% total (excluding current prize)
            var totalProbability = await _context.GiftWheelPrizes
                .Where(p => p.GiftWheelId == prize.GiftWheelId && p.IsActive && p.Id != prizeId)
                .SumAsync(p => p.Probability);

            if (totalProbability + request.Probability > 100)
            {
                return (false, $"Toplam olasılık %100'ü geçemez. Diğer ödüllerin toplamı: %{totalProbability}, Yeni değer: %{request.Probability}");
            }

            prize.Name = request.Name;
            prize.PrizeType = request.PrizeType;
            prize.DiscountAmount = request.DiscountAmount;
            prize.DiscountType = request.DiscountType;
            prize.ValidityDays = request.ValidityDays;
            prize.Probability = request.Probability;
            prize.Color = request.Color;
            prize.IsActive = request.IsActive;
            prize.SortOrder = request.SortOrder;

            await _context.SaveChangesAsync();
            
            _logger.LogInformation("Prize updated: {PrizeId} - {PrizeName}", prizeId, request.Name);
            return (true, "Ödül başarıyla güncellendi.");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating prize {PrizeId}", prizeId);
            return (false, "Ödül güncellenirken hata oluştu.");
        }
    }

    public async Task<(bool Success, string Message)> DeletePrizeAsync(int prizeId)
    {
        try
        {
            var prize = await _context.GiftWheelPrizes.FindAsync(prizeId);
            if (prize == null)
            {
                return (false, "Ödül bulunamadı.");
            }

            _context.GiftWheelPrizes.Remove(prize);
            await _context.SaveChangesAsync();
            
            _logger.LogInformation("Prize deleted: {PrizeId} - {PrizeName}", prizeId, prize.Name);
            return (true, "Ödül başarıyla silindi.");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting prize {PrizeId}", prizeId);
            return (false, "Ödül silinirken hata oluştu.");
        }
    }

    public async Task<(bool Success, string Message)> ReorderPrizesAsync(int wheelId, Dictionary<int, int> prizeOrders)
    {
        try
        {
            foreach (var kvp in prizeOrders)
            {
                var prize = await _context.GiftWheelPrizes.FindAsync(kvp.Key);
                if (prize != null && prize.GiftWheelId == wheelId)
                {
                    prize.SortOrder = kvp.Value;
                }
            }

            await _context.SaveChangesAsync();

            _logger.LogInformation("Prizes reordered for wheel {WheelId}", wheelId);
            return (true, "Ödül sıralaması güncellendi.");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error reordering prizes for wheel {WheelId}", wheelId);
            return (false, "Sıralama güncellenirken hata oluştu.");
        }
    }

    /// <summary>
    /// Toggles the status of a specific prize
    /// </summary>
    public async Task<(bool Success, string Message)> TogglePrizeStatusAsync(int prizeId, bool isActive)
    {
        try
        {
            var prize = await _context.GiftWheelPrizes.FindAsync(prizeId);
            if (prize == null)
            {
                return (false, "Ödül bulunamadı.");
            }

            prize.IsActive = isActive;
            await _context.SaveChangesAsync();

            var statusText = isActive ? "aktif" : "pasif";
            _logger.LogInformation("Prize {PrizeId} status changed to {Status}", prizeId, statusText);
            return (true, $"Ödül {statusText} duruma getirildi.");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error toggling prize status for prize {PrizeId}", prizeId);
            return (false, "Ödül durumu değiştirilirken hata oluştu.");
        }
    }

    /// <summary>
    /// Toggles the status of all prizes in a wheel
    /// </summary>
    public async Task<(bool Success, string Message)> ToggleAllPrizesAsync(int wheelId, bool isActive)
    {
        try
        {
            var wheel = await _context.GiftWheels
                .Include(w => w.Prizes)
                .FirstOrDefaultAsync(w => w.Id == wheelId);

            if (wheel == null)
            {
                return (false, "Çark bulunamadı.");
            }

            foreach (var prize in wheel.Prizes)
            {
                prize.IsActive = isActive;
            }

            await _context.SaveChangesAsync();

            var statusText = isActive ? "aktif" : "pasif";
            _logger.LogInformation("All prizes in wheel {WheelId} status changed to {Status}", wheelId, statusText);
            return (true, $"Tüm ödüller {statusText} duruma getirildi.");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error toggling all prizes status for wheel {WheelId}", wheelId);
            return (false, "Ödül durumları değiştirilirken hata oluştu.");
        }
    }

    #endregion

    #region Settings Management

    public async Task<GiftWheelSettingsDto?> GetWheelSettingsAsync(int wheelId)
    {
        try
        {
            var settings = await _context.GiftWheelSettings
                .FirstOrDefaultAsync(s => s.GiftWheelId == wheelId);

            if (settings == null)
            {
                return null;
            }

            return new GiftWheelSettingsDto
            {
                WheelId = wheelId,
                WheelTitle = settings.WheelTitle,
                WheelSubtitle = settings.WheelSubtitle,
                ButtonText = settings.ButtonText,
                WinMessage = settings.WinMessage,
                LoseMessage = settings.LoseMessage,
                MaxSpinsPerDay = settings.MaxSpinsPerDay,
                RequirePhone = settings.RequirePhone,
                RequireEmail = settings.RequireEmail,
                NotificationTemplate = settings.NotificationTemplate,
                PrimaryColor = settings.PrimaryColor,
                SecondaryColor = settings.SecondaryColor,
                WheelSize = settings.WheelSize,
                ShowConfetti = settings.ShowConfetti
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting settings for wheel {WheelId}", wheelId);
            return null;
        }
    }

    public async Task<(bool Success, string Message)> UpdateWheelSettingsAsync(int wheelId, UpdateGiftWheelSettingsRequest request, string userId)
    {
        try
        {
            var settings = await _context.GiftWheelSettings
                .FirstOrDefaultAsync(s => s.GiftWheelId == wheelId);

            if (settings == null)
            {
                settings = new GiftWheelSettings
                {
                    GiftWheelId = wheelId,
                    UpdatedByUserId = userId
                };
                _context.GiftWheelSettings.Add(settings);
            }

            settings.WheelTitle = request.WheelTitle;
            settings.WheelSubtitle = request.WheelSubtitle;
            settings.ButtonText = request.ButtonText;
            settings.WinMessage = request.WinMessage;
            settings.LoseMessage = request.LoseMessage;
            settings.MaxSpinsPerDay = request.MaxSpinsPerDay;
            settings.RequirePhone = request.RequirePhone;
            settings.RequireEmail = request.RequireEmail;
            settings.NotificationTemplate = request.NotificationTemplate;
            settings.PrimaryColor = request.PrimaryColor;
            settings.SecondaryColor = request.SecondaryColor;
            settings.WheelSize = request.WheelSize;
            settings.ShowConfetti = request.ShowConfetti;
            settings.UpdatedAt = DateTime.UtcNow;
            settings.UpdatedByUserId = userId;

            await _context.SaveChangesAsync();

            _logger.LogInformation("Wheel settings updated for wheel {WheelId}", wheelId);
            return (true, "Çark ayarları başarıyla güncellendi.");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating settings for wheel {WheelId}", wheelId);
            return (false, "Ayarlar güncellenirken hata oluştu.");
        }
    }

    #endregion

    #region Spin Processing

    public async Task<GiftWheelSpinResult> ProcessSpinAsync(Guid companyId, GiftWheelSpinRequest request, string ipAddress)
    {
        try
        {
            // Validate spin eligibility
            var (canSpin, message) = await ValidateSpinEligibilityAsync(companyId, request.CustomerPhone, ipAddress);
            if (!canSpin)
            {
                return new GiftWheelSpinResult
                {
                    Success = false,
                    Message = message,
                    ErrorCode = "SPIN_NOT_ALLOWED"
                };
            }

            // Get company wheel
            var wheel = await GetCompanyWheelAsync(companyId);
            if (wheel == null || !wheel.IsActive)
            {
                return new GiftWheelSpinResult
                {
                    Success = false,
                    Message = "Hediye çarkı bulunamadı veya aktif değil.",
                    ErrorCode = "WHEEL_NOT_FOUND"
                };
            }

            // Determine winning prize
            var winningPrize = await DetermineWinningPrizeAsync(wheel.Id);
            if (winningPrize == null)
            {
                return new GiftWheelSpinResult
                {
                    Success = false,
                    Message = "Ödül belirlenirken hata oluştu.",
                    ErrorCode = "PRIZE_DETERMINATION_ERROR"
                };
            }

            // Create spin record
            var spin = new Models.GiftWheelSpin
            {
                GiftWheelId = wheel.Id,
                CustomerName = request.CustomerName,
                CustomerPhone = request.CustomerPhone,
                CustomerEmail = request.CustomerEmail,
                PrizeId = winningPrize.Id,
                IpAddress = ipAddress,
                SpinDate = DateTime.UtcNow
            };

            _context.GiftWheelSpins.Add(spin);
            await _context.SaveChangesAsync();

            var result = new GiftWheelSpinResult
            {
                Success = true,
                Message = "Çark başarıyla çevrildi!",
                Prize = new GiftWheelPrizeDto
                {
                    Id = winningPrize.Id,
                    Name = winningPrize.Name,
                    PrizeType = winningPrize.PrizeType,
                    DiscountAmount = winningPrize.DiscountAmount,
                    DiscountType = winningPrize.DiscountType,
                    ValidityDays = winningPrize.ValidityDays,
                    Probability = winningPrize.Probability,
                    Color = winningPrize.Color,
                    IsActive = winningPrize.IsActive,
                    SortOrder = winningPrize.SortOrder
                }
            };

            // Process voucher creation if prize type is voucher
            if (winningPrize.PrizeType == "voucher" && winningPrize.DiscountAmount.HasValue)
            {
                var customer = new Customer
                {
                    FirstName = request.CustomerName.Split(' ').FirstOrDefault() ?? request.CustomerName,
                    LastName = request.CustomerName.Split(' ').Skip(1).FirstOrDefault() ?? "",
                    Phone = request.CustomerPhone,
                    Email = request.CustomerEmail ?? ""
                };

                var (voucherSuccess, voucherCode, voucherMessage) = await CreateGiftVoucherAsync(companyId, customer, winningPrize);

                if (voucherSuccess && !string.IsNullOrEmpty(voucherCode))
                {
                    spin.VoucherCode = voucherCode;
                    spin.VoucherCreated = true;
                    result.VoucherCode = voucherCode;
                }
                else
                {
                    spin.ErrorMessage = voucherMessage;
                    _logger.LogWarning("Voucher creation failed for spin {SpinId}: {Message}", spin.Id, voucherMessage);
                }
            }

            // Send notification
            var (notificationSuccess, notificationMessage) = await SendPrizeNotificationAsync(companyId, spin);
            spin.NotificationSent = notificationSuccess;

            if (!notificationSuccess)
            {
                spin.ErrorMessage = string.IsNullOrEmpty(spin.ErrorMessage)
                    ? notificationMessage
                    : $"{spin.ErrorMessage}; {notificationMessage}";
            }

            result.NotificationSent = notificationSuccess;

            await _context.SaveChangesAsync();

            _logger.LogInformation("Spin processed for company {CompanyId}, customer {CustomerName}, prize: {PrizeName}",
                companyId, request.CustomerName, winningPrize.Name);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing spin for company {CompanyId}", companyId);
            return new GiftWheelSpinResult
            {
                Success = false,
                Message = "Çark çevrilirken hata oluştu.",
                ErrorCode = "PROCESSING_ERROR"
            };
        }
    }

    #endregion

    #region Helper Methods

    public async Task<(bool CanSpin, string Message)> ValidateSpinEligibilityAsync(Guid companyId, string customerPhone, string ipAddress)
    {
        try
        {
            // Check if company has active module
            if (!await HasActiveModuleAsync(companyId))
            {
                return (false, "Hediye çarkı modülü aktif değil.");
            }

            var wheel = await GetCompanyWheelAsync(companyId);
            if (wheel?.Settings == null)
            {
                return (false, "Çark ayarları bulunamadı.");
            }

            // Check daily spin limit per phone number
            var today = DateTime.Today;
            var todaySpins = await _context.GiftWheelSpins
                .CountAsync(s => s.GiftWheelId == wheel.Id &&
                               s.CustomerPhone == customerPhone &&
                               s.SpinDate >= today);

            if (todaySpins >= wheel.Settings.MaxSpinsPerDay)
            {
                return (false, $"Günlük maksimum çevirme sayısına ulaştınız. ({wheel.Settings.MaxSpinsPerDay})");
            }

            // Check IP-based rate limiting (optional)
            var lastHour = DateTime.UtcNow.AddHours(-1);
            var recentSpinsFromIp = await _context.GiftWheelSpins
                .CountAsync(s => s.GiftWheelId == wheel.Id &&
                               s.IpAddress == ipAddress &&
                               s.SpinDate >= lastHour);

            if (recentSpinsFromIp >= 10) // Max 10 spins per hour per IP
            {
                return (false, "Çok fazla deneme yapıldı. Lütfen daha sonra tekrar deneyin.");
            }

            return (true, "Çevirme izni verildi.");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating spin eligibility for company {CompanyId}", companyId);
            return (false, "Doğrulama sırasında hata oluştu.");
        }
    }

    public async Task<GiftWheelPrize?> DetermineWinningPrizeAsync(int wheelId)
    {
        try
        {
            var prizes = await _context.GiftWheelPrizes
                .Where(p => p.GiftWheelId == wheelId && p.IsActive)
                .OrderBy(p => p.SortOrder)
                .ToListAsync();

            if (!prizes.Any())
            {
                return null;
            }

            // Calculate total probability
            var totalProbability = prizes.Sum(p => p.Probability);
            if (totalProbability <= 0)
            {
                return null;
            }

            // Generate random number
            var random = new Random();
            var randomValue = random.Next(1, totalProbability + 1);

            // Determine winning prize based on probability
            var currentSum = 0;
            foreach (var prize in prizes)
            {
                currentSum += prize.Probability;
                if (randomValue <= currentSum)
                {
                    return prize;
                }
            }

            // Fallback to first prize if something goes wrong
            return prizes.First();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error determining winning prize for wheel {WheelId}", wheelId);
            return null;
        }
    }

    public async Task<(bool Success, string? VoucherCode, string Message)> CreateGiftVoucherAsync(Guid companyId, Customer customer, GiftWheelPrize prize)
    {
        try
        {
            if (prize.PrizeType != "voucher" || !prize.DiscountAmount.HasValue)
            {
                return (false, null, "Bu ödül için hediye çeki oluşturulamaz.");
            }

            var giftVoucherSettings = new GiftVoucherSettings
            {
                Amount = (double)prize.DiscountAmount.Value,
                DiscountType = prize.DiscountType ?? 1,
                ValidityDays = prize.ValidityDays ?? 30,
                Description = prize.Name
            };

            var result = await _giftVoucherFactory.CreateGiftVoucherAsync(companyId, customer, giftVoucherSettings);

            if (result.Success && !string.IsNullOrEmpty(result.VoucherCode))
            {
                // Log usage cost
                await LogUsageAndDeductCostAsync(companyId, "voucher_creation", 2.00m,
                    $"Hediye çeki oluşturuldu: {result.VoucherCode}", "system", result.VoucherCode);

                return (true, result.VoucherCode, "Hediye çeki başarıyla oluşturuldu.");
            }

            return (false, null, result.ErrorMessage ?? "Hediye çeki oluşturulamadı.");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating gift voucher for company {CompanyId}", companyId);
            return (false, null, "Hediye çeki oluşturulurken hata oluştu.");
        }
    }

    public async Task<(bool Success, string Message)> SendPrizeNotificationAsync(Guid companyId, Models.GiftWheelSpin spin)
    {
        try
        {
            var wheel = await _context.GiftWheels
                .Include(w => w.Settings)
                .Include(w => w.Company)
                .FirstOrDefaultAsync(w => w.Id == spin.GiftWheelId);

            if (wheel?.Settings == null)
            {
                return (false, "Çark ayarları bulunamadı.");
            }

            var prize = await _context.GiftWheelPrizes.FindAsync(spin.PrizeId);
            if (prize == null)
            {
                return (false, "Ödül bilgisi bulunamadı.");
            }

            // Get WhatsApp notification channel
            var whatsAppChannel = await _notificationChannelFactory.GetChannelAsync(companyId, "whatsapp");
            if (whatsAppChannel == null)
            {
                return (false, "WhatsApp entegrasyonu bulunamadı.");
            }

            // Prepare notification variables
            var variables = new Dictionary<string, string>
            {
                ["name"] = spin.CustomerName,
                ["prize"] = prize.Name,
                ["siteUrl"] = wheel.Company?.Website ?? "https://example.com"
            };

            if (!string.IsNullOrEmpty(spin.VoucherCode))
            {
                variables["voucherCode"] = spin.VoucherCode;
            }

            // Create customer object for notification
            var customer = new Customer
            {
                FirstName = spin.CustomerName.Split(' ').FirstOrDefault() ?? spin.CustomerName,
                LastName = spin.CustomerName.Split(' ').Skip(1).FirstOrDefault() ?? "",
                Phone = spin.CustomerPhone,
                Email = spin.CustomerEmail ?? ""
            };

            // Send notification using template
            var success = await whatsAppChannel.SendNotificationWithCostTrackingAsync(
                companyId, customer, "gift_wheel_prize", variables, "system", null, spin.Id.ToString());

            if (success)
            {
                _logger.LogInformation("Prize notification sent for spin {SpinId}", spin.Id);
                return (true, "Bildirim başarıyla gönderildi.");
            }

            return (false, "Bildirim gönderilemedi.");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending prize notification for spin {SpinId}", spin.Id);
            return (false, "Bildirim gönderilirken hata oluştu.");
        }
    }

    public async Task<bool> HasActiveModuleAsync(Guid companyId)
    {
        try
        {
            var giftWheelModule = await _context.Modules
                .FirstOrDefaultAsync(m => m.Name == "Hediye Çarkı" && m.IsActive);

            if (giftWheelModule == null)
            {
                return false;
            }

            return await _context.CompanyModules
                .AnyAsync(cm => cm.CompanyId == companyId &&
                               cm.ModuleId == giftWheelModule.Id &&
                               cm.IsActive &&
                               (cm.ExpiresAt == null || cm.ExpiresAt > DateTime.UtcNow));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking active module for company {CompanyId}", companyId);
            return false;
        }
    }

    public async Task<(bool HasCredits, decimal RequiredAmount, decimal AvailableBalance)> CheckCreditsAsync(Guid companyId, decimal requiredAmount)
    {
        try
        {
            var company = await _context.Companies.FindAsync(companyId);
            if (company == null)
            {
                return (false, requiredAmount, 0);
            }

            return (company.CreditBalance >= requiredAmount, requiredAmount, company.CreditBalance);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking credits for company {CompanyId}", companyId);
            return (false, requiredAmount, 0);
        }
    }

    public async Task<(bool Success, string Message)> LogUsageAndDeductCostAsync(Guid companyId, string usageType, decimal cost, string description, string userId, string? referenceId = null)
    {
        try
        {
            return await _moduleUsageService.LogUsageAndDeductCostAsync(companyId, 0, usageType, cost, description, userId, referenceId, "gift_wheel");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error logging usage for company {CompanyId}", companyId);
            return (false, "Kullanım kaydedilirken hata oluştu.");
        }
    }

    #endregion

    #region Statistics and Reporting

    public async Task<GiftWheelStatsDto> GetWheelStatsAsync(Guid companyId, DateTime? startDate = null, DateTime? endDate = null)
    {
        try
        {
            var wheel = await GetCompanyWheelAsync(companyId);
            if (wheel == null)
            {
                return new GiftWheelStatsDto();
            }

            var query = _context.GiftWheelSpins.Where(s => s.GiftWheelId == wheel.Id);

            if (startDate.HasValue)
                query = query.Where(s => s.SpinDate >= startDate.Value);
            if (endDate.HasValue)
                query = query.Where(s => s.SpinDate <= endDate.Value);

            var spins = await query.ToListAsync();

            var today = DateTime.Today;
            var weekStart = today.AddDays(-(int)today.DayOfWeek);
            var monthStart = new DateTime(today.Year, today.Month, 1);

            var stats = new GiftWheelStatsDto
            {
                TotalSpins = spins.Count,
                TotalVouchersCreated = spins.Count(s => s.VoucherCreated),
                TotalNotificationsSent = spins.Count(s => s.NotificationSent),
                TotalCost = spins.Sum(s => s.Cost ?? 0),
                SpinsToday = spins.Count(s => s.SpinDate >= today),
                SpinsThisWeek = spins.Count(s => s.SpinDate >= weekStart),
                SpinsThisMonth = spins.Count(s => s.SpinDate >= monthStart)
            };

            // Prize statistics
            var prizes = await _context.GiftWheelPrizes
                .Where(p => p.GiftWheelId == wheel.Id)
                .ToListAsync();

            var prizeStats = prizes.Select(p => new PrizeStatsDto
            {
                PrizeId = p.Id,
                PrizeName = p.Name,
                TimesWon = spins.Count(s => s.PrizeId == p.Id),
                WinRate = spins.Count > 0 ? (decimal)spins.Count(s => s.PrizeId == p.Id) / spins.Count * 100 : 0
            }).ToList();

            stats.PrizeStats = prizeStats;

            // Daily statistics for last 30 days
            var dailyStats = spins
                .Where(s => s.SpinDate >= DateTime.Today.AddDays(-30))
                .GroupBy(s => s.SpinDate.Date)
                .Select(g => new DailySpinStatsDto
                {
                    Date = g.Key,
                    SpinCount = g.Count(),
                    VoucherCount = g.Count(s => s.VoucherCreated)
                })
                .OrderBy(d => d.Date)
                .ToList();

            stats.DailyStats = dailyStats;

            return stats;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting wheel stats for company {CompanyId}", companyId);
            return new GiftWheelStatsDto();
        }
    }

    public async Task<List<RecentSpinDto>> GetRecentSpinsAsync(Guid companyId, int limit = 50)
    {
        try
        {
            var wheel = await GetCompanyWheelAsync(companyId);
            if (wheel == null)
            {
                return new List<RecentSpinDto>();
            }

            var recentSpins = await _context.GiftWheelSpins
                .Where(s => s.GiftWheelId == wheel.Id)
                .Include(s => s.Prize)
                .OrderByDescending(s => s.SpinDate)
                .Take(limit)
                .Select(s => new RecentSpinDto
                {
                    Id = s.Id,
                    CustomerName = s.CustomerName,
                    CustomerPhone = s.CustomerPhone,
                    PrizeName = s.Prize.Name,
                    VoucherCode = s.VoucherCode,
                    VoucherCreated = s.VoucherCreated,
                    NotificationSent = s.NotificationSent,
                    SpinDate = s.SpinDate,
                    ErrorMessage = s.ErrorMessage
                })
                .ToListAsync();

            return recentSpins;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting recent spins for company {CompanyId}", companyId);
            return new List<RecentSpinDto>();
        }
    }

    public async Task<List<DailySpinStatsDto>> GetDailyStatsAsync(Guid companyId, int days = 30)
    {
        try
        {
            var wheel = await GetCompanyWheelAsync(companyId);
            if (wheel == null)
            {
                return new List<DailySpinStatsDto>();
            }

            var startDate = DateTime.Today.AddDays(-days);
            var spins = await _context.GiftWheelSpins
                .Where(s => s.GiftWheelId == wheel.Id && s.SpinDate >= startDate)
                .ToListAsync();

            var dailyStats = spins
                .GroupBy(s => s.SpinDate.Date)
                .Select(g => new DailySpinStatsDto
                {
                    Date = g.Key,
                    SpinCount = g.Count(),
                    VoucherCount = g.Count(s => s.VoucherCreated)
                })
                .OrderBy(d => d.Date)
                .ToList();

            return dailyStats;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting daily stats for company {CompanyId}", companyId);
            return new List<DailySpinStatsDto>();
        }
    }

    #endregion

    #region Script Generation

    public async Task<string> GenerateEmbedScriptAsync(Guid companyId)
    {
        try
        {
            var config = await GetWheelConfigAsync(companyId);
            if (config == null)
            {
                return "// Hediye çarkı yapılandırması bulunamadı";
            }

            var scriptTemplate = await GetEmbedScriptTemplateAsync();

            var configJson = JsonSerializer.Serialize(config, new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase
            });

            var script = scriptTemplate
                .Replace("{config}", configJson)
                .Replace("{apiBaseUrl}", "https://yourdomain.com") // This should come from configuration
                .Replace("{companyId}", companyId.ToString());

            return script;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating embed script for company {CompanyId}", companyId);
            return "// Script oluşturulurken hata oluştu";
        }
    }

    private async Task<string> GetEmbedScriptTemplateAsync()
    {
        return @"
(function() {
    'use strict';

    // Gift Wheel Configuration
    const WHEEL_CONFIG = {config};
    const API_BASE_URL = '{apiBaseUrl}';
    const COMPANY_ID = '{companyId}';

    // Gift Wheel Implementation
    class GiftWheel {
        constructor(containerId) {
            this.container = document.getElementById(containerId);
            if (!this.container) {
                console.error('Gift Wheel: Container element not found:', containerId);
                return;
            }

            this.canvas = null;
            this.ctx = null;
            this.isSpinning = false;
            this.currentRotation = 0;
            this.prizes = WHEEL_CONFIG.prizes || [];
            this.activePrizes = this.prizes.filter(p => p.isActive);

            this.init();
        }

        init() {
            this.createWheelHTML();
            this.setupCanvas();
            this.attachEventListeners();
            this.drawWheel();
            this.checkEligibility();
        }

        createWheelHTML() {
            const emailField = WHEEL_CONFIG.requireEmail ?
                `<input type='email' id='gw-customerEmail' placeholder='E-posta Adresiniz' class='gw-input' required>` : '';

            this.container.innerHTML = `
                <div class='gw-container'>
                    <div class='gw-header'>
                        <h2 class='gw-title'>${WHEEL_CONFIG.title}</h2>
                        <p class='gw-subtitle'>${WHEEL_CONFIG.subtitle}</p>
                    </div>

                    <div class='gw-wheel-section'>
                        <div class='gw-wheel-wrapper'>
                            <canvas id='gw-wheelCanvas' class='gw-canvas'></canvas>
                            <div class='gw-wheel-pointer'></div>
                            <div class='gw-wheel-center'></div>
                        </div>
                    </div>

                    <div class='gw-form-section'>
                        <div id='gw-customerForm' class='gw-form'>
                            <input type='text' id='gw-customerName' placeholder='Adınız' class='gw-input' required>
                            <input type='tel' id='gw-customerPhone' placeholder='Telefon Numaranız' class='gw-input' required>
                            ${emailField}
                        </div>

                        <button id='gw-spinButton' class='gw-spin-button' disabled>
                            <span class='gw-button-text'>${WHEEL_CONFIG.buttonText}</span>
                            <div class='gw-spinner hidden'>
                                <div class='gw-spinner-icon'></div>
                            </div>
                        </button>
                    </div>

                    <div id='gw-result' class='gw-result hidden'></div>
                    <div id='gw-error' class='gw-error hidden'></div>
                </div>
            `;

            this.injectStyles();
        }

        injectStyles() {
            if (document.getElementById('gw-styles')) return;

            const styles = document.createElement('style');
            styles.id = 'gw-styles';
            styles.textContent = `
                .gw-container {
                    max-width: 400px;
                    margin: 0 auto;
                    padding: 20px;
                    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                    text-align: center;
                    background: #ffffff;
                    border-radius: 16px;
                    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
                }

                .gw-header {
                    margin-bottom: 30px;
                }

                .gw-title {
                    font-size: 24px;
                    font-weight: bold;
                    color: #1f2937;
                    margin: 0 0 8px 0;
                }

                .gw-subtitle {
                    font-size: 16px;
                    color: #6b7280;
                    margin: 0;
                }

                .gw-wheel-section {
                    margin-bottom: 30px;
                }

                .gw-wheel-wrapper {
                    position: relative;
                    display: inline-block;
                }

                .gw-canvas {
                    border-radius: 50%;
                    box-shadow: 0 8px 32px rgba(0,0,0,0.2);
                    transition: transform 0.1s ease;
                }

                .gw-wheel-pointer {
                    position: absolute;
                    top: -8px;
                    left: 50%;
                    transform: translateX(-50%);
                    width: 0;
                    height: 0;
                    border-left: 12px solid transparent;
                    border-right: 12px solid transparent;
                    border-bottom: 20px solid #1f2937;
                    z-index: 10;
                }

                .gw-wheel-center {
                    position: absolute;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    width: 40px;
                    height: 40px;
                    background: #ffffff;
                    border: 3px solid #1f2937;
                    border-radius: 50%;
                    z-index: 10;
                    box-shadow: 0 2px 8px rgba(0,0,0,0.2);
                }

                .gw-form-section {
                    margin-bottom: 20px;
                }

                .gw-form {
                    display: flex;
                    flex-direction: column;
                    gap: 12px;
                    margin-bottom: 20px;
                }

                .gw-input {
                    padding: 12px 16px;
                    border: 2px solid #e5e7eb;
                    border-radius: 8px;
                    font-size: 16px;
                    transition: border-color 0.2s ease;
                    outline: none;
                }

                .gw-input:focus {
                    border-color: ${WHEEL_CONFIG.primaryColor};
                    box-shadow: 0 0 0 3px ${WHEEL_CONFIG.primaryColor}20;
                }

                .gw-input.error {
                    border-color: #ef4444;
                    box-shadow: 0 0 0 3px #ef444420;
                }

                .gw-spin-button {
                    background: linear-gradient(135deg, ${WHEEL_CONFIG.primaryColor}, ${WHEEL_CONFIG.primaryColor}dd);
                    color: white;
                    border: none;
                    padding: 16px 32px;
                    font-size: 18px;
                    font-weight: bold;
                    border-radius: 50px;
                    cursor: pointer;
                    transition: all 0.3s ease;
                    position: relative;
                    overflow: hidden;
                    min-width: 200px;
                    min-height: 56px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                }

                .gw-spin-button:hover:not(:disabled) {
                    transform: translateY(-2px);
                    box-shadow: 0 8px 25px rgba(0,0,0,0.2);
                }

                .gw-spin-button:disabled {
                    opacity: 0.6;
                    cursor: not-allowed;
                    transform: none;
                }

                .gw-spinner {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                }

                .gw-spinner-icon {
                    width: 20px;
                    height: 20px;
                    border: 2px solid #ffffff40;
                    border-top: 2px solid #ffffff;
                    border-radius: 50%;
                    animation: gw-spin 1s linear infinite;
                }

                @keyframes gw-spin {
                    0% { transform: rotate(0deg); }
                    100% { transform: rotate(360deg); }
                }

                .gw-result, .gw-error {
                    padding: 16px;
                    border-radius: 8px;
                    margin-top: 20px;
                    font-weight: 500;
                }

                .gw-result {
                    background: #dcfce7;
                    color: #166534;
                    border: 1px solid #bbf7d0;
                }

                .gw-error {
                    background: #fef2f2;
                    color: #dc2626;
                    border: 1px solid #fecaca;
                }

                .hidden {
                    display: none !important;
                }

                .gw-confetti {
                    position: fixed;
                    width: 10px;
                    height: 10px;
                    background: #ff6b6b;
                    z-index: 1000;
                    pointer-events: none;
                }

                @media (max-width: 480px) {
                    .gw-container {
                        padding: 16px;
                        margin: 10px;
                    }

                    .gw-title {
                        font-size: 20px;
                    }

                    .gw-subtitle {
                        font-size: 14px;
                    }

                    .gw-canvas {
                        width: 250px !important;
                        height: 250px !important;
                    }

                    .gw-spin-button {
                        font-size: 16px;
                        padding: 14px 28px;
                        min-width: 180px;
                    }
                }
            `;

            document.head.appendChild(styles);
        }

        setupCanvas() {
            this.canvas = document.getElementById('gw-wheelCanvas');
            if (!this.canvas) return;

            this.ctx = this.canvas.getContext('2d');

            // Set canvas size
            const size = Math.min(WHEEL_CONFIG.wheelSize || 300, 300);
            this.canvas.width = size;
            this.canvas.height = size;
            this.canvas.style.width = size + 'px';
            this.canvas.style.height = size + 'px';
        }

        drawWheel() {
            if (!this.ctx || this.activePrizes.length === 0) return;

            const centerX = this.canvas.width / 2;
            const centerY = this.canvas.height / 2;
            const radius = Math.min(centerX, centerY) - 5;

            // Clear canvas
            this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);

            // Calculate total probability
            const totalProbability = this.activePrizes.reduce((sum, prize) => sum + prize.probability, 0);
            if (totalProbability === 0) return;

            let currentAngle = this.currentRotation;

            // Draw prize segments
            this.activePrizes.forEach((prize, index) => {
                const segmentAngle = (prize.probability / totalProbability) * 2 * Math.PI;

                // Draw segment
                this.ctx.beginPath();
                this.ctx.moveTo(centerX, centerY);
                this.ctx.arc(centerX, centerY, radius, currentAngle, currentAngle + segmentAngle);
                this.ctx.closePath();

                // Fill with prize color
                this.ctx.fillStyle = prize.color || '#3B82F6';
                this.ctx.fill();

                // Add border
                this.ctx.strokeStyle = '#ffffff';
                this.ctx.lineWidth = 2;
                this.ctx.stroke();

                // Draw text
                this.drawPrizeText(centerX, centerY, radius, currentAngle, segmentAngle, prize.name);

                currentAngle += segmentAngle;
            });
        }

        drawPrizeText(centerX, centerY, radius, startAngle, segmentAngle, text) {
            const textAngle = startAngle + segmentAngle / 2;
            const textRadius = radius * 0.7;
            const textX = centerX + Math.cos(textAngle) * textRadius;
            const textY = centerY + Math.sin(textAngle) * textRadius;

            this.ctx.save();
            this.ctx.translate(textX, textY);
            this.ctx.rotate(textAngle + Math.PI / 2);

            // Text styling
            this.ctx.fillStyle = '#ffffff';
            this.ctx.font = 'bold 12px Arial';
            this.ctx.textAlign = 'center';
            this.ctx.textBaseline = 'middle';

            // Add text shadow for better readability
            this.ctx.shadowColor = 'rgba(0,0,0,0.5)';
            this.ctx.shadowBlur = 2;
            this.ctx.shadowOffsetX = 1;
            this.ctx.shadowOffsetY = 1;

            // Wrap text if too long
            const maxWidth = radius * 0.4;
            this.wrapText(text, 0, 0, maxWidth, 14);

            this.ctx.restore();
        }

        wrapText(text, x, y, maxWidth, lineHeight) {
            const words = text.split(' ');
            let line = '';
            let lines = [];

            for (let n = 0; n < words.length; n++) {
                const testLine = line + words[n] + ' ';
                const metrics = this.ctx.measureText(testLine);
                const testWidth = metrics.width;

                if (testWidth > maxWidth && n > 0) {
                    lines.push(line);
                    line = words[n] + ' ';
                } else {
                    line = testLine;
                }
            }
            lines.push(line);

            // Draw lines centered
            const startY = y - (lines.length - 1) * lineHeight / 2;
            lines.forEach((line, index) => {
                this.ctx.fillText(line, x, startY + index * lineHeight);
            });
        }

        attachEventListeners() {
            const spinButton = document.getElementById('gw-spinButton');
            const nameInput = document.getElementById('gw-customerName');
            const phoneInput = document.getElementById('gw-customerPhone');
            const emailInput = document.getElementById('gw-customerEmail');

            // Form validation
            const validateForm = () => {
                const name = nameInput.value.trim();
                const phone = phoneInput.value.trim();
                const email = emailInput ? emailInput.value.trim() : '';

                let isValid = true;

                // Clear previous errors
                [nameInput, phoneInput, emailInput].forEach(input => {
                    if (input) input.classList.remove('error');
                });

                if (!name) {
                    nameInput.classList.add('error');
                    isValid = false;
                }

                if (!phone) {
                    phoneInput.classList.add('error');
                    isValid = false;
                }

                if (emailInput && WHEEL_CONFIG.requireEmail && !email) {
                    emailInput.classList.add('error');
                    isValid = false;
                }

                spinButton.disabled = !isValid || this.isSpinning;
                return isValid;
            };

            // Add input listeners
            [nameInput, phoneInput, emailInput].forEach(input => {
                if (input) {
                    input.addEventListener('input', validateForm);
                    input.addEventListener('blur', validateForm);
                }
            });

            // Spin button click
            spinButton.addEventListener('click', () => this.handleSpin());

            // Initial validation
            validateForm();
        }

        async checkEligibility() {
            // Check if user can spin (optional pre-validation)
            const phoneInput = document.getElementById('gw-customerPhone');
            if (phoneInput && phoneInput.value.trim()) {
                try {
                    const response = await fetch(`${API_BASE_URL}/api/gift-wheel/${COMPANY_ID}/validate-spin`, {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ customerPhone: phoneInput.value.trim() })
                    });

                    const result = await response.json();
                    if (!result.canSpin) {
                        this.showError(result.message);
                        document.getElementById('gw-spinButton').disabled = true;
                    }
                } catch (error) {
                    // Ignore validation errors, allow spin attempt
                }
            }
        }

        async handleSpin() {
            if (this.isSpinning) return;

            const customerData = this.getCustomerData();
            if (!this.validateCustomerData(customerData)) return;

            this.setSpinningState(true);
            this.hideMessages();

            try {
                // Start wheel animation
                this.animateWheelSpin();

                // Make API call
                const response = await fetch(`${API_BASE_URL}/api/gift-wheel/${COMPANY_ID}/spin`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(customerData)
                });

                const result = await response.json();

                // Wait for animation to complete
                await this.waitForSpinAnimation();

                if (result.success) {
                    this.showWinResult(result);
                } else {
                    this.showError(result.message);
                }

            } catch (error) {
                console.error('Spin error:', error);
                this.showError('Bağlantı hatası. Lütfen tekrar deneyin.');
            } finally {
                this.setSpinningState(false);
            }
        }

        getCustomerData() {
            return {
                customerName: document.getElementById('gw-customerName').value.trim(),
                customerPhone: document.getElementById('gw-customerPhone').value.trim(),
                customerEmail: document.getElementById('gw-customerEmail')?.value.trim() || null
            };
        }

        validateCustomerData(data) {
            if (!data.customerName) {
                this.showError('Lütfen adınızı giriniz.');
                return false;
            }

            if (!data.customerPhone) {
                this.showError('Lütfen telefon numaranızı giriniz.');
                return false;
            }

            if (WHEEL_CONFIG.requireEmail && !data.customerEmail) {
                this.showError('Lütfen e-posta adresinizi giriniz.');
                return false;
            }

            return true;
        }

        setSpinningState(spinning) {
            this.isSpinning = spinning;
            const button = document.getElementById('gw-spinButton');
            const buttonText = button.querySelector('.gw-button-text');
            const spinner = button.querySelector('.gw-spinner');

            button.disabled = spinning;

            if (spinning) {
                buttonText.classList.add('hidden');
                spinner.classList.remove('hidden');
            } else {
                buttonText.classList.remove('hidden');
                spinner.classList.add('hidden');
            }
        }

        animateWheelSpin() {
            return new Promise((resolve) => {
                const duration = 3000; // 3 seconds
                const rotations = 5 + Math.random() * 3; // 5-8 full rotations
                const finalRotation = rotations * 2 * Math.PI;

                const startTime = Date.now();
                const startRotation = this.currentRotation;

                const animate = () => {
                    const elapsed = Date.now() - startTime;
                    const progress = Math.min(elapsed / duration, 1);

                    // Easing function (ease-out cubic)
                    const easeOut = 1 - Math.pow(1 - progress, 3);

                    this.currentRotation = startRotation + (finalRotation * easeOut);
                    this.drawWheel();

                    if (progress < 1) {
                        requestAnimationFrame(animate);
                    } else {
                        this.currentRotation = this.currentRotation % (2 * Math.PI);
                        resolve();
                    }
                };

                animate();
            });
        }

        waitForSpinAnimation() {
            return new Promise(resolve => setTimeout(resolve, 500)); // Small delay for effect
        }

        showWinResult(result) {
            const resultDiv = document.getElementById('gw-result');
            const prize = result.prize;

            let message = WHEEL_CONFIG.winMessage.replace('{prize}', prize.name);

            if (result.voucherCode) {
                message += `<br><strong>Hediye Çeki Kodu:</strong> ${result.voucherCode}`;
            }

            if (result.notificationSent) {
                message += '<br><small>WhatsApp bildirimi gönderildi.</small>';
            }

            resultDiv.innerHTML = `
                <div class='gw-result-icon'>🎉</div>
                <div class='gw-result-message'>${message}</div>
            `;

            resultDiv.classList.remove('hidden');

            // Show confetti if enabled and it's a voucher
            if (WHEEL_CONFIG.showConfetti && prize.prizeType === 'voucher') {
                this.showConfetti();
            }

            // Scroll to result
            resultDiv.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
        }

        showError(message) {
            const errorDiv = document.getElementById('gw-error');
            errorDiv.innerHTML = `
                <div class='gw-error-icon'>⚠️</div>
                <div class='gw-error-message'>${message}</div>
            `;
            errorDiv.classList.remove('hidden');

            // Scroll to error
            errorDiv.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
        }

        hideMessages() {
            document.getElementById('gw-result').classList.add('hidden');
            document.getElementById('gw-error').classList.add('hidden');
        }

        showConfetti() {
            const colors = ['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#feca57', '#ff9ff3', '#a8e6cf', '#ffd93d'];
            const confettiCount = 50;
            const container = this.container;

            for (let i = 0; i < confettiCount; i++) {
                setTimeout(() => {
                    this.createConfettiPiece(colors[Math.floor(Math.random() * colors.length)], container);
                }, i * 50); // Stagger the confetti
            }
        }

        createConfettiPiece(color, container) {
            const confetti = document.createElement('div');
            confetti.className = 'gw-confetti';
            confetti.style.backgroundColor = color;
            confetti.style.left = Math.random() * 100 + '%';
            confetti.style.top = '-10px';
            confetti.style.borderRadius = Math.random() > 0.5 ? '50%' : '0';

            // Random size
            const size = Math.random() * 8 + 4;
            confetti.style.width = size + 'px';
            confetti.style.height = size + 'px';

            container.appendChild(confetti);

            // Animate falling
            const containerRect = container.getBoundingClientRect();
            const fallDistance = containerRect.height + 100;
            const fallDuration = Math.random() * 2000 + 2000; // 2-4 seconds
            const horizontalDrift = (Math.random() - 0.5) * 100; // -50 to 50px drift

            const animation = confetti.animate([
                {
                    transform: 'translateY(0px) translateX(0px) rotate(0deg)',
                    opacity: 1
                },
                {
                    transform: `translateY(${fallDistance}px) translateX(${horizontalDrift}px) rotate(${Math.random() * 360}deg)`,
                    opacity: 0
                }
            ], {
                duration: fallDuration,
                easing: 'cubic-bezier(0.25, 0.46, 0.45, 0.94)'
            });

            animation.onfinish = () => {
                if (confetti.parentNode) {
                    confetti.parentNode.removeChild(confetti);
                }
            };
        }

        // Utility method to handle responsive canvas
        handleResize() {
            if (window.innerWidth <= 480) {
                const size = 250;
                this.canvas.width = size;
                this.canvas.height = size;
                this.canvas.style.width = size + 'px';
                this.canvas.style.height = size + 'px';
            } else {
                const size = Math.min(WHEEL_CONFIG.wheelSize || 300, 300);
                this.canvas.width = size;
                this.canvas.height = size;
                this.canvas.style.width = size + 'px';
                this.canvas.style.height = size + 'px';
            }
            this.drawWheel();
        }
    }

    // Auto-initialize when DOM is ready
    function initGiftWheel() {
        if (document.getElementById('gift-wheel-container')) {
            const wheel = new GiftWheel('gift-wheel-container');

            // Handle window resize
            let resizeTimeout;
            window.addEventListener('resize', () => {
                clearTimeout(resizeTimeout);
                resizeTimeout = setTimeout(() => {
                    if (wheel.handleResize) {
                        wheel.handleResize();
                    }
                }, 250);
            });

            return wheel;
        } else {
            console.warn('Gift Wheel: Container element #gift-wheel-container not found');
        }
    }

    // Initialize
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initGiftWheel);
    } else {
        initGiftWheel();
    }

    // Expose for debugging (optional)
    window.GiftWheel = GiftWheel;

})();";
        }

    public async Task<GiftWheelConfigDto?> GetWheelConfigAsync(Guid companyId)
    {
        try
        {
            var wheel = await GetCompanyWheelAsync(companyId);
            if (wheel?.Settings == null)
            {
                return null;
            }

            var prizes = await GetWheelPrizesAsync(wheel.Id);

            return new GiftWheelConfigDto
            {
                WheelId = wheel.Id,
                Title = wheel.Settings.WheelTitle,
                Subtitle = wheel.Settings.WheelSubtitle,
                ButtonText = wheel.Settings.ButtonText,
                WinMessage = wheel.Settings.WinMessage,
                LoseMessage = wheel.Settings.LoseMessage,
                RequirePhone = wheel.Settings.RequirePhone,
                RequireEmail = wheel.Settings.RequireEmail,
                PrimaryColor = wheel.Settings.PrimaryColor,
                SecondaryColor = wheel.Settings.SecondaryColor,
                WheelSize = wheel.Settings.WheelSize,
                ShowConfetti = wheel.Settings.ShowConfetti,
                Prizes = prizes
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting wheel config for company {CompanyId}", companyId);
            return null;
        }
    }

    #endregion

    #region R2 Export

    public async Task<string> ExportGiftWheelToR2Async(Guid companyId)
    {
        try
        {
            _logger.LogInformation("Starting R2 export for gift wheel. CompanyId: {CompanyId}", companyId);

            // Get wheel configuration
            var wheelConfig = await GetWheelConfigAsync(companyId);
            if (wheelConfig == null)
            {
                _logger.LogWarning("No wheel configuration found for company {CompanyId}", companyId);
                return "No wheel configuration found";
            }

            // Serialize configuration to JSON
            var jsonOptions = new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                WriteIndented = true,
                DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull
            };

            var jsonContent = JsonSerializer.Serialize(wheelConfig, jsonOptions);

            // Generate standalone script
            var scriptContent = GenerateStandaloneGiftWheelScript(companyId);

            // Upload JSON configuration
            var jsonFileName = $"{companyId}/giftWheel.json";
            var jsonUrl = await _r2StorageService.UploadJsonAsync(jsonFileName, jsonContent);

            // Upload standalone script
            var scriptFileName = $"{companyId}/giftWheel.js";
            var scriptUrl = await _r2StorageService.UploadJavaScriptAsync(scriptFileName, scriptContent);

            _logger.LogInformation("Successfully exported gift wheel to R2. CompanyId: {CompanyId}", companyId);
            return "Gift wheel exported successfully to R2";

        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error exporting gift wheel to R2. CompanyId: {CompanyId}", companyId);
            return $"Export failed: {ex.Message}";
        }
    }

    public string GenerateStandaloneGiftWheelScript(Guid companyId)
    {
        var timestamp = DateTime.UtcNow.ToString("yyyy-MM-dd HH:mm:ss UTC");
        var r2BaseUrl = _configuration["R2Storage:PublicUrl"];
        var apiBaseUrl = _configuration["ApiSettings:BaseUrl"] ?? "https://app.pushonica.com";

        return $@"/**
 * Pushonica Gift Wheel Module - Standalone Version
 * Company ID: {companyId}
 * Generated: {timestamp}
 *
 * This script provides a complete gift wheel implementation
 * that loads configuration from R2 storage and communicates
 * with the Pushonica API.
 */

(function() {{
    'use strict';

    // Configuration
    const COMPANY_ID = '{companyId}';
    const API_BASE_URL = '{apiBaseUrl}';
    const R2_BASE_URL = '{r2BaseUrl}';

    // Gift Wheel Module
    class PushonicaGiftWheel {{
        constructor() {{
            this.companyId = COMPANY_ID;
            this.apiBaseUrl = API_BASE_URL;
            this.r2BaseUrl = R2_BASE_URL;
            this.config = null;
            this.isInitialized = false;
            this.wheelContainer = null;
            this.canvas = null;
            this.ctx = null;
            this.isSpinning = false;
            this.currentRotation = 0;
            this.cookieKey = `pushonica_gift_wheel_${{this.companyId}}`;

            console.log('[GiftWheel] Standalone module loading for company:', this.companyId);
            this.init();
        }}

        async init() {{
            if (this.isInitialized) return;

            try {{
                // Load configuration from R2
                await this.loadConfigFromR2();

                if (!this.config) {{
                    console.log('[GiftWheel] No configuration found');
                    return;
                }}

                // Check eligibility
                const eligibility = await this.checkEligibility();
                if (!eligibility.canSpin) {{
                    console.log('[GiftWheel] Not eligible:', eligibility.message);
                    return;
                }}

                // Check cookie limits
                if (!this.canSpinBasedOnCookie()) {{
                    console.log('[GiftWheel] Cookie limit reached');
                    return;
                }}

                // Render wheel
                this.renderWheel();
                this.isInitialized = true;

                console.log('[GiftWheel] Standalone module initialized successfully');
            }} catch (error) {{
                console.error('[GiftWheel] Initialization failed:', error);
            }}
        }}

        async loadConfigFromR2() {{
            try {{
                const configUrl = `${{this.r2BaseUrl}}/${{this.companyId}}/giftWheel.json`;
                const response = await fetch(configUrl);

                if (!response.ok) {{
                    throw new Error(`HTTP ${{response.status}}`);
                }}

                this.config = await response.json();
                console.log('[GiftWheel] Config loaded from R2:', this.config);
            }} catch (error) {{
                console.warn('[GiftWheel] Failed to load config from R2:', error.message);

                // Fallback to API
                try {{
                    const apiResponse = await fetch(`${{this.apiBaseUrl}}/api/gift-wheel/${{this.companyId}}/config`);
                    if (apiResponse.ok) {{
                        this.config = await apiResponse.json();
                        console.log('[GiftWheel] Config loaded from API fallback');
                    }}
                }} catch (apiError) {{
                    console.warn('[GiftWheel] API fallback also failed:', apiError.message);
                    this.config = null;
                }}
            }}
        }}

        // Include the complete gift wheel implementation from the existing script
        // This would include all methods from gift-wheel-module.js
        // For brevity, showing key methods only

        async checkEligibility() {{
            try {{
                const response = await fetch(`${{this.apiBaseUrl}}/api/gift-wheel/${{this.companyId}}/eligibility`);
                if (!response.ok) throw new Error(`HTTP ${{response.status}}`);
                return await response.json();
            }} catch (error) {{
                return {{ canSpin: false, message: 'Eligibility check failed' }};
            }}
        }}

        canSpinBasedOnCookie() {{
            try {{
                const cookieData = this.getCookieData();
                if (!cookieData) return true;

                const today = new Date().toDateString();
                const lastSpinDate = new Date(cookieData.lastSpinDate).toDateString();

                if (today !== lastSpinDate) return true;

                const maxSpins = this.config?.maxSpinsPerDay || 1;
                return cookieData.spinCount < maxSpins;
            }} catch (error) {{
                return true;
            }}
        }}

        renderWheel() {{
            this.createFloatingButton();
        }}

        createFloatingButton() {{
            const existingButton = document.getElementById('pushonica-gift-wheel-btn');
            if (existingButton) existingButton.remove();

            const button = document.createElement('div');
            button.id = 'pushonica-gift-wheel-btn';
            button.innerHTML = `
                <div class=""wheel-icon"">🎁</div>
                <span class=""wheel-text"">${{this.config?.buttonText || 'Hediye Çarkı'}}</span>
            `;

            this.injectStyles();
            button.addEventListener('click', () => this.showWheelModal());
            document.body.appendChild(button);

            setTimeout(() => button.classList.add('show'), 1000);
        }}

        // Additional methods would be included here...
        // getCookieData, setCookie, showWheelModal, etc.
    }}

    // Auto-initialize when DOM is ready
    if (document.readyState === 'loading') {{
        document.addEventListener('DOMContentLoaded', () => {{
            window.pushonicaGiftWheel = new PushonicaGiftWheel();
        }});
    }} else {{
        window.pushonicaGiftWheel = new PushonicaGiftWheel();
    }}

}})();";
    }

    #endregion
}
