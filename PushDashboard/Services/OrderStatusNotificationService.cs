using Microsoft.EntityFrameworkCore;
using PushDashboard.Data;
using PushDashboard.Models;
using PushDashboard.Services.WhatsApp;
using System.Text.Json;
using System.Text.RegularExpressions;

namespace PushDashboard.Services;

public interface IOrderStatusNotificationService
{
    Task ProcessOrderStatusNotificationAsync(Guid companyId, OrderStatusChangeLog orderStatusChange);
    Task<bool> SendTestNotificationAsync(Guid companyId, string orderStatus, string testEmail, string? testPhone, string testOrderId, string? testCustomerName, List<string> testChannels);
}

public class OrderStatusNotificationService : IOrderStatusNotificationService
{
    private readonly ApplicationDbContext _context;
    private readonly IEmailService _emailService;
    private readonly IWhatsAppService _whatsAppService;
    private readonly ILogger<OrderStatusNotificationService> _logger;

    public OrderStatusNotificationService(
        ApplicationDbContext context,
        IEmailService emailService,
        IWhatsAppService whatsAppService,
        ILogger<OrderStatusNotificationService> logger)
    {
        _context = context;
        _emailService = emailService;
        _whatsAppService = whatsAppService;
        _logger = logger;
    }

    public async Task ProcessOrderStatusNotificationAsync(Guid companyId, OrderStatusChangeLog orderStatusChange)
    {
        try
        {
            _logger.LogInformation("Processing order status notification for company {CompanyId}, order {OrderId}, status {Status}",
                companyId, orderStatusChange.OrderId, orderStatusChange.NewStatus);

            // Bu sipariş durumu için aktif bildirim ayarları var mı kontrol et
            var notification = await _context.OrderStatusNotifications
                .FirstOrDefaultAsync(osn => osn.CompanyId == companyId &&
                                          osn.OrderStatus == orderStatusChange.NewStatus &&
                                          osn.IsActive);

            if (notification == null)
            {
                _logger.LogInformation("No active notification settings found for order status {Status} in company {CompanyId}",
                    orderStatusChange.NewStatus, companyId);
                return;
            }

            // Gecikme varsa bekle
            if (notification.DelayMinutes > 0)
            {
                _logger.LogInformation("Delaying notification for {DelayMinutes} minutes", notification.DelayMinutes);
                await Task.Delay(TimeSpan.FromMinutes(notification.DelayMinutes));
            }

            var sentChannels = new List<string>();
            var errors = new List<string>();

            // E-posta bildirimi
            if (notification.EmailNotificationEnabled && notification.EmailTemplateId.HasValue)
            {
                var emailResult = await SendEmailNotificationAsync(companyId, notification.EmailTemplateId.Value, orderStatusChange);
                if (emailResult.Success)
                {
                    sentChannels.Add("email");
                    _logger.LogInformation("Email notification sent successfully for order {OrderId}", orderStatusChange.OrderId);
                }
                else
                {
                    errors.Add($"Email: {emailResult.ErrorMessage}");
                    _logger.LogWarning("Email notification failed for order {OrderId}: {Error}", orderStatusChange.OrderId, emailResult.ErrorMessage);
                }
            }

            // SMS bildirimi
            if (notification.SmsNotificationEnabled && notification.SmsTemplateId.HasValue && !string.IsNullOrEmpty(orderStatusChange.CustomerPhone))
            {
                var smsResult = await SendSmsNotificationAsync(companyId, notification.SmsTemplateId.Value, orderStatusChange);
                if (smsResult.Success)
                {
                    sentChannels.Add("sms");
                    _logger.LogInformation("SMS notification sent successfully for order {OrderId}", orderStatusChange.OrderId);
                }
                else
                {
                    errors.Add($"SMS: {smsResult.ErrorMessage}");
                    _logger.LogWarning("SMS notification failed for order {OrderId}: {Error}", orderStatusChange.OrderId, smsResult.ErrorMessage);
                }
            }

            // WhatsApp bildirimi
            if (notification.WhatsAppNotificationEnabled && !string.IsNullOrWhiteSpace(notification.WhatsAppTemplateId) && !string.IsNullOrEmpty(orderStatusChange.CustomerPhone))
            {
                var whatsAppResult = await SendWhatsAppNotificationAsync(companyId, notification.WhatsAppTemplateId, orderStatusChange);
                if (whatsAppResult.Success)
                {
                    sentChannels.Add("whatsapp");
                    _logger.LogInformation("WhatsApp notification sent successfully for order {OrderId}", orderStatusChange.OrderId);
                }
                else
                {
                    errors.Add($"WhatsApp: {whatsAppResult.ErrorMessage}");
                    _logger.LogWarning("WhatsApp notification failed for order {OrderId}: {Error}", orderStatusChange.OrderId, whatsAppResult.ErrorMessage);
                }
            }

            // Sonuçları kaydet
            await UpdateNotificationResultAsync(orderStatusChange, notification, sentChannels, errors);

            _logger.LogInformation("Order status notification processing completed for order {OrderId}. Sent channels: {Channels}",
                orderStatusChange.OrderId, string.Join(", ", sentChannels));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing order status notification for company {CompanyId}, order {OrderId}",
                companyId, orderStatusChange.OrderId);
        }
    }

    public async Task<bool> SendTestNotificationAsync(Guid companyId, string orderStatus, string testEmail, string? testPhone, string testOrderId, string? testCustomerName, List<string> testChannels)
    {
        try
        {
            _logger.LogInformation("Sending test notification for company {CompanyId}, order status {OrderStatus}", companyId, orderStatus);

            // Test için sahte OrderStatusChangeLog oluştur
            var testOrderStatusChange = new OrderStatusChangeLog
            {
                CompanyId = companyId,
                OrderId = testOrderId,
                OrderNumber = $"TEST-{testOrderId}",
                CustomerEmail = testEmail,
                CustomerName = testCustomerName ?? "Test Müşteri",
                CustomerPhone = testPhone,
                NewStatus = orderStatus,
                NewStatusDisplayName = GetOrderStatusDisplayName(orderStatus),
                OrderAmount = 299.99m,
                OrderCurrency = "TRY",
                StatusChangedAt = DateTime.UtcNow
            };

            var notification = await _context.OrderStatusNotifications
                .FirstOrDefaultAsync(osn => osn.CompanyId == companyId && osn.OrderStatus == orderStatus);

            if (notification == null)
            {
                _logger.LogWarning("No notification settings found for test - order status {OrderStatus} in company {CompanyId}", orderStatus, companyId);
                return false;
            }

            var success = true;

            // Test kanallarına göre bildirim gönder
            if (testChannels.Contains("email") && notification.EmailNotificationEnabled && notification.EmailTemplateId.HasValue)
            {
                var emailResult = await SendEmailNotificationAsync(companyId, notification.EmailTemplateId.Value, testOrderStatusChange);
                if (!emailResult.Success)
                {
                    success = false;
                    _logger.LogWarning("Test email notification failed: {Error}", emailResult.ErrorMessage);
                }
            }

            if (testChannels.Contains("sms") && notification.SmsNotificationEnabled && notification.SmsTemplateId.HasValue && !string.IsNullOrEmpty(testPhone))
            {
                var smsResult = await SendSmsNotificationAsync(companyId, notification.SmsTemplateId.Value, testOrderStatusChange);
                if (!smsResult.Success)
                {
                    success = false;
                    _logger.LogWarning("Test SMS notification failed: {Error}", smsResult.ErrorMessage);
                }
            }

            if (testChannels.Contains("whatsapp") && notification.WhatsAppNotificationEnabled && !string.IsNullOrWhiteSpace(notification.WhatsAppTemplateId) && !string.IsNullOrEmpty(testPhone))
            {
                var whatsAppResult = await SendWhatsAppNotificationAsync(companyId, notification.WhatsAppTemplateId, testOrderStatusChange);
                if (!whatsAppResult.Success)
                {
                    success = false;
                    _logger.LogWarning("Test WhatsApp notification failed: {Error}", whatsAppResult.ErrorMessage);
                }
            }

            return success;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending test notification for company {CompanyId}, order status {OrderStatus}", companyId, orderStatus);
            return false;
        }
    }

    private async Task<NotificationResult> SendEmailNotificationAsync(Guid companyId, int templateId, OrderStatusChangeLog orderStatusChange)
    {
        try
        {
            // Company email template'ını al
            var companyTemplate = await _context.CompanyEmailTemplates
                .Include(cet => cet.EmailTemplate)
                .FirstOrDefaultAsync(cet => cet.Id == templateId && cet.CompanyId == companyId && cet.IsEnabled);

            if (companyTemplate?.EmailTemplate == null)
            {
                return new NotificationResult { Success = false, ErrorMessage = "Email template bulunamadı." };
            }

            // Template değişkenlerini değiştir
            var subject = ReplaceTemplateVariables(companyTemplate.CustomSubject ?? companyTemplate.EmailTemplate.DefaultSubject ?? "", orderStatusChange);
            var body = ReplaceTemplateVariables(companyTemplate.CustomContent, orderStatusChange);

            // Email gönder
            var emailResult = await _emailService.SendEmailAsync(
                orderStatusChange.CustomerEmail,
                subject,
                body
            );

            return new NotificationResult
            {
                Success = emailResult,
                ErrorMessage = emailResult ? null : "Email gönderim hatası"
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending email notification for order {OrderId}", orderStatusChange.OrderId);
            return new NotificationResult { Success = false, ErrorMessage = ex.Message };
        }
    }

    private async Task<NotificationResult> SendSmsNotificationAsync(Guid companyId, int templateId, OrderStatusChangeLog orderStatusChange)
    {
        try
        {
            // SMS template sistemi henüz yok, şimdilik log'la
            var message = $"Merhaba {orderStatusChange.CustomerName}, {orderStatusChange.OrderNumber} numaralı siparişinizin durumu '{orderStatusChange.NewStatusDisplayName}' olarak güncellendi.";

            _logger.LogInformation("SMS notification would be sent to {Phone}: {Message}", orderStatusChange.CustomerPhone, message);

            // TODO: SMS servisi entegrasyonu yapılacak
            return new NotificationResult
            {
                Success = true,
                ErrorMessage = null
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending SMS notification for order {OrderId}", orderStatusChange.OrderId);
            return new NotificationResult { Success = false, ErrorMessage = ex.Message };
        }
    }

    private async Task<NotificationResult> SendWhatsAppNotificationAsync(Guid companyId, string templateId, OrderStatusChangeLog orderStatusChange)
    {
        try
        {
            // WhatsApp template sistemi henüz tam entegre değil, şimdilik log'la
            var message = $"Merhaba {orderStatusChange.CustomerName}, {orderStatusChange.OrderNumber} numaralı siparişinizin durumu '{orderStatusChange.NewStatusDisplayName}' olarak güncellendi.";

            _logger.LogInformation("WhatsApp notification would be sent to {Phone}: {Message}", orderStatusChange.CustomerPhone, message);

            // TODO: WhatsApp template servisi entegrasyonu yapılacak
            var whatsAppResult = new { Success = true, ErrorMessage = (string?)null };

            return new NotificationResult
            {
                Success = whatsAppResult.Success,
                ErrorMessage = whatsAppResult.Success ? null : whatsAppResult.ErrorMessage
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending WhatsApp notification for order {OrderId}", orderStatusChange.OrderId);
            return new NotificationResult { Success = false, ErrorMessage = ex.Message };
        }
    }

    private async Task UpdateNotificationResultAsync(OrderStatusChangeLog orderStatusChange, OrderStatusNotification notification, List<string> sentChannels, List<string> errors)
    {
        try
        {
            // OrderStatusChangeLog'u güncelle
            orderStatusChange.NotificationSent = sentChannels.Any();
            orderStatusChange.NotificationSentAt = sentChannels.Any() ? DateTime.UtcNow : null;
            orderStatusChange.NotificationChannels = sentChannels.Any() ? JsonSerializer.Serialize(sentChannels) : null;
            orderStatusChange.NotificationError = errors.Any() ? string.Join("; ", errors) : null;

            // OrderStatusNotification istatistiklerini güncelle
            if (sentChannels.Any())
            {
                notification.TotalNotificationsSent++;
                notification.LastNotificationAt = DateTime.UtcNow;
            }
            notification.UpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating notification result for order {OrderId}", orderStatusChange.OrderId);
        }
    }

    private string ReplaceTemplateVariables(string template, OrderStatusChangeLog orderStatusChange)
    {
        if (string.IsNullOrEmpty(template))
            return template;

        var result = template;

        // Sipariş bilgileri
        result = result.Replace("{{siparis_no}}", orderStatusChange.OrderNumber ?? orderStatusChange.OrderId);
        result = result.Replace("{{siparis_id}}", orderStatusChange.OrderId);
        result = result.Replace("{{durum}}", orderStatusChange.NewStatusDisplayName ?? orderStatusChange.NewStatus);
        result = result.Replace("{{eski_durum}}", GetOrderStatusDisplayName(orderStatusChange.OldStatus) ?? "");

        // Müşteri bilgileri
        result = result.Replace("{{musteri_adi}}", orderStatusChange.CustomerName ?? "");
        result = result.Replace("{{musteri_email}}", orderStatusChange.CustomerEmail);
        result = result.Replace("{{musteri_telefon}}", orderStatusChange.CustomerPhone ?? "");

        // Sipariş detayları
        result = result.Replace("{{tutar}}", orderStatusChange.OrderAmount?.ToString("C") ?? "");
        result = result.Replace("{{para_birimi}}", orderStatusChange.OrderCurrency ?? "TRY");

        // Tarih bilgileri
        result = result.Replace("{{tarih}}", orderStatusChange.StatusChangedAt.ToString("dd.MM.yyyy"));
        result = result.Replace("{{saat}}", orderStatusChange.StatusChangedAt.ToString("HH:mm"));
        result = result.Replace("{{tarih_saat}}", orderStatusChange.StatusChangedAt.ToString("dd.MM.yyyy HH:mm"));

        // Şirket bilgileri (ileride eklenebilir)
        result = result.Replace("{{sirket_adi}}", "Şirketiniz");

        return result;
    }



    private string? GetOrderStatusDisplayName(string? status)
    {
        if (string.IsNullOrEmpty(status))
            return null;

        return status switch
        {
            "1" => "Beklemede",
            "2" => "Onaylandı",
            "3" => "Hazırlanıyor",
            "4" => "Kargoya Verildi",
            "5" => "Teslim Edildi",
            "6" => "İptal Edildi",
            "7" => "İade Edildi",
            _ => status
        };
    }
}

public class NotificationResult
{
    public bool Success { get; set; }
    public string? ErrorMessage { get; set; }
}
