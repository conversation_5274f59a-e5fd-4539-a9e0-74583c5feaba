using PushDashboard.Models;
using System.Text.Json.Serialization;

namespace PushDashboard.Services.WhatsApp;

public interface IWhatsAppService
{
    /// <summary>
    /// Sends a WhatsApp message using Facebook Business API
    /// </summary>
    Task<WhatsAppResult> SendMessageAsync(WhatsAppMessageRequest request);

    /// <summary>
    /// Sends a WhatsApp template message using Facebook Business API
    /// </summary>
    Task<WhatsAppResult> SendTemplateMessageAsync(WhatsAppTemplateMessageRequest request);

    /// <summary>
    /// Tests the WhatsApp Business API connection
    /// </summary>
    Task<WhatsAppResult> TestConnectionAsync(WhatsAppConnectionSettings settings);

    /// <summary>
    /// Gets available message templates from Facebook
    /// </summary>
    Task<WhatsAppResult<List<FacebookMessageTemplate>>> GetMessageTemplatesAsync(WhatsAppConnectionSettings settings);

    /// <summary>
    /// Creates a new message template in Facebook
    /// </summary>
    Task<WhatsAppResult<FacebookMessageTemplate>> CreateMessageTemplateAsync(WhatsAppConnectionSettings settings, CreateTemplateRequest request);

    /// <summary>
    /// Updates an existing message template in Facebook
    /// </summary>
    Task<WhatsAppResult> UpdateMessageTemplateAsync(WhatsAppConnectionSettings settings, string templateId, UpdateTemplateRequest request);

    /// <summary>
    /// Deletes a message template from Facebook
    /// </summary>
    Task<WhatsAppResult> DeleteMessageTemplateAsync(WhatsAppConnectionSettings settings, string templateName);

    /// <summary>
    /// Gets a specific message template from Facebook
    /// </summary>
    Task<WhatsAppResult<FacebookMessageTemplate>> GetMessageTemplateAsync(WhatsAppConnectionSettings settings, string templateId);

    /// <summary>
    /// Validates phone number format for WhatsApp
    /// </summary>
    bool IsValidPhoneNumber(string phoneNumber);
}

public class WhatsAppResult
{
    public bool Success { get; set; }
    public string? Message { get; set; }
    public string? ErrorCode { get; set; }
    public Dictionary<string, object>? AdditionalInfo { get; set; }

    public static WhatsAppResult CreateSuccess(string? message = null)
    {
        return new WhatsAppResult { Success = true, Message = message };
    }

    public static WhatsAppResult CreateFailure(string message, string? errorCode = null)
    {
        return new WhatsAppResult { Success = false, Message = message, ErrorCode = errorCode };
    }
}

public class WhatsAppResult<T> : WhatsAppResult
{
    public T? Data { get; set; }

    public static WhatsAppResult<T> CreateSuccess(T data, string? message = null)
    {
        return new WhatsAppResult<T> { Success = true, Data = data, Message = message };
    }

    public static new WhatsAppResult<T> CreateFailure(string message, string? errorCode = null)
    {
        return new WhatsAppResult<T> { Success = false, Message = message, ErrorCode = errorCode };
    }
}

public class WhatsAppMessageRequest
{
    public string ToPhoneNumber { get; set; } = string.Empty;
    public string Message { get; set; } = string.Empty;
    public string? TemplateType { get; set; } = "TEXT";
    public string? MediaUrl { get; set; }
    public string? ButtonText { get; set; }
    public string? ButtonUrl { get; set; }
    public WhatsAppConnectionSettings ConnectionSettings { get; set; } = new();
}

public class WhatsAppConnectionSettings
{
    public string AccessToken { get; set; } = string.Empty;
    public string BusinessAccountId { get; set; } = string.Empty;
    public string PhoneNumberId { get; set; } = string.Empty;
}

public class FacebookMessageTemplate
{
    [JsonPropertyName("id")]
    public string Id { get; set; } = string.Empty;

    [JsonPropertyName("name")]
    public string Name { get; set; } = string.Empty;

    [JsonPropertyName("status")]
    public string Status { get; set; } = string.Empty; // APPROVED, PENDING, REJECTED, DISABLED

    [JsonPropertyName("category")]
    public string Category { get; set; } = string.Empty; // MARKETING, UTILITY, AUTHENTICATION

    [JsonPropertyName("language")]
    public string Language { get; set; } = string.Empty;

    [JsonPropertyName("components")]
    public List<TemplateComponent>? Components { get; set; }

    [JsonPropertyName("qualityScore")]
    public int QualityScore { get; set; }
}

public class TemplateComponent
{
    [JsonPropertyName("type")]
    public string Type { get; set; } = string.Empty; // HEADER, BODY, FOOTER, BUTTONS

    [JsonPropertyName("text")]
    public string? Text { get; set; }

    [JsonPropertyName("format")]
    public string? Format { get; set; } // TEXT, IMAGE, VIDEO, DOCUMENT

    //[JsonPropertyName("example")]
    //public string? Example { get; set; }

    [JsonPropertyName("parameters")]
    public List<TemplateParameter>? Parameters { get; set; }

    [JsonPropertyName("buttons")]
    public List<TemplateButton>? Buttons { get; set; }
}

public class TemplateParameter
{
    [JsonPropertyName("type")]
    public string Type { get; set; } = string.Empty; // TEXT

    [JsonPropertyName("text")]
    public string? Text { get; set; }
}

public class TemplateButton
{
    [JsonPropertyName("type")]
    public string Type { get; set; } = string.Empty; // QUICK_REPLY, PHONE_NUMBER, URL

    [JsonPropertyName("text")]
    public string Text { get; set; } = string.Empty;

    [JsonPropertyName("url")]
    public string? Url { get; set; }

    [JsonPropertyName("phone_number")]
    public string? PhoneNumber { get; set; }
}

public class CreateTemplateRequest
{
    public string Name { get; set; } = string.Empty;
    public string Category { get; set; } = "MARKETING";
    public string Language { get; set; } = "tr";
    public List<TemplateComponent> Components { get; set; } = new();
}

public class UpdateTemplateRequest
{
    public List<TemplateComponent> Components { get; set; } = new();
}

// WhatsApp template message için yeni request sınıfları
public class WhatsAppTemplateMessageRequest
{
    public string ToPhoneNumber { get; set; } = string.Empty;
    public string TemplateName { get; set; } = string.Empty;
    public string TemplateLanguage { get; set; } = "tr";
    public List<WhatsAppTemplateComponent> Components { get; set; } = new();
    public WhatsAppConnectionSettings ConnectionSettings { get; set; } = new();
}

public class WhatsAppTemplateComponent
{
    public string Type { get; set; } = string.Empty;
    public List<WhatsAppTemplateParameter> Parameters { get; set; } = new();
}

public class WhatsAppTemplateParameter
{
    public string Type { get; set; } = "text";
    public string Text { get; set; } = string.Empty;
}
