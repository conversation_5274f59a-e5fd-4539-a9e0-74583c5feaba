using System.Text;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Text.RegularExpressions;

namespace PushDashboard.Services.WhatsApp;

public class WhatsAppService : IWhatsAppService
{
    private readonly HttpClient _httpClient;
    private readonly ILogger<WhatsAppService> _logger;
    private const string FacebookApiBaseUrl = "https://graph.facebook.com/v21.0";

    public WhatsAppService(HttpClient httpClient, ILogger<WhatsAppService> logger)
    {
        _httpClient = httpClient;
        _logger = logger;
    }

    public async Task<WhatsAppResult> SendMessageAsync(WhatsAppMessageRequest request)
    {
        try
        {
            if (!IsValidPhoneNumber(request.ToPhoneNumber))
            {
                return WhatsAppResult.CreateFailure("Geçersiz telefon numarası formatı.");
            }

            var messagePayload = CreateMessagePayload(request);
            var json = JsonSerializer.Serialize(messagePayload);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            var url = $"{FacebookApiBaseUrl}/{request.ConnectionSettings.PhoneNumberId}/messages";

            _httpClient.DefaultRequestHeaders.Clear();
            _httpClient.DefaultRequestHeaders.Add("Authorization", $"Bearer {request.ConnectionSettings.AccessToken}");

            var response = await _httpClient.PostAsync(url, content);
            var responseContent = await response.Content.ReadAsStringAsync();

            _logger.LogInformation("Delete template response: Status={StatusCode}, Content={Content}",
                response.StatusCode, responseContent);
            if (response.IsSuccessStatusCode)
            {
                _logger.LogInformation("WhatsApp message sent successfully to {PhoneNumber}", request.ToPhoneNumber);
                return WhatsAppResult.CreateSuccess("WhatsApp mesajı başarıyla gönderildi.");
            }
            else
            {
                _logger.LogError("Failed to send WhatsApp message. Status: {StatusCode}, Response: {Response}",
                    response.StatusCode, responseContent);

                var errorInfo = ParseFacebookError(responseContent);
                return WhatsAppResult.CreateFailure(
                    $"WhatsApp mesajı gönderilemedi: {errorInfo.Message}",
                    errorInfo.Code);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending WhatsApp message to {PhoneNumber}", request.ToPhoneNumber);
            return WhatsAppResult.CreateFailure("WhatsApp mesajı gönderilirken hata oluştu: " + ex.Message);
        }
    }

    public async Task<WhatsAppResult> SendTemplateMessageAsync(WhatsAppTemplateMessageRequest request)
    {
        try
        {
            if (!IsValidPhoneNumber(request.ToPhoneNumber))
            {
                return WhatsAppResult.CreateFailure("Geçersiz telefon numarası formatı.");
            }

            var messagePayload = CreateTemplateMessagePayload(request);
            var json = JsonSerializer.Serialize(messagePayload, new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                WriteIndented = true
            });

            _logger.LogInformation("Sending WhatsApp template message: {Json}", json);

            var content = new StringContent(json, Encoding.UTF8, "application/json");
            var url = $"{FacebookApiBaseUrl}/{request.ConnectionSettings.PhoneNumberId}/messages";

            _httpClient.DefaultRequestHeaders.Clear();
            _httpClient.DefaultRequestHeaders.Add("Authorization", $"Bearer {request.ConnectionSettings.AccessToken}");

            var response = await _httpClient.PostAsync(url, content);
            var responseContent = await response.Content.ReadAsStringAsync();

            _logger.LogInformation("WhatsApp template message response: Status={StatusCode}, Content={Content}",
                response.StatusCode, responseContent);

            if (response.IsSuccessStatusCode)
            {
                _logger.LogInformation("WhatsApp template message sent successfully to {PhoneNumber}", request.ToPhoneNumber);
                return WhatsAppResult.CreateSuccess("WhatsApp şablon mesajı başarıyla gönderildi.");
            }
            else
            {
                _logger.LogError("Failed to send WhatsApp template message. Status: {StatusCode}, Response: {Response}",
                    response.StatusCode, responseContent);

                var errorInfo = ParseFacebookError(responseContent);
                return WhatsAppResult.CreateFailure(
                    $"WhatsApp şablon mesajı gönderilemedi: {errorInfo.Message}",
                    errorInfo.Code);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending WhatsApp template message to {PhoneNumber}", request.ToPhoneNumber);
            return WhatsAppResult.CreateFailure("WhatsApp şablon mesajı gönderilirken hata oluştu: " + ex.Message);
        }
    }

    public async Task<WhatsAppResult> TestConnectionAsync(WhatsAppConnectionSettings settings)
    {
        try
        {
            var url = $"{FacebookApiBaseUrl}/{settings.BusinessAccountId}/phone_numbers";

            _httpClient.DefaultRequestHeaders.Clear();
            _httpClient.DefaultRequestHeaders.Add("Authorization", $"Bearer {settings.AccessToken}");

            var response = await _httpClient.GetAsync(url);
            var responseContent = await response.Content.ReadAsStringAsync();

            _logger.LogInformation("Delete template response: Status={StatusCode}, Content={Content}",
                response.StatusCode, responseContent);
            if (response.IsSuccessStatusCode)
            {
                var options = new JsonSerializerOptions
                {
                    PropertyNamingPolicy = JsonNamingPolicy.SnakeCaseLower
                };
                var phoneNumbers = JsonSerializer.Deserialize<FacebookPhoneNumbersResponse>(responseContent, options);
                if (phoneNumbers?.Data?.Any() == true)
                {
                    return WhatsAppResult.CreateSuccess("WhatsApp Business API bağlantısı başarılı!");
                }
                else
                {
                    return WhatsAppResult.CreateFailure("WhatsApp Business hesabında telefon numarası bulunamadı.");
                }
            }
            else
            {
                var errorInfo = ParseFacebookError(responseContent);
                return WhatsAppResult.CreateFailure(
                    $"WhatsApp Business API bağlantısı başarısız: {errorInfo.Message}",
                    errorInfo.Code);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error testing WhatsApp connection");
            return WhatsAppResult.CreateFailure("WhatsApp bağlantısı test edilirken hata oluştu: " + ex.Message);
        }
    }

    public async Task<WhatsAppResult<List<FacebookMessageTemplate>>> GetMessageTemplatesAsync(WhatsAppConnectionSettings settings)
    {
        try
        {
            var url = $"{FacebookApiBaseUrl}/{settings.BusinessAccountId}/message_templates?fields=id,name,status,category,language,components,quality_score,created_time,modified_time,rejection_reason";

            _httpClient.DefaultRequestHeaders.Clear();
            _httpClient.DefaultRequestHeaders.Add("Authorization", $"Bearer {settings.AccessToken}");

            var response = await _httpClient.GetAsync(url);
            var responseContent = await response.Content.ReadAsStringAsync();

            _logger.LogInformation("Delete template response: Status={StatusCode}, Content={Content}",
                response.StatusCode, responseContent);
            if (response.IsSuccessStatusCode)
            {
                var options = new JsonSerializerOptions
                {
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                };
                var templatesResponse = JsonSerializer.Deserialize<FacebookTemplatesResponse>(responseContent, options);
                return WhatsAppResult<List<FacebookMessageTemplate>>.CreateSuccess(
                    templatesResponse?.Data ?? new List<FacebookMessageTemplate>(),
                    "Şablonlar başarıyla alındı.");
            }
            else
            {
                var errorInfo = ParseFacebookError(responseContent);
                return WhatsAppResult<List<FacebookMessageTemplate>>.CreateFailure(
                    $"Şablonlar alınamadı: {errorInfo.Message}",
                    errorInfo.Code);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting WhatsApp templates");
            return WhatsAppResult<List<FacebookMessageTemplate>>.CreateFailure(
                "Şablonlar alınırken hata oluştu: " + ex.Message);
        }
    }

    public async Task<WhatsAppResult<FacebookMessageTemplate>> CreateMessageTemplateAsync(WhatsAppConnectionSettings settings, CreateTemplateRequest request)
    {
        try
        {
            // Prepare components according to WhatsApp Business Management API format
            var components = request.Components.Select(c => CreateApiComponent(c)).ToList();

            var templatePayload = new
            {
                name = request.Name,
                category = request.Category.ToUpper(),
                language = request.Language,
                components = components
            };

            var json = JsonSerializer.Serialize(templatePayload, new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                WriteIndented = true
            });

            // Debug: Log the JSON being sent to Facebook
            _logger.LogInformation("Sending template to Facebook API: {Json}", json);

            var content = new StringContent(json, Encoding.UTF8, "application/json");

            // Use Business Management API endpoint
            var url = $"{FacebookApiBaseUrl}/{settings.BusinessAccountId}/message_templates";

            _httpClient.DefaultRequestHeaders.Clear();
            _httpClient.DefaultRequestHeaders.Add("Authorization", $"Bearer {settings.AccessToken}");

            var response = await _httpClient.PostAsync(url, content);
            var responseContent = await response.Content.ReadAsStringAsync();

            // Debug: Log Facebook API response
            _logger.LogInformation("Facebook API Response Status: {StatusCode}", response.StatusCode);
            _logger.LogInformation("Facebook API Response Content: {Content}", responseContent);

            if (response.IsSuccessStatusCode)
            {
                var options = new JsonSerializerOptions
                {
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                };
                var createResponse = JsonSerializer.Deserialize<FacebookCreateTemplateResponse>(responseContent, options);
                if (createResponse?.Id != null)
                {
                    // Get the created template details
                    var templateResult = await GetMessageTemplateAsync(settings, createResponse.Id);
                    if (templateResult.Success && templateResult.Data != null)
                    {
                        return WhatsAppResult<FacebookMessageTemplate>.CreateSuccess(
                            templateResult.Data,
                            "Şablon başarıyla oluşturuldu ve onay için gönderildi.");
                    }
                }
                return WhatsAppResult<FacebookMessageTemplate>.CreateSuccess(
                    new FacebookMessageTemplate { Id = createResponse?.Id ?? "", Name = request.Name, Status = "PENDING" },
                    "Şablon başarıyla oluşturuldu ve onay için gönderildi.");
            }
            else
            {
                var errorInfo = ParseFacebookError(responseContent);
                return WhatsAppResult<FacebookMessageTemplate>.CreateFailure(
                    $"Şablon oluşturulamadı: {errorInfo.Message}",
                    errorInfo.Code);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating WhatsApp template");
            return WhatsAppResult<FacebookMessageTemplate>.CreateFailure("Şablon oluşturulurken hata oluştu: " + ex.Message);
        }
    }

    private object CreateApiComponent(TemplateComponent component)
    {
        var apiComponent = new Dictionary<string, object>
        {
            ["type"] = component.Type.ToUpper()
        };

        switch (component.Type.ToUpper())
        {
            case "HEADER":
                if (component.Format == "TEXT")
                {
                    apiComponent["text"] = component.Text ?? "";
                    apiComponent["format"] = component.Format?.ToUpper();
                }
                else
                {
                    apiComponent["format"] = component.Format?.ToUpper();
                    if (!string.IsNullOrEmpty(component.Text))
                    {
                        apiComponent["example"] = new
                        {
                            header_handle = new[] { component.Text }
                        };
                    }
                }
                break;

            case "BODY":
                apiComponent["text"] = component.Text ?? "";

                // Extract variables and create example
                var variables = ExtractVariablesFromText(component.Text ?? "");
                if (variables.Any())
                {
                    apiComponent["example"] = new
                    {
                        body_text = new[] { variables.Select(v => $"sample_value_{v}").ToArray() }
                    };
                }
                break;

            case "FOOTER":
                apiComponent["text"] = component.Text ?? "";
                break;

            case "BUTTONS":
                if (component.Buttons?.Any() == true)
                {
                    apiComponent["buttons"] = component.Buttons.Select(CreateApiButton).ToArray();
                }
                break;
        }

        return apiComponent;
    }

    private object CreateApiButton(TemplateButton button)
    {
        var apiButton = new Dictionary<string, object>
        {
            ["type"] = button.Type.ToUpper(),
            ["text"] = button.Text
        };

        switch (button.Type.ToUpper())
        {
            case "URL":
                if (!string.IsNullOrEmpty(button.Url))
                {
                    apiButton["url"] = button.Url;
                    // If URL contains variables, add example
                    if (button.Url.Contains("{{"))
                    {
                        apiButton["example"] = new[] { "https://example.com/sample" };
                    }
                }
                break;

            case "PHONE_NUMBER":
                if (!string.IsNullOrEmpty(button.PhoneNumber))
                {
                    apiButton["phone_number"] = button.PhoneNumber;
                }
                break;

            case "QUICK_REPLY":
                // Quick reply buttons don't need additional properties
                break;
        }

        return apiButton;
    }

    private List<int> ExtractVariablesFromText(string text)
    {
        var variables = new List<int>();
        var regex = new Regex(@"\{\{(\d+)\}\}");
        var matches = regex.Matches(text);

        foreach (Match match in matches)
        {
            if (int.TryParse(match.Groups[1].Value, out int varNumber))
            {
                if (!variables.Contains(varNumber))
                {
                    variables.Add(varNumber);
                }
            }
        }

        return variables.OrderBy(v => v).ToList();
    }

    public async Task<WhatsAppResult> UpdateMessageTemplateAsync(WhatsAppConnectionSettings settings, string templateId, UpdateTemplateRequest request)
    {
        try
        {
            // Note: WhatsApp Business Management API doesn't support direct template updates
            // Templates need to be deleted and recreated for major changes
            // For minor changes, you can use the edit endpoint (if available)

            _logger.LogWarning("Template update attempted for {TemplateId}. WhatsApp templates cannot be directly updated - consider delete and recreate workflow.", templateId);

            return WhatsAppResult.CreateFailure(
                "WhatsApp şablonları doğrudan güncellenemez. Şablonu silip yeniden oluşturmanız gerekebilir.",
                "UPDATE_NOT_SUPPORTED");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating WhatsApp template {TemplateId}", templateId);
            return WhatsAppResult.CreateFailure("Şablon güncellenirken hata oluştu: " + ex.Message);
        }
    }

    public async Task<WhatsAppResult> DeleteMessageTemplateAsync(WhatsAppConnectionSettings settings, string templateName)
    {
        try
        {
            var url = $"{FacebookApiBaseUrl}/{settings.BusinessAccountId}/message_templates?name={templateName}";

            _httpClient.DefaultRequestHeaders.Clear();
            _httpClient.DefaultRequestHeaders.Add("Authorization", $"Bearer {settings.AccessToken}");

            var response = await _httpClient.DeleteAsync(url);
            var responseContent = await response.Content.ReadAsStringAsync();

            _logger.LogInformation("Delete template response: Status={StatusCode}, Content={Content}",
                response.StatusCode, responseContent);
            if (response.IsSuccessStatusCode)
            {
                return WhatsAppResult.CreateSuccess("Şablon başarıyla silindi.");
            }
            else
            {
                var errorInfo = ParseFacebookError(responseContent);
                return WhatsAppResult.CreateFailure(
                    $"Şablon silinemedi: {errorInfo.Message}",
                    errorInfo.Code);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting WhatsApp template {TemplateName}", templateName);
            return WhatsAppResult.CreateFailure("Şablon silinirken hata oluştu: " + ex.Message);
        }
    }

    public async Task<WhatsAppResult<FacebookMessageTemplate>> GetMessageTemplateAsync(WhatsAppConnectionSettings settings, string templateId)
    {
        try
        {
            var url = $"{FacebookApiBaseUrl}/{templateId}?fields=id,name,status,category,language,components,quality_score";

            _httpClient.DefaultRequestHeaders.Clear();
            _httpClient.DefaultRequestHeaders.Add("Authorization", $"Bearer {settings.AccessToken}");

            var response = await _httpClient.GetAsync(url);
            var responseContent = await response.Content.ReadAsStringAsync();

            _logger.LogInformation("Delete template response: Status={StatusCode}, Content={Content}",
                response.StatusCode, responseContent);
            if (response.IsSuccessStatusCode)
            {
                var options = new JsonSerializerOptions
                {
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                };
                var template = JsonSerializer.Deserialize<FacebookMessageTemplate>(responseContent, options);
                return WhatsAppResult<FacebookMessageTemplate>.CreateSuccess(
                    template ?? new FacebookMessageTemplate(),
                    "Şablon başarıyla alındı.");
            }
            else
            {
                var errorInfo = ParseFacebookError(responseContent);
                return WhatsAppResult<FacebookMessageTemplate>.CreateFailure(
                    $"Şablon alınamadı: {errorInfo.Message}",
                    errorInfo.Code);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting WhatsApp template {TemplateId}", templateId);
            return WhatsAppResult<FacebookMessageTemplate>.CreateFailure("Şablon alınırken hata oluştu: " + ex.Message);
        }
    }

    public bool IsValidPhoneNumber(string phoneNumber)
    {
        if (string.IsNullOrWhiteSpace(phoneNumber))
            return false;

        // Remove all non-digit characters
        var digitsOnly = Regex.Replace(phoneNumber, @"\D", "");

        // Check if it's a valid international format (10-15 digits)
        return digitsOnly.Length >= 10 && digitsOnly.Length <= 15;
    }

    #region Helper Methods

    private object CreateMessagePayload(WhatsAppMessageRequest request)
    {
        var payload = new
        {
            messaging_product = "whatsapp",
            to = FormatPhoneNumber(request.ToPhoneNumber),
            type = request.TemplateType?.ToLower() ?? "text"
        };

        if (request.TemplateType?.ToUpper() == "TEXT" || string.IsNullOrEmpty(request.TemplateType))
        {
            return new
            {
                payload.messaging_product,
                payload.to,
                payload.type,
                text = new { body = request.Message }
            };
        }

        // For other template types, extend as needed
        return payload;
    }

    private object CreateTemplateMessagePayload(WhatsAppTemplateMessageRequest request)
    {
        var templatePayload = new
        {
            messaging_product = "whatsapp",
            to = FormatPhoneNumber(request.ToPhoneNumber),
            type = "template",
            template = new
            {
                name = request.TemplateName,
                language = new { code = request.TemplateLanguage },
                components = request.Components.Select(c => new
                {
                    type = c.Type.ToUpper(),
                    parameters = c.Parameters.Select(p => new
                    {
                        type = p.Type,
                        text = p.Text
                    }).ToArray()
                }).ToArray()
            }
        };

        return templatePayload;
    }

    private string FormatPhoneNumber(string phoneNumber)
    {
        // Remove all non-digit characters
        var digitsOnly = Regex.Replace(phoneNumber, @"\D", "");

        // Ensure it starts with country code
        if (digitsOnly.StartsWith("0"))
        {
            digitsOnly = "90" + digitsOnly.Substring(1); // Turkish numbers
        }
        else if (!digitsOnly.StartsWith("90") && digitsOnly.Length == 10)
        {
            digitsOnly = "90" + digitsOnly; // Add Turkish country code
        }

        return digitsOnly;
    }

    private (string Message, string Code) ParseFacebookError(string responseContent)
    {
        try
        {
            var options = new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase
            };
            var errorResponse = JsonSerializer.Deserialize<FacebookErrorResponse>(responseContent, options);
            return (errorResponse?.Error?.Message ?? "Bilinmeyen hata",
                   errorResponse?.Error?.Code?.ToString() ?? "UNKNOWN");
        }
        catch
        {
            return ("API yanıtı ayrıştırılamadı", "PARSE_ERROR");
        }
    }

    #endregion

    #region Response Models

    private class FacebookPhoneNumbersResponse
    {
        [JsonPropertyName("data")]
        public List<FacebookPhoneNumber>? Data { get; set; }
    }

    private class FacebookPhoneNumber
    {
        [JsonPropertyName("id")]
        public string? Id { get; set; }

        [JsonPropertyName("display_phone_number")]
        public string? Display_phone_number { get; set; }

        [JsonPropertyName("verified_name")]
        public string? Verified_name { get; set; }
    }

    private class FacebookTemplatesResponse
    {
        [JsonPropertyName("data")]
        public List<FacebookMessageTemplate>? Data { get; set; }
    }

    private class FacebookCreateTemplateResponse
    {
        [JsonPropertyName("id")]
        public string? Id { get; set; }

        [JsonPropertyName("status")]
        public string? Status { get; set; }
    }

    private class FacebookErrorResponse
    {
        [JsonPropertyName("error")]
        public FacebookError? Error { get; set; }
    }

    private class FacebookError
    {
        [JsonPropertyName("message")]
        public string? Message { get; set; }

        [JsonPropertyName("code")]
        public int? Code { get; set; }

        [JsonPropertyName("type")]
        public string? Type { get; set; }
    }

    #endregion
}
