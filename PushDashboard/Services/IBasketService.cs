using PushDashboard.ViewModels;

namespace PushDashboard.Services;

public interface IBasketService
{
    Task<BasketIndexViewModel> GetBasketsAsync(Guid companyId, int page = 1, int pageSize = 50, string? searchTerm = null, string? status = null, string? sortBy = null, string? sortDirection = null);
    Task<BasketIndexViewModel.BasketViewModel?> GetBasketDetailsAsync(string guidBasketId, Guid companyId);
    Task<BasketIndexViewModel.BasketItemsPagedViewModel> GetBasketItemsPagedAsync(string guidBasketId, Guid companyId, int page = 1, int pageSize = 10);
    Task<List<BasketIndexViewModel.CurrencyTotalViewModel>> GetCurrencyTotalsAsync(Guid companyId);
    Task<List<BasketIndexViewModel.BasketViewModel>> GetBasketsByCustomerAsync(int customerId, Guid companyId);
    Task<(bool Success, string Message)> SyncBasketsAsync(Guid companyId);
    Task<List<BasketIndexViewModel.BasketSyncLogViewModel>> GetRecentSyncLogsAsync(Guid companyId, int count = 10);
}
