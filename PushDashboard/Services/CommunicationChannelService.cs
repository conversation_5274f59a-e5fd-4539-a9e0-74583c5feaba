using Microsoft.EntityFrameworkCore;
using PushDashboard.Data;
using PushDashboard.Services.WhatsApp;
using System.Text.Json;

namespace PushDashboard.Services;

public interface ICommunicationChannelService
{
    Task<List<CommunicationChannel>> GetAvailableChannelsAsync(Guid companyId);
    Task<bool> IsChannelActiveAsync(Guid companyId, string channelName);
    Task<Dictionary<string, bool>> GetChannelStatusAsync(Guid companyId);
}

public class CommunicationChannel
{
    public string Name { get; set; } = string.Empty;
    public string DisplayName { get; set; } = string.Empty;
    public string IconClass { get; set; } = string.Empty;
    public string ColorClass { get; set; } = string.Empty;
    public bool IsActive { get; set; }
    public string Description { get; set; } = string.Empty;
}

public class CommunicationChannelService : ICommunicationChannelService
{
    private readonly ApplicationDbContext _context;
    private readonly IEmailService _emailService;
    private readonly ILogger<CommunicationChannelService> _logger;

    public CommunicationChannelService(
        ApplicationDbContext context,
        IEmailService emailService,
        ILogger<CommunicationChannelService> logger)
    {
        _context = context;
        _emailService = emailService;
        _logger = logger;
    }

    public async Task<List<CommunicationChannel>> GetAvailableChannelsAsync(Guid companyId)
    {
        var channels = new List<CommunicationChannel>();

        // Email kanalını kontrol et
        var emailActive = await _emailService.IsConfiguredAsync();
        channels.Add(new CommunicationChannel
        {
            Name = "Email",
            DisplayName = "E-posta",
            IconClass = "mail",
            ColorClass = "text-blue-600",
            IsActive = emailActive,
            Description = emailActive ? "E-posta gönderimi aktif" : "E-posta ayarları yapılmamış"
        });

        // WhatsApp kanalını kontrol et
        var whatsAppActive = await IsWhatsAppConfiguredAsync(companyId);
        channels.Add(new CommunicationChannel
        {
            Name = "WhatsApp",
            DisplayName = "WhatsApp",
            IconClass = "message-circle",
            ColorClass = "text-green-600",
            IsActive = whatsAppActive,
            Description = whatsAppActive ? "WhatsApp entegrasyonu aktif" : "WhatsApp entegrasyonu yapılmamış"
        });

        // Telegram kanalını kontrol et (şimdilik pasif)
        var telegramActive = await IsTelegramConfiguredAsync(companyId);
        channels.Add(new CommunicationChannel
        {
            Name = "Telegram",
            DisplayName = "Telegram",
            IconClass = "send",
            ColorClass = "text-blue-500",
            IsActive = telegramActive,
            Description = telegramActive ? "Telegram bot aktif" : "Telegram bot yapılandırılmamış"
        });

        return channels;
    }

    public async Task<bool> IsChannelActiveAsync(Guid companyId, string channelName)
    {
        return channelName switch
        {
            "Email" => await _emailService.IsConfiguredAsync(),
            "WhatsApp" => await IsWhatsAppConfiguredAsync(companyId),
            "Telegram" => await IsTelegramConfiguredAsync(companyId),
            _ => false
        };
    }

    public async Task<Dictionary<string, bool>> GetChannelStatusAsync(Guid companyId)
    {
        var status = new Dictionary<string, bool>();

        status["Email"] = await _emailService.IsConfiguredAsync();
        status["WhatsApp"] = await IsWhatsAppConfiguredAsync(companyId);
        status["Telegram"] = await IsTelegramConfiguredAsync(companyId);

        return status;
    }

    private async Task<bool> IsWhatsAppConfiguredAsync(Guid companyId)
    {
        try
        {
            var integration = await _context.CompanyIntegrations
                .Include(ci => ci.Integration)
                .FirstOrDefaultAsync(ci => ci.CompanyId == companyId &&
                                         ci.Integration.Name == "WhatsApp" &&
                                         ci.IsActive);

            if (integration?.SettingsJson == null || string.IsNullOrWhiteSpace(integration.SettingsJson))
                return false;

            // SettingsJson string alanını güvenli şekilde deserialize et
            Dictionary<string, JsonElement>? settings;
            try
            {
                settings = JsonSerializer.Deserialize<Dictionary<string, JsonElement>>(integration.SettingsJson);
            }
            catch (JsonException)
            {
                // JSON parse hatası durumunda false döndür
                return false;
            }

            if (settings == null)
                return false;

            // WhatsApp için gerekli ayarları kontrol et
            return settings.ContainsKey("accessToken") &&
                   settings.ContainsKey("phoneNumberId") &&
                   settings["accessToken"].ValueKind != JsonValueKind.Null &&
                   settings["phoneNumberId"].ValueKind != JsonValueKind.Null &&
                   !string.IsNullOrEmpty(settings["accessToken"].GetString()) &&
                   !string.IsNullOrEmpty(settings["phoneNumberId"].GetString());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking WhatsApp configuration for company {CompanyId}", companyId);
            return false;
        }
    }

    private async Task<bool> IsTelegramConfiguredAsync(Guid companyId)
    {
        try
        {
            var integration = await _context.CompanyIntegrations
                .Include(ci => ci.Integration)
                .FirstOrDefaultAsync(ci => ci.CompanyId == companyId &&
                                         ci.Integration.Name == "Telegram" &&
                                         ci.IsActive);

            if (integration?.SettingsJson == null || string.IsNullOrWhiteSpace(integration.SettingsJson))
                return false;

            // SettingsJson string alanını güvenli şekilde deserialize et
            Dictionary<string, JsonElement>? settings;
            try
            {
                settings = JsonSerializer.Deserialize<Dictionary<string, JsonElement>>(integration.SettingsJson);
            }
            catch (JsonException)
            {
                // JSON parse hatası durumunda false döndür
                return false;
            }

            if (settings == null)
                return false;

            // Telegram için gerekli ayarları kontrol et
            return settings.ContainsKey("botToken") &&
                   settings["botToken"].ValueKind != JsonValueKind.Null &&
                   !string.IsNullOrEmpty(settings["botToken"].GetString());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking Telegram configuration for company {CompanyId}", companyId);
            return false;
        }
    }
}
