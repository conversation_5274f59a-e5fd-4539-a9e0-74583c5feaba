using Microsoft.EntityFrameworkCore;
using PushDashboard.Data;
using PushDashboard.Models;
using PushDashboard.ViewModels;
using System.Text.Json;

namespace PushDashboard.Services;

public interface IStoreService
{
    Task<StoreIndexViewModel> GetStoreDataAsync(string userId);
    Task<List<StoreIndexViewModel.ModuleCategoryViewModel>> GetCategoriesAsync();
    Task<List<StoreIndexViewModel.ModuleViewModel>> GetModulesAsync(string userId, int? categoryId = null);
    Task<StoreIndexViewModel.ModuleDetailsViewModel?> GetModuleDetailsAsync(int moduleId, string userId);
    Task<(bool Success, string Message)> PurchaseModuleAsync(int moduleId, string userId, decimal paidAmount, string? transactionId = null);
    Task<bool> IsModuleOwnedAsync(int moduleId, string userId);
    Task<List<StoreIndexViewModel.ModuleViewModel>> GetUserModulesAsync(string userId);
    Task<(bool Success, string Message)> AddCreditAsync(string userId, decimal amount, string? description = null);
    Task<decimal> GetUserCreditBalanceAsync(string userId);
    Task<StoreIndexViewModel.PagedTransactionHistoryViewModel> GetTransactionHistoryAsync(string userId, int page = 1, int pageSize = 10);
    Task<(Dictionary<string, object>? Settings, Module? ModuleInfo)> GetModuleSettingsAsync(int moduleId, string userId);
    Task<bool> SaveModuleSettingsAsync(int moduleId, string userId, Dictionary<string, object> settings);
    Task<bool> ResetModuleSettingsToDefaultAsync(int moduleId, string userId);
    Task<Module?> GetModuleInfoAsync(int moduleId);
    Task<(bool Success, string Message)> ToggleModuleStatusAsync(int moduleId, string userId);}

public class StoreService : IStoreService
{
    private readonly ApplicationDbContext _context;
    private readonly ILogger<StoreService> _logger;
    private readonly IBasketReminderService _basketReminderService;

    public StoreService(ApplicationDbContext context, ILogger<StoreService> logger, IBasketReminderService basketReminderService)
    {
        _context = context;
        _logger = logger;
        _basketReminderService = basketReminderService;
    }

    public async Task<StoreIndexViewModel> GetStoreDataAsync(string userId)
    {
        var categories = await GetCategoriesAsync();
        var modules = await GetModulesAsync(userId);
        var ownedModules = modules.Where(m => m.IsOwned).ToList();
        var availableModules = modules.Where(m => !m.IsOwned).ToList();

        // Get user's company credit balance
        var user = await _context.Users.Include(u => u.Company).FirstOrDefaultAsync(u => u.Id == userId);
        var companyCreditBalance = user?.Company?.CreditBalance ?? 0.00m;

        var stats = new StoreIndexViewModel.StoreStatsViewModel
        {
            TotalCredits = companyCreditBalance,
            LastUsage = ownedModules.Any() ? ownedModules.Max(m => m.LastUsedAt) : null,
            ActiveModules = ownedModules.Count,
            ActiveModuleNames = ownedModules.Select(m => m.Name).ToList()
        };

        return new StoreIndexViewModel
        {
            Categories = categories,
            Modules = modules,
            OwnedModules = ownedModules,
            AvailableModules = availableModules,
            Stats = stats,
            RecentTransactions = new List<StoreIndexViewModel.TransactionHistoryViewModel>()
        };
    }

    public async Task<(bool Success, string Message)> PurchaseModuleAsync(int moduleId, string userId, decimal paidAmount, string? transactionId = null)
    {
        using var transaction = await _context.Database.BeginTransactionAsync();
        try
        {
            // Check if module exists and is active
            var module = await _context.Modules
                .FirstOrDefaultAsync(m => m.Id == moduleId && m.IsActive);

            if (module == null)
            {
                _logger.LogWarning("Attempted to purchase non-existent or inactive module {ModuleId}", moduleId);
                return (false, "Modül bulunamadı veya aktif değil.");
            }

            // Get user and company first
            var user = await _context.Users.Include(u => u.Company).FirstOrDefaultAsync(u => u.Id == userId);
            if (user == null)
            {
                return (false, "Kullanıcı bulunamadı.");
            }

            if (user.Company == null)
            {
                return (false, "Şirket bilgisi bulunamadı.");
            }

            // Check if company already owns the module
            var existingCompanyModule = await _context.CompanyModules
                .FirstOrDefaultAsync(cm => cm.ModuleId == moduleId && cm.CompanyId == user.CompanyId);

            if (existingCompanyModule != null)
            {
                _logger.LogWarning("Company {CompanyId} already owns module {ModuleId}", user.CompanyId, moduleId);
                return (false, "Şirketiniz bu modüle zaten sahip.");
            }

            // Check credit balance

            // Check if company has enough credit
            if (user.Company.CreditBalance < module.Price)
            {
                _logger.LogWarning("Company {CompanyId} has insufficient credit balance. Required: {Required}, Available: {Available}",
                    user.Company.Id,
                    module.Price,
                    user.Company.CreditBalance);

                return (false, $"Yetersiz kredi bakiyesi. Gerekli: ₺{module.Price:N2}, Mevcut: ₺{user.Company.CreditBalance:N2}");
            }

            // Deduct credit from company balance
            user.Company.CreditBalance -= module.Price;
            _context.Companies.Update(user.Company);

            // Create company module record
            var companyModule = new CompanyModule
            {
                CompanyId = user.CompanyId.Value,
                ModuleId = moduleId,
                PurchasedAt = DateTime.UtcNow,
                IsActive = true,
                PaidAmount = module.Price, // Use actual module price
                TransactionId = transactionId,
                PurchasedByUserId = userId
            };

            _context.CompanyModules.Add(companyModule);
            await _context.SaveChangesAsync();

            // Create default settings for the purchased module
            if (!string.IsNullOrEmpty(module.DefaultSettings))
            {
                var defaultSettings = new CompanyModuleSettings
                {
                    CompanyModuleId = companyModule.Id,
                    SettingsJson = module.DefaultSettings,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow,
                    UpdatedByUserId = userId
                };

                _context.CompanyModuleSettings.Add(defaultSettings);
                await _context.SaveChangesAsync();
            }

            // Sepet Hatırlatma modülü satın alındıysa BasketReminderSettings tablosunda varsayılan kayıt oluştur
            if (module.Name == "Sepet Hatırlatma")
            {
                try
                {
                    var (settingsSuccess, settingsMessage) = await _basketReminderService.CreateDefaultSettingsAsync(user.CompanyId.Value, userId);
                    if (settingsSuccess)
                    {
                        _logger.LogInformation("Created default BasketReminderSettings for company {CompanyId} after purchasing module {ModuleId}",
                            user.CompanyId.Value, moduleId);
                    }
                    else
                    {
                        _logger.LogWarning("Failed to create default BasketReminderSettings for company {CompanyId}: {Message}",
                            user.CompanyId.Value, settingsMessage);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error creating default BasketReminderSettings for company {CompanyId} after purchasing module {ModuleId}",
                        user.CompanyId.Value, moduleId);
                    // Ana transaction'ı etkilememek için hata fırlatmıyoruz
                }
            }

            await transaction.CommitAsync();

            _logger.LogInformation("User {UserId} successfully purchased module {ModuleId} for {Amount}. Remaining company balance: {Balance}",
                userId,
                moduleId,
                module.Price,
                user.Company.CreditBalance);

            return (true, $"Modül başarıyla satın alındı! Kalan bakiye: ₺{user.Company.CreditBalance:N2}");
        }
        catch (Exception ex)
        {
            await transaction.RollbackAsync();
            _logger.LogError(ex, "Error purchasing module {ModuleId} for user {UserId}", moduleId, userId);
            return (false, "Satın alma işlemi sırasında bir hata oluştu.");
        }
    }

    public async Task<(bool Success, string Message)> AddCreditAsync(string userId, decimal amount, string? description = null)
    {
        try
        {
            var user = await _context.Users.FirstOrDefaultAsync(u => u.Id == userId);
            if (user == null)
            {
                return (false, "Kullanıcı bulunamadı.");
            }

            if (amount <= 0)
            {
                return (false, "Kredi miktarı pozitif olmalıdır.");
            }

            user.CreditBalance += amount;
            _context.Users.Update(user);
            await _context.SaveChangesAsync();

            _logger.LogInformation("Added {Amount} credit to user {UserId}. New balance: {Balance}. Description: {Description}",
                amount,
                userId,
                user.CreditBalance,
                description ?? "Manual credit addition");

            return (true, $"₺{amount:N2} kredi başarıyla eklendi. Yeni bakiye: ₺{user.CreditBalance:N2}");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error adding credit to user {UserId}", userId);
            return (false, "Kredi ekleme işlemi sırasında bir hata oluştu.");
        }
    }

    public async Task<decimal> GetUserCreditBalanceAsync(string userId)
    {
        var user = await _context.Users.Include(u => u.Company).FirstOrDefaultAsync(u => u.Id == userId);
        return user?.Company?.CreditBalance ?? 0.00m;
    }

    public async Task<List<StoreIndexViewModel.ModuleCategoryViewModel>> GetCategoriesAsync()
    {
        return await _context.ModuleCategories
            .Where(c => c.IsActive)
            .OrderBy(c => c.SortOrder)
            .Select(c => new StoreIndexViewModel.ModuleCategoryViewModel
            {
                Id = c.Id,
                Name = c.Name,
                Description = c.Description,
                IconClass = c.IconClass,
                ModuleCount = c.Modules.Count(m => m.IsActive)
            })
            .ToListAsync();
    }

    public async Task<List<StoreIndexViewModel.ModuleViewModel>> GetModulesAsync(string userId, int? categoryId = null)
    {
        var query = _context.Modules
            .Include(m => m.Category)
            .Where(m => m.IsActive);

        if (categoryId.HasValue)
        {
            query = query.Where(m => m.CategoryId == categoryId.Value);
        }

        var modules = await query
            .OrderByDescending(m => m.IsFeatured)
            .ThenByDescending(m => m.IsNew)
            .ThenBy(m => m.Name)
            .ToListAsync();

        // Get user's company modules instead of user modules
        var user = await _context.Users.FirstOrDefaultAsync(u => u.Id == userId);
        var companyModules = new List<CompanyModule>();

        if (user?.CompanyId != null)
        {
            companyModules = await _context.CompanyModules
                .Where(cm => cm.CompanyId == user.CompanyId && cm.IsActive)
                .ToListAsync();
        }

        return modules.Select(m =>
        {
            var companyModule = companyModules.FirstOrDefault(cm => cm.ModuleId == m.Id);

            var features = new List<string>();
            if (!string.IsNullOrEmpty(m.Features))
            {
                try
                {
                    features = JsonSerializer.Deserialize<List<string>>(m.Features) ?? new List<string>();
                }
                catch (JsonException ex)
                {
                    _logger.LogWarning(ex, "Failed to deserialize features for module {ModuleId}", m.Id);
                }
            }

            return new StoreIndexViewModel.ModuleViewModel
            {
                Id = m.Id,
                Name = m.Name,
                Description = m.Description,
                DetailedDescription = m.DetailedDescription,
                Price = m.Price,
                IconClass = m.IconClass,
                IconColor = m.IconColor,
                BackgroundColor = m.BackgroundColor,
                IsActive = m.IsActive,
                IsNew = m.IsNew,
                IsFeatured = m.IsFeatured,
                IsOwned = companyModule != null,
                CategoryId = m.CategoryId,
                CategoryName = m.Category.Name,
                Features = features,
                PurchasedAt = companyModule?.PurchasedAt,
                LastUsedAt = companyModule?.LastUsedAt
            };
        }).ToList();
    }

    public async Task<StoreIndexViewModel.ModuleDetailsViewModel?> GetModuleDetailsAsync(int moduleId, string userId)
    {
        var module = await _context.Modules
            .Include(m => m.Category)
            .FirstOrDefaultAsync(m => m.Id == moduleId && m.IsActive);

        if (module == null)
            return null;

        // Get user's company
        var user = await _context.Users.FirstOrDefaultAsync(u => u.Id == userId);
        CompanyModule? companyModule = null;

        if (user?.CompanyId != null)
        {
            companyModule = await _context.CompanyModules
                .FirstOrDefaultAsync(cm => cm.ModuleId == moduleId && cm.CompanyId == user.CompanyId && cm.IsActive);
        }

        var features = new List<string>();
        if (!string.IsNullOrEmpty(module.Features))
        {
            try
            {
                features = JsonSerializer.Deserialize<List<string>>(module.Features) ?? new List<string>();
            }
            catch (JsonException ex)
            {
                _logger.LogWarning(ex, "Failed to deserialize features for module {ModuleId}", moduleId);
            }
        }

        var moduleViewModel = new StoreIndexViewModel.ModuleViewModel
        {
            Id = module.Id,
            Name = module.Name,
            Description = module.Description,
            DetailedDescription = module.DetailedDescription,
            Price = module.Price,
            IconClass = module.IconClass,
            IconColor = module.IconColor,
            BackgroundColor = module.BackgroundColor,
            IsActive = module.IsActive,
            IsNew = module.IsNew,
            IsFeatured = module.IsFeatured,
            IsOwned = companyModule != null,
            CategoryName = module.Category.Name,
            Features = features,
            PurchasedAt = companyModule?.PurchasedAt,
            LastUsedAt = companyModule?.LastUsedAt
        };

        // Get related modules from the same category
        var relatedModules = await GetModulesAsync(userId, module.CategoryId);
        relatedModules = relatedModules.Where(m => m.Id != moduleId).Take(3).ToList();

        return new StoreIndexViewModel.ModuleDetailsViewModel
        {
            Module = moduleViewModel,
            CanPurchase = companyModule == null,
            PurchaseBlockReason = companyModule != null ? "Şirketiniz bu modüle zaten sahip." : null,
            RelatedModules = relatedModules
        };
    }

    public async Task<bool> IsModuleOwnedAsync(int moduleId, string userId)
    {
        // Get user's company
        var user = await _context.Users.FirstOrDefaultAsync(u => u.Id == userId);
        if (user?.CompanyId == null)
            return false;

        // Check if company owns the module
        return await _context.CompanyModules
            .AnyAsync(cm => cm.ModuleId == moduleId && cm.CompanyId == user.CompanyId && cm.IsActive);
    }


    public async Task<List<StoreIndexViewModel.ModuleViewModel>> GetUserModulesAsync(string userId)
    {
        // Get user's company
        var user = await _context.Users.FirstOrDefaultAsync(u => u.Id == userId);
        if (user?.CompanyId == null)
            return new List<StoreIndexViewModel.ModuleViewModel>();

        var companyModules = await _context.CompanyModules
            .Include(cm => cm.Module)
            .ThenInclude(m => m.Category)
            .Where(cm => cm.CompanyId == user.CompanyId && cm.IsActive && cm.Module.IsActive)
            .ToListAsync();

        return companyModules.Select(cm =>
        {
            var features = new List<string>();
            if (!string.IsNullOrEmpty(cm.Module.Features))
            {
                try
                {
                    features = JsonSerializer.Deserialize<List<string>>(cm.Module.Features) ?? new List<string>();
                }
                catch (JsonException ex)
                {
                    _logger.LogWarning(ex, "Failed to deserialize features for module {ModuleId}", cm.ModuleId);
                }
            }

            return new StoreIndexViewModel.ModuleViewModel
            {
                Id = cm.Module.Id,
                Name = cm.Module.Name,
                Description = cm.Module.Description,
                DetailedDescription = cm.Module.DetailedDescription,
                Price = cm.Module.Price,
                IconClass = cm.Module.IconClass,
                IconColor = cm.Module.IconColor,
                BackgroundColor = cm.Module.BackgroundColor,
                IsActive = cm.Module.IsActive,
                IsNew = cm.Module.IsNew,
                IsFeatured = cm.Module.IsFeatured,
                IsOwned = true,
                CategoryName = cm.Module.Category.Name,
                Features = features,
                PurchasedAt = cm.PurchasedAt,
                LastUsedAt = cm.LastUsedAt
            };
        }).ToList();
    }

    public async Task<StoreIndexViewModel.PagedTransactionHistoryViewModel> GetTransactionHistoryAsync(string userId, int page = 1, int pageSize = 10)
    {
        // Get user's company
        var user = await _context.Users.FirstOrDefaultAsync(u => u.Id == userId);
        if (user?.CompanyId == null)
        {
            return new StoreIndexViewModel.PagedTransactionHistoryViewModel
            {
                Transactions = new List<StoreIndexViewModel.TransactionHistoryViewModel>(),
                CurrentPage = page,
                TotalPages = 0,
                TotalItems = 0,
                PageSize = pageSize
            };
        }

        var query = _context.CompanyModules
            .Include(cm => cm.Module)
            .ThenInclude(m => m.Category)
            .Where(cm => cm.CompanyId == user.CompanyId && cm.IsActive)
            .OrderByDescending(cm => cm.PurchasedAt);

        var totalItems = await query.CountAsync();
        var totalPages = (int)Math.Ceiling((double)totalItems / pageSize);

        var transactions = await query
            .Skip((page - 1) * pageSize)
            .Take(pageSize)
            .Select(cm => new StoreIndexViewModel.TransactionHistoryViewModel
            {
                Id = cm.Id,
                ModuleName = cm.Module.Name,
                PaidAmount = cm.PaidAmount,
                PurchasedAt = cm.PurchasedAt,
                TransactionId = cm.TransactionId,
                Status = "Tamamlandı",
                CategoryName = cm.Module.Category.Name,
                ModuleIcon = cm.Module.IconClass,
                ModuleIconColor = cm.Module.IconColor,
                ModuleBackgroundColor = cm.Module.BackgroundColor
            })
            .ToListAsync();

        return new StoreIndexViewModel.PagedTransactionHistoryViewModel
        {
            Transactions = transactions,
            CurrentPage = page,
            TotalPages = totalPages,
            TotalItems = totalItems,
            PageSize = pageSize
        };
    }

    public async Task<(Dictionary<string, object>? Settings, Module? ModuleInfo)> GetModuleSettingsAsync(int moduleId, string userId)
    {
        try
        {
            // Get user's company
            var user = await _context.Users.FirstOrDefaultAsync(u => u.Id == userId);
            if (user?.CompanyId == null)
            {
                return (null, null);
            }

            // Check if company owns the module
            var companyModule = await _context.CompanyModules
                .Include(cm => cm.Module)
                .Include(cm => cm.Settings)
                .FirstOrDefaultAsync(cm => cm.ModuleId == moduleId && cm.CompanyId == user.CompanyId && cm.IsActive);

            if (companyModule == null)
            {
                return (null, null);
            }

            var settings = new Dictionary<string, object>();

            if (companyModule.Settings != null && !string.IsNullOrEmpty(companyModule.Settings.SettingsJson))
            {
                try
                {
                    settings = JsonSerializer.Deserialize<Dictionary<string, object>>(companyModule.Settings.SettingsJson)
                               ?? new Dictionary<string, object>();
                }
                catch (JsonException ex)
                {
                    _logger.LogWarning(ex, "Failed to deserialize settings for company module {CompanyModuleId}", companyModule.Id);
                }
            }

            return (settings, companyModule.Module);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting module settings for module {ModuleId} and user {UserId}", moduleId, userId);
            return (null, null);
        }
    }

    public async Task<bool> SaveModuleSettingsAsync(int moduleId, string userId, Dictionary<string, object> settings)
    {
        try
        {
            // Get user's company
            var user = await _context.Users.FirstOrDefaultAsync(u => u.Id == userId);
            if (user?.CompanyId == null)
            {
                return false;
            }

            // Check if company owns the module
            var companyModule = await _context.CompanyModules
                .Include(cm => cm.Settings)
                .Include(cm => cm.Module)
                .FirstOrDefaultAsync(cm => cm.ModuleId == moduleId && cm.CompanyId == user.CompanyId && cm.IsActive);

            if (companyModule == null)
            {
                return false;
            }
            
            var settingsJson = JsonSerializer.Serialize(settings.Values.First());

            if (companyModule.Settings == null)
            {
                // Create new settings record
                companyModule.Settings = new CompanyModuleSettings
                {
                    CompanyModuleId = companyModule.Id,
                    SettingsJson = settingsJson,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow,
                    UpdatedByUserId = userId
                };
                _context.CompanyModuleSettings.Add(companyModule.Settings);
            }
            else
            {
                // Update existing settings
                companyModule.Settings.SettingsJson = settingsJson;
                companyModule.Settings.UpdatedAt = DateTime.UtcNow;
                companyModule.Settings.UpdatedByUserId = userId;
                _context.CompanyModuleSettings.Update(companyModule.Settings);
            }

            await _context.SaveChangesAsync();

            _logger.LogInformation("Updated settings for company module {CompanyModuleId} (Module: {ModuleId}, Company: {CompanyId}, User: {UserId})",
                companyModule.Id, moduleId, user.CompanyId, userId);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error saving module settings for module {ModuleId} and user {UserId}", moduleId, userId);
            return false;
        }
    }
    public async Task<bool> ResetModuleSettingsToDefaultAsync(int moduleId, string userId)
    {
        try
        {
            // Get user's company
            var user = await _context.Users.FirstOrDefaultAsync(u => u.Id == userId);
            if (user?.CompanyId == null)
            {
                return false;
            }

            // Check if company owns the module
            var companyModule = await _context.CompanyModules
                .Include(cm => cm.Module)
                .Include(cm => cm.Settings)
                .FirstOrDefaultAsync(cm => cm.ModuleId == moduleId && cm.CompanyId == user.CompanyId && cm.IsActive);

            if (companyModule == null || companyModule.Module == null)
            {
                return false;
            }

            // Get default settings from module
            if (string.IsNullOrEmpty(companyModule.Module.DefaultSettings))
            {
                return false;
            }

            if (companyModule.Settings == null)
            {
                // Create new settings record with default values
                companyModule.Settings = new CompanyModuleSettings
                {
                    CompanyModuleId = companyModule.Id,
                    SettingsJson = companyModule.Module.DefaultSettings,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow,
                    UpdatedByUserId = userId
                };

                _context.CompanyModuleSettings.Add(companyModule.Settings);
            }
            else
            {
                // Reset existing settings to default
                companyModule.Settings.SettingsJson = companyModule.Module.DefaultSettings;
                companyModule.Settings.UpdatedAt = DateTime.UtcNow;
                companyModule.Settings.UpdatedByUserId = userId;
                _context.CompanyModuleSettings.Update(companyModule.Settings);
            }

            await _context.SaveChangesAsync();

            _logger.LogInformation("Reset settings to default for company module {CompanyModuleId} (Module: {ModuleId}, Company: {CompanyId}, User: {UserId})",
                companyModule.Id,
                moduleId,
                user.CompanyId,
                userId);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error resetting module settings to default for module {ModuleId} and user {UserId}", moduleId, userId);
            return false;
        }
    }

    public async Task<Module?> GetModuleInfoAsync(int moduleId)
    {
        try
        {
            return await _context.Modules
                .FirstOrDefaultAsync(m => m.Id == moduleId && m.IsActive);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting module info for module {ModuleId}", moduleId);
            return null;
        }
    }

    public async Task<(bool Success, string Message)> ToggleModuleStatusAsync(int moduleId, string userId)
    {
        try
        {
            // Get user's company
            var user = await _context.Users.FirstOrDefaultAsync(u => u.Id == userId);
            if (user?.CompanyId == null)
            {
                return (false, "Kullanıcı veya şirket bulunamadı.");
            }

            // Check if company owns the module
            var companyModule = await _context.CompanyModules
                .Include(cm => cm.Module)
                .FirstOrDefaultAsync(cm => cm.ModuleId == moduleId && cm.CompanyId == user.CompanyId);

            if (companyModule == null)
            {
                return (false, "Modül bulunamadı veya size ait değil.");
            }

            // Toggle the module status
            companyModule.IsActive = !companyModule.IsActive;
            companyModule.UpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();

            var statusText = companyModule.IsActive ? "aktif" : "pasif";
            _logger.LogInformation("Module {ModuleId} status changed to {Status} for company {CompanyId} by user {UserId}",
                moduleId, statusText, user.CompanyId, userId);

            return (true, $"Modül başarıyla {statusText} yapıldı.");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error toggling module status for module {ModuleId} and user {UserId}", moduleId, userId);
            return (false, "Modül durumu güncellenirken bir hata oluştu.");
        }
    }
}