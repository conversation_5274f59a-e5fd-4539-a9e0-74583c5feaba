using Microsoft.EntityFrameworkCore;
using PushDashboard.Data;
using PushDashboard.Models;
using PushDashboard.Services.WhatsApp;
using System.Text.Json;

namespace PushDashboard.Services.Notifications;

public class WhatsAppNotificationChannelService : INotificationChannelService
{
    private readonly ApplicationDbContext _context;
    private readonly IWhatsAppService _whatsAppService;
    private readonly IWhatsAppTemplateService _templateService;
    private readonly ILogger<WhatsAppNotificationChannelService> _logger;

    public string ChannelType => "whatsapp";

    public WhatsAppNotificationChannelService(
        ApplicationDbContext context,
        IWhatsAppService whatsAppService,
        IWhatsAppTemplateService templateService,
        ILogger<WhatsAppNotificationChannelService> logger)
    {
        _context = context;
        _whatsAppService = whatsAppService;
        _templateService = templateService;
        _logger = logger;
    }

    public async Task<bool> SendNotificationAsync(Guid companyId, Customer customer, string templateName, Dictionary<string, string> variables)
    {
        try
        {
            // WhatsApp entegrasyonunu kontrol et
            if (!await IsConfiguredAsync(companyId))
            {
                _logger.LogWarning("WhatsApp integration not configured for company {CompanyId}", companyId);
                return false;
            }

            // Müşterinin telefon numarasını kontrol et
            if (string.IsNullOrWhiteSpace(customer.Phone) || string.IsNullOrWhiteSpace(customer.MobilePhone))
            {
                _logger.LogWarning("Customer {CustomerId} has no phone number for WhatsApp", customer.Id);
                return false;
            }

            // Facebook'tan onaylanmış şablonları al
            var facebookTemplates = await _templateService.GetFacebookTemplatesAsync(companyId);
            var template = facebookTemplates.FirstOrDefault(t => t.Name == templateName && t.Status == "APPROVED");

            if (template == null)
            {
                _logger.LogWarning("Approved WhatsApp template '{TemplateName}' not found for company {CompanyId}", templateName, companyId);
                return false;
            }

            // Şablon içeriğini hazırla (BODY component'ından)
            var bodyComponent = template.Components?.FirstOrDefault(c => c.Type == "BODY");
            var messageContent = bodyComponent?.Text ?? "";

            // Değişkenleri değiştir
            foreach (var variable in variables)
            {
                messageContent = messageContent.Replace($"{{{{{variable.Key}}}}}", variable.Value);
            }

            // WhatsApp ayarlarını al
            var settings = await GetWhatsAppSettingsAsync(companyId);
            if (settings == null)
            {
                _logger.LogError("WhatsApp settings not found for company {CompanyId}", companyId);
                return false;
            }

            // WhatsApp mesajını gönder
            var request = new WhatsAppMessageRequest
            {
                ToPhoneNumber = customer.Phone ?? customer.MobilePhone,
                Message = messageContent,
                TemplateType = "TEXT", // Facebook şablonları için basit metin
                ConnectionSettings = settings
            };

            var result = await _whatsAppService.SendMessageAsync(request);

            if (result.Success)
            {
                _logger.LogInformation("WhatsApp notification sent successfully to customer {CustomerId}", customer.Id);
                return true;
            }
            else
            {
                _logger.LogError("Failed to send WhatsApp notification to customer {CustomerId}: {Error}",
                    customer.Id, result.Message);
                return false;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending WhatsApp notification to customer {CustomerId}", customer.Id);
            return false;
        }
    }

    public async Task<bool> SendNotificationWithCostTrackingAsync(Guid companyId, Customer customer, string templateName,
        Dictionary<string, string> variables, string userId, int? moduleId = null, string? referenceId = null)
    {
        try
        {
            // Maliyet hesapla (WhatsApp için varsayılan maliyet)
            decimal cost = 1.00m; // Bu değer ayarlardan alınabilir

            // Şirketin bakiyesini kontrol et
            var company = await _context.Companies.FindAsync(companyId);
            if (company == null || company.CreditBalance < cost)
            {
                _logger.LogWarning("Insufficient credit balance for company {CompanyId}. Required: {Cost}, Available: {Balance}",
                    companyId, cost, company?.CreditBalance ?? 0);
                return false;
            }

            // Bildirimi gönder
            var success = await SendNotificationAsync(companyId, customer, templateName, variables);

            if (success)
            {
                // ModuleUsageService kullanarak harcama kaydı oluştur
                var loggerFactory = LoggerFactory.Create(builder => builder.AddConsole());
                var moduleUsageLogger = loggerFactory.CreateLogger<ModuleUsageService>();
                var moduleUsageService = new ModuleUsageService(_context, moduleUsageLogger);
                var result = await moduleUsageService.DeductUsageCostAsync(
                    companyId: companyId,
                    moduleId: moduleId ?? 0,
                    usageType: "whatsapp_notification",
                    cost: cost,
                    description: $"WhatsApp bildirimi - {templateName}",
                    userId: userId,
                    referenceId: referenceId,
                    channel: "whatsapp",
                    metadata: new Dictionary<string, object>
                    {
                        ["templateName"] = templateName,
                        ["customerEmail"] = customer.Email,
                        ["customerName"] = customer.FullName,
                        ["sentAt"] = DateTime.UtcNow
                    }
                );

                if (!result.Success)
                {
                    _logger.LogError("Failed to record WhatsApp notification usage: {Message}", result.Message);
                }
                else
                {
                    _logger.LogInformation("WhatsApp notification cost {Cost} deducted from company {CompanyId} balance",
                        cost, companyId);
                }
            }

            return success;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending WhatsApp notification with cost tracking for customer {CustomerId}", customer.Id);
            return false;
        }
    }

    public async Task<bool> IsConfiguredAsync(Guid companyId)
    {
        try
        {
            var whatsappIntegration = await _context.CompanyIntegrations
                .Include(ci => ci.Integration)
                .FirstOrDefaultAsync(ci => ci.CompanyId == companyId &&
                                         ci.Integration.Name == "WhatsApp" &&
                                         ci.IsActive &&
                                         ci.IsConfigured);

            if (whatsappIntegration == null)
                return false;

            var settings = whatsappIntegration.Settings;
            return settings != null &&
                   settings.ContainsKey("accessToken") &&
                   settings.ContainsKey("businessAccountId") &&
                   settings.ContainsKey("phoneNumberId") &&
                   !string.IsNullOrEmpty(settings["accessToken"]?.ToString()) &&
                   !string.IsNullOrEmpty(settings["businessAccountId"]?.ToString()) &&
                   !string.IsNullOrEmpty(settings["phoneNumberId"]?.ToString());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking WhatsApp configuration for company {CompanyId}", companyId);
            return false;
        }
    }

    public async Task<ConnectionTestResult> TestConnectionAsync(Guid companyId)
    {
        try
        {
            var whatsappIntegration = await _context.CompanyIntegrations
                .Include(ci => ci.Integration)
                .FirstOrDefaultAsync(ci => ci.CompanyId == companyId &&
                                         ci.Integration.Name == "WhatsApp" &&
                                         ci.IsActive &&
                                         ci.IsConfigured);

            if (whatsappIntegration == null)
            {
                return ConnectionTestResult.CreateFailure(
                    "WhatsApp entegrasyonu bulunamadı veya yapılandırılmamış.",
                    "WHATSAPP_NOT_CONFIGURED",
                    "WhatsApp"
                );
            }

            var settings = whatsappIntegration.Settings;
            if (settings == null || !settings.Any())
            {
                return ConnectionTestResult.CreateFailure(
                    "WhatsApp ayarları bulunamadı.",
                    "WHATSAPP_SETTINGS_MISSING",
                    "WhatsApp"
                );
            }

            return await TestConnectionWithSettingsAsync(companyId, settings);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error testing WhatsApp connection for company {CompanyId}", companyId);
            return ConnectionTestResult.CreateError(ex, "WhatsApp");
        }
    }

    public async Task<ConnectionTestResult> TestConnectionWithSettingsAsync(Guid companyId, Dictionary<string, object> settings)
    {
        try
        {
            var whatsappSettings = new WhatsAppConnectionSettings
            {
                AccessToken = settings.GetValueOrDefault("accessToken")?.ToString() ?? "",
                BusinessAccountId = settings.GetValueOrDefault("businessAccountId")?.ToString() ?? "",
                PhoneNumberId = settings.GetValueOrDefault("phoneNumberId")?.ToString() ?? "",
            };

            if (string.IsNullOrEmpty(whatsappSettings.AccessToken) ||
                string.IsNullOrEmpty(whatsappSettings.BusinessAccountId) ||
                string.IsNullOrEmpty(whatsappSettings.PhoneNumberId))
            {
                return ConnectionTestResult.CreateFailure(
                    "WhatsApp ayarları eksik. Access Token, Business Account ID ve Phone Number ID gereklidir.",
                    "WHATSAPP_SETTINGS_INCOMPLETE",
                    "WhatsApp"
                );
            }

            var startTime = DateTime.UtcNow;
            var result = await _whatsAppService.TestConnectionAsync(whatsappSettings);
            var endTime = DateTime.UtcNow;
            var responseTime = endTime - startTime;

            if (result.Success)
            {
                return ConnectionTestResult.CreateSuccess(
                    result.Message ?? "WhatsApp Business API bağlantısı başarılı!",
                    "WhatsApp",
                    responseTime
                );
            }
            else
            {
                return ConnectionTestResult.CreateFailure(
                    result.Message ?? "WhatsApp bağlantısı başarısız.",
                    result.ErrorCode ?? "WHATSAPP_TEST_FAILED",
                    "WhatsApp"
                );
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error testing WhatsApp connection with settings for company {CompanyId}", companyId);
            return ConnectionTestResult.CreateError(ex, "WhatsApp");
        }
    }

    private async Task<WhatsAppConnectionSettings?> GetWhatsAppSettingsAsync(Guid companyId)
    {
        try
        {
            var whatsappIntegration = await _context.CompanyIntegrations
                .Include(ci => ci.Integration)
                .FirstOrDefaultAsync(ci => ci.CompanyId == companyId &&
                                         ci.Integration.Name == "WhatsApp" &&
                                         ci.IsActive &&
                                         ci.IsConfigured);

            if (whatsappIntegration?.Settings == null)
                return null;

            var settings = whatsappIntegration.Settings;
            return new WhatsAppConnectionSettings
            {
                AccessToken = settings.GetValueOrDefault("accessToken")?.ToString() ?? "",
                BusinessAccountId = settings.GetValueOrDefault("businessAccountId")?.ToString() ?? "",
                PhoneNumberId = settings.GetValueOrDefault("phoneNumberId")?.ToString() ?? "",
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting WhatsApp settings for company {CompanyId}", companyId);
            return null;
        }
    }
}
