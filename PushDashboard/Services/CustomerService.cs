using Microsoft.EntityFrameworkCore;
using PushDashboard.Data;
using PushDashboard.Models;
using PushDashboard.ViewModels;
using PushDashboard.Services.Integrations;
using PushDashboard.Services.Integrations.Common.Models;
using PushDashboard.Helpers;

namespace PushDashboard.Services;

public class CustomerService : ICustomerService
{
    private readonly ApplicationDbContext _context;
    private readonly ILogger<CustomerService> _logger;
    private readonly IEcommerceServiceFactory _ecommerceServiceFactory;
    private readonly ICustomerImportService _customerImportService;
    private readonly IServiceProvider _serviceProvider;

    public CustomerService(
        ApplicationDbContext context,
        ILogger<CustomerService> logger,
        IEcommerceServiceFactory ecommerceServiceFactory,
        ICustomerImportService customerImportService,
        IServiceProvider serviceProvider)
    {
        _context = context;
        _logger = logger;
        _ecommerceServiceFactory = ecommerceServiceFactory;
        _customerImportService = customerImportService;
        _serviceProvider = serviceProvider;
    }

    public async Task<CustomerIndexViewModel> GetCustomersAsync(Guid companyId, int page = 1, int pageSize = 50, string? searchTerm = null, string? status = null, string? sortBy = null, string? sortDirection = null)
    {
        try
        {
            var query = _context.Customers
                .Where(c => c.CompanyId == companyId);

            // Arama filtresi - isim, soyisim, email, telefon, cep telefonu
            if (!string.IsNullOrWhiteSpace(searchTerm))
            {
                var searchLower = searchTerm.ToLower();
                query = query.Where(c =>
                    c.FirstName.ToLower().Contains(searchLower) ||
                    c.LastName.ToLower().Contains(searchLower) ||
                    c.Email.ToLower().Contains(searchLower) ||
                    (c.Phone != null && c.Phone.Contains(searchTerm)) ||
                    (c.MobilePhone != null && c.MobilePhone.Contains(searchTerm)));
            }

            // Durum filtresi
            if (!string.IsNullOrEmpty(status))
            {
                switch (status.ToLower())
                {
                    case "active":
                        query = query.Where(c => c.IsActive);
                        break;
                    case "inactive":
                        query = query.Where(c => !c.IsActive);
                        break;
                    case "new":
                        var oneMonthAgo = DateTime.UtcNow.AddMonths(-1);
                        query = query.Where(c => c.MembershipDate >= oneMonthAgo);
                        break;
                }
            }

            var totalRecords = await query.CountAsync();
            var totalPages = (int)Math.Ceiling((double)totalRecords / pageSize);

            // Sıralama uygula
            query = ApplySorting(query, sortBy, sortDirection);

            var customers = await query
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();

            var customerViewModels = customers.Select(CustomerIndexViewModel.CustomerViewModel.FromCustomer).ToList();

            // İstatistikleri al
            var stats = await GetCustomerStatsAsync(companyId);

            // Son senkronizasyon loglarını al
            var syncLogs = await GetSyncLogsAsync(companyId, 5);

            return new CustomerIndexViewModel
            {
                Customers = customerViewModels,
                Stats = new CustomerIndexViewModel.CustomerStatsViewModel
                {
                    TotalCustomers = stats.TotalCustomers,
                    ActiveCustomers = stats.ActiveCustomers,
                    InactiveCustomers = stats.TotalCustomers - stats.ActiveCustomers,
                    NewCustomersThisMonth = stats.NewCustomers,
                    LastSyncDate = stats.LastSync
                },
                RecentSyncLogs = syncLogs.Select(CustomerIndexViewModel.CustomerSyncLogViewModel.FromSyncLog).ToList(),
                Pagination = new CustomerIndexViewModel.PaginationViewModel
                {
                    CurrentPage = page,
                    TotalPages = totalPages,
                    TotalRecords = totalRecords,
                    PageSize = pageSize
                }
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting customers for company {CompanyId}", companyId);
            return new CustomerIndexViewModel();
        }
    }

    public async Task<(bool Success, string Message)> SyncCustomersAsync(Guid companyId)
    {
        try
        {
            _logger.LogInformation("Starting customer sync for company {CompanyId}", companyId);

            // Aktif entegrasyon servisini al
            var ecommerceService = await _ecommerceServiceFactory.GetEcommerceServiceAsync(companyId);
            if (ecommerceService == null)
            {
                var errorMessage = "Bu şirket için aktif e-ticaret entegrasyonu bulunamadı.";
                _logger.LogWarning(errorMessage + " CompanyId: {CompanyId}", companyId);
                return (false, errorMessage);
            }

            // Entegrasyon ayarlarını al
            var settings = await _ecommerceServiceFactory.GetActiveIntegrationSettingsAsync(companyId);
            if (settings == null)
            {
                var errorMessage = "Entegrasyon ayarları bulunamadı.";
                _logger.LogWarning(errorMessage + " CompanyId: {CompanyId}", companyId);
                return (false, errorMessage);
            }

            // Entegrasyon tipini logla
            var integrationType = await _ecommerceServiceFactory.GetActiveIntegrationTypeAsync(companyId);
            _logger.LogInformation("Using {IntegrationType} integration for customer sync. CompanyId: {CompanyId}",
                integrationType, companyId);

            // İlgili entegrasyon servisini kullanarak senkronizasyonu gerçekleştir
            // Artık direkt IEcommerceService kullanıyoruz
            var filter = new EcommerceCustomerFilter
            {
                IsActive = null, // Tüm müşterileri getir
                EmailPermission = null,
                SmsPermission = null
            };
            var pagination = new EcommercePagination();

            var ecommerceCustomers = await ecommerceService.GetCustomersAsync(companyId, filter, pagination);
            if (ecommerceCustomers == null || ecommerceCustomers.Length == 0)
            {
                return (true, "Senkronize edilecek müşteri bulunamadı");
            }

            // Müşterileri veritabanına kaydet
            var newRecordsAdded = 0;
            var recordsUpdated = 0;

            foreach (var ecommerceCustomer in ecommerceCustomers)
            {
                // Mevcut müşteriyi kontrol et
                var existingCustomer = await _context.Customers
                    .FirstOrDefaultAsync(c => c.ExternalId == int.Parse(ecommerceCustomer.ExternalId) && c.CompanyId == companyId);

                if (existingCustomer == null)
                {
                    // Yeni müşteri oluştur
                    var newCustomer = MapEcommerceCustomerToCustomer(ecommerceCustomer, companyId);
                    _context.Customers.Add(newCustomer);
                    newRecordsAdded++;
                }
                else
                {
                    // Mevcut müşteriyi güncelle
                    UpdateCustomerFromEcommerceCustomer(existingCustomer, ecommerceCustomer);
                    recordsUpdated++;
                }
            }

            await _context.SaveChangesAsync();

            var message = $"{ecommerceCustomers.Length} müşteri başarıyla senkronize edildi (Yeni: {newRecordsAdded}, Güncellenen: {recordsUpdated})";
            return (true, message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during customer sync for company {CompanyId}", companyId);
            return (false, $"Senkronizasyon sırasında hata oluştu: {(ex.Message.Length > 100 ? ex.Message.Substring(0, 100) + "..." : ex.Message)}");
        }
    }

    public async Task<Customer?> GetCustomerByIdAsync(int id, Guid companyId)
    {
        return await _context.Customers
            .FirstOrDefaultAsync(c => c.Id == id && c.CompanyId == companyId);
    }

    public async Task<Customer?> GetCustomerByExternalIdAsync(Guid companyId, int externalId)
    {
        return await _context.Customers
            .FirstOrDefaultAsync(c => c.ExternalId == externalId && c.CompanyId == companyId);
    }

    public async Task<List<SyncLog>> GetSyncLogsAsync(Guid companyId, int limit = 10)
    {
        return await _context.SyncLogs
            .Where(sl => sl.CompanyId == companyId && sl.SyncType == "Customer")
            .OrderByDescending(sl => sl.SyncStartTime)
            .Take(limit)
            .ToListAsync();
    }

    public async Task<(int TotalCustomers, int ActiveCustomers, int NewCustomers, DateTime? LastSync)> GetCustomerStatsAsync(Guid companyId)
    {
        var totalCustomers = await _context.Customers.CountAsync(c => c.CompanyId == companyId);
        var activeCustomers = await _context.Customers.CountAsync(c => c.CompanyId == companyId && c.IsActive);

        var oneMonthAgo = DateTime.UtcNow.AddMonths(-1);
        var newCustomers = await _context.Customers.CountAsync(c => c.CompanyId == companyId && c.MembershipDate >= oneMonthAgo);

        var lastSync = await _context.SyncLogs
            .Where(sl => sl.CompanyId == companyId && sl.SyncType == "Customer" && sl.IsSuccessful)
            .OrderByDescending(sl => sl.SyncEndTime)
            .Select(sl => sl.SyncEndTime)
            .FirstOrDefaultAsync();

        return (totalCustomers, activeCustomers, newCustomers, lastSync);
    }

    private IQueryable<Customer> ApplySorting(IQueryable<Customer> query, string? sortBy, string? sortDirection)
    {
        var isDescending = sortDirection?.ToLower() == "desc";

        return sortBy?.ToLower() switch
        {
            "name" => isDescending
                ? query.OrderByDescending(c => c.FirstName).ThenByDescending(c => c.LastName)
                : query.OrderBy(c => c.FirstName).ThenBy(c => c.LastName),
            "email" => isDescending
                ? query.OrderByDescending(c => c.Email)
                : query.OrderBy(c => c.Email),
            "city" => isDescending
                ? query.OrderByDescending(c => c.City)
                : query.OrderBy(c => c.City),
            "membershipdate" => isDescending
                ? query.OrderByDescending(c => c.MembershipDate)
                : query.OrderBy(c => c.MembershipDate),
            "lastlogindate" => isDescending
                ? query.OrderByDescending(c => c.LastLoginDate)
                : query.OrderBy(c => c.LastLoginDate),
            "status" => isDescending
                ? query.OrderByDescending(c => c.IsActive)
                : query.OrderBy(c => c.IsActive),
            _ => isDescending
                ? query.OrderByDescending(c => c.UpdatedAt)
                : query.OrderBy(c => c.UpdatedAt)
        };
    }

    private Customer MapEcommerceCustomerToCustomer(EcommerceCustomer ecommerceCustomer, Guid companyId)
    {
        return new Customer
        {
            ExternalId = int.TryParse(ecommerceCustomer.ExternalId, out var id) ? id : 0,
            Email = TruncateString(ecommerceCustomer.Email, 200),
            FirstName = TruncateString(ecommerceCustomer.FirstName, 100),
            LastName = TruncateString(ecommerceCustomer.LastName, 100),
            MobilePhone = TruncateString(ecommerceCustomer.Phone, 20),
            BirthDate = ConvertToUtc(ecommerceCustomer.BirthDate),
            GenderId = ecommerceCustomer.Gender switch
            {
                "Male" => 1,
                "Female" => 2,
                _ => 0
            },
            IsActive = ecommerceCustomer.IsActive,
            EmailPermission = ecommerceCustomer.EmailPermission,
            SmsPermission = ecommerceCustomer.SmsPermission,
            MembershipDate = ConvertToUtc(ecommerceCustomer.CreatedAt) ?? DateTime.UtcNow,
            CompanyId = companyId,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow,
            LastSyncDate = DateTime.UtcNow
        };
    }

    private void UpdateCustomerFromEcommerceCustomer(Customer existingCustomer, EcommerceCustomer ecommerceCustomer)
    {
        existingCustomer.Email = TruncateString(ecommerceCustomer.Email, 200);
        existingCustomer.FirstName = TruncateString(ecommerceCustomer.FirstName, 100);
        existingCustomer.LastName = TruncateString(ecommerceCustomer.LastName, 100);
        existingCustomer.MobilePhone = TruncateString(ecommerceCustomer.Phone, 20);
        existingCustomer.BirthDate = ConvertToUtc(ecommerceCustomer.BirthDate);
        existingCustomer.GenderId = ecommerceCustomer.Gender switch
        {
            "Male" => 1,
            "Female" => 2,
            _ => 0
        };
        existingCustomer.IsActive = ecommerceCustomer.IsActive;
        existingCustomer.EmailPermission = ecommerceCustomer.EmailPermission;
        existingCustomer.SmsPermission = ecommerceCustomer.SmsPermission;
        existingCustomer.UpdatedAt = DateTime.UtcNow;
        existingCustomer.LastSyncDate = DateTime.UtcNow;
    }

    private static string TruncateString(string? value, int maxLength)
    {
        if (string.IsNullOrEmpty(value))
            return string.Empty;

        return value.Length <= maxLength ? value : value.Substring(0, maxLength);
    }

    private static DateTime? ConvertToUtc(DateTime? dateTime)
    {
        if (!dateTime.HasValue || dateTime.Value == DateTime.MinValue)
            return null;

        return dateTime.Value.Kind == DateTimeKind.Utc ? dateTime.Value : DateTime.SpecifyKind(dateTime.Value, DateTimeKind.Utc);
    }

    // Yeni metodlar
    public async Task<(bool Success, string Message, int? CustomerId)> CreateCustomerAsync(Guid companyId, CreateCustomerViewModel model)
    {
        return await _customerImportService.CreateCustomerAsync(companyId, model);
    }

    public async Task<(bool Success, string Message, int? JobId)> StartBulkImportAsync(Guid companyId, string userId, IFormFile file)
    {
        try
        {
            // Import job'ı oluştur
            var result = await _customerImportService.CreateImportJobAsync(companyId, userId, file);

            if (result.Success && result.JobId.HasValue)
            {
                // Task.Run ile arka planda çalıştır
                _ = Task.Run(async () =>
                {
                    try
                    {
                        // Scoped service oluştur
                        using var scope = _serviceProvider.CreateScope();
                        var scopedImportService = scope.ServiceProvider.GetRequiredService<ICustomerImportService>();

                        await scopedImportService.ProcessImportJobAsync(result.JobId.Value);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error processing import job {JobId}", result.JobId.Value);
                    }
                });
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error starting bulk import for company {CompanyId}", companyId);
            return (false, "Toplu import başlatılırken hata oluştu: " + ex.Message, null);
        }
    }

    public async Task<CustomerImportProgressViewModel?> GetImportProgressAsync(int jobId, Guid companyId)
    {
        return await _customerImportService.GetImportProgressAsync(jobId, companyId);
    }

    public async Task<List<CustomerImportJobViewModel>> GetImportHistoryAsync(Guid companyId, int limit = 10)
    {
        return await _customerImportService.GetImportHistoryAsync(companyId, limit);
    }

    public byte[] GenerateExcelTemplate()
    {
        return _customerImportService.GenerateExcelTemplate();
    }
}
