namespace PushDashboard.Services;

public interface IR2StorageService
{
    /// <summary>
    /// Upload a JSON file to R2 storage
    /// </summary>
    /// <param name="key">File path/key in R2 (e.g., "company-id/productSlider.json")</param>
    /// <param name="jsonContent">JSON content to upload</param>
    /// <returns>Public URL of the uploaded file</returns>
    Task<string> UploadJsonAsync(string key, string jsonContent);
    
    /// <summary>
    /// Upload a JavaScript file to R2 storage
    /// </summary>
    /// <param name="key">File path/key in R2 (e.g., "company-id/productSlider.js")</param>
    /// <param name="jsContent">JavaScript content to upload</param>
    /// <returns>Public URL of the uploaded file</returns>
    Task<string> UploadJavaScriptAsync(string key, string jsContent);
    
    /// <summary>
    /// Delete a file from R2 storage
    /// </summary>
    /// <param name="key">File path/key in R2</param>
    Task DeleteFileAsync(string key);
    
    /// <summary>
    /// Check if a file exists in R2 storage
    /// </summary>
    /// <param name="key">File path/key in R2</param>
    /// <returns>True if file exists</returns>
    Task<bool> FileExistsAsync(string key);
    
    /// <summary>
    /// Get the public URL for a file
    /// </summary>
    /// <param name="key">File path/key in R2</param>
    /// <returns>Public URL</returns>
    string GetPublicUrl(string key);
}
