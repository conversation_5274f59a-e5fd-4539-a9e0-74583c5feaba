using Microsoft.EntityFrameworkCore;
using PushDashboard.Data;
using PushDashboard.Models;

namespace PushDashboard.Services;

public class CookieManagementService : ICookieManagementService
{
    private readonly ApplicationDbContext _context;
    private readonly ILogger<CookieManagementService> _logger;

    public CookieManagementService(
        ApplicationDbContext context,
        ILogger<CookieManagementService> logger)
    {
        _context = context;
        _logger = logger;
    }

    public async Task<CookieManagement?> GetCookieManagementAsync(Guid companyId)
    {
        try
        {
            return await _context.CookieManagements
                .Include(cm => cm.Company)
                .Include(cm => cm.UpdatedByUser)
                .FirstOrDefaultAsync(cm => cm.CompanyId == companyId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting cookie management for company {CompanyId}", companyId);
            return null;
        }
    }

    public async Task<bool> SaveCookieManagementAsync(CookieManagement cookieManagement)
    {
        try
        {
            var existing = await _context.CookieManagements
                .FirstOrDefaultAsync(cm => cm.CompanyId == cookieManagement.CompanyId);

            if (existing != null)
            {
                // Update existing
                existing.BannerTitle = cookieManagement.BannerTitle;
                existing.BannerDescription = cookieManagement.BannerDescription;
                existing.AcceptButtonText = cookieManagement.AcceptButtonText;
                existing.RejectButtonText = cookieManagement.RejectButtonText;
                existing.SettingsButtonText = cookieManagement.SettingsButtonText;
                existing.SaveButtonText = cookieManagement.SaveButtonText;
                existing.BannerPosition = cookieManagement.BannerPosition;
                existing.BannerBackgroundColor = cookieManagement.BannerBackgroundColor;
                existing.BannerTextColor = cookieManagement.BannerTextColor;
                existing.AcceptButtonColor = cookieManagement.AcceptButtonColor;
                existing.RejectButtonColor = cookieManagement.RejectButtonColor;
                existing.SettingsButtonColor = cookieManagement.SettingsButtonColor;
                existing.BorderRadius = cookieManagement.BorderRadius;
                existing.ShowSettingsButton = cookieManagement.ShowSettingsButton;
                existing.EnableAnimation = cookieManagement.EnableAnimation;
                existing.CategoriesJson = cookieManagement.CategoriesJson;
                existing.IsActive = cookieManagement.IsActive;
                existing.CookieExpiryDays = cookieManagement.CookieExpiryDays;
                existing.UpdatedAt = DateTime.UtcNow;
                existing.UpdatedByUserId = cookieManagement.UpdatedByUserId;

                _context.CookieManagements.Update(existing);
            }
            else
            {
                // Create new
                cookieManagement.CreatedAt = DateTime.UtcNow;
                cookieManagement.UpdatedAt = DateTime.UtcNow;
                _context.CookieManagements.Add(cookieManagement);
            }

            await _context.SaveChangesAsync();
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error saving cookie management for company {CompanyId}", cookieManagement.CompanyId);
            return false;
        }
    }

    public async Task<CookieManagementScriptConfig> GetScriptConfigAsync(Guid companyId)
    {
        try
        {
            // Önce modül kontrolü yap
            var hasModule = await HasCookieManagementModuleAsync(companyId);
            if (!hasModule)
            {
                return new CookieManagementScriptConfig { IsActive = false };
            }

            var cookieManagement = await GetCookieManagementAsync(companyId);
            if (cookieManagement == null || !cookieManagement.IsActive)
            {
                return new CookieManagementScriptConfig { IsActive = false };
            }

            return new CookieManagementScriptConfig
            {
                IsActive = cookieManagement.IsActive,
                BannerTitle = cookieManagement.BannerTitle,
                BannerDescription = cookieManagement.BannerDescription,
                AcceptButtonText = cookieManagement.AcceptButtonText,
                RejectButtonText = cookieManagement.RejectButtonText,
                SettingsButtonText = cookieManagement.SettingsButtonText,
                SaveButtonText = cookieManagement.SaveButtonText,
                BannerPosition = cookieManagement.BannerPosition,
                BannerBackgroundColor = cookieManagement.BannerBackgroundColor,
                BannerTextColor = cookieManagement.BannerTextColor,
                AcceptButtonColor = cookieManagement.AcceptButtonColor,
                RejectButtonColor = cookieManagement.RejectButtonColor,
                SettingsButtonColor = cookieManagement.SettingsButtonColor,
                BorderRadius = cookieManagement.BorderRadius,
                ShowSettingsButton = cookieManagement.ShowSettingsButton,
                EnableAnimation = cookieManagement.EnableAnimation,
                CookieExpiryDays = cookieManagement.CookieExpiryDays,
                Categories = cookieManagement.Categories
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting script config for company {CompanyId}", companyId);
            return new CookieManagementScriptConfig { IsActive = false };
        }
    }

    public async Task<bool> HasCookieManagementModuleAsync(Guid companyId)
    {
        try
        {
            var cookieModule = await _context.Modules
                .FirstOrDefaultAsync(m => m.Name == "Çerez Yönetimi" && m.IsActive);

            if (cookieModule == null)
                return false;

            return await _context.CompanyModules
                .AnyAsync(cm => cm.CompanyId == companyId && 
                               cm.ModuleId == cookieModule.Id && 
                               cm.IsActive);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking cookie management module for company {CompanyId}", companyId);
            return false;
        }
    }

    public async Task<CookieManagement> CreateDefaultSettingsAsync(Guid companyId, string userId)
    {
        var defaultSettings = new CookieManagement
        {
            CompanyId = companyId,
            BannerTitle = "Bu site çerezleri kullanır",
            BannerDescription = "Web sitemizde size en iyi deneyimi sunabilmek için çerezleri kullanıyoruz. Çerez kullanımını kabul ederek daha iyi bir deneyim yaşayabilirsiniz.",
            AcceptButtonText = "Tümünü Kabul Et",
            RejectButtonText = "Tümünü Reddet",
            SettingsButtonText = "Ayarları Yönet",
            SaveButtonText = "Seçimleri Kaydet",
            BannerPosition = "bottom",
            BannerBackgroundColor = "#ffffff",
            BannerTextColor = "#333333",
            AcceptButtonColor = "#4CAF50",
            RejectButtonColor = "#f44336",
            SettingsButtonColor = "#2196F3",
            BorderRadius = "8px",
            ShowSettingsButton = true,
            EnableAnimation = true,
            IsActive = true,
            CookieExpiryDays = 365,
            UpdatedByUserId = userId
        };

        // Varsayılan kategorileri ayarla
        defaultSettings.Categories = defaultSettings.Categories; // Bu GetDefaultCategories() çağırır

        return defaultSettings;
    }
}
