using Microsoft.EntityFrameworkCore;
using PushDashboard.Data;
using PushDashboard.Models;
using PushDashboard.ViewModels;
using PushDashboard.Services.Integrations;
using PushDashboard.Services.Notifications;
using PushDashboard.Services.WhatsApp;
using System.Text.Json;

namespace PushDashboard.Services;

public class BasketReminderService : IBasketReminderService
{
    private readonly ApplicationDbContext _context;
    private readonly ILogger<BasketReminderService> _logger;
    private readonly IEcommerceServiceFactory _ecommerceServiceFactory;
    private readonly EmailNotificationChannelService _emailNotificationService;
    private readonly WhatsAppNotificationChannelService _whatsAppNotificationService;
    private readonly ICustomerService _customerService;
    private readonly ICommunicationChannelService _communicationChannelService;
    private readonly IServiceProvider _serviceProvider;

    public BasketReminderService(
        ApplicationDbContext context,
        ILogger<BasketReminderService> logger,
        IEcommerceServiceFactory ecommerceServiceFactory,
        EmailNotificationChannelService emailNotificationService,
        WhatsAppNotificationChannelService whatsAppNotificationService,
        ICustomerService customerService,
        ICommunicationChannelService communicationChannelService,
        IServiceProvider serviceProvider)
    {
        _context = context;
        _logger = logger;
        _ecommerceServiceFactory = ecommerceServiceFactory;
        _emailNotificationService = emailNotificationService;
        _whatsAppNotificationService = whatsAppNotificationService;
        _customerService = customerService;
        _communicationChannelService = communicationChannelService;
        _serviceProvider = serviceProvider;
    }

    #region Settings Management

    public async Task<BasketReminderSettings?> GetSettingsAsync(Guid companyId)
    {
        return await _context.BasketReminderSettings
            .FirstOrDefaultAsync(brs => brs.CompanyId == companyId);
    }

    public async Task<(bool Success, string Message)> UpdateSettingsAsync(Guid companyId, BasketReminderSettings settings, string userId)
    {
        try
        {
            var existingSettings = await _context.BasketReminderSettings
                .FirstOrDefaultAsync(brs => brs.CompanyId == companyId);

            if (existingSettings == null)
            {
                // Yeni ayar oluştur
                var newSettings = new BasketReminderSettings
                {
                    CompanyId = companyId,
                    MaxNotificationsPerCustomer = settings.MaxNotificationsPerCustomer,
                    NotificationContent = settings.NotificationContent,
                    IsEnabled = settings.IsEnabled,
                    UpdatedByUserId = userId
                };

                _context.BasketReminderSettings.Add(newSettings);
            }
            else
            {
                // Mevcut ayarı güncelle
                existingSettings.MaxNotificationsPerCustomer = settings.MaxNotificationsPerCustomer;
                existingSettings.NotificationContent = settings.NotificationContent;
                existingSettings.IsEnabled = settings.IsEnabled;
                existingSettings.UpdatedAt = DateTime.UtcNow;
                existingSettings.UpdatedByUserId = userId;
            }

            await _context.SaveChangesAsync();

            _logger.LogInformation("Basket reminder settings updated for company {CompanyId} by user {UserId}", companyId, userId);
            return (true, "Ayarlar başarıyla güncellendi.");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating basket reminder settings for company {CompanyId}", companyId);
            return (false, "Ayarlar güncellenirken hata oluştu: " + ex.Message);
        }
    }

    public async Task<(bool Success, string Message)> CreateDefaultSettingsAsync(Guid companyId, string userId)
    {
        try
        {
            // Zaten ayar var mı kontrol et
            var existingSettings = await _context.BasketReminderSettings
                .FirstOrDefaultAsync(brs => brs.CompanyId == companyId);

            if (existingSettings != null)
            {
                _logger.LogInformation("Basket reminder settings already exist for company {CompanyId}", companyId);
                return (true, "Ayarlar zaten mevcut.");
            }

            // Varsayılan ayarları oluştur
            var defaultSettings = new BasketReminderSettings
            {
                CompanyId = companyId,
                MaxNotificationsPerCustomer = 1, // İstediğiniz gibi 1
                NotificationContent = "", // Boş string
                IsEnabled = true, // Varsayılan olarak aktif
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow,
                UpdatedByUserId = userId
            };

            _context.BasketReminderSettings.Add(defaultSettings);
            await _context.SaveChangesAsync();

            _logger.LogInformation("Created default basket reminder settings for company {CompanyId} by user {UserId}", companyId, userId);
            return (true, "Varsayılan ayarlar başarıyla oluşturuldu.");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating default basket reminder settings for company {CompanyId}", companyId);
            return (false, "Varsayılan ayarlar oluşturulurken bir hata oluştu.");
        }
    }

    #endregion

    #region Schedule Management

    public async Task<List<BasketReminderSchedule>> GetActiveSchedulesAsync(Guid companyId)
    {
        return await _context.BasketReminderSchedules
            .Where(brs => brs.CompanyId == companyId && brs.IsActive)
            .OrderBy(brs => brs.ReminderTimeForHours)
            .ToListAsync();
    }

    public async Task<List<BasketReminderSchedule>> GetAllSchedulesAsync(Guid companyId)
    {
        return await _context.BasketReminderSchedules
            .Include(brs => brs.CreatedByUser)
            .Where(brs => brs.CompanyId == companyId)
            .OrderBy(brs => brs.ReminderTimeForHours)
            .ToListAsync();
    }

    public async Task<(bool Success, string Message, int? ScheduleId)> CreateScheduleAsync(Guid companyId, BasketReminderSchedule schedule, string userId)
    {
        try
        {
            // Aynı zamanlama zaten var mı kontrol et
            var existingSchedule = await _context.BasketReminderSchedules
                .FirstOrDefaultAsync(brs => brs.CompanyId == companyId && 
                                          brs.ReminderTimeForHours == schedule.ReminderTimeForHours);

            if (existingSchedule != null)
            {
                return (false, "Bu zamanlama için zaten bir hatırlatma ayarı bulunmaktadır.", null);
            }

            var newSchedule = new BasketReminderSchedule
            {
                CompanyId = companyId,
                Name = schedule.Name,
                ReminderTimeForHours = schedule.ReminderTimeForHours,
                NotificationContent = schedule.NotificationContent,
                CommunicationChannels = schedule.CommunicationChannels,
                ChannelMessages = schedule.ChannelMessages,
                IsActive = schedule.IsActive,
                CreatedAt = DateTime.UtcNow,
                CreatedByUserId = userId
            };

            _context.BasketReminderSchedules.Add(newSchedule);
            await _context.SaveChangesAsync();

            _logger.LogInformation("Basket reminder schedule created for company {CompanyId} by user {UserId}. Schedule: {ScheduleName} ({Hours}h)", 
                companyId, userId, schedule.Name, schedule.ReminderTimeForHours);

            return (true, "Hatırlatma zamanlaması başarıyla oluşturuldu.", newSchedule.Id);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating basket reminder schedule for company {CompanyId}", companyId);
            return (false, "Zamanlama oluşturulurken hata oluştu: " + ex.Message, null);
        }
    }

    public async Task<(bool Success, string Message)> UpdateScheduleAsync(Guid companyId, BasketReminderSchedule schedule, string userId)
    {
        try
        {
            var existingSchedule = await _context.BasketReminderSchedules
                .FirstOrDefaultAsync(brs => brs.Id == schedule.Id && brs.CompanyId == companyId);

            if (existingSchedule == null)
            {
                return (false, "Zamanlama bulunamadı.");
            }

            // Aynı zamanlama başka bir kayıtta var mı kontrol et
            var duplicateSchedule = await _context.BasketReminderSchedules
                .FirstOrDefaultAsync(brs => brs.CompanyId == companyId && 
                                          brs.ReminderTimeForHours == schedule.ReminderTimeForHours &&
                                          brs.Id != schedule.Id);

            if (duplicateSchedule != null)
            {
                return (false, "Bu zamanlama için zaten başka bir hatırlatma ayarı bulunmaktadır.");
            }

            existingSchedule.Name = schedule.Name;
            existingSchedule.NotificationContent = schedule.NotificationContent;
            existingSchedule.CommunicationChannels = schedule.CommunicationChannels;
            existingSchedule.ChannelMessages = schedule.ChannelMessages;
            existingSchedule.ReminderTimeForHours = schedule.ReminderTimeForHours;
            existingSchedule.IsActive = schedule.IsActive;
            existingSchedule.UpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();

            _logger.LogInformation("Basket reminder schedule updated for company {CompanyId} by user {UserId}. Schedule: {ScheduleId}", 
                companyId, userId, schedule.Id);

            return (true, "Hatırlatma zamanlaması başarıyla güncellendi.");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating basket reminder schedule for company {CompanyId}", companyId);
            return (false, "Zamanlama güncellenirken hata oluştu: " + ex.Message);
        }
    }

    public async Task<(bool Success, string Message)> DeleteScheduleAsync(Guid companyId, int scheduleId, string userId)
    {
        try
        {
            var schedule = await _context.BasketReminderSchedules
                .FirstOrDefaultAsync(brs => brs.Id == scheduleId && brs.CompanyId == companyId);

            if (schedule == null)
            {
                return (false, "Zamanlama bulunamadı.");
            }

            _context.BasketReminderSchedules.Remove(schedule);
            await _context.SaveChangesAsync();

            _logger.LogInformation("Basket reminder schedule deleted for company {CompanyId} by user {UserId}. Schedule: {ScheduleId}", 
                companyId, userId, scheduleId);

            return (true, "Hatırlatma zamanlaması başarıyla silindi.");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting basket reminder schedule for company {CompanyId}", companyId);
            return (false, "Zamanlama silinirken hata oluştu: " + ex.Message);
        }
    }

    public async Task<(bool Success, string Message)> ToggleScheduleStatusAsync(Guid companyId, int scheduleId, string userId)
    {
        try
        {
            var schedule = await _context.BasketReminderSchedules
                .FirstOrDefaultAsync(brs => brs.Id == scheduleId && brs.CompanyId == companyId);

            if (schedule == null)
            {
                return (false, "Zamanlama bulunamadı.");
            }

            schedule.IsActive = !schedule.IsActive;
            schedule.UpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();

            var statusText = schedule.IsActive ? "aktif" : "pasif";
            _logger.LogInformation("Basket reminder schedule status changed to {Status} for company {CompanyId} by user {UserId}. Schedule: {ScheduleId}", 
                statusText, companyId, userId, scheduleId);

            return (true, $"Zamanlama durumu {statusText} olarak güncellendi.");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error toggling basket reminder schedule status for company {CompanyId}", companyId);
            return (false, "Zamanlama durumu değiştirilirken hata oluştu: " + ex.Message);
        }
    }

    #endregion

    #region Reminder Processing

    public async Task<(bool Success, string Message, int ProcessedCount)> ProcessRemindersForCompanyAsync(Guid companyId)
    {
        try
        {
            _logger.LogInformation("Starting basket reminder processing for company {CompanyId}", companyId);

            // E-ticaret entegrasyonu kontrolü
            var (isValid, validationMessage) = await ValidateEcommerceIntegrationAsync(companyId);
            if (!isValid)
            {
                return (false, validationMessage, 0);
            }

            // Şirket ayarlarını al
            var settings = await GetSettingsAsync(companyId);
            if (settings == null || !settings.IsEnabled)
            {
                return (false, "Sepet hatırlatma modülü bu şirket için aktif değil.", 0);
            }

            // Aktif zamanlamaları al
            var activeSchedules = await GetActiveSchedulesAsync(companyId);
            if (!activeSchedules.Any())
            {
                return (false, "Aktif hatırlatma zamanlaması bulunamadı.", 0);
            }

            int totalProcessed = 0;
            var errors = new List<string>();

            foreach (var schedule in activeSchedules)
            {
                try
                {
                    // Bu zamanlama için uygun sepetleri al
                    var eligibleBaskets = await GetEligibleBasketsForReminderAsync(companyId, schedule.ReminderTimeForHours);

                    foreach (var basket in eligibleBaskets)
                    {
                        // Müşteri bildirim limitini schedule bazında kontrol et
                        var (canSend, limitMessage, sentCount) = await CheckCustomerNotificationLimitForScheduleAsync(
                            companyId, basket.CustomerId, schedule.Id, settings.MaxNotificationsPerCustomer);

                        if (!canSend)
                        {
                            _logger.LogInformation("Customer {CustomerId} has reached notification limit for schedule {ScheduleId} ({SentCount}/{MaxLimit})",
                                basket.CustomerId, schedule.Id, sentCount, settings.MaxNotificationsPerCustomer);
                            continue;
                        }

                        // Sepet platformda hala var mı kontrol et
                        var (basketExists, basketMessage) = await ValidateBasketExistsAsync(companyId, basket.ExternalId);
                        if (!basketExists)
                        {
                            _logger.LogInformation("Basket {BasketExternalId} no longer exists on platform: {Message}",
                                basket.ExternalId, basketMessage);
                            continue;
                        }

                        // Hatırlatma gönder - schedule'dan kanal bilgilerini kullan
                        var (success, message) = await SendReminderWithScheduleAsync(companyId, basket, schedule);
                        if (success)
                        {
                            totalProcessed++;
                        }
                        else
                        {
                            errors.Add($"Sepet {basket.ExternalId}: {message}");
                        }
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error processing schedule {ScheduleId} for company {CompanyId}", schedule.Id, companyId);
                    errors.Add($"Zamanlama {schedule.Name}: {ex.Message}");
                }
            }

            var resultMessage = $"{totalProcessed} hatırlatma başarıyla gönderildi.";
            if (errors.Any())
            {
                resultMessage += $" {errors.Count} hata oluştu.";
            }

            _logger.LogInformation("Basket reminder processing completed for company {CompanyId}. Processed: {ProcessedCount}, Errors: {ErrorCount}",
                companyId, totalProcessed, errors.Count);

            return (true, resultMessage, totalProcessed);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing basket reminders for company {CompanyId}", companyId);
            return (false, "Hatırlatma işlemi sırasında hata oluştu: " + ex.Message, 0);
        }
    }

    public async Task<(bool Success, string Message, int ProcessedCompanies, int TotalReminders)> ProcessAllRemindersAsync()
    {
        try
        {
            _logger.LogInformation("Starting basket reminder processing for all companies");

            // Sepet hatırlatma modülüne sahip aktif şirketleri al
            var companiesWithModule = await _context.CompanyModules
                .Include(cm => cm.Module)
                .Include(cm => cm.Company)
                .Where(cm => cm.Module.Name == "Sepet Hatırlatma" && cm.IsActive)
                .Select(cm => cm.CompanyId)
                .ToListAsync();

            if (!companiesWithModule.Any())
            {
                return (true, "Sepet hatırlatma modülüne sahip aktif şirket bulunamadı.", 0, 0);
            }

            int processedCompanies = 0;
            int totalReminders = 0;
            var errors = new List<string>();

            foreach (var companyId in companiesWithModule)
            {
                try
                {
                    var (success, message, processedCount) = await ProcessRemindersForCompanyAsync(companyId);
                    if (success)
                    {
                        processedCompanies++;
                        totalReminders += processedCount;
                    }
                    else
                    {
                        errors.Add($"Şirket {companyId}: {message}");
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error processing reminders for company {CompanyId}", companyId);
                    errors.Add($"Şirket {companyId}: {ex.Message}");
                }
            }

            var resultMessage = $"{processedCompanies} şirket için {totalReminders} hatırlatma işlendi.";
            if (errors.Any())
            {
                resultMessage += $" {errors.Count} hata oluştu.";
            }

            _logger.LogInformation("Basket reminder processing completed for all companies. Companies: {ProcessedCompanies}, Total reminders: {TotalReminders}, Errors: {ErrorCount}",
                processedCompanies, totalReminders, errors.Count);

            return (true, resultMessage, processedCompanies, totalReminders);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing basket reminders for all companies");
            return (false, "Tüm şirketler için hatırlatma işlemi sırasında hata oluştu: " + ex.Message, 0, 0);
        }
    }

    public async Task<(bool Success, string Message)> SendReminderAsync(Guid companyId, Basket basket, int reminderTimeForHours, string notificationContent)
    {
        // Geriye dönük uyumluluk için sadece email kanalını kullan
        var channels = new List<string> { "Email" };
        var channelMessages = new Dictionary<string, string> { { "Email", notificationContent } };

        return await SendReminderWithChannelsAsync(companyId, basket, reminderTimeForHours, channels, channelMessages);
    }

    public async Task<(bool Success, string Message)> SendReminderWithScheduleAsync(Guid companyId, Basket basket, BasketReminderSchedule schedule)
    {
        try
        {
            // Schedule'dan kanal bilgilerini parse et
            var channels = new List<string>();
            var channelMessages = new Dictionary<string, object>();

            try
            {
                channels = JsonSerializer.Deserialize<List<string>>(schedule.CommunicationChannels ?? "[]") ?? new List<string>();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error parsing communication channels for schedule {ScheduleId}", schedule.Id);
                channels = new List<string> { "Email" }; // Fallback
            }

            try
            {
                var channelMessagesJson = JsonSerializer.Deserialize<Dictionary<string, object>>(schedule.ChannelMessages ?? "{}") ?? new Dictionary<string, object>();
                channelMessages = channelMessagesJson;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error parsing channel messages for schedule {ScheduleId}", schedule.Id);
                channelMessages = new Dictionary<string, object> { { "default", schedule.NotificationContent } };
            }

            // Eğer kanal yoksa, geriye dönük uyumluluk için Email kullan
            if (!channels.Any())
            {
                channels = new List<string> { "Email" };
                channelMessages = new Dictionary<string, object> { { "Email", schedule.NotificationContent } };
            }

            return await SendReminderWithChannelsAsync(companyId, basket, schedule.ReminderTimeForHours, channels, channelMessages, schedule.Id);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending reminder with schedule {ScheduleId}", schedule.Id);
            return (false, "Hatırlatma gönderilirken hata oluştu: " + ex.Message);
        }
    }

    public async Task<(bool Success, string Message)> SendReminderWithChannelsAsync(
        Guid companyId,
        Basket basket,
        int reminderTimeForHours,
        List<string> channels,
        Dictionary<string, string> channelMessages)
    {
        // Geriye dönük uyumluluk için basit string mesajları kullan
        var channelMessagesObj = channelMessages.ToDictionary(
            kvp => kvp.Key,
            kvp => (object)kvp.Value
        );

        return await SendReminderWithChannelsAsync(companyId, basket, reminderTimeForHours, channels, channelMessagesObj, null);
    }

    public async Task<(bool Success, string Message)> SendReminderWithChannelsAsync(
        Guid companyId,
        Basket basket,
        int reminderTimeForHours,
        List<string> channels,
        Dictionary<string, object> channelMessages,
        int? scheduleId = null)
    {
        try
        {
            // Müşteri bilgilerini al
            var customer = await _customerService.GetCustomerByExternalIdAsync(companyId, basket.CustomerId);
            if (customer == null)
            {
                return (false, "Müşteri bilgisi bulunamadı.");
            }

            var successfulChannels = new List<string>();
            var failedChannels = new List<string>();
            var errors = new List<string>();

            // Her kanal için mesaj gönder
            foreach (var channel in channels)
            {
                try
                {
                    var channelMessage = channelMessages.ContainsKey(channel)
                        ? channelMessages[channel]?.ToString() ?? ""
                        : channelMessages.Values.FirstOrDefault()?.ToString() ?? "";

                    // Placeholder'ları değiştir
                    var personalizedContent = ReplacePlaceholders(channelMessage, customer.FirstName, customer.LastName);

                    var variables = new Dictionary<string, string>
                    {
                        {"FirstName", customer.FirstName},
                        {"LastName", customer.LastName},
                        {"FullName", customer.FullName},
                        {"Content", personalizedContent}
                    };

                    bool sent = false;
                    string errorMessage = "";

                    switch (channel)
                    {
                        case "Email":
                            if (customer.EmailPermission && !string.IsNullOrEmpty(customer.Email))
                            {
                                sent = await _emailNotificationService.SendNotificationAsync(
                                    companyId, customer, "basket_reminder", variables);
                                if (!sent) errorMessage = "Email gönderimi başarısız oldu.";
                            }
                            else
                            {
                                errorMessage = "Müşterinin email izni yok veya email adresi eksik.";
                            }
                            break;

                        case "WhatsApp":
                            if (customer.SmsPermission && (!string.IsNullOrEmpty(customer.Phone) || !string.IsNullOrEmpty(customer.MobilePhone)))
                            {
                                // WhatsApp template kontrolü
                                if (channelMessages.ContainsKey(channel) && channelMessages[channel] is Dictionary<string, object> whatsappConfig)
                                {
                                    // Template kullanarak gönder
                                    if (whatsappConfig.ContainsKey("templateId") && whatsappConfig["templateId"] is string templateId)
                                    {
                                        sent = await SendWhatsAppTemplateMessageAsync(companyId, customer, templateId, variables);
                                        if (!sent) errorMessage = "WhatsApp template mesajı gönderilemedi.";
                                    }
                                    else
                                    {
                                        errorMessage = "WhatsApp template ID bulunamadı.";
                                    }
                                }
                                else
                                {
                                    // Geriye dönük uyumluluk - normal mesaj gönder
                                    sent = await _whatsAppNotificationService.SendNotificationAsync(
                                        companyId, customer, "basket_reminder", variables);
                                    if (!sent) errorMessage = "WhatsApp mesajı gönderilemedi.";
                                }
                            }
                            else
                            {
                                errorMessage = "Müşterinin SMS izni yok veya telefon numarası eksik.";
                            }
                            break;

                        case "Telegram":
                            // Telegram desteği gelecekte eklenecek
                            errorMessage = "Telegram desteği henüz aktif değil.";
                            break;

                        default:
                            errorMessage = $"Desteklenmeyen kanal: {channel}";
                            break;
                    }

                    if (sent)
                    {
                        successfulChannels.Add(channel);
                        _logger.LogInformation("Basket reminder sent via {Channel}. Basket: {BasketExternalId}, Customer: {CustomerEmail}",
                            channel, basket.ExternalId, customer.Email);
                    }
                    else
                    {
                        failedChannels.Add(channel);
                        errors.Add($"{channel}: {errorMessage}");
                        _logger.LogWarning("Failed to send basket reminder via {Channel}. Basket: {BasketExternalId}, Error: {Error}",
                            channel, basket.ExternalId, errorMessage);
                    }

                    // Her kanal için ayrı log oluştur
                    var log = new BasketReminderLog
                    {
                        BasketId = basket.Id,
                        BasketExternalId = basket.ExternalId,
                        CompanyId = companyId,
                        CustomerId = basket.CustomerId,
                        CustomerEmail = customer.Email,
                        CustomerName = customer.FullName,
                        ReminderTimeForHours = reminderTimeForHours,
                        BasketReminderScheduleId = scheduleId,
                        IsSuccessful = sent,
                        NotificationContent = $"[{channel}] {personalizedContent}",
                        ErrorMessage = sent ? null : errorMessage
                    };

                    await CreateReminderLogAsync(log);
                }
                catch (Exception ex)
                {
                    failedChannels.Add(channel);
                    errors.Add($"{channel}: {ex.Message}");
                    _logger.LogError(ex, "Error sending basket reminder via {Channel} for basket {BasketExternalId}",
                        channel, basket.ExternalId);
                }
            }

            // Sonuç mesajını oluştur
            if (successfulChannels.Any())
            {
                var message = $"Hatırlatma başarıyla gönderildi: {string.Join(", ", successfulChannels)}";
                if (failedChannels.Any())
                {
                    message += $". Başarısız: {string.Join(", ", failedChannels)}";
                }
                return (true, message);
            }
            else
            {
                var errorMessage = $"Tüm kanallar başarısız oldu: {string.Join(", ", errors)}";
                return (false, errorMessage);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending basket reminder for basket {BasketExternalId}", basket.ExternalId);

            // Hata logunu da kaydet
            var errorLog = new BasketReminderLog
            {
                BasketId = basket.Id,
                BasketExternalId = basket.ExternalId,
                CompanyId = companyId,
                CustomerId = basket.CustomerId,
                CustomerEmail = basket.CustomerEmail,
                CustomerName = basket.CustomerName,
                ReminderTimeForHours = reminderTimeForHours,
                BasketReminderScheduleId = scheduleId,
                IsSuccessful = false,
                NotificationContent = string.Join(", ", channels),
                ErrorMessage = ex.Message
            };

            await CreateReminderLogAsync(errorLog);

            return (false, "Hatırlatma gönderilirken hata oluştu: " + ex.Message);
        }
    }

    #endregion

    #region Validation

    public async Task<(bool IsValid, string Message)> ValidateEcommerceIntegrationAsync(Guid companyId)
    {
        try
        {
            // E-ticaret entegrasyonu var mı kontrol et
            var ecommerceIntegration = await _context.CompanyIntegrations
                .Include(ci => ci.Integration)
                .FirstOrDefaultAsync(ci => ci.CompanyId == companyId &&
                                         ci.Integration.Type == "ecommerce" &&
                                         ci.IsActive &&
                                         ci.IsConfigured);

            if (ecommerceIntegration == null)
            {
                return (false, "Aktif e-ticaret entegrasyonu bulunamadı. Lütfen önce e-ticaret entegrasyonunuzu yapılandırın.");
            }

            // Sepet senkronizasyonu aktif mi kontrol et
            var settings = ecommerceIntegration.Settings;
            if (settings.ContainsKey("syncCarts") && settings["syncCarts"].ToString() == "True")
            {
                return (true, "E-ticaret entegrasyonu ve sepet senkronizasyonu aktif.");
            }

            return (false, "Sepet senkronizasyonu aktif değil. Lütfen e-ticaret entegrasyon ayarlarınızda 'Sepetleri Senkronize Et' seçeneğini aktif hale getirin.");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating ecommerce integration for company {CompanyId}", companyId);
            return (false, "E-ticaret entegrasyonu kontrolü sırasında hata oluştu.");
        }
    }

    public async Task<(bool IsValid, string Message)> ValidateBasketExistsAsync(Guid companyId, string basketExternalId)
    {
        try
        {
            var ecommerceService = await _ecommerceServiceFactory.GetEcommerceServiceAsync(companyId);
            if (ecommerceService == null)
            {
                return (false, "E-ticaret servisi bulunamadı.");
            }

            // Platform üzerinde sepeti kontrol et
            var basketExists = await ecommerceService.ValidateBasketExistsAsync(companyId, basketExternalId);
            if (basketExists)
            {
                return (true, "Sepet platformda mevcut.");
            }

            return (false, "Sepet platformda bulunamadı.");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating basket existence for basket {BasketExternalId}", basketExternalId);
            return (false, "Sepet doğrulama sırasında hata oluştu.");
        }
    }

    public async Task<(bool CanSend, string Message, int SentCount)> CheckCustomerNotificationLimitAsync(Guid companyId, int customerId, int maxNotifications)
    {
        try
        {
            // Son 30 gün içinde bu müşteriye gönderilen hatırlatma sayısını al
            var thirtyDaysAgo = DateTime.UtcNow.AddDays(-30);
            var sentCount = await _context.BasketReminderLogs
                .CountAsync(brl => brl.CompanyId == companyId &&
                                 brl.CustomerId == customerId &&
                                 brl.IsSuccessful &&
                                 brl.SentAt >= thirtyDaysAgo);

            if (sentCount >= maxNotifications)
            {
                return (false, $"Müşteri son 30 gün içinde maksimum bildirim limitine ulaştı ({sentCount}/{maxNotifications}).", sentCount);
            }

            return (true, "Müşteri bildirim limiti uygun.", sentCount);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking customer notification limit for customer {CustomerId}", customerId);
            return (false, "Müşteri bildirim limiti kontrolü sırasında hata oluştu.", 0);
        }
    }

    public async Task<(bool CanSend, string Message, int SentCount)> CheckCustomerNotificationLimitForScheduleAsync(Guid companyId, int customerId, int scheduleId, int maxNotifications)
    {
        try
        {
            // Son 30 gün içinde bu müşteriye bu schedule için gönderilen hatırlatma sayısını al
            var thirtyDaysAgo = DateTime.UtcNow.AddDays(-30);
            var sentCount = await _context.BasketReminderLogs
                .CountAsync(brl => brl.CompanyId == companyId &&
                                 brl.CustomerId == customerId &&
                                 brl.BasketReminderScheduleId == scheduleId &&
                                 brl.IsSuccessful &&
                                 brl.SentAt >= thirtyDaysAgo);

            if (sentCount >= maxNotifications)
            {
                return (false, $"Müşteri bu zamanlama için son 30 gün içinde maksimum bildirim limitine ulaştı ({sentCount}/{maxNotifications}).", sentCount);
            }

            return (true, "Müşteri bu zamanlama için bildirim limiti uygun.", sentCount);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking customer notification limit for schedule {ScheduleId} and customer {CustomerId}", scheduleId, customerId);
            return (false, "Müşteri bildirim limiti kontrolü sırasında hata oluştu.", 0);
        }
    }

    #endregion

    #region Logs and Statistics

    public async Task<List<BasketReminderLog>> GetReminderLogsAsync(Guid companyId, int page = 1, int pageSize = 50)
    {
        return await _context.BasketReminderLogs
            .Where(brl => brl.CompanyId == companyId)
            .OrderByDescending(brl => brl.SentAt)
            .Skip((page - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync();
    }

    public async Task<BasketReminderStatsViewModel> GetReminderStatsAsync(Guid companyId, DateTime? startDate = null, DateTime? endDate = null)
    {
        var today = DateTime.UtcNow.Date;
        var weekStart = today.AddDays(-(int)today.DayOfWeek);
        var monthStart = new DateTime(today.Year, today.Month, 1);

        startDate ??= today.AddDays(-7);
        endDate ??= today.AddDays(1);

        var stats = new BasketReminderStatsViewModel();

        // Bugünkü istatistikler
        var todayLogs = await _context.BasketReminderLogs
            .Where(brl => brl.CompanyId == companyId && brl.SentAt >= today)
            .ToListAsync();

        stats.TotalRemindersToday = todayLogs.Count;
        stats.SuccessfulRemindersToday = todayLogs.Count(l => l.IsSuccessful);
        stats.FailedRemindersToday = todayLogs.Count(l => !l.IsSuccessful);
        stats.SuccessRateToday = stats.TotalRemindersToday > 0 ?
            (decimal)stats.SuccessfulRemindersToday / stats.TotalRemindersToday * 100 : 0;

        // Haftalık istatistikler
        stats.TotalRemindersThisWeek = await _context.BasketReminderLogs
            .CountAsync(brl => brl.CompanyId == companyId && brl.SentAt >= weekStart);

        // Aylık istatistikler
        stats.TotalRemindersThisMonth = await _context.BasketReminderLogs
            .CountAsync(brl => brl.CompanyId == companyId && brl.SentAt >= monthStart);

        // Aktif zamanlama sayısı
        stats.ActiveSchedulesCount = await _context.BasketReminderSchedules
            .CountAsync(brs => brs.CompanyId == companyId && brs.IsActive);

        // Uygun sepet sayısı (örnek olarak son 24 saat)
        stats.EligibleBasketsCount = await GetEligibleBasketsForReminderAsync(companyId, 24)
            .ContinueWith(t => t.Result.Count);

        // Günlük istatistikler (grafik için)
        var dailyStats = await _context.BasketReminderLogs
            .Where(brl => brl.CompanyId == companyId && brl.SentAt >= startDate && brl.SentAt < endDate)
            .GroupBy(brl => brl.SentAt.Date)
            .Select(g => new DailyReminderStatsViewModel
            {
                Date = g.Key,
                TotalReminders = g.Count(),
                SuccessfulReminders = g.Count(l => l.IsSuccessful),
                FailedReminders = g.Count(l => !l.IsSuccessful)
            })
            .OrderBy(ds => ds.Date)
            .ToListAsync();

        stats.DailyStats = dailyStats;

        return stats;
    }

    public async Task CreateReminderLogAsync(BasketReminderLog log)
    {
        try
        {
            _context.BasketReminderLogs.Add(log);
            await _context.SaveChangesAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating basket reminder log for basket {BasketExternalId}", log.BasketExternalId);
        }
    }

    public async Task CreateReminderLogAsync(BasketReminderLog log, int? scheduleId)
    {
        try
        {
            log.BasketReminderScheduleId = scheduleId;
            _context.BasketReminderLogs.Add(log);
            await _context.SaveChangesAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating basket reminder log for basket {BasketExternalId} with schedule {ScheduleId}",
                log.BasketExternalId, scheduleId);
        }
    }

    #endregion

    #region Helper Methods

    public string ReplacePlaceholders(string content, string firstName, string lastName)
    {
        if (string.IsNullOrEmpty(content))
            return content;

        var fullName = $"{firstName} {lastName}".Trim();

        return content
            // İngilizce placeholder'lar
            .Replace("{FirstName}", firstName ?? "")
            .Replace("{LastName}", lastName ?? "")
            .Replace("{FullName}", fullName)
            // Türkçe placeholder'lar (geriye dönük uyumluluk için)
            .Replace("{ISIM}", firstName ?? "")
            .Replace("{SOYISIM}", lastName ?? "")
            .Replace("{ADSOYAD}", fullName)
            // Küçük harf versiyonları
            .Replace("{isim}", firstName ?? "")
            .Replace("{soyisim}", lastName ?? "")
            .Replace("{adsoyad}", fullName);
    }

    public async Task<List<Basket>> GetEligibleBasketsForReminderAsync(Guid companyId, int reminderTimeForHours)
    {
        try
        {
            var cutoffTime = DateTime.UtcNow.AddHours(-reminderTimeForHours);

            // Hatırlatma zamanı geçmiş, aktif, terk edilmiş sepetleri al
            var eligibleBaskets = await _context.Baskets
                .Where(b => b.CompanyId == companyId &&
                           b.IsActive &&
                           b.IsAbandoned &&
                           b.LastUpdateDate.HasValue &&
                           b.LastUpdateDate.Value <= cutoffTime &&
                           !string.IsNullOrEmpty(b.CustomerEmail))
                .ToListAsync();

            // Bu sepetler için zaten hatırlatma gönderilmiş mi kontrol et
            var basketIds = eligibleBaskets.Select(b => b.Id).ToList();
            var alreadySentBaskets = await _context.BasketReminderLogs
                .Where(brl => basketIds.Contains(brl.BasketId) &&
                             brl.ReminderTimeForHours == reminderTimeForHours &&
                             brl.IsSuccessful)
                .Select(brl => brl.BasketId)
                .ToListAsync();

            // Zaten hatırlatma gönderilmiş sepetleri filtrele
            var filteredBaskets = eligibleBaskets
                .Where(b => !alreadySentBaskets.Contains(b.Id))
                .ToList();

            _logger.LogInformation("Found {EligibleCount} eligible baskets for {Hours}h reminder for company {CompanyId}",
                filteredBaskets.Count, reminderTimeForHours, companyId);

            return filteredBaskets;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting eligible baskets for company {CompanyId}", companyId);
            return new List<Basket>();
        }
    }

    public async Task<List<CommunicationChannel>> GetAvailableChannelsAsync(Guid companyId)
    {
        return await _communicationChannelService.GetAvailableChannelsAsync(companyId);
    }

    private async Task<bool> SendWhatsAppTemplateMessageAsync(Guid companyId, Customer customer, string templateId, Dictionary<string, string> variables)
    {
        try
        {
            // WhatsApp servisini kullanarak template mesajı gönder
            var whatsAppService = _serviceProvider.GetService<IWhatsAppService>();
            if (whatsAppService == null)
            {
                _logger.LogError("WhatsApp service not found");
                return false;
            }

            // WhatsApp ayarlarını al
            var whatsAppSettings = await GetWhatsAppSettingsAsync(companyId);
            if (whatsAppSettings == null)
            {
                _logger.LogError("WhatsApp settings not found for company {CompanyId}", companyId);
                return false;
            }

            // Template mesajı için request hazırla
            var templateRequest = new WhatsAppTemplateMessageRequest
            {
                ToPhoneNumber = GetFormattedPhoneNumber(customer),
                TemplateName = await GetTemplateNameByIdAsync(companyId, templateId),
                TemplateLanguage = "tr",
                ConnectionSettings = whatsAppSettings,
                Components = new List<WhatsAppTemplateComponent>
                {
                    new WhatsAppTemplateComponent
                    {
                        Type = "body",
                        Parameters = new List<WhatsAppTemplateParameter>
                        {
                            new WhatsAppTemplateParameter { Type = "text", Text = variables.GetValueOrDefault("FirstName", "") },
                            new WhatsAppTemplateParameter { Type = "text", Text = variables.GetValueOrDefault("LastName", "") }
                        }
                    }
                }
            };

            var result = await whatsAppService.SendTemplateMessageAsync(templateRequest);

            if (result.Success)
            {
                _logger.LogInformation("WhatsApp template message sent successfully. Customer: {CustomerEmail}, Template: {TemplateId}",
                    customer.Email, templateId);
                return true;
            }
            else
            {
                _logger.LogError("Failed to send WhatsApp template message. Customer: {CustomerEmail}, Template: {TemplateId}, Error: {Error}",
                    customer.Email, templateId, result.Message);
                return false;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending WhatsApp template message. Customer: {CustomerEmail}, Template: {TemplateId}",
                customer.Email, templateId);
            return false;
        }
    }

    private async Task<WhatsAppConnectionSettings?> GetWhatsAppSettingsAsync(Guid companyId)
    {
        try
        {
            var integration = await _context.CompanyIntegrations
                .Include(ci => ci.Integration)
                .FirstOrDefaultAsync(ci => ci.CompanyId == companyId &&
                                         ci.Integration.Name == "WhatsApp" &&
                                         ci.IsActive);

            if (integration?.SettingsJson == null)
                return null;

            var settings = System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, System.Text.Json.JsonElement>>(integration.SettingsJson);
            if (settings == null)
                return null;

            return new WhatsAppConnectionSettings
            {
                AccessToken = settings.GetValueOrDefault("accessToken").GetString() ?? "",
                BusinessAccountId = settings.GetValueOrDefault("businessAccountId").GetString() ?? "",
                PhoneNumberId = settings.GetValueOrDefault("phoneNumberId").GetString() ?? ""
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting WhatsApp settings for company {CompanyId}", companyId);
            return null;
        }
    }

    private async Task<string> GetTemplateNameByIdAsync(Guid companyId, string templateId)
    {
        try
        {
            var whatsAppTemplateService = _serviceProvider.GetService<IWhatsAppTemplateService>();
            if (whatsAppTemplateService == null)
                return templateId;

            var template = await whatsAppTemplateService.GetFacebookTemplateAsync(companyId, templateId);
            return template?.Name ?? templateId;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting template name for ID {TemplateId}", templateId);
            return templateId;
        }
    }

    private string GetFormattedPhoneNumber(Customer customer)
    {
        var phone = !string.IsNullOrEmpty(customer.MobilePhone) ? customer.MobilePhone : customer.Phone;

        if (string.IsNullOrEmpty(phone))
            return "";

        // Telefon numarasını WhatsApp formatına çevir (90 ile başlayacak şekilde)
        phone = phone.Trim().Replace(" ", "").Replace("-", "").Replace("(", "").Replace(")", "");

        if (phone.StartsWith("0"))
            phone = "90" + phone.Substring(1);
        else if (!phone.StartsWith("90"))
            phone = "90" + phone;

        return phone;
    }

    #endregion
}
