using Microsoft.EntityFrameworkCore;
using PushDashboard.Data;
using PushDashboard.Models;

namespace PushDashboard.Services;

public interface ISocialProofService
{
    Task<SocialProofSettings?> GetSettingsAsync(Guid companyId);
    Task<SocialProofSettings> GetOrCreateSettingsAsync(Guid companyId, string userId);
    Task<bool> UpdateSettingsAsync(Guid companyId, SocialProofSettings settings, string userId);
    Task<bool> ToggleActiveStatusAsync(Guid companyId, string userId);
    Task<SocialProofScriptConfig> GetScriptConfigAsync(Guid companyId);
}

public class SocialProofService : ISocialProofService
{
    private readonly ApplicationDbContext _context;
    private readonly ILogger<SocialProofService> _logger;

    public SocialProofService(
        ApplicationDbContext context,
        ILogger<SocialProofService> logger)
    {
        _context = context;
        _logger = logger;
    }

    public async Task<SocialProofSettings?> GetSettingsAsync(Guid companyId)
    {
        try
        {
            return await _context.SocialProofSettings
                .Include(sps => sps.Company)
                .Include(sps => sps.UpdatedByUser)
                .FirstOrDefaultAsync(sps => sps.CompanyId == companyId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting social proof settings for company {CompanyId}", companyId);
            return null;
        }
    }

    public async Task<SocialProofSettings> GetOrCreateSettingsAsync(Guid companyId, string userId)
    {
        try
        {
            var settings = await GetSettingsAsync(companyId);
            
            if (settings == null)
            {
                // Default ayarlarla yeni settings oluştur
                settings = new SocialProofSettings
                {
                    CompanyId = companyId,
                    IsActive = true,
                    ViewersMin = 15,
                    ViewersMax = 45,
                    FollowersMin = 5,
                    FollowersMax = 20,
                    BuyersMin = 2,
                    BuyersMax = 8,
                    UpdateInterval = 60,
                    DisplayDuration = 5,
                    UpdatedByUserId = userId,
                    TextTemplates = new SocialProofTextTemplates(),
                    DisplaySettings = new SocialProofDisplaySettings()
                };

                _context.SocialProofSettings.Add(settings);
                await _context.SaveChangesAsync();

                _logger.LogInformation("Created default social proof settings for company {CompanyId}", companyId);
            }

            return settings;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating social proof settings for company {CompanyId}", companyId);
            throw;
        }
    }

    public async Task<bool> UpdateSettingsAsync(Guid companyId, SocialProofSettings settings, string userId)
    {
        try
        {
            var existingSettings = await _context.SocialProofSettings
                .FirstOrDefaultAsync(sps => sps.CompanyId == companyId);

            if (existingSettings == null)
            {
                return false;
            }

            // Ayarları güncelle
            existingSettings.IsActive = settings.IsActive;
            existingSettings.ViewersMin = settings.ViewersMin;
            existingSettings.ViewersMax = settings.ViewersMax;
            existingSettings.FollowersMin = settings.FollowersMin;
            existingSettings.FollowersMax = settings.FollowersMax;
            existingSettings.BuyersMin = settings.BuyersMin;
            existingSettings.BuyersMax = settings.BuyersMax;
            existingSettings.UpdateInterval = settings.UpdateInterval;
            existingSettings.DisplayDuration = settings.DisplayDuration;
            existingSettings.TextTemplates = settings.TextTemplates;
            existingSettings.DisplaySettings = settings.DisplaySettings;
            existingSettings.UpdatedByUserId = userId;
            existingSettings.UpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();

            _logger.LogInformation("Updated social proof settings for company {CompanyId} by user {UserId}", companyId, userId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating social proof settings for company {CompanyId}", companyId);
            return false;
        }
    }

    public async Task<bool> ToggleActiveStatusAsync(Guid companyId, string userId)
    {
        try
        {
            var settings = await _context.SocialProofSettings
                .FirstOrDefaultAsync(sps => sps.CompanyId == companyId);

            if (settings == null)
            {
                return false;
            }

            settings.IsActive = !settings.IsActive;
            settings.UpdatedByUserId = userId;
            settings.UpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();

            _logger.LogInformation("Toggled social proof status to {Status} for company {CompanyId}", 
                settings.IsActive ? "Active" : "Inactive", companyId);
            
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error toggling social proof status for company {CompanyId}", companyId);
            return false;
        }
    }

    public async Task<SocialProofScriptConfig> GetScriptConfigAsync(Guid companyId)
    {
        try
        {
            var settings = await GetSettingsAsync(companyId);
            
            if (settings == null || !settings.IsActive)
            {
                return new SocialProofScriptConfig { IsActive = false };
            }

            return new SocialProofScriptConfig
            {
                IsActive = true,
                CompanyId = companyId,
                ViewersMin = settings.ViewersMin,
                ViewersMax = settings.ViewersMax,
                FollowersMin = settings.FollowersMin,
                FollowersMax = settings.FollowersMax,
                BuyersMin = settings.BuyersMin,
                BuyersMax = settings.BuyersMax,
                UpdateInterval = settings.UpdateInterval * 1000, // JavaScript için milisaniye
                DisplayDuration = settings.DisplayDuration * 1000, // JavaScript için milisaniye
                TextTemplates = settings.TextTemplates,
                DisplaySettings = settings.DisplaySettings
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting script config for company {CompanyId}", companyId);
            return new SocialProofScriptConfig { IsActive = false };
        }
    }
}

/// <summary>
/// JavaScript widget için konfigürasyon modeli
/// </summary>
public class SocialProofScriptConfig
{
    public bool IsActive { get; set; }
    public Guid CompanyId { get; set; }
    public int ViewersMin { get; set; }
    public int ViewersMax { get; set; }
    public int FollowersMin { get; set; }
    public int FollowersMax { get; set; }
    public int BuyersMin { get; set; }
    public int BuyersMax { get; set; }
    public int UpdateInterval { get; set; } // milisaniye
    public int DisplayDuration { get; set; } // milisaniye
    public SocialProofTextTemplates TextTemplates { get; set; } = new();
    public SocialProofDisplaySettings DisplaySettings { get; set; } = new();
}
