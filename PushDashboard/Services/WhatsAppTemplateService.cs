using Microsoft.EntityFrameworkCore;
using PushDashboard.Data;
using PushDashboard.Models;
using PushDashboard.Services.WhatsApp;
using System.Text.Json;

namespace PushDashboard.Services;

public interface IWhatsAppTemplateService
{
    Task<bool> HasWhatsAppConfigurationAsync(Guid companyId);
    Task<List<FacebookMessageTemplate>> GetFacebookTemplatesAsync(Guid companyId);
    Task<FacebookMessageTemplate?> GetFacebookTemplateAsync(Guid companyId, string templateId);
    Task<FacebookMessageTemplate?> CreateFacebookTemplateAsync(Guid companyId, CreateTemplateRequest request);

    Task<bool> DeleteFacebookTemplateAsync(Guid companyId, string templateName);
    Task<Dictionary<string, List<FacebookMessageTemplate>>> GetGroupedFacebookTemplatesAsync(Guid companyId);
}

public class WhatsAppTemplateService : IWhatsAppTemplateService
{
    private readonly ApplicationDbContext _context;
    private readonly IWhatsAppService _whatsAppService;
    private readonly ILogger<WhatsAppTemplateService> _logger;

    public WhatsAppTemplateService(
        ApplicationDbContext context,
        IWhatsAppService whatsAppService,
        ILogger<WhatsAppTemplateService> logger)
    {
        _context = context;
        _whatsAppService = whatsAppService;
        _logger = logger;
    }

    public async Task<bool> HasWhatsAppConfigurationAsync(Guid companyId)
    {
        try
        {
            var whatsappIntegration = await _context.CompanyIntegrations
                .Include(ci => ci.Integration)
                .FirstOrDefaultAsync(ci => ci.CompanyId == companyId &&
                                         ci.Integration.Name == "WhatsApp" &&
                                         ci.IsActive &&
                                         ci.IsConfigured);

            return whatsappIntegration != null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking WhatsApp configuration for company {CompanyId}", companyId);
            return false;
        }
    }

    public async Task<List<FacebookMessageTemplate>> GetFacebookTemplatesAsync(Guid companyId)
    {
        try
        {
            var settings = await GetWhatsAppSettingsAsync(companyId);
            if (settings == null)
            {
                _logger.LogWarning("WhatsApp settings not found for company {CompanyId}", companyId);
                return new List<FacebookMessageTemplate>();
            }

            var result = await _whatsAppService.GetMessageTemplatesAsync(settings);
            if (result.Success && result.Data != null)
            {
                return result.Data;
            }
            else
            {
                _logger.LogError("Failed to get Facebook templates for company {CompanyId}: {Error}", companyId, result.Message);
                return new List<FacebookMessageTemplate>();
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting Facebook templates for company {CompanyId}", companyId);
            return new List<FacebookMessageTemplate>();
        }
    }

    public async Task<FacebookMessageTemplate?> GetFacebookTemplateAsync(Guid companyId, string templateId)
    {
        try
        {
            var settings = await GetWhatsAppSettingsAsync(companyId);
            if (settings == null)
            {
                _logger.LogWarning("WhatsApp settings not found for company {CompanyId}", companyId);
                return null;
            }

            var result = await _whatsAppService.GetMessageTemplateAsync(settings, templateId);
            if (result.Success && result.Data != null)
            {
                return result.Data;
            }
            else
            {
                _logger.LogError("Failed to get Facebook template {TemplateId} for company {CompanyId}: {Error}",
                    templateId, companyId, result.Message);
                return null;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting Facebook template {TemplateId} for company {CompanyId}", templateId, companyId);
            return null;
        }
    }

    public async Task<FacebookMessageTemplate?> CreateFacebookTemplateAsync(Guid companyId, CreateTemplateRequest request)
    {
        try
        {
            var settings = await GetWhatsAppSettingsAsync(companyId);
            if (settings == null)
            {
                _logger.LogWarning("WhatsApp settings not found for company {CompanyId}", companyId);
                return null;
            }

            var result = await _whatsAppService.CreateMessageTemplateAsync(settings, request);
            if (result.Success && result.Data != null)
            {
                _logger.LogInformation("Successfully created Facebook template {TemplateName} for company {CompanyId}",
                    request.Name, companyId);
                return result.Data;
            }
            else
            {
                _logger.LogError("Failed to create Facebook template {TemplateName} for company {CompanyId}: {Error}",
                    request.Name, companyId, result.Message);
                return null;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating Facebook template {TemplateName} for company {CompanyId}", request.Name, companyId);
            return null;
        }
    }



    public async Task<bool> DeleteFacebookTemplateAsync(Guid companyId, string templateName)
    {
        try
        {
            var settings = await GetWhatsAppSettingsAsync(companyId);
            if (settings == null)
            {
                _logger.LogWarning("WhatsApp settings not found for company {CompanyId}", companyId);
                return false;
            }

            var result = await _whatsAppService.DeleteMessageTemplateAsync(settings, templateName);
            if (result.Success)
            {
                _logger.LogInformation("Successfully deleted Facebook template {TemplateName} for company {CompanyId}",
                    templateName, companyId);
                return true;
            }
            else
            {
                _logger.LogError("Failed to delete Facebook template {TemplateName} for company {CompanyId}: {Error}",
                    templateName, companyId, result.Message);
                return false;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting Facebook template {TemplateName} for company {CompanyId}", templateName, companyId);
            return false;
        }
    }

    public async Task<Dictionary<string, List<FacebookMessageTemplate>>> GetGroupedFacebookTemplatesAsync(Guid companyId)
    {
        try
        {
            var templates = await GetFacebookTemplatesAsync(companyId);
            return templates.GroupBy(t => t.Category).ToDictionary(g => g.Key, g => g.ToList());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting grouped Facebook templates for company {CompanyId}", companyId);
            return new Dictionary<string, List<FacebookMessageTemplate>>();
        }
    }

    private async Task<WhatsAppConnectionSettings?> GetWhatsAppSettingsAsync(Guid companyId)
    {
        try
        {
            var whatsappIntegration = await _context.CompanyIntegrations
                .Include(ci => ci.Integration)
                .FirstOrDefaultAsync(ci => ci.CompanyId == companyId &&
                                         ci.Integration.Name == "WhatsApp" &&
                                         ci.IsActive &&
                                         ci.IsConfigured);

            if (whatsappIntegration?.Settings == null)
                return null;

            var settings = whatsappIntegration.Settings;
            return new WhatsAppConnectionSettings
            {
                AccessToken = settings.GetValueOrDefault("accessToken")?.ToString() ?? "",
                BusinessAccountId = settings.GetValueOrDefault("businessAccountId")?.ToString() ?? "",
                PhoneNumberId = settings.GetValueOrDefault("phoneNumberId")?.ToString() ?? "",
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting WhatsApp settings for company {CompanyId}", companyId);
            return null;
        }
    }
}
