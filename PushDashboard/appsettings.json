{"ConnectionStrings": {"DefaultConnection": "Server=localhost;Port=5432;Database=pushonica;Userid=postgres;Password=******;Timeout=300;Command Timeout=300;Connection Idle Lifetime=300;"}, "EmailSettings": {"SmtpServer": "smtp.gmail.com", "SmtpPort": 587, "EnableSsl": true, "SenderEmail": "<EMAIL>", "SenderName": "Pushonica Info", "Username": "<EMAIL>", "Password": "swpb jpyl atgu cpdx"}, "BaseUrl": "https://localhost:7004", "CommentScraperApi": {"BaseUrl": "http://127.0.0.1:5001", "ScrapeReviewsEndpoint": "/scrape-reviews", "JobStatusEndpoint": "/job/{0}"}, "R2Storage": {"AccountId": "3176f5780c0677b02161822a53c16f54", "AccessKeyId": "b56fb4433fe9411840d2a477846288ce", "SecretAccessKey": "****************************************************************", "BucketName": "test", "Region": "auto", "PublicUrl": "https://pub-2bba9adab676432eb161192ccf1b6f98.r2.dev"}, "AWS": {"AccessKey": "YOUR_AWS_ACCESS_KEY", "SecretKey": "YOUR_AWS_SECRET_KEY", "Region": "eu-west-1", "S3": {"BucketName": "push<PERSON>a-video-hosting", "VideoFolder": "videos", "ThumbnailFolder": "thumbnails"}}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*"}