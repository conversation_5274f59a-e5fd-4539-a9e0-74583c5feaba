using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using PushDashboard.Models;

namespace PushDashboard.Data.Configurations;

public class UserSessionConfiguration : IEntityTypeConfiguration<UserSession>
{
    public void Configure(EntityTypeBuilder<UserSession> builder)
    {
        // Configure properties from Data Annotations
        builder.HasKey(us => us.Id);

        builder.Property(us => us.UserId)
            .IsRequired()
            .HasMaxLength(450);

        builder.Property(us => us.SessionId)
            .IsRequired()
            .HasMaxLength(500);

        builder.Property(us => us.DeviceInfo)
            .HasMaxLength(200);

        builder.Property(us => us.Browser)
            .HasMaxLength(100);

        builder.Property(us => us.OperatingSystem)
            .HasMaxLength(100);

        builder.Property(us => us.IpAddress)
            .HasMaxLength(45);

        builder.Property(us => us.Location)
            .HasMaxLength(200);

        builder.Property(us => us.DeviceType)
            .HasMaxLength(50);

        // Configure the relationship between User and UserSessions
        builder
            .HasOne(us => us.User)
            .WithMany(u => u.UserSessions)
            .HasForeignKey(us => us.UserId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}
