using Microsoft.AspNetCore.Identity;
using PushDashboard.Models;

namespace PushDashboard.Data;

public static class SeedData
{
    public static async Task Initialize(
        UserManager<ApplicationUser> userManager,
        RoleManager<IdentityRole> roleManager,
        ApplicationDbContext context)
    {
        // Create roles if they don't exist
        string[] roleNames = { "Admin", "User", "CompanyOwner" };

        foreach (var roleName in roleNames)
        {
            if (!await roleManager.RoleExistsAsync(roleName))
            {
                await roleManager.CreateAsync(new IdentityRole(roleName));
            }
        }

        // Create default company if it doesn't exist
        if (!context.Companies.Any())
        {
            var defaultCompany = new Company
            {
                Name = "Default Company",
                Email = "<EMAIL>",
                CreditBalance = 5000.00m, // 5000 TL başlangıç kredisi
                CreatedAt = DateTime.UtcNow
            };

            context.Companies.Add(defaultCompany);
            await context.SaveChangesAsync();

            // Create admin user if it doesn't exist
            var adminEmail = "<EMAIL>";

            if (await userManager.FindByEmailAsync(adminEmail) == null)
            {
                var adminUser = new ApplicationUser
                {
                    UserName = adminEmail,
                    Email = adminEmail,
                    EmailConfirmed = true,
                    FirstName = "Admin",
                    LastName = "User",
                    CompanyId = defaultCompany.Id,
                    CreditBalance = 1000,
                    CreatedAt = DateTime.UtcNow
                };

                var result = await userManager.CreateAsync(adminUser, "Admin123!");

                if (result.Succeeded)
                {
                    await userManager.AddToRoleAsync(adminUser, "Admin");
                    await userManager.AddToRoleAsync(adminUser, "CompanyOwner");
                }
            }

            // Create 20 test users for Default Company
            await CreateTestUsers(userManager, defaultCompany.Id);
        }
    }

    private static async Task CreateTestUsers(UserManager<ApplicationUser> userManager, Guid companyId)
    {
        var testUsers = new[]
        {
            new { FirstName = "Ahmet", LastName = "Yılmaz", Email = "<EMAIL>" },
            new { FirstName = "Mehmet", LastName = "Kaya", Email = "<EMAIL>" },
            new { FirstName = "Ayşe", LastName = "Demir", Email = "<EMAIL>" },
            new { FirstName = "Fatma", LastName = "Çelik", Email = "<EMAIL>" },
            new { FirstName = "Mustafa", LastName = "Şahin", Email = "<EMAIL>" },
            new { FirstName = "Emine", LastName = "Yıldız", Email = "<EMAIL>" },
            new { FirstName = "Ali", LastName = "Aydın", Email = "<EMAIL>" },
            new { FirstName = "Hatice", LastName = "Özkan", Email = "<EMAIL>" },
            new { FirstName = "Hüseyin", LastName = "Arslan", Email = "<EMAIL>" },
            new { FirstName = "Zeynep", LastName = "Doğan", Email = "<EMAIL>" },
            new { FirstName = "İbrahim", LastName = "Aslan", Email = "<EMAIL>" },
            new { FirstName = "Elif", LastName = "Polat", Email = "<EMAIL>" },
            new { FirstName = "Ömer", LastName = "Koç", Email = "<EMAIL>" },
            new { FirstName = "Merve", LastName = "Güneş", Email = "<EMAIL>" },
            new { FirstName = "Burak", LastName = "Erdoğan", Email = "<EMAIL>" },
            new { FirstName = "Seda", LastName = "Yavuz", Email = "<EMAIL>" },
            new { FirstName = "Emre", LastName = "Özdemir", Email = "<EMAIL>" },
            new { FirstName = "Gizem", LastName = "Kılıç", Email = "<EMAIL>" },
            new { FirstName = "Serkan", LastName = "Aksoy", Email = "<EMAIL>" },
            new { FirstName = "Deniz", LastName = "Tunç", Email = "<EMAIL>" }
        };

        foreach (var userData in testUsers)
        {
            // Check if user already exists
            if (await userManager.FindByEmailAsync(userData.Email) == null)
            {
                var user = new ApplicationUser
                {
                    UserName = userData.Email,
                    Email = userData.Email,
                    EmailConfirmed = true,
                    FirstName = userData.FirstName,
                    LastName = userData.LastName,
                    CompanyId = companyId,
                    CreatedAt = DateTime.UtcNow.AddDays(-Random.Shared.Next(1, 365)), // Random creation date within last year
                    LastLoginAt = DateTime.UtcNow.AddDays(-Random.Shared.Next(1, 30)) // Random last login within last month
                };

                var result = await userManager.CreateAsync(user, "User123!");

                if (result.Succeeded)
                {
                    await userManager.AddToRoleAsync(user, "User");
                }
            }
        }
    }
}
