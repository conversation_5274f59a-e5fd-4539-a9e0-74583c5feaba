using System.ComponentModel.DataAnnotations;

namespace PushDashboard.DTOs;

#region Request DTOs

/// <summary>
/// Request DTO for spinning the gift wheel
/// </summary>
public class GiftWheelSpinRequest
{
    [Required(ErrorMessage = "Müşteri adı gereklidir")]
    [StringLength(100, ErrorMessage = "Müşteri adı en fazla 100 karakter olabilir")]
    public string CustomerName { get; set; } = string.Empty;

    [Required(ErrorMessage = "Telefon numarası gereklidir")]
    [Phone(ErrorMessage = "Geçerli bir telefon numarası giriniz")]
    [StringLength(20, ErrorMessage = "Telefon numarası en fazla 20 karakter olabilir")]
    public string CustomerPhone { get; set; } = string.Empty;

    [EmailAddress(ErrorMessage = "Geçerli bir e-posta adresi giriniz")]
    [StringLength(100, ErrorMessage = "E-posta adresi en fazla 100 karakter olabilir")]
    public string? CustomerEmail { get; set; }
}

/// <summary>
/// Request DTO for adding/updating prizes
/// </summary>
public class GiftWheelPrizeRequest
{
    [Required(ErrorMessage = "Ödül adı gereklidir")]
    [StringLength(100, ErrorMessage = "Ödül adı en fazla 100 karakter olabilir")]
    public string Name { get; set; } = string.Empty;

    [Required(ErrorMessage = "Ödül türü gereklidir")]
    [RegularExpression("^(voucher|message|none)$", ErrorMessage = "Geçersiz ödül türü")]
    public string PrizeType { get; set; } = "voucher";

    [Range(0.01, 999999.99, ErrorMessage = "İndirim miktarı 0.01 ile 999999.99 arasında olmalıdır")]
    public decimal? DiscountAmount { get; set; }

    [Range(1, 2, ErrorMessage = "İndirim türü 1 (tutar) veya 2 (yüzde) olmalıdır")]
    public int? DiscountType { get; set; } = 1;

    [Range(1, 365, ErrorMessage = "Geçerlilik süresi 1 ile 365 gün arasında olmalıdır")]
    public int? ValidityDays { get; set; } = 30;

    [Required(ErrorMessage = "Olasılık gereklidir")]
    [Range(1, 100, ErrorMessage = "Olasılık 1 ile 100 arasında olmalıdır")]
    public int Probability { get; set; } = 10;

    [Required(ErrorMessage = "Renk gereklidir")]
    [RegularExpression("^#[0-9A-Fa-f]{6}$", ErrorMessage = "Geçerli bir hex renk kodu giriniz")]
    public string Color { get; set; } = "#3B82F6";

    [Range(0, 100, ErrorMessage = "Sıralama 0 ile 100 arasında olmalıdır")]
    public int SortOrder { get; set; } = 0;

    public bool IsActive { get; set; } = true;
}

/// <summary>
/// Request DTO for updating gift wheel settings
/// </summary>
public class UpdateGiftWheelSettingsRequest
{
    [Required(ErrorMessage = "Çark başlığı gereklidir")]
    [StringLength(200, ErrorMessage = "Çark başlığı en fazla 200 karakter olabilir")]
    public string WheelTitle { get; set; } = "Çarkı Çevir, Hediyeni Kazan!";

    [StringLength(300, ErrorMessage = "Alt başlık en fazla 300 karakter olabilir")]
    public string WheelSubtitle { get; set; } = "Şansını dene ve harika hediyeler kazan";

    [Required(ErrorMessage = "Buton metni gereklidir")]
    [StringLength(50, ErrorMessage = "Buton metni en fazla 50 karakter olabilir")]
    public string ButtonText { get; set; } = "Çarkı Çevir";

    [Required(ErrorMessage = "Kazanma mesajı gereklidir")]
    [StringLength(200, ErrorMessage = "Kazanma mesajı en fazla 200 karakter olabilir")]
    public string WinMessage { get; set; } = "Tebrikler! {prize} kazandınız!";

    [Required(ErrorMessage = "Kaybetme mesajı gereklidir")]
    [StringLength(200, ErrorMessage = "Kaybetme mesajı en fazla 200 karakter olabilir")]
    public string LoseMessage { get; set; } = "Bu sefer olmadı, tekrar deneyin!";

    [Range(1, 10, ErrorMessage = "Günlük maksimum çevirme sayısı 1 ile 10 arasında olmalıdır")]
    public int MaxSpinsPerDay { get; set; } = 1;

    public bool RequirePhone { get; set; } = true;

    public bool RequireEmail { get; set; } = false;

    [StringLength(500, ErrorMessage = "Bildirim şablonu en fazla 500 karakter olabilir")]
    public string NotificationTemplate { get; set; } = "🎉 Tebrikler {name}! {prize} kazandınız. Hemen alışverişe başlayın: {siteUrl}";

    [Required(ErrorMessage = "Ana renk gereklidir")]
    [RegularExpression("^#[0-9A-Fa-f]{6}$", ErrorMessage = "Geçerli bir hex renk kodu giriniz")]
    public string PrimaryColor { get; set; } = "#6366f1";

    [Required(ErrorMessage = "İkincil renk gereklidir")]
    [RegularExpression("^#[0-9A-Fa-f]{6}$", ErrorMessage = "Geçerli bir hex renk kodu giriniz")]
    public string SecondaryColor { get; set; } = "#f3f4f6";

    [Range(200, 500, ErrorMessage = "Çark boyutu 200 ile 500 piksel arasında olmalıdır")]
    public int WheelSize { get; set; } = 300;

    public bool ShowConfetti { get; set; } = true;
}

#endregion

#region Response DTOs

/// <summary>
/// Response DTO for spin results
/// </summary>
public class GiftWheelSpinResult
{
    public bool Success { get; set; }
    public string Message { get; set; } = string.Empty;
    public string? ErrorCode { get; set; }
    public GiftWheelPrizeDto? Prize { get; set; }
    public string? VoucherCode { get; set; }
    public bool VoucherCreated { get; set; }
    public bool NotificationSent { get; set; }
    public decimal Cost { get; set; }
}

/// <summary>
/// Response DTO for eligibility check
/// </summary>
public class GiftWheelEligibilityDto
{
    public bool CanSpin { get; set; }
    public string Message { get; set; } = string.Empty;
    public string? ErrorCode { get; set; }
}

/// <summary>
/// DTO for gift wheel prize information
/// </summary>
public class GiftWheelPrizeDto
{
    public int Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string PrizeType { get; set; } = string.Empty;
    public decimal? DiscountAmount { get; set; }
    public int? DiscountType { get; set; }
    public int? ValidityDays { get; set; }
    public int Probability { get; set; }
    public string Color { get; set; } = string.Empty;
    public int SortOrder { get; set; }
    public bool IsActive { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
}

/// <summary>
/// DTO for gift wheel settings
/// </summary>
public class GiftWheelSettingsDto
{
    public int Id { get; set; }
    public int WheelId { get; set; }
    public string WheelTitle { get; set; } = string.Empty;
    public string WheelSubtitle { get; set; } = string.Empty;
    public string ButtonText { get; set; } = string.Empty;
    public string WinMessage { get; set; } = string.Empty;
    public string LoseMessage { get; set; } = string.Empty;
    public int MaxSpinsPerDay { get; set; }
    public bool RequirePhone { get; set; }
    public bool RequireEmail { get; set; }
    public string NotificationTemplate { get; set; } = string.Empty;
    public string PrimaryColor { get; set; } = string.Empty;
    public string SecondaryColor { get; set; } = string.Empty;
    public int WheelSize { get; set; }
    public bool ShowConfetti { get; set; }
    public DateTime UpdatedAt { get; set; }
}

/// <summary>
/// DTO for wheel configuration (used in embed script)
/// </summary>
public class GiftWheelConfigDto
{
    public int WheelId { get; set; }
    public string Title { get; set; } = string.Empty;
    public string Subtitle { get; set; } = string.Empty;
    public string ButtonText { get; set; } = string.Empty;
    public string WinMessage { get; set; } = string.Empty;
    public string LoseMessage { get; set; } = string.Empty;
    public bool RequirePhone { get; set; }
    public bool RequireEmail { get; set; }
    public string PrimaryColor { get; set; } = string.Empty;
    public string SecondaryColor { get; set; } = string.Empty;
    public int WheelSize { get; set; }
    public bool ShowConfetti { get; set; }
    public List<GiftWheelPrizeDto> Prizes { get; set; } = new();
}

/// <summary>
/// DTO for gift wheel statistics
/// </summary>
public class GiftWheelStatsDto
{
    public int TotalSpins { get; set; }
    public int TotalVouchersCreated { get; set; }
    public int TotalNotificationsSent { get; set; }
    public decimal TotalCost { get; set; }
    public int SpinsToday { get; set; }
    public int SpinsThisWeek { get; set; }
    public int SpinsThisMonth { get; set; }
    public List<PrizeStatsDto> PrizeStats { get; set; } = new();
    public List<DailySpinStatsDto> DailyStats { get; set; } = new();
}

/// <summary>
/// DTO for individual prize statistics
/// </summary>
public class PrizeStatsDto
{
    public int PrizeId { get; set; }
    public string PrizeName { get; set; } = string.Empty;
    public int TimesWon { get; set; }
    public decimal WinRate { get; set; }
}

/// <summary>
/// DTO for daily spin statistics
/// </summary>
public class DailySpinStatsDto
{
    public DateTime Date { get; set; }
    public int SpinCount { get; set; }
    public int VoucherCount { get; set; }
}

/// <summary>
/// DTO for recent spin information
/// </summary>
public class RecentSpinDto
{
    public int Id { get; set; }
    public string CustomerName { get; set; } = string.Empty;
    public string CustomerPhone { get; set; } = string.Empty;
    public string PrizeName { get; set; } = string.Empty;
    public string? VoucherCode { get; set; }
    public bool VoucherCreated { get; set; }
    public bool NotificationSent { get; set; }
    public DateTime SpinDate { get; set; }
    public string? ErrorMessage { get; set; }
}

#endregion

#region Module DTOs

/// <summary>
/// DTO for gift wheel module settings
/// </summary>
public class GiftWheelModuleSettingsDto
{
    public bool IsEnabled { get; set; } = true;
    public decimal VoucherCreationCost { get; set; } = 2.00m;
    public decimal NotificationCost { get; set; } = 1.00m;
    public int MaxSpinsPerHour { get; set; } = 100;
    public bool EnableRateLimiting { get; set; } = true;
    public string? CompanyWebsiteUrl { get; set; }
}

/// <summary>
/// DTO for gift wheel module statistics
/// </summary>
public class GiftWheelModuleStatsDto
{
    public int ActiveWheels { get; set; }
    public int TotalSpinsAllTime { get; set; }
    public int TotalVouchersCreated { get; set; }
    public decimal TotalCostAllTime { get; set; }
    public int SpinsLast30Days { get; set; }
    public decimal CostLast30Days { get; set; }
    public decimal AverageSpinsPerDay { get; set; }
    public decimal ConversionRate { get; set; }
}

/// <summary>
/// DTO for module usage information
/// </summary>
public class ModuleUsageDto
{
    public int Id { get; set; }
    public string UsageType { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public decimal Cost { get; set; }
    public bool IsSuccessful { get; set; }
    public DateTime CreatedAt { get; set; }
    public string? ReferenceId { get; set; }
    public string? ErrorMessage { get; set; }
}

/// <summary>
/// DTO for module status information
/// </summary>
public class ModuleStatusDto
{
    public bool IsActive { get; set; }
    public bool IsConfigured { get; set; }
    public DateTime? PurchasedAt { get; set; }
    public DateTime? ExpiresAt { get; set; }
    public decimal? PaidAmount { get; set; }
    public string? TransactionId { get; set; }
    public DateTime? LastUsedAt { get; set; }
}

/// <summary>
/// DTO for integration status information
/// </summary>
public class IntegrationStatusDto
{
    public bool HasEcommerceIntegration { get; set; }
    public string? EcommercePlatform { get; set; }
    public bool IsEcommerceConfigured { get; set; }
    public bool HasWhatsAppIntegration { get; set; }
    public bool IsWhatsAppConfigured { get; set; }
    public List<string> MissingIntegrations { get; set; } = new();
    public List<string> ConfigurationIssues { get; set; } = new();
}

#endregion
