using System.ComponentModel.DataAnnotations;

namespace PushDashboard.DTOs;

/// <summary>
/// Aktif iletişim kanalı bilgileri
/// </summary>
public class ActiveChannelDto
{
    public string ChannelType { get; set; } = string.Empty;
    public string DisplayName { get; set; } = string.Empty;
    public bool IsConfigured { get; set; }
    public bool IsEnabled { get; set; }
    public decimal CostPerMessage { get; set; }
    public string? ConfigurationStatus { get; set; }
    public string? LastTestResult { get; set; }
    public DateTime? LastTestedAt { get; set; }
}

/// <summary>
/// İlk alışveriş modülü ayarları
/// </summary>
public class FirstOrderSettingsDto
{
    [Display(Name = "Modül Etkin")]
    public bool Enabled { get; set; }

    [Display(Name = "Email Bildirimi")]
    public bool EmailEnabled { get; set; }

    [Display(Name = "SMS Bildirimi")]
    public bool SmsEnabled { get; set; }

    [Display(Name = "WhatsApp Bildirimi")]
    public bool WhatsAppEnabled { get; set; }

    [Display(Name = "Push Bildirimi")]
    public bool PushEnabled { get; set; }

    [Display(Name = "Hediye Çeki Etkin")]
    public bool GiftVoucherEnabled { get; set; }

    [Display(Name = "Hediye Çeki Tutarı")]
    [Range(1, 10000, ErrorMessage = "Hediye çeki tutarı 1-10000 arasında olmalıdır")]
    public decimal GiftVoucherAmount { get; set; } = 50;

    [Display(Name = "İndirim Tipi")]
    public int GiftVoucherDiscountType { get; set; } = 1; // 1: Sabit tutar, 2: Yüzde

    [Display(Name = "Geçerlilik Süresi (Gün)")]
    [Range(1, 365, ErrorMessage = "Geçerlilik süresi 1-365 gün arasında olmalıdır")]
    public int GiftVoucherValidityDays { get; set; } = 30;

    public List<ActiveChannelDto> AvailableChannels { get; set; } = new();
    public bool HasActiveEcommerceIntegration { get; set; }
    public string? ActiveEcommercePlatform { get; set; }
}

/// <summary>
/// İlk alışveriş modülü ayarları güncelleme request'i
/// </summary>
public class UpdateFirstOrderSettingsRequest
{
    [Required]
    public bool Enabled { get; set; }

    public bool EmailEnabled { get; set; }
    public bool SmsEnabled { get; set; }
    public bool WhatsAppEnabled { get; set; }
    public bool PushEnabled { get; set; }

    public bool GiftVoucherEnabled { get; set; }

    [Range(1, 10000, ErrorMessage = "Hediye çeki tutarı 1-10000 arasında olmalıdır")]
    public decimal GiftVoucherAmount { get; set; } = 50;

    [Range(1, 2, ErrorMessage = "İndirim tipi 1 (Sabit tutar) veya 2 (Yüzde) olmalıdır")]
    public int GiftVoucherDiscountType { get; set; } = 1;

    [Range(1, 365, ErrorMessage = "Geçerlilik süresi 1-365 gün arasında olmalıdır")]
    public int GiftVoucherValidityDays { get; set; } = 30;
}

/// <summary>
/// Hediye çeki test sonucu
/// </summary>
public class GiftVoucherTestResult
{
    public bool Success { get; set; }
    public string? VoucherCode { get; set; }
    public string? Amount { get; set; }
    public string? ErrorMessage { get; set; }
    public string? Platform { get; set; }
    public DateTime TestedAt { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// İlk alışveriş modülü istatistikleri
/// </summary>
public class FirstOrderStatsDto
{
    public int TotalFirstOrderNotifications { get; set; }
    public int TotalGiftVouchersCreated { get; set; }
    public decimal TotalCostThisMonth { get; set; }
    public int NotificationsThisMonth { get; set; }
    public Dictionary<string, int> ChannelUsageStats { get; set; } = new();
    public DateTime? LastNotificationSent { get; set; }
}
