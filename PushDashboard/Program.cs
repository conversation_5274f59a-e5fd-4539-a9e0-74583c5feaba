using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.RateLimiting;
using Microsoft.EntityFrameworkCore;
using PushDashboard.Data;
using PushDashboard.Models;
using PushDashboard.Services;
using System.Threading.RateLimiting;
var builder = WebApplication.CreateBuilder(args);

// Add services to the container.

// Configure DbContext with PostgreSQL
builder.Services.AddDbContext<ApplicationDbContext>(options =>
    options.UseNpgsql(builder.Configuration.GetConnectionString("DefaultConnection"),
        npgsqlOptions => npgsqlOptions.CommandTimeout(300))); // 5 dakika timeout

// Configure Identity
builder.Services.AddIdentity<ApplicationUser, IdentityRole>(options =>
{
    // Password settings
    options.Password.RequireDigit = true;
    options.Password.RequireLowercase = true;
    options.Password.RequireUppercase = true;
    options.Password.RequireNonAlphanumeric = true;
    options.Password.RequiredLength = 8;

    // Lockout settings
    options.Lockout.DefaultLockoutTimeSpan = TimeSpan.FromMinutes(15);
    options.Lockout.MaxFailedAccessAttempts = 5;

    // User settings
    options.User.RequireUniqueEmail = true;
})
.AddEntityFrameworkStores<ApplicationDbContext>()
.AddDefaultTokenProviders();

// Configure cookie settings
builder.Services.ConfigureApplicationCookie(options =>
{
    options.Cookie.HttpOnly = true;
    options.ExpireTimeSpan = TimeSpan.FromDays(7);
    options.LoginPath = "/Account/Login";
    options.LogoutPath = "/Account/Logout";
    options.AccessDeniedPath = "/Account/AccessDenied";
    options.SlidingExpiration = true;
});

// Register TwoFactorService
builder.Services.AddScoped<PushDashboard.Services.ITwoFactorService, PushDashboard.Services.TwoFactorService>();
builder.Services.AddScoped<PushDashboard.Services.IStoreService, PushDashboard.Services.StoreService>();
builder.Services.AddScoped<PushDashboard.Services.IModuleSeedService, PushDashboard.Services.ModuleSeedService>();
builder.Services.AddScoped<PushDashboard.Services.IIntegrationSeedService, PushDashboard.Services.IntegrationSeedService>();
builder.Services.AddScoped<PushDashboard.Services.IEmailTemplateSeedService, PushDashboard.Services.EmailTemplateSeedService>();
builder.Services.AddScoped<PushDashboard.Services.IEmailTemplateService, PushDashboard.Services.EmailTemplateService>();
builder.Services.AddScoped<PushDashboard.Services.ISessionService, PushDashboard.Services.SessionService>();
builder.Services.AddScoped<PushDashboard.Services.ICustomerService, PushDashboard.Services.CustomerService>();
builder.Services.AddScoped<PushDashboard.Services.ICustomerImportService, PushDashboard.Services.CustomerImportService>();
builder.Services.AddScoped<PushDashboard.Services.IBasketService, PushDashboard.Services.BasketService>();
builder.Services.AddScoped<PushDashboard.Services.ICommentService, PushDashboard.Services.CommentService>();
builder.Services.AddScoped<PushDashboard.Services.ITrendyolScraperService, PushDashboard.Services.TrendyolScraperService>();
builder.Services.AddScoped<PushDashboard.Services.IProductSliderService, PushDashboard.Services.ProductSliderService>();

// Configure R2 Storage Settings
builder.Services.Configure<PushDashboard.Configuration.R2StorageSettings>(
    builder.Configuration.GetSection(PushDashboard.Configuration.R2StorageSettings.SectionName));

// Register R2 Storage Service
builder.Services.AddScoped<PushDashboard.Services.IR2StorageService, PushDashboard.Services.R2StorageService>();

builder.Services.AddHttpClient(); // HttpClient for external API calls
builder.Services.AddScoped<Microsoft.AspNetCore.Authentication.IClaimsTransformation, PushDashboard.Services.ClaimsTransformationService>();

// Add UserContext service
builder.Services.AddScoped<PushDashboard.Services.IUserContextService, PushDashboard.Services.UserContextService>();

// Add Social Proof service
builder.Services.AddScoped<PushDashboard.Services.ISocialProofService, PushDashboard.Services.SocialProofService>();

// Add Cookie Management service
builder.Services.AddScoped<PushDashboard.Services.ICookieManagementService, PushDashboard.Services.CookieManagementService>();

// Add Exit Survey service
builder.Services.AddScoped<PushDashboard.Services.IExitSurveyService, PushDashboard.Services.ExitSurveyService>();

// Configure Email Settings
builder.Services.Configure<PushDashboard.Models.EmailSettings>(
    builder.Configuration.GetSection("EmailSettings"));

// Configure Comment Scraper API Settings
builder.Services.Configure<PushDashboard.Configuration.CommentScraperApiSettings>(
    builder.Configuration.GetSection(PushDashboard.Configuration.CommentScraperApiSettings.SectionName));

// Configure AWS Settings
builder.Services.Configure<PushDashboard.Configuration.AwsSettings>(
    builder.Configuration.GetSection(PushDashboard.Configuration.AwsSettings.SectionName));

// Register AWS S3 Client
builder.Services.AddSingleton<Amazon.S3.IAmazonS3>(provider =>
{
    var awsSettings = provider.GetRequiredService<Microsoft.Extensions.Options.IOptions<PushDashboard.Configuration.AwsSettings>>().Value;
    var config = new Amazon.S3.AmazonS3Config
    {
        RegionEndpoint = Amazon.RegionEndpoint.GetBySystemName(awsSettings.Region)
    };
    return new Amazon.S3.AmazonS3Client(awsSettings.AccessKey, awsSettings.SecretKey, config);
});

// Register Invitation Services
builder.Services.AddScoped<PushDashboard.Services.IInvitationService, PushDashboard.Services.InvitationService>();
builder.Services.AddScoped<PushDashboard.Services.IEmailService, PushDashboard.Services.EmailService>();

// Register Integration Services
builder.Services.AddScoped<PushDashboard.Services.Integrations.IEcommerceServiceFactory, PushDashboard.Services.Integrations.EcommerceServiceFactory>();

// Connection test services are now integrated into notification channel services

// Register Welcome Service (commented out - service not implemented yet)
// builder.Services.AddScoped<PushDashboard.Controllers.IWelcomeService, PushDashboard.Services.WelcomeService>();

// Register Webhook Services
builder.Services.AddScoped<PushDashboard.Services.Integrations.IEcommerceWebhookFactory, PushDashboard.Services.Integrations.EcommerceWebhookFactory>();

// Register Module Usage Service
builder.Services.AddScoped<PushDashboard.Services.IModuleUsageService, PushDashboard.Services.ModuleUsageService>();
builder.Services.AddScoped<PushDashboard.Services.ModuleUsageSeedService>();

// Register Notification Services
builder.Services.AddScoped<PushDashboard.Services.Notifications.INotificationChannelService, PushDashboard.Services.Notifications.EmailNotificationChannelService>();
builder.Services.AddScoped<PushDashboard.Services.Notifications.EmailNotificationChannelService>();
builder.Services.AddScoped<PushDashboard.Services.Notifications.WhatsAppNotificationChannelService>();
builder.Services.AddScoped<PushDashboard.Services.Notifications.INotificationChannelFactory, PushDashboard.Services.Notifications.NotificationChannelFactory>();

// Register WhatsApp Services
builder.Services.AddHttpClient<PushDashboard.Services.WhatsApp.IWhatsAppService, PushDashboard.Services.WhatsApp.WhatsAppService>();
builder.Services.AddScoped<PushDashboard.Services.IWhatsAppTemplateService, PushDashboard.Services.WhatsAppTemplateService>();
builder.Services.AddScoped<PushDashboard.Services.IWhatsAppTemplateSampleService, PushDashboard.Services.WhatsAppTemplateSampleService>();
// Register Module Services
builder.Services.AddScoped<PushDashboard.Services.Modules.Birthday.IBirthdayModuleService, PushDashboard.Services.Modules.Birthday.BirthdayModuleService>();
// Register First Order Notification Service
builder.Services.AddScoped<PushDashboard.Services.Modules.FirstOrder.IFirstOrderModuleService, PushDashboard.Services.Modules.FirstOrder.FirstOrderModuleService>();
// Register Gift Wheel Services
builder.Services.AddScoped<PushDashboard.Services.Modules.GiftWheel.IGiftWheelService, PushDashboard.Services.Modules.GiftWheel.GiftWheelService>();
builder.Services.AddScoped<PushDashboard.Services.Modules.GiftWheel.IGiftWheelModuleService, PushDashboard.Services.Modules.GiftWheel.GiftWheelModuleService>();

// Register Video Hosting Services
builder.Services.AddScoped<PushDashboard.Services.Modules.VideoHosting.IS3VideoService, PushDashboard.Services.Modules.VideoHosting.S3VideoService>();
builder.Services.AddScoped<PushDashboard.Services.Modules.VideoHosting.IVideoProcessingService, PushDashboard.Services.Modules.VideoHosting.VideoProcessingService>();
builder.Services.AddScoped<PushDashboard.Services.Modules.VideoHosting.IVideoHostingService, PushDashboard.Services.Modules.VideoHosting.VideoHostingService>();
builder.Services.AddScoped<PushDashboard.Services.Modules.VideoHosting.IVideoHostingModuleService, PushDashboard.Services.Modules.VideoHosting.VideoHostingModuleService>();

// Register Dashboard Service
builder.Services.AddScoped<PushDashboard.Services.IDashboardService, PushDashboard.Services.DashboardService>();
// Register Ticimax Base Service as IEcommerceService
builder.Services.AddScoped<PushDashboard.Services.Integrations.Common.IEcommerceService, PushDashboard.Services.Integrations.Ticimax.TicimaxService>();

// Register TicimaxService directly for connection testing
builder.Services.AddScoped<PushDashboard.Services.Integrations.Ticimax.TicimaxService>();

// Register E-commerce Gift Voucher Services
builder.Services.AddScoped<PushDashboard.Services.Integrations.EcommerceGiftVoucherFactory>();

// Register Bulk Messaging Services
builder.Services.AddScoped<PushDashboard.Services.BulkMessaging.IBulkMessagingService, PushDashboard.Services.BulkMessaging.BulkMessagingService>();

// Register Order Status Notification Service
builder.Services.AddScoped<PushDashboard.Services.IOrderStatusNotificationService, PushDashboard.Services.OrderStatusNotificationService>();

// Register Order Status Mapping Service
builder.Services.AddScoped<PushDashboard.Services.IOrderStatusMappingService, PushDashboard.Services.OrderStatusMappingService>();

// Register Basket Reminder Service
builder.Services.AddScoped<PushDashboard.Services.IBasketReminderService, PushDashboard.Services.BasketReminderService>();
builder.Services.AddScoped<PushDashboard.Services.ICommunicationChannelService, PushDashboard.Services.CommunicationChannelService>();

// Add session services
builder.Services.AddDistributedMemoryCache();
builder.Services.AddSession(options =>
{
    options.IdleTimeout = TimeSpan.FromMinutes(30);
    options.Cookie.HttpOnly = true;
    options.Cookie.IsEssential = true;
    options.Cookie.SecurePolicy = CookieSecurePolicy.SameAsRequest;
});

// Add rate limiting for Gift Wheel API
builder.Services.AddRateLimiter(options =>
{
    // Gift Wheel Spin Policy - Most restrictive
    options.AddFixedWindowLimiter("GiftWheelSpinPolicy", limiterOptions =>
    {
        limiterOptions.PermitLimit = 5; // 5 spins per minute per IP
        limiterOptions.Window = TimeSpan.FromMinutes(1);
        limiterOptions.QueueProcessingOrder = QueueProcessingOrder.OldestFirst;
        limiterOptions.QueueLimit = 2;
    });

    // Gift Wheel Config Policy - Moderate
    options.AddFixedWindowLimiter("GiftWheelConfigPolicy", limiterOptions =>
    {
        limiterOptions.PermitLimit = 30; // 30 requests per minute per IP
        limiterOptions.Window = TimeSpan.FromMinutes(1);
        limiterOptions.QueueProcessingOrder = QueueProcessingOrder.OldestFirst;
        limiterOptions.QueueLimit = 5;
    });

    // Gift Wheel Script Policy - Less restrictive
    options.AddFixedWindowLimiter("GiftWheelScriptPolicy", limiterOptions =>
    {
        limiterOptions.PermitLimit = 60; // 60 requests per minute per IP
        limiterOptions.Window = TimeSpan.FromMinutes(1);
        limiterOptions.QueueProcessingOrder = QueueProcessingOrder.OldestFirst;
        limiterOptions.QueueLimit = 10;
    });

    // Gift Wheel Validation Policy - Moderate
    options.AddFixedWindowLimiter("GiftWheelValidationPolicy", limiterOptions =>
    {
        limiterOptions.PermitLimit = 20; // 20 validations per minute per IP
        limiterOptions.Window = TimeSpan.FromMinutes(1);
        limiterOptions.QueueProcessingOrder = QueueProcessingOrder.OldestFirst;
        limiterOptions.QueueLimit = 3;
    });

    // Gift Wheel Eligibility Policy - Moderate
    options.AddFixedWindowLimiter("GiftWheelEligibilityPolicy", limiterOptions =>
    {
        limiterOptions.PermitLimit = 30; // 30 eligibility checks per minute per IP
        limiterOptions.Window = TimeSpan.FromMinutes(1);
        limiterOptions.QueueProcessingOrder = QueueProcessingOrder.OldestFirst;
        limiterOptions.QueueLimit = 5;
    });

    // Global rejection response
    options.OnRejected = async (context, token) =>
    {
        context.HttpContext.Response.StatusCode = 429;
        await context.HttpContext.Response.WriteAsync("Çok fazla istek gönderildi. Lütfen daha sonra tekrar deneyin.", token);
    };
});
builder.Services.AddControllersWithViews();
builder.Services.AddSignalR();
builder.Services.AddAuthentication(CookieAuthenticationDefaults.AuthenticationScheme)
    .AddCookie(options =>
    {
        options.LoginPath = "/login";
        options.AccessDeniedPath = "/access-denied";
    });

var app = builder.Build();

// Seed Social Proof Module
using (var scope = app.Services.CreateScope())
{
    var context = scope.ServiceProvider.GetRequiredService<PushDashboard.Data.ApplicationDbContext>();
    await PushDashboard.Data.Seeders.SocialProofModuleSeeder.SeedSocialProofModuleAsync(context);
}

// Seed Cookie Management Module
using (var scope = app.Services.CreateScope())
{
    var context = scope.ServiceProvider.GetRequiredService<PushDashboard.Data.ApplicationDbContext>();
    await PushDashboard.Data.Seeders.CookieManagementModuleSeeder.SeedCookieManagementModuleAsync(context);
}

// Configure the HTTP request pipeline.
if (!app.Environment.IsDevelopment())
{
    app.UseExceptionHandler("/Home/Error");
    // The default HSTS value is 30 days. You may want to change this for production scenarios, see https://aka.ms/aspnetcore-hsts.
    app.UseHsts();
}

app.UseHttpsRedirection();
app.UseStaticFiles();

app.UseRouting();

app.UseRateLimiter(); // Add rate limiting middleware

app.UseSession();
app.UseAuthentication();
app.UseAuthorization();

// Add UserContext middleware after authentication
app.UseMiddleware<PushDashboard.Middleware.UserContextMiddleware>();

// Create database and apply migrations if they don't exist
using (var scope = app.Services.CreateScope())
{
    var services = scope.ServiceProvider;
    try
    {
        var context = services.GetRequiredService<ApplicationDbContext>();
        context.Database.EnsureCreated();

        // Seed initial roles and admin user if needed
        var userManager = services.GetRequiredService<UserManager<ApplicationUser>>();
        var roleManager = services.GetRequiredService<RoleManager<IdentityRole>>();
        SeedData.Initialize(userManager, roleManager, context).Wait();

        // Seed module data
        var moduleSeedService = services.GetRequiredService<IModuleSeedService>();
        moduleSeedService.SeedAsync().Wait();

        // Seed integration data
        var integrationSeedService = services.GetRequiredService<IIntegrationSeedService>();
        integrationSeedService.SeedAsync().Wait();

        // Seed email template data
        var emailTemplateSeedService = services.GetRequiredService<IEmailTemplateSeedService>();
        emailTemplateSeedService.SeedAsync().Wait();

        // Seed module usage data
        var moduleUsageSeedService = services.GetRequiredService<ModuleUsageSeedService>();
        moduleUsageSeedService.SeedAsync().Wait();

        // Seed first order module data

        // Seed exit survey module data
        await PushDashboard.Data.Seeders.ExitSurveyModuleSeeder.SeedAsync(context);

        // Test entegrasyon mimarisi (sadece development ortamında)
        // if (app.Environment.IsDevelopment())
        // {
        //     try
        //     {
        //         await PushDashboard.Tests.IntegrationArchitectureTest.TestIntegrationArchitecture(services);
        //     }
        //     catch (Exception testEx)
        //     {
        //         var logger = services.GetRequiredService<ILogger<Program>>();
        //         logger.LogError(testEx, "Entegrasyon mimarisi testi başarısız");
        //     }
        // }
    }
    catch (Exception ex)
    {
        var logger = services.GetRequiredService<ILogger<Program>>();
        logger.LogError(ex, "An error occurred while migrating or seeding the database.");
    }
}

app.MapHub<PushDashboard.Hubs.SessionHub>("/sessionHub");
app.MapHub<PushDashboard.Hubs.CustomerImportHub>("/customerImportHub");
app.MapControllerRoute(
    name: "default",
    pattern: "{controller=Home}/{action=Index}/{id?}");

app.Run();