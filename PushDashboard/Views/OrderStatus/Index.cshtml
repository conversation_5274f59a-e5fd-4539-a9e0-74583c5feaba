@using PushDashboard.ViewModels
@model OrderStatusNotificationIndexViewModel
@{
    ViewData["Title"] = "Sipariş Durumu Bildirimleri";
}

@Html.AntiForgeryToken()

<div class="p-6">
    <!-- Header -->
    <div class="flex justify-between items-center mb-6">
        <div>
            <h1 class="text-2xl font-bold text-gray-900">Sipariş Durumu Bildirimleri</h1>
            <p class="text-gray-600 mt-1"><PERSON><PERSON><PERSON><PERSON> durumu değişikliklerinde otomatik bildirim gönderme ayarları</p>
        </div>
        <div class="flex space-x-3">
            <button type="button" class="px-4 py-2 border border-primary text-primary rounded-lg hover:bg-primary hover:text-white transition-colors" onclick="showStatistics()">
                <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                </svg>
                İstatistikler
            </button>
            <button type="button" class="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-dark transition-colors" onclick="showTestModal()">
                <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
                </svg>
                Test Gönder
            </button>
        </div>
    </div>



    @if (!Model.ActiveChannels.HasAnyChannel)
    {
        <!-- No Active Channels Warning -->
        <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
            <div class="flex items-start">
                <svg class="w-5 h-5 text-yellow-600 mr-3 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                </svg>
                <div class="text-yellow-800">
                    <p class="font-medium">İletişim Kanalı Gerekli:</p>
                    <p class="text-sm mt-1">Sipariş durumu bildirimleri gönderebilmek için en az bir iletişim kanalı yapılandırmanız gerekiyor.</p>
                    <div class="mt-3 space-y-2">
                        <p class="text-sm">• <strong>E-posta:</strong> <a href="/Integration" class="text-yellow-700 underline hover:text-yellow-900">SMTP ayarlarını yapılandırın</a></p>
                        <p class="text-sm">• <strong>WhatsApp:</strong> <a href="/Integration" class="text-yellow-700 underline hover:text-yellow-900">WhatsApp Business API entegrasyonu yapın</a></p>
                        <p class="text-sm">• <strong>SMS:</strong> Yakında eklenecek</p>
                    </div>
                </div>
            </div>
        </div>
    }
    else
    {
        <!-- Info Alert -->
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
            <div class="flex items-start">
                <svg class="w-5 h-5 text-blue-600 mr-3 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <div class="text-blue-800">
                    <p class="font-medium">Bilgi:</p>
                    <p class="text-sm mt-1">Sipariş durumu değiştiğinde seçtiğiniz şablonlar kullanılarak müşterilerinize otomatik bildirim gönderilir. Bildirimleri aktifleştirmek için her durum için en az bir iletişim kanalı seçmelisiniz.</p>
                    <p class="text-xs text-blue-600 mt-2">
                        Aktif kanallar:
                        @if (Model.ActiveChannels.HasEmail) { <span class="inline-flex items-center px-2 py-1 rounded-full text-xs bg-blue-100 text-blue-800 mr-1">E-posta</span> }
                        @if (Model.ActiveChannels.HasSms) { <span class="inline-flex items-center px-2 py-1 rounded-full text-xs bg-green-100 text-green-800 mr-1">SMS</span> }
                        @if (Model.ActiveChannels.HasWhatsApp) { <span class="inline-flex items-center px-2 py-1 rounded-full text-xs bg-green-100 text-green-800 mr-1">WhatsApp</span> }
                    </p>
                    <p class="text-xs text-blue-600 mt-1">Webhook'lar e-ticaret entegrasyonu kaydedildiğinde otomatik olarak eklenir.</p>
                </div>
            </div>
        </div>
    }

    <!-- Order Status Cards -->
    <div class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
        @foreach (var notification in Model.Notifications)
        {
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 h-full flex flex-col">
                <!-- Card Header -->
                <div class="p-4 border-b border-gray-100 flex justify-between items-center">
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900">@notification.OrderStatusDisplayName</h3>
                        <p class="text-sm text-gray-500">Durum Kodu: @notification.OrderStatus</p>
                    </div>
                    <div class="flex items-center space-x-3">
                        <span class="px-2 py-1 text-xs font-medium rounded-full @(notification.IsActive ? "bg-green-100 text-green-800" : "bg-gray-100 text-gray-800")">
                            @(notification.IsActive ? "Aktif" : "Pasif")
                        </span>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" class="sr-only peer"
                                   id="<EMAIL>"
                                   @(notification.IsActive ? "checked" : "")
                                   onchange="toggleNotification('@notification.OrderStatus', this.checked)">
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary"></div>
                        </label>
                    </div>
                </div>

                <!-- Card Body -->
                <div class="p-4 flex-1">
                    <!-- Statistics -->
                    <div class="grid grid-cols-2 gap-4 mb-6">
                        <div class="text-center border-r border-gray-200">
                            <div class="text-2xl font-bold text-primary">@notification.TotalNotificationsSent</div>
                            <div class="text-sm text-gray-500">Gönderilen</div>
                        </div>
                        <div class="text-center">
                            <div class="text-sm font-medium text-gray-700">@notification.FormattedLastNotificationAt</div>
                            <div class="text-sm text-gray-500">Son Gönderim</div>
                        </div>
                    </div>

                    <!-- Notification Channels -->
                    <div class="notification-channels space-y-4" data-status="@notification.OrderStatus">
                        @if (Model.ActiveChannels.HasEmail)
                        {
                            <!-- Email -->
                            <div class="border border-gray-200 rounded-lg p-3">
                            <div class="flex items-center justify-between mb-3">
                                <label class="flex items-center cursor-pointer">
                                    <input type="checkbox" class="w-4 h-4 text-primary bg-gray-100 border-gray-300 rounded focus:ring-primary focus:ring-2"
                                           id="<EMAIL>"
                                           @(notification.EmailNotificationEnabled ? "checked" : "")
                                           onchange="updateChannelSetting('@notification.OrderStatus', 'email', this.checked)">
                                    <span class="ml-2 flex items-center font-medium text-gray-700">
                                        <svg class="w-4 h-4 text-primary mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                                        </svg>
                                        E-posta
                                    </span>
                                </label>
                            </div>
                            <select class="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent @(notification.EmailNotificationEnabled ? "" : "bg-gray-100")"
                                    id="<EMAIL>"
                                    @(notification.EmailNotificationEnabled ? "" : "disabled")
                                    onchange="updateTemplateSetting('@notification.OrderStatus', 'email', this.value)">
                                <option value="">Şablon Seçin</option>
                                @foreach (var template in Model.EmailTemplates)
                                {
                                    @if (notification.EmailTemplateId == template.Id)
                                    {
                                        <option value="@template.Id" selected>[@template.Category] @template.Name</option>
                                    }
                                    else
                                    {
                                        <option value="@template.Id">[@template.Category] @template.Name</option>
                                    }
                                }
                            </select>
                        </div>
                        }

                        @if (Model.ActiveChannels.HasSms)
                        {
                            <!-- SMS -->
                        <div class="border border-gray-200 rounded-lg p-3">
                            <div class="flex items-center justify-between mb-3">
                                <label class="flex items-center cursor-pointer">
                                    <input type="checkbox" class="w-4 h-4 text-green bg-gray-100 border-gray-300 rounded focus:ring-green focus:ring-2"
                                           id="<EMAIL>"
                                           @(notification.SmsNotificationEnabled ? "checked" : "")
                                           onchange="updateChannelSetting('@notification.OrderStatus', 'sms', this.checked)">
                                    <span class="ml-2 flex items-center font-medium text-gray-700">
                                        <svg class="w-4 h-4 text-green mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                                        </svg>
                                        SMS
                                    </span>
                                </label>
                            </div>
                            <select class="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green focus:border-transparent @(notification.SmsNotificationEnabled ? "" : "bg-gray-100")"
                                    id="<EMAIL>"
                                    @(notification.SmsNotificationEnabled ? "" : "disabled")
                                    onchange="updateTemplateSetting('@notification.OrderStatus', 'sms', this.value)">
                                <option value="">Şablon Seçin</option>
                                @foreach (var template in Model.SmsTemplates)
                                {
                                    @if (notification.SmsTemplateId == template.Id)
                                    {
                                        <option value="@template.Id" selected>[@template.Category] @template.Name</option>
                                    }
                                    else
                                    {
                                        <option value="@template.Id">[@template.Category] @template.Name</option>
                                    }
                                }
                            </select>
                        </div>
                        }

                        @if (Model.ActiveChannels.HasWhatsApp)
                        {
                            <!-- WhatsApp -->
                        <div class="border border-gray-200 rounded-lg p-3">
                            <div class="flex items-center justify-between mb-3">
                                <label class="flex items-center cursor-pointer">
                                    <input type="checkbox" class="w-4 h-4 text-green bg-gray-100 border-gray-300 rounded focus:ring-green focus:ring-2"
                                           id="<EMAIL>"
                                           @(notification.WhatsAppNotificationEnabled ? "checked" : "")
                                           onchange="updateChannelSetting('@notification.OrderStatus', 'whatsapp', this.checked)">
                                    <span class="ml-2 flex items-center font-medium text-gray-700">
                                        <svg class="w-4 h-4 text-green mr-2" fill="currentColor" viewBox="0 0 24 24">
                                            <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.109"/>
                                        </svg>
                                        WhatsApp
                                    </span>
                                </label>
                            </div>
                            <select class="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green focus:border-transparent @(notification.WhatsAppNotificationEnabled ? "" : "bg-gray-100")"
                                    id="<EMAIL>"
                                    @(notification.WhatsAppNotificationEnabled ? "" : "disabled")
                                    onchange="updateTemplateSetting('@notification.OrderStatus', 'whatsapp', this.value)">
                                <option value="">Şablon Seçin</option>
                                @foreach (var template in Model.WhatsAppTemplates)
                                {
                                    @if (notification.WhatsAppTemplateId == template.WhatsappId)
                                    {
                                        <option value="@template.WhatsappId" selected>[@template.Category] @template.Name</option>
                                    }
                                    else
                                    {
                                        <option value="@template.WhatsappId">[@template.Category] @template.Name</option>
                                    }
                                }
                            </select>
                        </div>
                        }

                        <!-- Delay Settings -->
                        <div class="border border-gray-200 rounded-lg p-3">
                            <label class="flex items-center font-medium text-gray-700 mb-3">
                                <svg class="w-4 h-4 text-yellow mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                Gecikme (Dakika)
                            </label>
                            <input type="number" class="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-yellow focus:border-transparent"
                                   id="<EMAIL>"
                                   value="@notification.DelayMinutes"
                                   min="0" max="1440"
                                   onchange="updateDelaySetting('@notification.OrderStatus', this.value)">
                            <p class="text-xs text-gray-500 mt-2">0 = Anında gönder, Max: 1440 dakika (24 saat)</p>
                        </div>
                    </div>
                </div>

                <!-- Card Footer -->
                <div class="p-4 bg-gray-50 border-t border-gray-100 rounded-b-lg">
                    <div class="flex justify-between items-center space-x-3">
                        <button type="button" class="flex-1 px-4 py-2 border border-primary text-primary rounded-md hover:bg-primary hover:text-white transition-colors text-sm font-medium"
                                onclick="saveNotificationSettings('@notification.OrderStatus')">
                            <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3-3m0 0l-3 3m3-3v12"></path>
                            </svg>
                            Kaydet
                        </button>
                        <button type="button" class="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-100 transition-colors text-sm font-medium"
                                onclick="showOrderStatusLogs('@notification.OrderStatus')">
                            <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            Geçmiş
                        </button>
                    </div>
                </div>
            </div>
        }
    </div>
</div>

<!-- Test Modal -->
<div id="testModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50 hidden">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-lg font-medium text-gray-900">Bildirim Testi</h3>
            <button type="button" class="text-gray-400 hover:text-gray-600" onclick="closeTestModal()">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>
        <form id="testForm" class="space-y-4">
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Sipariş Durumu</label>
                <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent" id="testOrderStatus" required>
                    <option value="">Durum Seçin</option>
                    @foreach (var status in Model.OrderStatuses)
                    {
                        <option value="@status.Value">@status.Text</option>
                    }
                </select>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Test E-posta</label>
                <input type="email" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent" id="testEmail" required>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Test Telefon</label>
                <input type="tel" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent" id="testPhone">
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Test Sipariş ID</label>
                <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent" id="testOrderId" value="TEST-001" required>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Test Müşteri Adı</label>
                <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent" id="testCustomerName" value="Test Müşteri">
            </div>
        </form>
        <div class="flex justify-end space-x-3 mt-6">
            <button type="button" class="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-100 transition-colors" onclick="closeTestModal()">İptal</button>
            <button type="button" class="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark transition-colors" onclick="sendTestNotification()">Test Gönder</button>
        </div>
    </div>
</div>

<!-- Statistics Modal -->
<div id="statisticsModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50 hidden">
    <div class="relative top-20 mx-auto p-5 border w-4/5 max-w-4xl shadow-lg rounded-md bg-white">
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-lg font-medium text-gray-900">Bildirim İstatistikleri</h3>
            <button type="button" class="text-gray-400 hover:text-gray-600" onclick="closeStatisticsModal()">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>
        <div id="statisticsContent">
            <div class="text-center py-8">
                <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                <p class="mt-2 text-gray-600">Yükleniyor...</p>
            </div>
        </div>
    </div>
</div>

<!-- Order Status Logs Modal -->
<div id="logsModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50 hidden">
    <div class="relative top-10 mx-auto p-5 border w-11/12 max-w-6xl shadow-lg rounded-md bg-white">
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-lg font-medium text-gray-900">Sipariş Durumu Geçmişi</h3>
            <button type="button" class="text-gray-400 hover:text-gray-600" onclick="closeLogsModal()">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>
        <div id="logsContent">
            <div class="text-center py-8">
                <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                <p class="mt-2 text-gray-600">Yükleniyor...</p>
            </div>
        </div>
    </div>
</div>

<script>
// ===== GLOBAL VARIABLES =====
let notificationSettings = {};

// ===== INITIALIZATION =====
document.addEventListener('DOMContentLoaded', function() {
    initializeNotificationSettings();
    initializeUIElements();
    setupEventListeners();
});

function initializeNotificationSettings() {
    // Her sipariş durumu için ayarları initialize et
    @foreach (var notification in Model.Notifications)
    {
        <text>
        notificationSettings['@notification.OrderStatus'] = {
            orderStatus: '@notification.OrderStatus',
            isActive: @notification.IsActive.ToString().ToLower(),
            emailNotificationEnabled: @notification.EmailNotificationEnabled.ToString().ToLower(),
            emailTemplateId: @(notification.EmailTemplateId?.ToString() ?? "null"),
            smsNotificationEnabled: @notification.SmsNotificationEnabled.ToString().ToLower(),
            smsTemplateId: @(notification.SmsTemplateId?.ToString() ?? "null"),
            whatsAppNotificationEnabled: @notification.WhatsAppNotificationEnabled.ToString().ToLower(),
            whatsAppTemplateId: @(notification.WhatsAppTemplateId?.ToString() ?? "null"),
            delayMinutes: @notification.DelayMinutes
        };
        </text>
    }
}

function initializeUIElements() {
    // HTML elementlerini JavaScript değişkenlerine göre set et
    @foreach (var notification in Model.Notifications)
    {
        <text>
        // @notification.OrderStatus için UI elementlerini set et
        (function() {
            const orderStatus = '@notification.OrderStatus';

            // Toggle switch
            const toggle = document.getElementById(`toggle_${orderStatus}`);
            if (toggle) {
                toggle.checked = @notification.IsActive.ToString().ToLower();
            }

            // Email checkbox ve template
            const emailCheckbox = document.getElementById(`email_${orderStatus}`);
            if (emailCheckbox) {
                emailCheckbox.checked = @notification.EmailNotificationEnabled.ToString().ToLower();
            }

            const emailTemplate = document.getElementById(`emailTemplate_${orderStatus}`);
            if (emailTemplate) {
                emailTemplate.value = '@(notification.EmailTemplateId?.ToString() ?? "")';
                emailTemplate.disabled = !@notification.EmailNotificationEnabled.ToString().ToLower();
            }

            // SMS checkbox ve template
            const smsCheckbox = document.getElementById(`sms_${orderStatus}`);
            if (smsCheckbox) {
                smsCheckbox.checked = @notification.SmsNotificationEnabled.ToString().ToLower();
            }

            const smsTemplate = document.getElementById(`smsTemplate_${orderStatus}`);
            if (smsTemplate) {
                smsTemplate.value = '@(notification.SmsTemplateId?.ToString() ?? "")';
                smsTemplate.disabled = !@notification.SmsNotificationEnabled.ToString().ToLower();
            }

            // WhatsApp checkbox ve template
            const whatsAppCheckbox = document.getElementById(`whatsapp_${orderStatus}`);
            if (whatsAppCheckbox) {
                whatsAppCheckbox.checked = @notification.WhatsAppNotificationEnabled.ToString().ToLower();
            }

            const whatsAppTemplate = document.getElementById(`whatsappTemplate_${orderStatus}`);
            if (whatsAppTemplate) {
                whatsAppTemplate.value = '@(notification.WhatsAppTemplateId?.ToString() ?? "")';
                whatsAppTemplate.disabled = !@notification.WhatsAppNotificationEnabled.ToString().ToLower();
            }

            // Delay input
            const delayInput = document.getElementById(`delay_${orderStatus}`);
            if (delayInput) {
                delayInput.value = @notification.DelayMinutes;
            }
        })();

        </text>
    }
}

function setupEventListeners() {
    // Modal event listeners - TailwindCSS modals don't need Bootstrap events
}

// ===== NOTIFICATION TOGGLE =====
async function toggleNotification(orderStatus, isActive) {
    try {
        // Eğer aktif etmeye çalışıyorsa, önce validasyon yap
        if (isActive) {
            const validationResult = validateChannelsAndTemplates(orderStatus);
            if (!validationResult.isValid) {
                // Toggle'ı geri çevir
                document.getElementById(`toggle_${orderStatus}`).checked = false;
                showNotification(validationResult.message, 'warning');
                return;
            }
        }

        showLoading(`toggle_${orderStatus}`, true);

        const response = await fetch('/OrderStatus/ToggleNotification', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]')?.value || ''
            },
            body: JSON.stringify({
                orderStatus: orderStatus,
                isActive: isActive
            })
        });

        const result = await response.json();

        if (result.success) {
            notificationSettings[orderStatus].isActive = isActive;
            updateToggleUI(orderStatus, isActive);
            showNotification(result.message, 'success');
        } else {
            // Revert toggle
            document.getElementById(`toggle_${orderStatus}`).checked = !isActive;
            showNotification(result.message, 'error');
        }
    } catch (error) {
        console.error('Error toggling notification:', error);
        document.getElementById(`toggle_${orderStatus}`).checked = !isActive;
        showNotification('Bildirim durumu güncellenirken hata oluştu.', 'error');
    } finally {
        showLoading(`toggle_${orderStatus}`, false);
    }
}

// İletişim kanalları ve şablonları validasyonu
function validateChannelsAndTemplates(orderStatus) {
    const emailCheckbox = document.getElementById(`email_${orderStatus}`);
    const emailTemplate = document.getElementById(`emailTemplate_${orderStatus}`);
    const smsCheckbox = document.getElementById(`sms_${orderStatus}`);
    const smsTemplate = document.getElementById(`smsTemplate_${orderStatus}`);
    const whatsAppCheckbox = document.getElementById(`whatsapp_${orderStatus}`);
    const whatsAppTemplate = document.getElementById(`whatsappTemplate_${orderStatus}`);

    // En az bir kanal aktif mi kontrol et
    const hasEmailChannel = emailCheckbox && emailCheckbox.checked;
    const hasSmsChannel = smsCheckbox && smsCheckbox.checked;
    const hasWhatsAppChannel = whatsAppCheckbox && whatsAppCheckbox.checked;

    const hasAnyChannel = hasEmailChannel || hasSmsChannel || hasWhatsAppChannel;

    if (!hasAnyChannel) {
        return {
            isValid: false,
            message: 'Sipariş durumunu aktif etmek için en az bir iletişim kanalını aktif etmelisiniz.'
        };
    }

    // Aktif kanalların şablonları seçilmiş mi kontrol et
    if (hasEmailChannel && (!emailTemplate || !emailTemplate.value)) {
        return {
            isValid: false,
            message: 'E-posta kanalı aktif olduğu için e-posta şablonu seçmelisiniz.'
        };
    }

    if (hasSmsChannel && (!smsTemplate || !smsTemplate.value)) {
        return {
            isValid: false,
            message: 'SMS kanalı aktif olduğu için SMS şablonu seçmelisiniz.'
        };
    }

    if (hasWhatsAppChannel && (!whatsAppTemplate || !whatsAppTemplate.value)) {
        return {
            isValid: false,
            message: 'WhatsApp kanalı aktif olduğu için WhatsApp şablonu seçmelisiniz.'
        };
    }

    return {
        isValid: true,
        message: 'Validasyon başarılı'
    };
}

function updateToggleUI(orderStatus, isActive) {
    // TailwindCSS badge elementini bul
    const toggleElement = document.getElementById(`toggle_${orderStatus}`);
    if (toggleElement) {
        // Toggle'ın parent container'ında badge'i bul
        const badgeElement = toggleElement.closest('.flex').querySelector('span.px-2');
        if (badgeElement) {
            if (isActive) {
                badgeElement.className = 'px-2 py-1 text-xs font-medium rounded-full bg-green-100 text-green-800';
                badgeElement.textContent = 'Aktif';
            } else {
                badgeElement.className = 'px-2 py-1 text-xs font-medium rounded-full bg-gray-100 text-gray-800';
                badgeElement.textContent = 'Pasif';
            }
        }
    }
}

// ===== CHANNEL SETTINGS =====
function updateChannelSetting(orderStatus, channel, enabled) {
    const templateSelect = document.getElementById(`${channel}Template_${orderStatus}`);
    if (templateSelect) {
        templateSelect.disabled = !enabled;

        if (!enabled) {
            templateSelect.value = '';
        }
    }

    // Update local settings
    if (notificationSettings[orderStatus]) {
        notificationSettings[orderStatus][`${channel}NotificationEnabled`] = enabled;
        if (!enabled) {
            notificationSettings[orderStatus][`${channel}TemplateId`] = null;
        }
    }
}

function updateTemplateSetting(orderStatus, channel, templateId) {
    if (notificationSettings[orderStatus]) {
        notificationSettings[orderStatus][`${channel}TemplateId`] = templateId || null;
    }
}

function updateDelaySetting(orderStatus, delayMinutes) {
    if (notificationSettings[orderStatus]) {
        notificationSettings[orderStatus].delayMinutes = parseInt(delayMinutes) || 0;
    }
}

// ===== SAVE SETTINGS =====
async function saveNotificationSettings(orderStatus) {
    try {
        // HTML elementlerinden güncel verileri topla
        const settings = collectCurrentSettings(orderStatus);

        console.log('Saving settings for', orderStatus, ':', settings);

        // Validation
        if (settings.isActive) {
            const hasAnyChannel = settings.emailNotificationEnabled ||
                                settings.smsNotificationEnabled ||
                                settings.whatsAppNotificationEnabled;

            if (!hasAnyChannel) {
                showNotification('Aktif bildirim için en az bir iletişim kanalı seçmelisiniz.', 'warning');
                return;
            }

            // Template validation
            if (settings.emailNotificationEnabled && !settings.emailTemplateId) {
                showNotification('E-posta bildirimi için şablon seçmelisiniz.', 'warning');
                return;
            }

            if (settings.smsNotificationEnabled && !settings.smsTemplateId) {
                showNotification('SMS bildirimi için şablon seçmelisiniz.', 'warning');
                return;
            }

            if (settings.whatsAppNotificationEnabled && !settings.whatsAppTemplateId) {
                showNotification('WhatsApp bildirimi için şablon seçmelisiniz.', 'warning');
                return;
            }
        }

        // showLoading(`save_${orderStatus}`, true);

        const response = await fetch('/OrderStatus/SaveNotificationSettings', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]')?.value || ''
            },
            body: JSON.stringify(settings)
        });

        const result = await response.json();

        if (result.success) {
            showNotification(result.message, 'success');
            // Başarılı kayıt sonrası notificationSettings objesini güncelle
            notificationSettings[orderStatus] = settings;
        } else {
            showNotification(result.message, 'error');
        }
    } catch (error) {
        console.error('Error saving notification settings:', error);
        showNotification('Ayarlar kaydedilirken hata oluştu.', 'error');
    } finally {
        // showLoading(`save_${orderStatus}`, false);
    }
}

// HTML elementlerinden güncel ayarları topla
function collectCurrentSettings(orderStatus) {
    const toggle = document.getElementById(`toggle_${orderStatus}`);
    const emailCheckbox = document.getElementById(`email_${orderStatus}`);
    const emailTemplate = document.getElementById(`emailTemplate_${orderStatus}`);
    const smsCheckbox = document.getElementById(`sms_${orderStatus}`);
    const smsTemplate = document.getElementById(`smsTemplate_${orderStatus}`);
    const whatsAppCheckbox = document.getElementById(`whatsapp_${orderStatus}`);
    const whatsAppTemplate = document.getElementById(`whatsappTemplate_${orderStatus}`);
    const delayInput = document.getElementById(`delay_${orderStatus}`);

    return {
        orderStatus: orderStatus,
        isActive: toggle ? toggle.checked : false,
        emailNotificationEnabled: emailCheckbox ? emailCheckbox.checked : false,
        emailTemplateId: emailTemplate && emailTemplate.value ? parseInt(emailTemplate.value) : null,
        smsNotificationEnabled: smsCheckbox ? smsCheckbox.checked : false,
        smsTemplateId: smsTemplate && smsTemplate.value ? parseInt(smsTemplate.value) : null,
        whatsAppNotificationEnabled: whatsAppCheckbox ? whatsAppCheckbox.checked : false,
        whatsAppTemplateId: whatsAppTemplate && whatsAppTemplate.value ? whatsAppTemplate.value : null,
        delayMinutes: delayInput ? parseInt(delayInput.value) || 0 : 0
    };
}

// ===== TEST NOTIFICATION =====
function showTestModal() {
    document.getElementById('testModal').classList.remove('hidden');
}

function closeTestModal() {
    document.getElementById('testModal').classList.add('hidden');
    document.getElementById('testForm').reset();
}

async function sendTestNotification() {
    try {
        const form = document.getElementById('testForm');
        if (!form.checkValidity()) {
            form.reportValidity();
            return;
        }

        const testData = {
            orderStatus: document.getElementById('testOrderStatus').value,
            testEmail: document.getElementById('testEmail').value,
            testPhone: document.getElementById('testPhone').value,
            testOrderId: document.getElementById('testOrderId').value,
            testCustomerName: document.getElementById('testCustomerName').value,
            testChannels: ['email'] // Default to email for now
        };

        showLoading('testSend', true);

        // TODO: Implement test notification endpoint
        showNotification('Test bildirimi gönderildi.', 'success');

        closeTestModal();

    } catch (error) {
        console.error('Error sending test notification:', error);
        showNotification('Test bildirimi gönderilirken hata oluştu.', 'error');
    } finally {
        showLoading('testSend', false);
    }
}

// ===== STATISTICS =====
function showStatistics() {
    document.getElementById('statisticsModal').classList.remove('hidden');
    loadStatistics();
}

function closeStatisticsModal() {
    document.getElementById('statisticsModal').classList.add('hidden');
}

async function loadStatistics() {
    try {
        // TODO: Implement statistics endpoint
        const statisticsContent = document.getElementById('statisticsContent');
        statisticsContent.innerHTML = `
            <div class="row">
                <div class="col-md-6 mb-3">
                    <div class="card">
                        <div class="card-body text-center">
                            <h5 class="card-title">Toplam Bildirim</h5>
                            <h2 class="text-primary">1,234</h2>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 mb-3">
                    <div class="card">
                        <div class="card-body text-center">
                            <h5 class="card-title">Başarı Oranı</h5>
                            <h2 class="text-success">98.5%</h2>
                        </div>
                    </div>
                </div>
            </div>
            <p class="text-muted text-center">İstatistik detayları yakında eklenecek.</p>
        `;
    } catch (error) {
        console.error('Error loading statistics:', error);
        document.getElementById('statisticsContent').innerHTML =
            '<div class="alert alert-danger">İstatistikler yüklenirken hata oluştu.</div>';
    }
}

// ===== ORDER STATUS LOGS =====
function showOrderStatusLogs(orderStatus) {
    document.getElementById('logsModal').classList.remove('hidden');
    loadOrderStatusLogs(orderStatus);
}

function closeLogsModal() {
    document.getElementById('logsModal').classList.add('hidden');
}

async function loadOrderStatusLogs(orderStatus) {
    try {
        const response = await fetch(`/OrderStatus/GetOrderStatusLogs?orderStatus=${orderStatus}&page=1&pageSize=20`);
        const result = await response.json();

        if (result.success) {
            renderOrderStatusLogs(result.data);
        } else {
            document.getElementById('logsContent').innerHTML =
                '<div class="alert alert-danger">Geçmiş yüklenirken hata oluştu.</div>';
        }
    } catch (error) {
        console.error('Error loading order status logs:', error);
        document.getElementById('logsContent').innerHTML =
            '<div class="alert alert-danger">Geçmiş yüklenirken hata oluştu.</div>';
    }
}

function renderOrderStatusLogs(logs) {
    if (logs.length === 0) {
        document.getElementById('logsContent').innerHTML =
            '<div class="alert alert-info">Henüz sipariş durumu değişikliği bulunmuyor.</div>';
        return;
    }

    let html = `
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>Sipariş</th>
                        <th>Müşteri</th>
                        <th>Durum Değişikliği</th>
                        <th>Tarih</th>
                        <th>Bildirim</th>
                    </tr>
                </thead>
                <tbody>
    `;

    logs.forEach(log => {
        html += `
            <tr>
                <td>
                    <div class="fw-bold">${log.orderNumber || log.orderId}</div>
                    <small class="text-muted">${log.orderId}</small>
                </td>
                <td>
                    <div>${log.customerName || 'N/A'}</div>
                    <small class="text-muted">${log.customerEmail}</small>
                </td>
                <td>
                    <span class="badge bg-info">${log.statusChangeText}</span>
                </td>
                <td>${log.formattedStatusChangedAt}</td>
                <td>
                    ${log.notificationSent
                        ? '<span class="badge bg-success">Gönderildi</span>'
                        : '<span class="badge bg-warning">Gönderilmedi</span>'}
                </td>
            </tr>
        `;
    });

    html += `
                </tbody>
            </table>
        </div>
    `;

    document.getElementById('logsContent').innerHTML = html;
}

// ===== UTILITY FUNCTIONS =====
function showLoading(elementId, show) {
    const element = document.getElementById(elementId);
    if (element) {
        if (show) {
            element.disabled = true;
            element.innerHTML = '<span class="spinner-border spinner-border-sm me-1"></span>Yükleniyor...';
        } else {
            element.disabled = false;
            // Restore original text based on element type
            if (elementId.startsWith('toggle_')) {
                // Toggle elements don't need text restoration
            } else if (elementId.startsWith('save_')) {
                element.innerHTML = '<i class="fas fa-save me-1"></i>Kaydet';
            }
        }
    }
}

function showNotification(message, type) {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show position-fixed`;
    notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(notification);

    // Auto remove after 5 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 5000);
}


</script>