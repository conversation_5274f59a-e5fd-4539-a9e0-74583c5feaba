<!-- Loading Skeleton for Dashboard -->
<div id="dashboardSkeleton" class="hidden">
    <!-- Stats Cards Skeleton -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        @for (int i = 0; i < 4; i++)
        {
            <div class="bg-white p-6 rounded-2xl shadow-sm border border-gray-200 animate-pulse">
                <div class="flex items-center justify-between mb-4">
                    <div class="w-16 h-16 bg-gray-300 rounded-2xl"></div>
                    <div class="w-8 h-8 bg-gray-200 rounded-full"></div>
                </div>
                <div class="space-y-3">
                    <div class="h-4 bg-gray-200 rounded w-3/4"></div>
                    <div class="h-8 bg-gray-300 rounded w-1/2"></div>
                    <div class="h-3 bg-gray-200 rounded w-2/3"></div>
                </div>
                <div class="mt-4">
                    <div class="w-full h-2 bg-gray-200 rounded-full"></div>
                </div>
            </div>
        }
    </div>

    <!-- Performance Summary Skeleton -->
    <div class="bg-white rounded-2xl shadow-sm border border-gray-200 p-6 mb-8 animate-pulse">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-8">
                <div class="flex items-center">
                    <div class="w-4 h-4 bg-gray-300 rounded-full mr-3"></div>
                    <div class="h-4 bg-gray-200 rounded w-32"></div>
                </div>
                <div class="flex items-center">
                    <div class="w-4 h-4 bg-gray-300 rounded-full mr-3"></div>
                    <div class="h-4 bg-gray-200 rounded w-28"></div>
                </div>
                <div class="flex items-center">
                    <div class="w-4 h-4 bg-gray-300 rounded-full mr-3"></div>
                    <div class="h-4 bg-gray-200 rounded w-24"></div>
                </div>
            </div>
            <div class="flex space-x-1">
                <div class="w-2 h-8 bg-gray-300 rounded-full"></div>
                <div class="w-2 h-6 bg-gray-300 rounded-full"></div>
                <div class="w-2 h-4 bg-gray-300 rounded-full"></div>
                <div class="w-2 h-7 bg-gray-300 rounded-full"></div>
            </div>
        </div>
    </div>

    <!-- Quick Actions Skeleton -->
    <div class="bg-white rounded-2xl shadow-sm border border-gray-200 p-6 mb-6 animate-pulse">
        <div class="flex justify-between items-center mb-6">
            <div>
                <div class="h-6 bg-gray-300 rounded w-32 mb-2"></div>
                <div class="h-4 bg-gray-200 rounded w-48"></div>
            </div>
        </div>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            @for (int i = 0; i < 4; i++)
            {
                <div class="border border-gray-200 rounded-lg p-4">
                    <div class="flex items-center mb-3">
                        <div class="w-10 h-10 bg-gray-300 rounded-lg mr-3"></div>
                        <div class="flex-1">
                            <div class="h-4 bg-gray-300 rounded w-3/4 mb-1"></div>
                            <div class="h-3 bg-gray-200 rounded w-1/2"></div>
                        </div>
                    </div>
                    <div class="h-3 bg-gray-200 rounded w-full mb-4"></div>
                    <div class="h-10 bg-gray-300 rounded"></div>
                </div>
            }
        </div>
    </div>

    <!-- Charts Skeleton -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
        <!-- Large Chart -->
        <div class="lg:col-span-2 bg-white p-6 rounded-2xl shadow-sm border border-gray-200 animate-pulse">
            <div class="flex items-center justify-between mb-6">
                <div>
                    <div class="h-6 bg-gray-300 rounded w-32 mb-2"></div>
                    <div class="h-4 bg-gray-200 rounded w-48"></div>
                </div>
                <div class="flex space-x-2">
                    <div class="w-8 h-6 bg-gray-200 rounded-full"></div>
                    <div class="w-8 h-6 bg-gray-200 rounded-full"></div>
                    <div class="w-8 h-8 bg-gray-200 rounded"></div>
                </div>
            </div>
            <div class="h-64 bg-gray-200 rounded"></div>
        </div>

        <!-- Donut Chart -->
        <div class="bg-white p-6 rounded-2xl shadow-sm border border-gray-200 animate-pulse">
            <div class="flex items-center justify-between mb-6">
                <div>
                    <div class="h-6 bg-gray-300 rounded w-24 mb-2"></div>
                    <div class="h-4 bg-gray-200 rounded w-32"></div>
                </div>
            </div>
            <div class="h-48 bg-gray-200 rounded-full mx-auto w-48"></div>
        </div>
    </div>

    <!-- Bottom Charts -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        @for (int i = 0; i < 2; i++)
        {
            <div class="bg-white p-6 rounded-2xl shadow-sm border border-gray-200 animate-pulse">
                <div class="flex items-center justify-between mb-6">
                    <div>
                        <div class="h-6 bg-gray-300 rounded w-32 mb-2"></div>
                        <div class="h-4 bg-gray-200 rounded w-40"></div>
                    </div>
                    <div class="h-4 bg-gray-200 rounded w-16"></div>
                </div>
                <div class="h-48 bg-gray-200 rounded"></div>
                <div class="mt-4 grid grid-cols-3 gap-4">
                    @for (int j = 0; j < 3; j++)
                    {
                        <div class="p-3 bg-gray-100 rounded-lg">
                            <div class="h-3 bg-gray-200 rounded w-16 mb-2"></div>
                            <div class="h-5 bg-gray-300 rounded w-12"></div>
                        </div>
                    }
                </div>
            </div>
        }
    </div>

    <!-- Activity Lists Skeleton -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        @for (int i = 0; i < 2; i++)
        {
            <div class="bg-white rounded-2xl shadow-sm border border-gray-200 p-6 animate-pulse">
                <div class="flex items-center justify-between mb-6">
                    <div>
                        <div class="h-6 bg-gray-300 rounded w-32 mb-2"></div>
                        <div class="h-4 bg-gray-200 rounded w-40"></div>
                    </div>
                    <div class="h-4 bg-gray-200 rounded w-24"></div>
                </div>
                <div class="space-y-4">
                    @for (int j = 0; j < 5; j++)
                    {
                        <div class="flex items-center space-x-4 p-3">
                            <div class="w-8 h-8 bg-gray-300 rounded-full"></div>
                            <div class="flex-1">
                                <div class="h-4 bg-gray-300 rounded w-3/4 mb-2"></div>
                                <div class="h-3 bg-gray-200 rounded w-1/2"></div>
                            </div>
                            <div class="h-4 bg-gray-200 rounded w-16"></div>
                        </div>
                    }
                </div>
            </div>
        }
    </div>
</div>

<style>
@@keyframes shimmer {
    0% {
        background-position: -468px 0;
    }
    100% {
        background-position: 468px 0;
    }
}

.animate-shimmer {
    animation: shimmer 2s infinite linear;
    background: linear-gradient(to right, #eff6ff 4%, #dbeafe 25%, #eff6ff 36%);
    background-size: 1000px 100%;
}

.animate-pulse {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: .5;
    }
}

/* Skeleton specific animations */
.skeleton-wave {
    position: relative;
    overflow: hidden;
}

.skeleton-wave::after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    transform: translateX(-100%);
    background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.6),
        transparent
    );
    animation: shimmer 1.5s infinite;
    content: '';
}
</style>

<script>
function showLoadingSkeleton() {
    const skeleton = document.getElementById('dashboardSkeleton');
    const content = document.getElementById('dashboardContent');
    
    if (skeleton && content) {
        skeleton.classList.remove('hidden');
        content.classList.add('hidden');
    }
}

function hideLoadingSkeleton() {
    const skeleton = document.getElementById('dashboardSkeleton');
    const content = document.getElementById('dashboardContent');
    
    if (skeleton && content) {
        skeleton.classList.add('hidden');
        content.classList.remove('hidden');
    }
}

// Export functions
window.showLoadingSkeleton = showLoadingSkeleton;
window.hideLoadingSkeleton = hideLoadingSkeleton;
</script>
