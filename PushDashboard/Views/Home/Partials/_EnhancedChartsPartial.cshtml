@model PushDashboard.ViewModels.DashboardChartsViewModel

<!-- Enhanced Charts Section -->
<div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
    <!-- Usage Trend Line Chart -->
    <div class="lg:col-span-2 bg-white p-6 rounded-xl shadow-sm border border-gray-200 hover:shadow-md transition-shadow duration-300">
        <div class="flex items-center justify-between mb-6">
            <div>
                <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                    <div class="w-3 h-3 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full mr-3"></div>
                    Kullanım Trendi
                </h3>
                <p class="text-sm text-gray-600 ml-6">Son 7 günlük modül kullanım analizi</p>
            </div>
            <div class="flex items-center space-x-2">
                <button class="px-3 py-1 text-xs bg-gray-100 hover:bg-gray-200 rounded-full transition-colors duration-200" onclick="toggleChartPeriod('usage', '7d')">7G</button>
                <button class="px-3 py-1 text-xs bg-blue-100 text-blue-600 rounded-full" onclick="toggleChartPeriod('usage', '30d')">30G</button>
                <button class="p-2 text-gray-400 hover:text-gray-600 transition-colors duration-200" onclick="refreshChart('usage')">
                    <svg class="w-4 h-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                    </svg>
                </button>
            </div>
        </div>
        <div class="relative h-64">
            <canvas id="usageLineChart" class="w-full h-full"></canvas>
        </div>
        <div class="mt-4 flex items-center justify-between text-sm text-gray-600">
            <div class="flex items-center space-x-4">
                <div class="flex items-center">
                    <div class="w-3 h-3 bg-blue-500 rounded-full mr-2"></div>
                    <span>Kullanım</span>
                </div>
                <div class="flex items-center">
                    <div class="w-3 h-3 bg-purple-500 rounded-full mr-2"></div>
                    <span>Trend</span>
                </div>
            </div>
            <div class="text-xs text-gray-500">
                Toplam: <span class="font-medium text-gray-700">@Model.UsageTrend.Sum(u => u.Value)</span> kullanım
            </div>
        </div>
    </div>

    <!-- Performance Donut Chart -->
    <div class="bg-white p-6 rounded-xl shadow-sm border border-gray-200 hover:shadow-md transition-shadow duration-300">
        <div class="flex items-center justify-between mb-6">
            <div>
                <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                    <div class="w-3 h-3 bg-gradient-to-r from-green-500 to-emerald-600 rounded-full mr-3"></div>
                    Performans
                </h3>
                <p class="text-sm text-gray-600 ml-6">Genel sistem durumu</p>
            </div>
        </div>
        <div class="relative h-48 flex items-center justify-center">
            <canvas id="performanceDonutChart" class="w-full h-full"></canvas>
            <div class="absolute inset-0 flex items-center justify-center">
                <div class="text-center">
                    <div class="text-3xl font-bold text-gray-900" id="performanceScore">85</div>
                    <div class="text-sm text-gray-500">Performans</div>
                </div>
            </div>
        </div>
        <div class="mt-4 space-y-2">
            <div class="flex items-center justify-between text-sm">
                <div class="flex items-center">
                    <div class="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                    <span class="text-gray-600">Başarılı</span>
                </div>
                <span class="font-medium text-gray-900">85%</span>
            </div>
            <div class="flex items-center justify-between text-sm">
                <div class="flex items-center">
                    <div class="w-2 h-2 bg-yellow-500 rounded-full mr-2"></div>
                    <span class="text-gray-600">Beklemede</span>
                </div>
                <span class="font-medium text-gray-900">10%</span>
            </div>
            <div class="flex items-center justify-between text-sm">
                <div class="flex items-center">
                    <div class="w-2 h-2 bg-red-500 rounded-full mr-2"></div>
                    <span class="text-gray-600">Hatalı</span>
                </div>
                <span class="font-medium text-gray-900">5%</span>
            </div>
        </div>
    </div>
</div>

<!-- Cost Analysis and Channel Distribution -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
    <!-- Cost Area Chart -->
    <div class="bg-white p-6 rounded-xl shadow-sm border border-gray-200 hover:shadow-md transition-shadow duration-300">
        <div class="flex items-center justify-between mb-6">
            <div>
                <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                    <div class="w-3 h-3 bg-gradient-to-r from-emerald-500 to-teal-600 rounded-full mr-3"></div>
                    Maliyet Analizi
                </h3>
                <p class="text-sm text-gray-600 ml-6">Haftalık harcama trendi</p>
            </div>
            <div class="text-right">
                <div class="text-sm text-gray-500">Bu hafta</div>
                <div class="text-lg font-bold text-emerald-600">₺@Model.CostTrend.Sum(c => c.Value).ToString("N2")</div>
            </div>
        </div>
        <div class="relative h-48">
            <canvas id="costAreaChart" class="w-full h-full"></canvas>
        </div>
        <div class="mt-4 grid grid-cols-3 gap-4 text-center">
            <div class="p-3 bg-gray-50 rounded-lg">
                <div class="text-sm text-gray-500">Ortalama</div>
                <div class="text-lg font-semibold text-gray-900">₺@(Model.CostTrend.Any() ? (Model.CostTrend.Sum(c => c.Value) / Model.CostTrend.Count).ToString("N2") : "0.00")</div>
            </div>
            <div class="p-3 bg-emerald-50 rounded-lg">
                <div class="text-sm text-emerald-600">En Yüksek</div>
                <div class="text-lg font-semibold text-emerald-700">₺@(Model.CostTrend.Any() ? Model.CostTrend.Max(c => c.Value).ToString("N2") : "0.00")</div>
            </div>
            <div class="p-3 bg-blue-50 rounded-lg">
                <div class="text-sm text-blue-600">En Düşük</div>
                <div class="text-lg font-semibold text-blue-700">₺@(Model.CostTrend.Any() ? Model.CostTrend.Min(c => c.Value).ToString("N2") : "0.00")</div>
            </div>
        </div>
    </div>

    <!-- Channel Distribution Polar Chart -->
    <div class="bg-white p-6 rounded-xl shadow-sm border border-gray-200 hover:shadow-md transition-shadow duration-300">
        <div class="flex items-center justify-between mb-6">
            <div>
                <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                    <div class="w-3 h-3 bg-gradient-to-r from-purple-500 to-pink-600 rounded-full mr-3"></div>
                    Kanal Dağılımı
                </h3>
                <p class="text-sm text-gray-600 ml-6">Bildirim kanalları</p>
            </div>
        </div>
        <div class="relative h-48">
            <canvas id="channelPolarChart" class="w-full h-full"></canvas>
        </div>
        <div class="mt-4 grid grid-cols-2 gap-3">
            <div class="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
                <div class="flex items-center">
                    <div class="w-3 h-3 bg-blue-500 rounded-full mr-2"></div>
                    <span class="text-sm text-blue-700">E-posta</span>
                </div>
                <span class="text-sm font-semibold text-blue-800">45%</span>
            </div>
            <div class="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                <div class="flex items-center">
                    <div class="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
                    <span class="text-sm text-green-700">WhatsApp</span>
                </div>
                <span class="text-sm font-semibold text-green-800">35%</span>
            </div>
            <div class="flex items-center justify-between p-3 bg-purple-50 rounded-lg">
                <div class="flex items-center">
                    <div class="w-3 h-3 bg-purple-500 rounded-full mr-2"></div>
                    <span class="text-sm text-purple-700">SMS</span>
                </div>
                <span class="text-sm font-semibold text-purple-800">20%</span>
            </div>
            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div class="flex items-center">
                    <div class="w-3 h-3 bg-gray-500 rounded-full mr-2"></div>
                    <span class="text-sm text-gray-700">Diğer</span>
                </div>
                <span class="text-sm font-semibold text-gray-800">5%</span>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    initializeEnhancedCharts();
});

function initializeEnhancedCharts() {
    // Usage Line Chart
    const usageCtx = document.getElementById('usageLineChart').getContext('2d');
    const usageData = @Html.Raw(Json.Serialize(Model.UsageTrend.Select(u => new { label = u.Label, value = u.Value })));
    
    new Chart(usageCtx, {
        type: 'line',
        data: {
            labels: usageData.map(d => d.label),
            datasets: [{
                label: 'Kullanım',
                data: usageData.map(d => d.value),
                borderColor: 'rgb(59, 130, 246)',
                backgroundColor: 'rgba(59, 130, 246, 0.1)',
                borderWidth: 3,
                fill: true,
                tension: 0.4,
                pointBackgroundColor: 'rgb(59, 130, 246)',
                pointBorderColor: '#fff',
                pointBorderWidth: 2,
                pointRadius: 6,
                pointHoverRadius: 8
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: { display: false },
                tooltip: {
                    backgroundColor: 'rgba(0, 0, 0, 0.8)',
                    titleColor: '#fff',
                    bodyColor: '#fff',
                    borderColor: 'rgb(59, 130, 246)',
                    borderWidth: 1,
                    cornerRadius: 8,
                    displayColors: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    grid: { color: 'rgba(0, 0, 0, 0.05)' },
                    ticks: { color: '#6b7280' }
                },
                x: {
                    grid: { display: false },
                    ticks: { color: '#6b7280' }
                }
            },
            interaction: {
                intersect: false,
                mode: 'index'
            },
            animation: {
                duration: 2000,
                easing: 'easeInOutQuart'
            }
        }
    });

    // Performance Donut Chart
    const performanceCtx = document.getElementById('performanceDonutChart').getContext('2d');
    new Chart(performanceCtx, {
        type: 'doughnut',
        data: {
            datasets: [{
                data: [85, 10, 5],
                backgroundColor: [
                    'rgb(34, 197, 94)',
                    'rgb(251, 191, 36)',
                    'rgb(239, 68, 68)'
                ],
                borderWidth: 0,
                cutout: '75%'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: { display: false },
                tooltip: { enabled: false }
            },
            animation: {
                animateRotate: true,
                duration: 2000
            }
        }
    });

    // Cost Area Chart
    const costCtx = document.getElementById('costAreaChart').getContext('2d');
    const costData = @Html.Raw(Json.Serialize(Model.CostTrend.Select(c => new { label = c.Label, value = c.Value })));
    
    new Chart(costCtx, {
        type: 'line',
        data: {
            labels: costData.map(d => d.label),
            datasets: [{
                label: 'Maliyet',
                data: costData.map(d => d.value),
                borderColor: 'rgb(16, 185, 129)',
                backgroundColor: 'rgba(16, 185, 129, 0.2)',
                borderWidth: 3,
                fill: true,
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: { display: false }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    grid: { color: 'rgba(0, 0, 0, 0.05)' },
                    ticks: { 
                        color: '#6b7280',
                        callback: function(value) {
                            return '₺' + value.toFixed(2);
                        }
                    }
                },
                x: {
                    grid: { display: false },
                    ticks: { color: '#6b7280' }
                }
            },
            animation: {
                duration: 2000,
                easing: 'easeInOutQuart'
            }
        }
    });

    // Channel Polar Chart
    const channelCtx = document.getElementById('channelPolarChart').getContext('2d');
    new Chart(channelCtx, {
        type: 'polarArea',
        data: {
            labels: ['E-posta', 'WhatsApp', 'SMS', 'Diğer'],
            datasets: [{
                data: [45, 35, 20, 5],
                backgroundColor: [
                    'rgba(59, 130, 246, 0.8)',
                    'rgba(34, 197, 94, 0.8)',
                    'rgba(168, 85, 247, 0.8)',
                    'rgba(107, 114, 128, 0.8)'
                ],
                borderWidth: 0
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: { display: false }
            },
            scales: {
                r: {
                    beginAtZero: true,
                    grid: { color: 'rgba(0, 0, 0, 0.1)' },
                    ticks: { display: false }
                }
            },
            animation: {
                duration: 2000,
                easing: 'easeInOutQuart'
            }
        }
    });
}

function toggleChartPeriod(chartType, period) {
    console.log('Toggling chart period:', chartType, period);
    // Implement period switching logic
}

function refreshChart(chartType) {
    console.log('Refreshing chart:', chartType);
    // Implement chart refresh logic
}
</script>
