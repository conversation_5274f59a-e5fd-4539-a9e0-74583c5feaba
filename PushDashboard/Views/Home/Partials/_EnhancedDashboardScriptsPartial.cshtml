<script>
document.addEventListener('DOMContentLoaded', function() {
    // Show loading skeleton initially
    if (typeof showLoadingSkeleton === 'function') {
        showLoadingSkeleton();
    }
    
    // Initialize dashboard with delay for smooth loading
    setTimeout(() => {
        initializeDashboard();
        
        if (typeof hideLoadingSkeleton === 'function') {
            hideLoadingSkeleton();
        }
        
        // Add entrance animations
        addEntranceAnimations();
        
        // Initialize enhanced stats
        if (typeof initializeEnhancedStats === 'function') {
            initializeEnhancedStats();
        }
    }, 1000);
    
    // Set up real-time updates
    setupRealTimeUpdates();
    
    // Set up refresh button
    setupRefreshButton();
    
    // Set up quick actions
    setupQuickActions();
    
    // Set up micro-interactions
    setupMicroInteractions();
    
    // Set up parallax effects
    setupParallaxEffects();
});

function initializeDashboard() {
    console.log('Enhanced Dashboard initialized');
    updateLastUpdateTime();
    
    // Add floating animations to key elements
    addFloatingAnimations();
    
    // Initialize tooltips
    initializeTooltips();
}

function addEntranceAnimations() {
    // Animate stats cards
    const statsCards = document.querySelectorAll('#statsContainer > div');
    statsCards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(30px) scale(0.95)';
        setTimeout(() => {
            card.style.transition = 'all 0.8s cubic-bezier(0.4, 0, 0.2, 1)';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0) scale(1)';
        }, index * 200);
    });

    // Animate other sections with stagger effect
    const sections = document.querySelectorAll('.bg-white, .bg-gradient-to-r, .bg-gradient-to-br');
    sections.forEach((section, index) => {
        if (!section.closest('#statsContainer')) {
            section.style.opacity = '0';
            section.style.transform = 'translateY(20px)';
            setTimeout(() => {
                section.style.transition = 'all 0.6s ease-out';
                section.style.opacity = '1';
                section.style.transform = 'translateY(0)';
            }, 800 + index * 150);
        }
    });
}

function setupMicroInteractions() {
    // Enhanced hover effects for cards
    const cards = document.querySelectorAll('.group');
    cards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transition = 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)';
            this.style.transform = 'translateY(-8px) scale(1.02)';
            this.style.filter = 'brightness(1.05)';
            
            // Add glow effect
            this.classList.add('glow');
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
            this.style.filter = 'brightness(1)';
            this.classList.remove('glow');
        });
    });

    // Add click ripple effect to buttons
    const buttons = document.querySelectorAll('button, .cursor-pointer');
    buttons.forEach(button => {
        button.addEventListener('click', function(e) {
            createRippleEffect(e, this);
            
            // Add success feedback
            this.style.transform = 'scale(0.95)';
            setTimeout(() => {
                this.style.transform = 'scale(1)';
            }, 150);
        });
    });

    // Add floating animation to icons
    const icons = document.querySelectorAll('svg');
    icons.forEach(icon => {
        icon.style.transition = 'transform 0.3s cubic-bezier(0.4, 0, 0.2, 1)';
        icon.addEventListener('mouseenter', function() {
            this.style.transform = 'scale(1.15) rotate(10deg)';
        });
        icon.addEventListener('mouseleave', function() {
            this.style.transform = 'scale(1) rotate(0deg)';
        });
    });

    // Add progress bar animations
    const progressBars = document.querySelectorAll('[data-width]');
    progressBars.forEach(bar => {
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const width = bar.getAttribute('data-width');
                    setTimeout(() => {
                        bar.style.width = width;
                    }, 500);
                    observer.unobserve(bar);
                }
            });
        });
        observer.observe(bar);
    });
}

function createRippleEffect(event, element) {
    const ripple = document.createElement('span');
    const rect = element.getBoundingClientRect();
    const size = Math.max(rect.width, rect.height);
    const x = event.clientX - rect.left - size / 2;
    const y = event.clientY - rect.top - size / 2;
    
    ripple.style.width = ripple.style.height = size + 'px';
    ripple.style.left = x + 'px';
    ripple.style.top = y + 'px';
    ripple.classList.add('ripple');
    
    element.style.position = 'relative';
    element.style.overflow = 'hidden';
    element.appendChild(ripple);
    
    setTimeout(() => {
        ripple.remove();
    }, 600);
}

function addFloatingAnimations() {
    // Add floating animation to specific elements
    const floatingElements = document.querySelectorAll('.animate-pulse, .animate-bounce');
    floatingElements.forEach((element, index) => {
        element.style.animationDelay = `${index * 0.5}s`;
        element.classList.add('floating');
    });
}

function initializeTooltips() {
    // Simple tooltip implementation
    const tooltipElements = document.querySelectorAll('[title]');
    tooltipElements.forEach(element => {
        element.addEventListener('mouseenter', function(e) {
            showTooltip(e, this.getAttribute('title'));
        });
        element.addEventListener('mouseleave', hideTooltip);
    });
}

function showTooltip(event, text) {
    const tooltip = document.createElement('div');
    tooltip.className = 'tooltip';
    tooltip.textContent = text;
    tooltip.style.cssText = `
        position: absolute;
        background: rgba(0, 0, 0, 0.9);
        color: white;
        padding: 8px 12px;
        border-radius: 6px;
        font-size: 12px;
        z-index: 1000;
        pointer-events: none;
        opacity: 0;
        transition: opacity 0.3s ease;
        left: ${event.pageX + 10}px;
        top: ${event.pageY - 30}px;
    `;
    
    document.body.appendChild(tooltip);
    setTimeout(() => tooltip.style.opacity = '1', 10);
}

function hideTooltip() {
    const tooltips = document.querySelectorAll('.tooltip');
    tooltips.forEach(tooltip => {
        tooltip.style.opacity = '0';
        setTimeout(() => tooltip.remove(), 300);
    });
}

function setupParallaxEffects() {
    let ticking = false;
    
    function updateParallax() {
        const scrolled = window.pageYOffset;
        const parallaxElements = document.querySelectorAll('.parallax');
        
        parallaxElements.forEach(element => {
            const speed = element.dataset.speed || 0.5;
            const yPos = -(scrolled * speed);
            element.style.transform = `translateY(${yPos}px)`;
        });
        
        ticking = false;
    }
    
    window.addEventListener('scroll', () => {
        if (!ticking) {
            requestAnimationFrame(updateParallax);
            ticking = true;
        }
    });
}

function setupRealTimeUpdates() {
    // Real-time updates disabled for better performance
    // Users can manually refresh using the refresh button
    console.log("Auto-refresh disabled for better performance. Use refresh button for manual updates.");
    
    // Optional: Add periodic indicator update without data refresh
    setInterval(function() {
        updateLastUpdateTime();
    }, 60000); // Update time display every minute
}}

function setupRefreshButton() {
    const refreshBtn = document.getElementById("refreshBtn");
    if (refreshBtn) {
        refreshBtn.addEventListener("click", function() {
            // Enhanced refresh animation and feedback
            const icon = this.querySelector("svg");
            const text = this.querySelector("span");
            const originalText = text.textContent;
            
            // Disable button during refresh
            this.disabled = true;
            this.classList.add("opacity-75", "cursor-not-allowed");
            
            // Animate icon rotation
            icon.style.transform = "rotate(360deg)";
            icon.style.transition = "transform 0.6s ease";
            text.textContent = "Yenileniyor...";
            
            // Refresh data
            refreshDashboardData().then(() => {
                // Success feedback
                text.textContent = "Tamamlandı!";
                this.classList.remove("bg-blue-50", "border-blue-200");
                this.classList.add("bg-green-50", "border-green-200", "text-green-600");
                
                setTimeout(() => {
                    // Reset button state
                    this.disabled = false;
                    this.classList.remove("opacity-75", "cursor-not-allowed", "bg-green-50", "border-green-200", "text-green-600");
                    this.classList.add("bg-blue-50", "border-blue-200");
                    text.textContent = originalText;
                    icon.style.transform = "rotate(0deg)";
                }, 1500);
            }).catch(() => {
                // Error feedback
                text.textContent = "Hata!";
                this.classList.remove("bg-blue-50", "border-blue-200");
                this.classList.add("bg-red-50", "border-red-200", "text-red-600");
                
                setTimeout(() => {
                    // Reset button state
                    this.disabled = false;
                    this.classList.remove("opacity-75", "cursor-not-allowed", "bg-red-50", "border-red-200", "text-red-600");
                    this.classList.add("bg-blue-50", "border-blue-200");
                    text.textContent = originalText;
                    icon.style.transform = "rotate(0deg)";
                }, 2000);
            });
        });
    }
}}

function setupQuickActions() {
    // Enhanced birthday notifications with better feedback
    window.sendBirthdayNotifications = async function() {
        const button = event.target;
        const originalText = button.innerHTML;
        
        try {
            // Show enhanced loading state
            button.disabled = true;
            button.classList.add('loading');
            button.innerHTML = `
                <div class="flex items-center justify-center">
                    <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    <span>Gönderiliyor...</span>
                </div>
            `;

            const response = await fetch('/Birthday/SendNotifications', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]')?.value || ''
                }
            });

            const result = await response.json();

            if (result.success) {
                showSuccessAnimation(button);
                showNotification('🎉 Doğum günü bildirimleri başarıyla gönderildi!', 'success');
                refreshDashboardData();
            } else {
                showErrorAnimation(button);
                showNotification('❌ Hata: ' + result.message, 'error');
            }
        } catch (error) {
            console.error('Error sending birthday notifications:', error);
            showErrorAnimation(button);
            showNotification('❌ Bildirimler gönderilirken bir hata oluştu.', 'error');
        } finally {
            // Restore button state with animation
            button.disabled = false;
            button.classList.remove('loading');
            button.innerHTML = originalText;
        }
    };
}

async function refreshDashboardData() {
    try {
        const refreshBtn = document.getElementById('refreshBtn');
        if (refreshBtn) {
            refreshBtn.classList.add('animate-spin');
        }

        const response = await fetch('/Home/GetRealTimeData');
        const result = await response.json();

        if (result.success) {
            updateDashboardElements(result.data);
            updateLastUpdateTime();
            
            // Show success feedback
            showNotification('📊 Dashboard güncellendi', 'success');
        } else {
            console.error('Failed to refresh dashboard data:', result.message);
            showNotification('⚠️ Veri güncellenirken hata oluştu', 'warning');
        }
    } catch (error) {
        console.error('Error refreshing dashboard data:', error);
        showNotification('❌ Bağlantı hatası', 'error');
    } finally {
        const refreshBtn = document.getElementById('refreshBtn');
        if (refreshBtn) {
            refreshBtn.classList.remove('animate-spin');
        }
    }
}

function updateDashboardElements(data) {
    // Enhanced update with animations
    const updates = [
        { id: 'creditBalance', value: data.creditBalance },
        { id: 'activeModules', value: data.activeModules },
        { id: 'notificationsToday', value: data.notificationsToday }
    ];

    updates.forEach(update => {
        const element = document.getElementById(update.id);
        if (element && update.value !== undefined) {
            // Add update animation
            element.style.transform = 'scale(1.1)';
            element.style.transition = 'transform 0.3s ease';
            
            setTimeout(() => {
                element.textContent = update.value;
                element.style.transform = 'scale(1)';
            }, 150);
        }
    });

    // Update real-time indicator with enhanced animation
    const realTimeIndicator = document.getElementById('realTimeIndicator');
    if (realTimeIndicator) {
        realTimeIndicator.innerHTML = `
            <div class="w-3 h-3 bg-green-400 rounded-full mr-2 animate-pulse"></div>
            <span class="font-medium text-green-600">Canlı</span>
        `;
    }
}

function updateLastUpdateTime() {
    const lastUpdate = document.getElementById('lastUpdate');
    if (lastUpdate) {
        const now = new Date();
        lastUpdate.textContent = now.toLocaleTimeString('tr-TR', { 
            hour: '2-digit', 
            minute: '2-digit' 
        });
        
        // Add update animation
        lastUpdate.style.transform = 'scale(1.1)';
        setTimeout(() => {
            lastUpdate.style.transform = 'scale(1)';
        }, 200);
    }
}

function showSuccessAnimation(element) {
    element.style.transform = 'scale(1.05)';
    element.style.background = 'linear-gradient(45deg, #10b981, #34d399)';
    element.style.boxShadow = '0 0 20px rgba(16, 185, 129, 0.5)';
    
    setTimeout(() => {
        element.style.transform = 'scale(1)';
        element.style.background = '';
        element.style.boxShadow = '';
    }, 500);
}

function showErrorAnimation(element) {
    element.style.animation = 'shake 0.5s ease-in-out';
    element.style.background = 'linear-gradient(45deg, #ef4444, #f87171)';
    
    setTimeout(() => {
        element.style.animation = '';
        element.style.background = '';
    }, 500);
}

// Enhanced notification system
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <div class="flex items-center">
            <span>${message}</span>
            <button onclick="this.parentElement.parentElement.remove()" class="ml-4 text-white hover:text-gray-200">
                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                </svg>
            </button>
        </div>
    `;
    
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 12px 16px;
        border-radius: 8px;
        color: white;
        font-size: 14px;
        z-index: 1000;
        transform: translateX(100%);
        transition: transform 0.3s ease;
        max-width: 300px;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
    `;
    
    // Set background color based on type
    const colors = {
        success: 'linear-gradient(45deg, #10b981, #34d399)',
        error: 'linear-gradient(45deg, #ef4444, #f87171)',
        warning: 'linear-gradient(45deg, #f59e0b, #fbbf24)',
        info: 'linear-gradient(45deg, #3b82f6, #60a5fa)'
    };
    notification.style.background = colors[type] || colors.info;
    
    document.body.appendChild(notification);
    
    // Animate in
    setTimeout(() => {
        notification.style.transform = 'translateX(0)';
    }, 10);
    
    // Auto remove after 5 seconds
    setTimeout(() => {
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => notification.remove(), 300);
    }, 5000);
}

// Add enhanced CSS
const enhancedStyle = document.createElement('style');
enhancedStyle.textContent = `
    .ripple {
        position: absolute;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.6);
        transform: scale(0);
        animation: ripple-animation 0.6s linear;
        pointer-events: none;
    }
    
    @@keyframes ripple-animation {
        to {
            transform: scale(4);
            opacity: 0;
        }
    }
    
    @@keyframes shake {
        0%, 100% { transform: translateX(0); }
        10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
        20%, 40%, 60%, 80% { transform: translateX(5px); }
    }
    
    .floating {
        animation: floating 3s ease-in-out infinite;
    }
    
    @@keyframes floating {
        0%, 100% { transform: translateY(0px); }
        50% { transform: translateY(-10px); }
    }
    
    .glow {
        box-shadow: 0 0 30px rgba(59, 130, 246, 0.3);
        transition: box-shadow 0.3s ease;
    }
    
    .pulse-slow {
        animation: pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;
    }
    
    .loading {
        position: relative;
        overflow: hidden;
    }
    
    .loading::after {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
        animation: loading-shimmer 1.5s infinite;
    }
    
    @@keyframes loading-shimmer {
        0% { left: -100%; }
        100% { left: 100%; }
    }
`;
document.head.appendChild(enhancedStyle);

// Export functions for global access
window.refreshDashboardData = refreshDashboardData;
window.toggleChartType = function(chartType) { console.log('Toggle chart type:', chartType); };
window.refreshMetrics = function() { console.log('Refresh metrics'); };
window.addEntranceAnimations = addEntranceAnimations;
window.setupMicroInteractions = setupMicroInteractions;
window.showSuccessAnimation = showSuccessAnimation;
window.showErrorAnimation = showErrorAnimation;
window.showNotification = showNotification;
</script>
