<!-- Quick Actions -->
<div class="bg-white rounded-lg shadow-sm p-6 mb-6">
    <div class="flex justify-between items-center mb-6">
        <div>
            <h3 class="text-lg font-semibold text-gray-900">H<PERSON><PERSON><PERSON><PERSON> İşlemler</h3>
            <p class="text-sm text-gray-600">Sık kullanılan işlemleri buradan gerçekleştirebilirsiniz</p>
        </div>
    </div>
    
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <!-- Do<PERSON><PERSON> Günü Bildirimleri -->
        <div class="border border-gray-200 rounded-lg p-4 hover:border-primary transition-colors duration-200">
            <div class="flex items-center mb-3">
                <div class="p-2 bg-pink-100 rounded-lg">
                    <svg class="w-5 h-5 text-pink-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v13m0-13V6a2 2 0 112 2h-2zm0 0V5.5A2.5 2.5 0 109.5 8H12zm-7 4h14M5 12a2 2 0 110-4h14a2 2 0 110 4M5 12v7a2 2 0 002 2h10a2 2 0 002-2v-7"></path>
                    </svg>
                </div>
                <div class="ml-3">
                    <h4 class="font-medium text-gray-900">Doğum Günü</h4>
                    <p class="text-xs text-gray-500">Bildirimleri</p>
                </div>
            </div>
            <p class="text-sm text-gray-600 mb-4">Bugün doğum günü olan müşterilere kutlama mesajı gönderin</p>
            <button id="sendBirthdayBtn" 
                    class="w-full px-4 py-2 bg-pink-600 text-white text-sm font-medium rounded-md hover:bg-pink-700 focus:outline-none focus:ring-2 focus:ring-pink-500 focus:ring-offset-2 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed">
                <span class="btn-text">Bildirimleri Gönder</span>
                <span class="btn-loading hidden">
                    <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white inline" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Gönderiliyor...
                </span>
            </button>
        </div>

        <!-- Sepet Hatırlatmaları -->
        <div class="border border-gray-200 rounded-lg p-4 hover:border-primary transition-colors duration-200">
            <div class="flex items-center mb-3">
                <div class="p-2 bg-blue-100 rounded-lg">
                    <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.5 5M7 13l2.5 5m6-5v6a2 2 0 01-2 2H9a2 2 0 01-2-2v-6m8 0V9a2 2 0 00-2-2H9a2 2 0 00-2 2v4.01"></path>
                    </svg>
                </div>
                <div class="ml-3">
                    <h4 class="font-medium text-gray-900">Sepet</h4>
                    <p class="text-xs text-gray-500">Hatırlatmaları</p>
                </div>
            </div>
            <p class="text-sm text-gray-600 mb-4">Terk edilmiş sepetler için hatırlatma mesajları gönderin</p>
            <button class="w-full px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors duration-200">
                Hatırlatmaları Gönder
            </button>
        </div>

        <!-- Email Şablonları -->
        <div class="border border-gray-200 rounded-lg p-4 hover:border-primary transition-colors duration-200">
            <div class="flex items-center mb-3">
                <div class="p-2 bg-green-100 rounded-lg">
                    <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                    </svg>
                </div>
                <div class="ml-3">
                    <h4 class="font-medium text-gray-900">Email</h4>
                    <p class="text-xs text-gray-500">Şablonları</p>
                </div>
            </div>
            <p class="text-sm text-gray-600 mb-4">Email şablonlarını düzenleyin ve yönetin</p>
            <a href="/EmailTemplate" class="w-full inline-block px-4 py-2 bg-green-600 text-white text-sm font-medium rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transition-colors duration-200 text-center">
                Şablonları Yönet
            </a>
        </div>

        <!-- Harcama Geçmişi -->
        <div class="border border-gray-200 rounded-lg p-4 hover:border-primary transition-colors duration-200">
            <div class="flex items-center mb-3">
                <div class="p-2 bg-purple-100 rounded-lg">
                    <svg class="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                </div>
                <div class="ml-3">
                    <h4 class="font-medium text-gray-900">Harcama</h4>
                    <p class="text-xs text-gray-500">Geçmişi</p>
                </div>
            </div>
            <p class="text-sm text-gray-600 mb-4">Modül kullanım maliyetlerinizi görüntüleyin</p>
            <a href="/UsageHistory" class="w-full inline-block px-4 py-2 bg-purple-600 text-white text-sm font-medium rounded-md hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 transition-colors duration-200 text-center">
                Geçmişi Görüntüle
            </a>
        </div>
    </div>
</div>

<!-- Toast Notification Container -->
<div id="toast-container" class="fixed top-4 right-4 z-50 space-y-2"></div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const sendBirthdayBtn = document.getElementById('sendBirthdayBtn');
    
    sendBirthdayBtn.addEventListener('click', async function() {
        // Button state'ini loading'e çevir
        setButtonLoading(true);
        
        try {
            const response = await fetch('/Notification/SendBirthdayNotifications', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]')?.value || ''
                }
            });
            
            const result = await response.json();
            
            if (result.success) {
                showToast('Başarılı!', result.message, 'success');
                
                // Başarı durumunda butonu geçici olarak devre dışı bırak
                setTimeout(() => {
                    setButtonLoading(false);
                }, 2000);
            } else {
                showToast('Hata!', result.message, 'error');
                setButtonLoading(false);
            }
        } catch (error) {
            console.error('Error:', error);
            showToast('Hata!', 'Bir hata oluştu. Lütfen tekrar deneyin.', 'error');
            setButtonLoading(false);
        }
    });
    
    function setButtonLoading(loading) {
        const btn = document.getElementById('sendBirthdayBtn');
        const btnText = btn.querySelector('.btn-text');
        const btnLoading = btn.querySelector('.btn-loading');
        
        if (loading) {
            btn.disabled = true;
            btnText.classList.add('hidden');
            btnLoading.classList.remove('hidden');
        } else {
            btn.disabled = false;
            btnText.classList.remove('hidden');
            btnLoading.classList.add('hidden');
        }
    }
    
    function showToast(title, message, type) {
        const toastContainer = document.getElementById('toast-container');
        const toastId = 'toast-' + Date.now();
        
        const bgColor = type === 'success' ? 'bg-green-500' : 'bg-red-500';
        const icon = type === 'success' 
            ? '<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path></svg>'
            : '<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path></svg>';
        
        const toast = document.createElement('div');
        toast.id = toastId;
        toast.className = `${bgColor} text-white px-6 py-4 rounded-lg shadow-lg transform transition-all duration-300 translate-x-full opacity-0 max-w-sm`;
        toast.innerHTML = `
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    ${icon}
                </div>
                <div class="ml-3">
                    <p class="text-sm font-medium">${title}</p>
                    <p class="text-sm opacity-90">${message}</p>
                </div>
                <div class="ml-4 flex-shrink-0">
                    <button onclick="removeToast('${toastId}')" class="text-white hover:text-gray-200">
                        <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                        </svg>
                    </button>
                </div>
            </div>
        `;
        
        toastContainer.appendChild(toast);
        
        // Animasyon için kısa bir gecikme
        setTimeout(() => {
            toast.classList.remove('translate-x-full', 'opacity-0');
        }, 100);
        
        // 5 saniye sonra otomatik kaldır
        setTimeout(() => {
            removeToast(toastId);
        }, 5000);
    }
    
    // Global function for removing toasts
    window.removeToast = function(toastId) {
        const toast = document.getElementById(toastId);
        if (toast) {
            toast.classList.add('translate-x-full', 'opacity-0');
            setTimeout(() => {
                toast.remove();
            }, 300);
        }
    };
});
</script>
