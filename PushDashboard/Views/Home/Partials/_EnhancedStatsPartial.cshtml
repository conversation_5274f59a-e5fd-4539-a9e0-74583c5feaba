@model PushDashboard.ViewModels.DashboardStatsViewModel

<!-- Enhanced Dashboard Stats Cards -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8" id="statsContainer">
    <!-- Credit Balance Card -->
    <div class="group bg-gradient-to-br from-green-50 via-emerald-50 to-teal-100 p-6 rounded-2xl shadow-sm border border-green-200 hover:shadow-xl transition-all duration-500 transform hover:-translate-y-2 hover:scale-105">
        <div class="flex items-center justify-between mb-4">
            <div class="p-4 rounded-2xl bg-gradient-to-br from-green-500 to-emerald-600 text-white shadow-lg group-hover:shadow-xl transition-shadow duration-300">
                <svg class="w-7 h-7" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                </svg>
            </div>
            <div class="text-right">
                <div class="w-12 h-12 rounded-full bg-green-200 flex items-center justify-center">
                    <div class="w-8 h-8 rounded-full bg-green-500 animate-pulse"></div>
                </div>
            </div>
        </div>
        <div>
            <h3 class="text-green-700 text-sm font-semibold mb-2">Mevcut Bakiye</h3>
            <p class="text-3xl font-bold text-green-900 mb-1">
                ₺<span id="creditBalance" class="counter" data-target="@Model.CreditBalance" data-duration="2000">0</span>
            </p>
            <p class="text-sm text-green-600">
                Bu ay: ₺<span class="counter" data-target="@Model.MonthlySpending" data-duration="2500">0</span> harcandı
            </p>
        </div>
        <div class="mt-4">
            <div class="w-full bg-green-200 rounded-full h-2 overflow-hidden">
                <div class="bg-gradient-to-r from-green-500 to-emerald-600 h-2 rounded-full transition-all duration-2000 ease-out animate-pulse" style="width: 0%" data-width="75%"></div>
            </div>
        </div>
    </div>

    <!-- Active Modules Card -->
    <div class="group bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-100 p-6 rounded-2xl shadow-sm border border-blue-200 hover:shadow-xl transition-all duration-500 transform hover:-translate-y-2 hover:scale-105">
        <div class="flex items-center justify-between mb-4">
            <div class="p-4 rounded-2xl bg-gradient-to-br from-blue-500 to-indigo-600 text-white shadow-lg group-hover:shadow-xl transition-shadow duration-300">
                <svg class="w-7 h-7" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                </svg>
            </div>
            <div class="flex space-x-1">
                <div class="w-2 h-2 bg-blue-400 rounded-full animate-bounce"></div>
                <div class="w-2 h-2 bg-blue-400 rounded-full animate-bounce" style="animation-delay: 0.1s"></div>
                <div class="w-2 h-2 bg-blue-400 rounded-full animate-bounce" style="animation-delay: 0.2s"></div>
            </div>
        </div>
        <div>
            <h3 class="text-blue-700 text-sm font-semibold mb-2">Aktif Modüller</h3>
            <p class="text-3xl font-bold text-blue-900 mb-1">
                <span id="activeModules" class="counter" data-target="@Model.ActiveModules" data-duration="1500">0</span>
            </p>
            <p class="text-sm text-blue-600">
                <span class="counter" data-target="@Model.ActiveIntegrations" data-duration="2000">0</span> entegrasyon aktif
            </p>
        </div>
        <div class="mt-4 flex items-center space-x-2">
            <div class="flex-1 bg-blue-200 rounded-full h-2">
                <div class="bg-gradient-to-r from-blue-500 to-indigo-600 h-2 rounded-full transition-all duration-2000 ease-out" style="width: 0%" data-width="85%"></div>
            </div>
            <span class="text-xs text-blue-600 font-medium">85%</span>
        </div>
    </div>

    <!-- Notifications Today Card -->
    <div class="group bg-gradient-to-br from-purple-50 via-violet-50 to-pink-100 p-6 rounded-2xl shadow-sm border border-purple-200 hover:shadow-xl transition-all duration-500 transform hover:-translate-y-2 hover:scale-105">
        <div class="flex items-center justify-between mb-4">
            <div class="p-4 rounded-2xl bg-gradient-to-br from-purple-500 to-violet-600 text-white shadow-lg group-hover:shadow-xl transition-shadow duration-300">
                <svg class="w-7 h-7" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM4 19h6v-2H4v2zM4 15h8v-2H4v2zM4 11h10V9H4v2z" />
                </svg>
            </div>
            <div class="relative">
                <div class="w-6 h-6 bg-purple-200 rounded-full animate-ping absolute"></div>
                <div class="w-6 h-6 bg-purple-500 rounded-full"></div>
            </div>
        </div>
        <div>
            <h3 class="text-purple-700 text-sm font-semibold mb-2">Bugün Gönderilen</h3>
            <p class="text-3xl font-bold text-purple-900 mb-1">
                <span id="notificationsToday" class="counter" data-target="@Model.NotificationsSentToday" data-duration="1800">0</span>
            </p>
            <p class="text-sm text-purple-600">
                Bu ay: <span class="counter" data-target="@Model.NotificationsSentThisMonth" data-duration="2200">0</span> bildirim
            </p>
        </div>
        <div class="mt-4">
            <div class="flex items-center justify-between text-xs text-purple-600">
                <span>Teslimat Oranı</span>
                <span class="font-semibold">@Model.FormattedDeliveryRate</span>
            </div>
            <div class="w-full bg-purple-200 rounded-full h-2 mt-1">
                <div class="bg-gradient-to-r from-purple-500 to-violet-600 h-2 rounded-full transition-all duration-2000 ease-out" style="width: 0%" data-width="@Model.AverageDeliveryRate%"></div>
            </div>
        </div>
    </div>

    <!-- Customers Card -->
    <div class="group bg-gradient-to-br from-orange-50 via-amber-50 to-yellow-100 p-6 rounded-2xl shadow-sm border border-orange-200 hover:shadow-xl transition-all duration-500 transform hover:-translate-y-2 hover:scale-105">
        <div class="flex items-center justify-between mb-4">
            <div class="p-4 rounded-2xl bg-gradient-to-br from-orange-500 to-amber-600 text-white shadow-lg group-hover:shadow-xl transition-shadow duration-300">
                <svg class="w-7 h-7" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
            </div>
            <div class="flex items-center space-x-1">
                <div class="w-3 h-3 bg-orange-400 rounded-full animate-pulse"></div>
                <div class="w-2 h-2 bg-orange-300 rounded-full animate-pulse" style="animation-delay: 0.5s"></div>
                <div class="w-1 h-1 bg-orange-200 rounded-full animate-pulse" style="animation-delay: 1s"></div>
            </div>
        </div>
        <div>
            <h3 class="text-orange-700 text-sm font-semibold mb-2">Toplam Müşteri</h3>
            <p class="text-3xl font-bold text-orange-900 mb-1">
                <span class="counter" data-target="@Model.TotalCustomers" data-duration="2500">0</span>
            </p>
            <p class="text-sm text-orange-600">
                Aktif müşteri tabanı
            </p>
        </div>
        <div class="mt-4">
            <div class="grid grid-cols-3 gap-2">
                <div class="text-center">
                    <div class="w-full bg-orange-200 rounded-full h-1 mb-1">
                        <div class="bg-orange-500 h-1 rounded-full transition-all duration-1500" style="width: 0%" data-width="70%"></div>
                    </div>
                    <span class="text-xs text-orange-600">Aktif</span>
                </div>
                <div class="text-center">
                    <div class="w-full bg-orange-200 rounded-full h-1 mb-1">
                        <div class="bg-orange-400 h-1 rounded-full transition-all duration-1800" style="width: 0%" data-width="25%"></div>
                    </div>
                    <span class="text-xs text-orange-600">Pasif</span>
                </div>
                <div class="text-center">
                    <div class="w-full bg-orange-200 rounded-full h-1 mb-1">
                        <div class="bg-orange-300 h-1 rounded-full transition-all duration-2100" style="width: 0%" data-width="5%"></div>
                    </div>
                    <span class="text-xs text-orange-600">Yeni</span>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Enhanced Performance Summary Bar -->
<div class="bg-gradient-to-r from-gray-50 to-gray-100 rounded-2xl shadow-sm border border-gray-200 p-6 mb-8 hover:shadow-md transition-shadow duration-300">
    <div class="flex items-center justify-between">
        <div class="flex items-center space-x-8">
            <div class="flex items-center">
                <div class="relative mr-3">
                    <div class="w-4 h-4 bg-green-500 rounded-full animate-pulse"></div>
                    <div class="absolute top-0 left-0 w-4 h-4 bg-green-400 rounded-full animate-ping"></div>
                </div>
                <span class="text-sm text-gray-600">Sistem Durumu: <span class="font-semibold text-green-600">Çevrimiçi</span></span>
            </div>
            <div class="flex items-center">
                <div class="w-4 h-4 bg-blue-500 rounded-full mr-3 animate-bounce"></div>
                <span class="text-sm text-gray-600">Ortalama Teslimat: <span class="font-semibold">@Model.FormattedDeliveryRate</span></span>
            </div>
            <div class="flex items-center">
                <div class="w-4 h-4 bg-purple-500 rounded-full mr-3"></div>
                <span class="text-sm text-gray-600">Son Aktivite: <span class="font-semibold">@Model.FormattedLastActivity</span></span>
            </div>
        </div>
        <div class="flex items-center space-x-4">
            <div class="text-sm text-gray-500">
        <span id="realTimeIndicator" class="inline-flex items-center">
            <div class="w-3 h-3 bg-blue-400 rounded-full mr-2"></div>
            <span class="font-medium text-blue-600">Manuel</span>
        </span>            </div>
            <div class="flex space-x-1">
                <div class="w-2 h-8 bg-green-500 rounded-full animate-pulse"></div>
                <div class="w-2 h-6 bg-blue-500 rounded-full animate-pulse" style="animation-delay: 0.2s"></div>
                <div class="w-2 h-4 bg-purple-500 rounded-full animate-pulse" style="animation-delay: 0.4s"></div>
                <div class="w-2 h-7 bg-orange-500 rounded-full animate-pulse" style="animation-delay: 0.6s"></div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    initializeEnhancedStats();
});

function initializeEnhancedStats() {
    // Initialize animated counters
    const counters = document.querySelectorAll('.counter');
    
    const observerOptions = {
        threshold: 0.5,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                animateCounter(entry.target);
                observer.unobserve(entry.target);
            }
        });
    }, observerOptions);

    counters.forEach(counter => {
        observer.observe(counter);
    });

    // Initialize progress bars
    setTimeout(() => {
        const progressBars = document.querySelectorAll('[data-width]');
        progressBars.forEach(bar => {
            const width = bar.getAttribute('data-width');
            bar.style.width = width;
        });
    }, 500);
}

function animateCounter(element) {
    const target = parseFloat(element.getAttribute('data-target'));
    const duration = parseInt(element.getAttribute('data-duration')) || 2000;
    
    if (typeof CountUp !== 'undefined') {
        const countUp = new CountUp(element, target, {
            duration: duration / 1000,
            useEasing: true,
            useGrouping: true,
            separator: '.',
            decimal: ',',
            decimalPlaces: target % 1 !== 0 ? 2 : 0
        });
        
        if (!countUp.error) {
            countUp.start();
        } else {
            element.textContent = target.toLocaleString('tr-TR');
        }
    } else {
        // Fallback animation
        let current = 0;
        const increment = target / (duration / 16);
        const timer = setInterval(() => {
            current += increment;
            if (current >= target) {
                current = target;
                clearInterval(timer);
            }
            element.textContent = Math.floor(current).toLocaleString('tr-TR');
        }, 16);
    }
}

// Export for global access
window.initializeEnhancedStats = initializeEnhancedStats;
</script>
