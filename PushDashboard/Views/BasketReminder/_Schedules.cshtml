@model List<BasketReminderSchedule>

<div class="bg-white shadow rounded-lg">
    <div class="px-4 py-5 sm:p-6">
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-lg leading-6 font-medium text-gray-900">Hatırlatma Zamanlamaları</h3>
            <button onclick="openScheduleModal()" 
                    class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700">
                <svg class="mr-2 -ml-1 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
                <PERSON><PERSON>
            </button>
        </div>

        @if (Model.Any())
        {
            <div class="overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
                <table class="min-w-full divide-y divide-gray-300">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Zamanlama Adı
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Hatırlatma Zamanı
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Durum
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Oluşturulma
                            </th>
                            <th scope="col" class="relative px-6 py-3">
                                <span class="sr-only">İşlemler</span>
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        @foreach (var schedule in Model)
                        {
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                    @schedule.Name
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    @schedule.ReminderTimeText
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full @schedule.StatusBadgeClass">
                                        @schedule.StatusText
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    @schedule.CreatedAt.ToString("dd.MM.yyyy HH:mm")
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                    <div class="flex justify-end space-x-2">
                                        <button onclick="editReminder(@schedule.Id, '@Html.Raw(Html.Encode(schedule.Name))', '@Html.Raw(Html.Encode(schedule.NotificationContent))', @schedule.ReminderTimeForHours, @schedule.IsActive.ToString().ToLower(), '@Html.Raw(schedule.CommunicationChannels ?? "[]")', '@Html.Raw(schedule.ChannelMessages ?? "{}")')"
                                                class="text-blue-600 hover:text-blue-900 text-sm">
                                            Düzenle
                                        </button>
                                        <button onclick="toggleScheduleStatus(@schedule.Id)" 
                                                class="text-yellow-600 hover:text-yellow-900 text-sm">
                                            @(schedule.IsActive ? "Pasif Et" : "Aktif Et")
                                        </button>
                                        <button onclick="deleteSchedule(@schedule.Id, '@schedule.Name')" 
                                                class="text-red-600 hover:text-red-900 text-sm">
                                            Sil
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        }
        else
        {
            <div class="text-center py-12">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <h3 class="mt-2 text-sm font-medium text-gray-900">Henüz zamanlama yok</h3>
                <p class="mt-1 text-sm text-gray-500">Sepet hatırlatmaları için zamanlama oluşturun.</p>
                <div class="mt-6">
                    <button onclick="openScheduleModal()" 
                            class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                        <svg class="mr-2 -ml-1 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                        </svg>
                        İlk Zamanlamayı Oluştur
                    </button>
                </div>
            </div>
        }

        <!-- Test Butonu -->
        @if (Model.Any(s => s.IsActive))
        {
            <div class="mt-6 pt-6 border-t border-gray-200">
                <div class="flex justify-between items-center">
                    <div>
                        <h4 class="text-sm font-medium text-gray-900">Test İşlemleri</h4>
                        <p class="text-sm text-gray-500">Mevcut ayarlarla test hatırlatması gönderebilirsiniz.</p>
                    </div>
                    <button onclick="processTestReminders()" 
                            class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                        <svg class="mr-2 -ml-1 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                        </svg>
                        Test Hatırlatması Gönder
                    </button>
                </div>
            </div>
        }
    </div>
</div>

<script>
// Zamanlama silme işlemi

window.deleteSchedule = function(id, name) {
    if (confirm(`"${name}" zamanlamasını silmek istediğinizden emin misiniz?`)) {
        const csrfToken = document.querySelector('input[name="__RequestVerificationToken"]');
        if (!csrfToken) {
            if (typeof showNotification === 'function') {
                showNotification('CSRF token bulunamadı. Sayfayı yenileyin.', 'error');
            }
            return;
        }

        fetch('@Url.Action("DeleteSchedule", "BasketReminder")', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'RequestVerificationToken': csrfToken.value
            },
            body: `scheduleId=${id}`
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                if (typeof showNotification === 'function') {
                    showNotification('Zamanlama başarıyla silindi.', 'success');
                }
                setTimeout(() => location.reload(), 1000);
            } else {
                if (typeof showNotification === 'function') {
                    showNotification('Hata: ' + data.message, 'error');
                }
            }
        })
        .catch(error => {
            console.error('Delete schedule error:', error);
            if (typeof showNotification === 'function') {
                showNotification('Bir hata oluştu: ' + error.message, 'error');
            }
        });
    }
};

window.toggleScheduleStatus = function(id) {
    const csrfToken = document.querySelector('input[name="__RequestVerificationToken"]');
    if (!csrfToken) {
        if (typeof showNotification === 'function') {
            showNotification('CSRF token bulunamadı. Sayfayı yenileyin.', 'error');
        }
        return;
    }

    fetch('@Url.Action("ToggleScheduleStatus", "BasketReminder")', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'RequestVerificationToken': csrfToken.value
        },
        body: `scheduleId=${id}`
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            if (typeof showNotification === 'function') {
                showNotification(data.message, 'success');
            }
            setTimeout(() => location.reload(), 1000);
        } else {
            if (typeof showNotification === 'function') {
                showNotification('Hata: ' + data.message, 'error');
            }
        }
    })
    .catch(error => {
        console.error('Toggle schedule status error:', error);
        if (typeof showNotification === 'function') {
            showNotification('Bir hata oluştu: ' + error.message, 'error');
        }
    });
};

window.processTestReminders = function() {
    if (confirm('Test hatırlatması göndermek istediğinizden emin misiniz?')) {
        const button = event.target;
        button.disabled = true;
        button.innerHTML = '<svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-gray-700" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg>İşleniyor...';
        
        fetch('@Url.Action("ProcessTestReminders", "BasketReminder")', {
            method: 'POST',
            headers: {
                'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]').value
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification(`Test tamamlandı. ${data.processedCount} hatırlatma işlendi.`, 'success');
            } else {
                showNotification('Hata: ' + data.message, 'error');
            }
        })
        .catch(error => {
            showNotification('Bir hata oluştu: ' + error.message, 'error');
        })
        .finally(() => {
            button.disabled = false;
            button.innerHTML = '<svg class="mr-2 -ml-1 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" /></svg>Test Hatırlatması Gönder';
        });
    }
};

// Modal ve form fonksiyonları artık Index.cshtml'de tanımlı
</script>
