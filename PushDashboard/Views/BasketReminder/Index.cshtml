@model List<PushDashboard.Models.BasketReminderSchedule>
@{
    ViewData["Title"] = "Sepet Hatırlatma";
}

<div class="min-h-screen bg-gray-50 py-8">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="mb-8">
            <div class="flex justify-between items-center">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">Sepet Hatırlatma</h1>
                    <p class="mt-2 text-gray-600">Terk edilmiş sepetler için kişiselleştirilmiş hatırlatma mesajları oluşturun ve yönetin.</p>
                    <p class="mt-1 text-sm text-blue-600">💡 Mesajlarınızda {FirstName}, {LastName} ve {FullName} değişkenlerini kullanabilirsiniz.</p>
                </div>
                <button onclick="openCreateModal()" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors">
                    <svg class="w-5 h-5 inline-block mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    Yeni Hatırlatma
                </button>
            </div>
        </div>

        <!-- Reminders List -->
        <div class="bg-white shadow-sm rounded-lg overflow-hidden">
            @if (Model.Any())
            {
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Hatırlatma Adı</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Mesaj</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Zaman</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">İletişim Kanalları</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Durum</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">İşlemler</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            @foreach (var reminder in Model)
                            {
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900">@reminder.Name</div>
                                    </td>
                                    <td class="px-6 py-4">
                                        <div class="text-sm text-gray-900 max-w-xs">
                                            <div class="truncate" title="@reminder.NotificationContent">@reminder.NotificationContent</div>
                                            @if (reminder.NotificationContent.Contains("{FirstName}") || reminder.NotificationContent.Contains("{LastName}") || reminder.NotificationContent.Contains("{FullName}") || reminder.NotificationContent.Contains("{ISIM}") || reminder.NotificationContent.Contains("{SOYISIM}") || reminder.NotificationContent.Contains("{ADSOYAD}"))
                                            {
                                                <div class="text-xs text-blue-600 mt-1">
                                                    <svg class="w-3 h-3 inline-block mr-1" fill="currentColor" viewBox="0 0 20 20">
                                                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                                                    </svg>
                                                    Kişiselleştirilmiş
                                                </div>
                                            }
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-900">@reminder.ReminderTimeForHours saat sonra</div>
                                    </td>
                                    <td class="px-6 py-4">
                                        <div class="flex flex-wrap gap-1">
                                            @{
                                                var channels = new List<string>();
                                                try
                                                {
                                                    channels = System.Text.Json.JsonSerializer.Deserialize<List<string>>(reminder.CommunicationChannels ?? "[]") ?? new List<string>();
                                                }
                                                catch
                                                {
                                                    channels = new List<string>();
                                                }
                                            }
                                            @if (channels.Any())
                                            {
                                                @foreach (var channel in channels)
                                                {
                                                    @if (channel == "Email")
                                                    {
                                                        <span class="inline-flex items-center px-2 py-1 text-xs font-medium rounded-full bg-blue-100 text-blue-800">
                                                            <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                                                            </svg>
                                                            E-posta
                                                        </span>
                                                    }
                                                    else if (channel == "WhatsApp")
                                                    {
                                                        <span class="inline-flex items-center px-2 py-1 text-xs font-medium rounded-full bg-green-100 text-green-800">
                                                            <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 24 24">
                                                                <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.097"/>
                                                            </svg>
                                                            WhatsApp
                                                        </span>
                                                    }
                                                    else if (channel == "Telegram")
                                                    {
                                                        <span class="inline-flex items-center px-2 py-1 text-xs font-medium rounded-full bg-blue-100 text-blue-800">
                                                            <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 24 24">
                                                                <path d="M11.944 0A12 12 0 0 0 0 12a12 12 0 0 0 12 12 12 12 0 0 0 12-12A12 12 0 0 0 12 0a12 12 0 0 0-.056 0zm4.962 7.224c.1-.002.321.023.465.14a.506.506 0 0 1 .171.325c.016.093.036.306.02.472-.18 1.898-.962 6.502-1.36 8.627-.168.9-.499 1.201-.82 1.23-.696.065-1.225-.46-1.9-.902-1.056-.693-1.653-1.124-2.678-1.8-1.185-.78-.417-1.21.258-1.91.177-.184 3.247-2.977 3.307-3.23.007-.032.014-.15-.056-.212s-.174-.041-.249-.024c-.106.024-1.793 1.14-5.061 3.345-.48.33-.913.49-1.302.48-.428-.008-1.252-.241-1.865-.44-.752-.245-1.349-.374-1.297-.789.027-.216.325-.437.893-.663 3.498-1.524 5.83-2.529 6.998-3.014 3.332-1.386 4.025-1.627 4.476-1.635z"/>
                                                            </svg>
                                                            Telegram
                                                        </span>
                                                    }
                                                }
                                            }
                                            else
                                            {
                                                <span class="text-xs text-gray-500">Kanal seçilmemiş</span>
                                            }
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full @(reminder.IsActive ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800")">
                                            @(reminder.IsActive ? "Aktif" : "Pasif")
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                        <button onclick="editReminder(@reminder.Id, '@Html.Raw(Html.Encode(reminder.Name))', '@Html.Raw(Html.Encode(reminder.NotificationContent))', @reminder.ReminderTimeForHours, @reminder.IsActive.ToString().ToLower(), '@Html.Raw(Html.Encode(reminder.CommunicationChannels))', '@Html.Raw(Html.Encode(reminder.ChannelMessages))')"
                                                class="text-blue-600 hover:text-blue-900">Düzenle</button>
                                        <button onclick="toggleStatus(@reminder.Id)" 
                                                class="text-yellow-600 hover:text-yellow-900">@(reminder.IsActive ? "Pasif Yap" : "Aktif Yap")</button>
                                        <button onclick="deleteReminder(@reminder.Id, '@Html.Raw(Html.Encode(reminder.Name))')" 
                                                class="text-red-600 hover:text-red-900">Sil</button>
                                    </td>
                                </tr>
                            }
                        </tbody>
                    </table>
                </div>
            }
            else
            {
                <div class="text-center py-12">
                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2M4 13h2m13-8V4a1 1 0 00-1-1H7a1 1 0 00-1 1v1m8 0V4.5"></path>
                    </svg>
                    <h3 class="mt-2 text-sm font-medium text-gray-900">Henüz hatırlatma yok</h3>
                    <p class="mt-1 text-sm text-gray-500">İlk hatırlatmanızı oluşturmak için yukarıdaki butona tıklayın.</p>
                </div>
            }
        </div>
    </div>
</div>

<!-- Create/Edit Modal -->
<div id="reminderModal" class="hidden fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
    <div class="relative top-20 mx-auto p-5 border w-full max-w-2xl shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <h3 id="modalTitle" class="text-lg font-medium text-gray-900 mb-4">Yeni Hatırlatma</h3>
            <form id="reminderForm">
                <input type="hidden" id="reminderId" value="0">
                
                <div class="mb-4">
                    <label for="reminderName" class="block text-sm font-medium text-gray-700 mb-2">Hatırlatma Adı</label>
                    <input type="text" id="reminderName" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                </div>

                <div class="mb-4">
                    <label for="reminderMessage" class="block text-sm font-medium text-gray-700 mb-2">Mesaj İçeriği</label>
                    <textarea id="reminderMessage" rows="4" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required maxlength="300" placeholder="Merhaba {FirstName}, sepetinizde ürünler bekliyor..."></textarea>

                    <!-- Karakter sayacı -->
                    <div class="flex justify-between items-center mt-1">
                        <span id="charCount" class="text-xs text-gray-500">0/300 karakter</span>
                        <span class="text-xs text-gray-400">Maksimum 300 karakter</span>
                    </div>

                    <!-- Kullanılabilir değişkenler -->
                    <div class="mt-3 p-3 bg-blue-50 rounded-md">
                        <h4 class="text-sm font-medium text-blue-900 mb-2">Kullanılabilir Değişkenler:</h4>
                        <div class="grid grid-cols-2 gap-2 mb-3">
                            <button type="button" onclick="insertPlaceholder('{FirstName}')" class="inline-flex items-center justify-center px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded hover:bg-blue-200 transition-colors">
                                {FirstName} - İsim
                            </button>
                            <button type="button" onclick="insertPlaceholder('{LastName}')" class="inline-flex items-center justify-center px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded hover:bg-blue-200 transition-colors">
                                {LastName} - Soyisim
                            </button>
                            <button type="button" onclick="insertPlaceholder('{FullName}')" class="inline-flex items-center justify-center px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded hover:bg-blue-200 transition-colors">
                                {FullName} - Ad Soyad
                            </button>
                        </div>
                        <div class="text-xs text-blue-700">
                            <p class="mb-1"><strong>Örnek:</strong> "Merhaba {FirstName}, sepetinizde {LastName} ailesine özel ürünler bekliyor!"</p>
                            <p><strong>Türkçe:</strong> "Sayın {FirstName}, sepetinizi tamamlamayı unutmayın!"</p>
                        </div>
                    </div>
                </div>

                <!-- İletişim Kanalları -->
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-3">İletişim Kanalları</label>
                    <div id="communicationChannels" class="space-y-2">
                        <!-- Kanallar dinamik olarak yüklenecek -->
                    </div>
                    <p class="text-xs text-gray-500 mt-2">En az bir kanal seçmelisiniz.</p>
                </div>

                <!-- WhatsApp Template Seçimi -->
                <div id="whatsappTemplateSection" class="mb-4 hidden">
                    <label for="whatsappTemplate" class="block text-sm font-medium text-gray-700 mb-2">WhatsApp Template</label>
                    <select id="whatsappTemplate" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">Template seçiniz...</option>
                    </select>
                    <div class="mt-2 p-3 bg-blue-50 border border-blue-200 rounded-md">
                        <div class="flex">
                            <svg class="w-4 h-4 text-blue-600 mt-0.5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                            </svg>
                            <div class="text-sm text-blue-800">
                                <p class="font-medium">WhatsApp Template Bilgisi</p>
                                <p class="mt-1">WhatsApp için yukarıda girdiğiniz özel mesaj içeriği kullanılmayacaktır. Bunun yerine seçtiğiniz onaylanmış template kullanılacaktır.</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="mb-4">
                    <label for="reminderHours" class="block text-sm font-medium text-gray-700 mb-2">Kaç Saat Sonra</label>
                    <select id="reminderHours" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                        <option value="">Seçiniz</option>
                        <option value="1">1 saat sonra</option>
                        <option value="2">2 saat sonra</option>
                        <option value="6">6 saat sonra</option>
                        <option value="12">12 saat sonra</option>
                        <option value="24">24 saat sonra</option>
                        <option value="48">48 saat sonra</option>
                        <option value="72">72 saat sonra</option>
                    </select>
                </div>

                <div class="mb-6">
                    <label class="flex items-center">
                        <input type="checkbox" id="reminderActive" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500" checked>
                        <span class="ml-2 text-sm text-gray-700">Aktif</span>
                    </label>
                </div>

                <div class="flex justify-end space-x-3">
                    <button type="button" onclick="closeModal()" class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 transition-colors">İptal</button>
                    <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">Kaydet</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Toast Notification Container -->
<div id="toastContainer" class="fixed top-4 right-4 z-50 space-y-2"></div>

@section Scripts {
    <script>
        console.log('Basket Reminder page loaded');

        let isEditMode = false;

        // Toast Notification System
        function showToast(message, type = 'info', duration = 4000) {
            const toastContainer = document.getElementById('toastContainer');
            const toastId = 'toast-' + Date.now();

            // Toast renk sınıfları
            const typeClasses = {
                success: 'bg-green-500 border-green-600',
                error: 'bg-red-500 border-red-600',
                warning: 'bg-yellow-500 border-yellow-600',
                info: 'bg-blue-500 border-blue-600'
            };

            // Toast ikonları
            const typeIcons = {
                success: `<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                </svg>`,
                error: `<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
                </svg>`,
                warning: `<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                </svg>`,
                info: `<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                </svg>`
            };

            const toast = document.createElement('div');
            toast.id = toastId;
            toast.className = `flex items-center p-4 mb-2 text-white rounded-lg shadow-lg border-l-4 transform transition-all duration-300 ease-in-out translate-x-full opacity-0 ${typeClasses[type] || typeClasses.info}`;

            toast.innerHTML = `
                <div class="flex-shrink-0 mr-3">
                    ${typeIcons[type] || typeIcons.info}
                </div>
                <div class="flex-1 text-sm font-medium">
                    ${message}
                </div>
                <button onclick="removeToast('${toastId}')" class="ml-3 text-white hover:text-gray-200 focus:outline-none">
                    <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                    </svg>
                </button>
            `;

            toastContainer.appendChild(toast);

            // Animasyon için kısa gecikme
            setTimeout(() => {
                toast.classList.remove('translate-x-full', 'opacity-0');
                toast.classList.add('translate-x-0', 'opacity-100');
            }, 100);

            // Otomatik kaldırma
            if (duration > 0) {
                setTimeout(() => {
                    removeToast(toastId);
                }, duration);
            }
        }

        function removeToast(toastId) {
            const toast = document.getElementById(toastId);
            if (toast) {
                toast.classList.add('translate-x-full', 'opacity-0');
                setTimeout(() => {
                    if (toast.parentNode) {
                        toast.parentNode.removeChild(toast);
                    }
                }, 300);
            }
        }

        // Confirm dialog için modern modal
        function showConfirmDialog(message, onConfirm, onCancel = null) {
            const confirmModal = document.createElement('div');
            confirmModal.className = 'fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50 flex items-center justify-center';

            confirmModal.innerHTML = `
                <div class="relative bg-white rounded-lg shadow-xl max-w-md w-full mx-4 transform transition-all">
                    <div class="p-6">
                        <div class="flex items-center mb-4">
                            <div class="flex-shrink-0 w-10 h-10 rounded-full bg-red-100 flex items-center justify-center">
                                <svg class="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                                </svg>
                            </div>
                            <div class="ml-4">
                                <h3 class="text-lg font-medium text-gray-900">Onay Gerekli</h3>
                            </div>
                        </div>
                        <div class="mb-6">
                            <p class="text-sm text-gray-600">${message}</p>
                        </div>
                        <div class="flex justify-end space-x-3">
                            <button id="cancelBtn" class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 transition-colors focus:outline-none focus:ring-2 focus:ring-gray-500">
                                İptal
                            </button>
                            <button id="confirmBtn" class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors focus:outline-none focus:ring-2 focus:ring-red-500">
                                Onayla
                            </button>
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(confirmModal);

            // Event listeners
            const confirmBtn = confirmModal.querySelector('#confirmBtn');
            const cancelBtn = confirmModal.querySelector('#cancelBtn');

            confirmBtn.addEventListener('click', () => {
                document.body.removeChild(confirmModal);
                if (onConfirm) onConfirm();
            });

            cancelBtn.addEventListener('click', () => {
                document.body.removeChild(confirmModal);
                if (onCancel) onCancel();
            });

            // Close on outside click
            confirmModal.addEventListener('click', (e) => {
                if (e.target === confirmModal) {
                    document.body.removeChild(confirmModal);
                    if (onCancel) onCancel();
                }
            });
        }

        // Karakter sayacını güncelle
        function updateCharCount() {
            const textarea = document.getElementById('reminderMessage');
            const charCount = document.getElementById('charCount');
            const currentLength = textarea.value.length;
            charCount.textContent = `${currentLength}/300 karakter`;

            // Karakter limiti aşıldığında renk değiştir
            if (currentLength > 280) {
                charCount.classList.add('text-red-500');
                charCount.classList.remove('text-gray-500');
            } else {
                charCount.classList.add('text-gray-500');
                charCount.classList.remove('text-red-500');
            }
        }

        // Placeholder ekle
        function insertPlaceholder(placeholder) {
            const textarea = document.getElementById('reminderMessage');
            const start = textarea.selectionStart;
            const end = textarea.selectionEnd;
            const text = textarea.value;

            // Seçili metni placeholder ile değiştir veya cursor pozisyonuna ekle
            const newText = text.substring(0, start) + placeholder + text.substring(end);
            textarea.value = newText;

            // Cursor'u placeholder'ın sonuna taşı
            const newCursorPos = start + placeholder.length;
            textarea.setSelectionRange(newCursorPos, newCursorPos);
            textarea.focus();

            // Karakter sayacını güncelle
            updateCharCount();
        }

        // Communication Channels
        let availableChannels = [];
        let whatsappTemplates = [];

        async function loadCommunicationChannels() {
            try {
                const response = await fetch('/BasketReminder/GetAvailableChannels');
                const result = await response.json();

                if (result.success) {
                    availableChannels = result.channels;
                    renderChannelSelection();
                } else {
                    console.error('Error loading channels:', result.message);
                    showToast('İletişim kanalları yüklenirken hata oluştu.', 'error');
                }
            } catch (error) {
                console.error('Error loading channels:', error);
                showToast('İletişim kanalları yüklenirken hata oluştu.', 'error');
            }
        }

        async function loadWhatsAppTemplates() {
            try {
                const response = await fetch('/BasketReminder/GetWhatsAppTemplates');
                const result = await response.json();

                if (result.success) {
                    whatsappTemplates = result.templates;
                    renderWhatsAppTemplateOptions();
                } else {
                    console.error('Error loading WhatsApp templates:', result.message);
                    showToast('WhatsApp template\'leri yüklenirken hata oluştu.', 'error');
                }
            } catch (error) {
                console.error('Error loading WhatsApp templates:', error);
                showToast('WhatsApp template\'leri yüklenirken hata oluştu.', 'error');
            }
        }

        function renderWhatsAppTemplateOptions() {
            const select = document.getElementById('whatsappTemplate');
            select.innerHTML = '<option value="">Template seçiniz...</option>';

            whatsappTemplates.forEach(template => {
                const option = document.createElement('option');
                option.value = template.id;
                option.textContent = `${template.name} (${template.category})`;
                select.appendChild(option);
            });
        }

        function renderChannelSelection() {
            const container = document.getElementById('communicationChannels');
            container.innerHTML = '';

            availableChannels.forEach(channel => {
                const channelDiv = document.createElement('div');
                channelDiv.className = 'flex items-center p-3 border rounded-lg';

                if (!channel.isActive) {
                    channelDiv.classList.add('bg-gray-50', 'border-gray-200');
                } else {
                    channelDiv.classList.add('bg-white', 'border-gray-300', 'hover:border-blue-300');
                }

                const iconClass = getChannelIcon(channel.name);
                const colorClass = channel.isActive ? channel.colorClass : 'text-gray-400';

                channelDiv.innerHTML = `
                    <div class="flex items-center flex-1">
                        <input type="checkbox"
                               id="channel_${channel.name}"
                               value="${channel.name}"
                               class="channel-checkbox rounded border-gray-300 text-blue-600 focus:ring-blue-500 mr-3"
                               ${!channel.isActive ? 'disabled' : ''}>
                        <div class="flex items-center">
                            <i data-lucide="${iconClass}" class="w-5 h-5 ${colorClass} mr-2"></i>
                            <div>
                                <span class="font-medium ${channel.isActive ? 'text-gray-900' : 'text-gray-500'}">${channel.displayName}</span>
                                <p class="text-xs ${channel.isActive ? 'text-gray-600' : 'text-gray-400'}">${channel.description}</p>
                            </div>
                        </div>
                    </div>
                    ${channel.isActive ? '<span class="text-xs text-green-600 font-medium">Aktif</span>' : '<span class="text-xs text-gray-400">Pasif</span>'}
                `;

                container.appendChild(channelDiv);
            });

            // Lucide iconları yeniden render et
            if (typeof lucide !== 'undefined') {
                lucide.createIcons();
            }

            // Channel checkbox'larına event listener ekle
            document.querySelectorAll('.channel-checkbox').forEach(checkbox => {
                checkbox.addEventListener('change', handleChannelSelectionChange);
            });
        }

        function handleChannelSelectionChange() {
            const selectedChannels = getSelectedChannels();
            const isWhatsAppSelected = selectedChannels.includes('WhatsApp');

            // WhatsApp template bölümünü göster/gizle
            const templateSection = document.getElementById('whatsappTemplateSection');
            if (isWhatsAppSelected) {
                templateSection.classList.remove('hidden');
                // WhatsApp template'lerini yükle (henüz yüklenmediyse)
                if (whatsappTemplates.length === 0) {
                    loadWhatsAppTemplates();
                }
            } else {
                templateSection.classList.add('hidden');
                // WhatsApp template seçimini temizle
                document.getElementById('whatsappTemplate').value = '';
            }
        }

        function getChannelIcon(channelName) {
            switch (channelName) {
                case 'Email': return 'mail';
                case 'WhatsApp': return 'message-circle';
                case 'Telegram': return 'send';
                default: return 'message-square';
            }
        }

        function getSelectedChannels() {
            const checkboxes = document.querySelectorAll('.channel-checkbox:checked');
            return Array.from(checkboxes).map(cb => cb.value);
        }

        function setSelectedChannels(channels) {
            document.querySelectorAll('.channel-checkbox').forEach(cb => {
                cb.checked = channels.includes(cb.value);
            });
            // WhatsApp template bölümünü güncelle
            handleChannelSelectionChange();
        }

        // Modal functions
        async function openCreateModal() {
            console.log('Opening create modal');
            isEditMode = false;
            document.getElementById('modalTitle').textContent = 'Yeni Hatırlatma';
            document.getElementById('reminderForm').reset();
            document.getElementById('reminderId').value = '0';
            document.getElementById('reminderActive').checked = true;

            // İletişim kanallarını yükle
            await loadCommunicationChannels();

            // Varsayılan olarak aktif kanalları seç
            const activeChannels = availableChannels.filter(c => c.isActive).map(c => c.name);
            setSelectedChannels(activeChannels);

            document.getElementById('reminderModal').classList.remove('hidden');
            updateCharCount(); // Karakter sayacını sıfırla
        }
        
        async function editReminder(id, name, message, hours, isActive, communicationChannels, channelMessages) {
            console.log('Editing reminder:', {id, name, message, hours, isActive, communicationChannels, channelMessages});
            isEditMode = true;
            document.getElementById('modalTitle').textContent = 'Hatırlatmayı Düzenle';
            document.getElementById('reminderId').value = id;
            document.getElementById('reminderName').value = name;
            document.getElementById('reminderMessage').value = message;
            document.getElementById('reminderHours').value = hours;
            document.getElementById('reminderActive').checked = isActive;

            // İletişim kanallarını yükle
            await loadCommunicationChannels();

            // Seçili kanalları ayarla
            try {
                const selectedChannels = JSON.parse(communicationChannels || '[]');
                setSelectedChannels(selectedChannels);

                // WhatsApp template seçimini geri yükle
                if (selectedChannels.includes('WhatsApp')) {
                    await loadWhatsAppTemplates();

                    try {
                        const channelMessagesObj = JSON.parse(channelMessages || '{}');
                        if (channelMessagesObj.WhatsApp && channelMessagesObj.WhatsApp.templateId) {
                            document.getElementById('whatsappTemplate').value = channelMessagesObj.WhatsApp.templateId;
                        }
                    } catch (e) {
                        console.error('Error parsing channel messages:', e);
                    }
                }
            } catch (e) {
                console.error('Error parsing communication channels:', e);
                // Varsayılan olarak Email seç
                setSelectedChannels(['Email']);
            }

            document.getElementById('reminderModal').classList.remove('hidden');
            updateCharCount(); // Karakter sayacını güncelle
        }
        
        function closeModal() {
            console.log('Closing modal');
            document.getElementById('reminderModal').classList.add('hidden');
        }
        
        // Form submit
        document.addEventListener('DOMContentLoaded', function() {
            // Karakter sayacı için event listener ekle
            const messageTextarea = document.getElementById('reminderMessage');
            if (messageTextarea) {
                messageTextarea.addEventListener('input', updateCharCount);
                messageTextarea.addEventListener('paste', function() {
                    setTimeout(updateCharCount, 10); // Paste işlemi tamamlandıktan sonra güncelle
                });
            }

            document.getElementById('reminderForm').addEventListener('submit', function(e) {
                e.preventDefault();
                console.log('Form submitted');
                
                const selectedChannels = getSelectedChannels();

                // WhatsApp template kontrolü
                const isWhatsAppSelected = selectedChannels.includes('WhatsApp');
                const whatsappTemplateId = document.getElementById('whatsappTemplate').value;

                if (isWhatsAppSelected && !whatsappTemplateId) {
                    showToast('WhatsApp kanalı seçiliyken template seçimi zorunludur.', 'warning');
                    return;
                }

                // Channel messages objesini hazırla
                const channelMessages = {
                    default: document.getElementById('reminderMessage').value.trim()
                };

                // WhatsApp template bilgisini ekle
                if (isWhatsAppSelected && whatsappTemplateId) {
                    const selectedTemplate = whatsappTemplates.find(t => t.id === whatsappTemplateId);
                    channelMessages.WhatsApp = {
                        templateId: whatsappTemplateId,
                        templateName: selectedTemplate ? selectedTemplate.name : ''
                    };
                }

                const formData = {
                    id: parseInt(document.getElementById('reminderId').value),
                    name: document.getElementById('reminderName').value.trim(),
                    message: document.getElementById('reminderMessage').value.trim(),
                    hours: parseInt(document.getElementById('reminderHours').value),
                    isActive: document.getElementById('reminderActive').checked,
                    communicationChannels: JSON.stringify(selectedChannels),
                    channelMessages: JSON.stringify(channelMessages)
                };

                if (!formData.name || !formData.message || !formData.hours) {
                    showToast('Lütfen tüm alanları doldurun.', 'warning');
                    return;
                }

                if (selectedChannels.length === 0) {
                    showToast('En az bir iletişim kanalı seçmelisiniz.', 'warning');
                    return;
                }
                
                const url = isEditMode ? '/BasketReminder/Update' : '/BasketReminder/Create';
                
                fetch(url, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(formData)
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showToast(data.message, 'success');
                        closeModal();
                        setTimeout(() => location.reload(), 1000);
                    } else {
                        showToast('Hata: ' + data.message, 'error');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    showToast('Bir hata oluştu.', 'error');
                });
            });
            
            // Close modal when clicking outside
            document.getElementById('reminderModal').addEventListener('click', function(e) {
                if (e.target === this) {
                    closeModal();
                }
            });
        });
        
        // Delete function
        function deleteReminder(id, name) {
            showConfirmDialog(`"${name}" hatırlatmasını silmek istediğinizden emin misiniz?`, () => {
                fetch('/BasketReminder/Delete', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ id: id })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showToast(data.message, 'success');
                        setTimeout(() => location.reload(), 1000);
                    } else {
                        showToast('Hata: ' + data.message, 'error');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    showToast('Bir hata oluştu.', 'error');
                });
            });
        }
        
        // Toggle status function
        function toggleStatus(id) {
            fetch('/BasketReminder/ToggleStatus', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ id: id })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showToast(data.message, 'success');
                    setTimeout(() => location.reload(), 1000);
                } else {
                    showToast('Hata: ' + data.message, 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showToast('Bir hata oluştu.', 'error');
            });
        }
    </script>
}
