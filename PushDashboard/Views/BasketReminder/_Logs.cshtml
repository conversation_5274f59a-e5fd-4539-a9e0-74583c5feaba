@model List<BasketReminderLog>

<div class="bg-white shadow rounded-lg">
    <div class="px-4 py-5 sm:p-6">
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-lg leading-6 font-medium text-gray-900">Son Hatırlatma Logları</h3>
            <button onclick="refreshLogs()" 
                    class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                <svg class="mr-2 -ml-1 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
                Ye<PERSON>le
            </button>
        </div>

        @if (Model.Any())
        {
            <div class="overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
                <table class="min-w-full divide-y divide-gray-300">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Sepet ID
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Müşteri
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Hatırlatma Zamanı
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Durum
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Gönderim Zamanı
                            </th>
                            <th scope="col" class="relative px-6 py-3">
                                <span class="sr-only">Detay</span>
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        @foreach (var log in Model)
                        {
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                    @log.BasketExternalId
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900">@log.CustomerName</div>
                                    <div class="text-sm text-gray-500">@log.CustomerEmail</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    @log.ReminderTimeText
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full @log.StatusBadgeClass">
                                        @log.StatusText
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    @log.SentAt.ToString("dd.MM.yyyy HH:mm")
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                    <button onclick="showLogDetail('@log.Id')" 
                                            class="text-blue-600 hover:text-blue-900">
                                        Detay
                                    </button>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>

            <!-- Daha Fazla Yükle -->
            <div class="mt-4 text-center">
                <button onclick="loadMoreLogs()" 
                        class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    Daha Fazla Yükle
                </button>
            </div>
        }
        else
        {
            <div class="text-center py-12">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                <h3 class="mt-2 text-sm font-medium text-gray-900">Henüz log yok</h3>
                <p class="mt-1 text-sm text-gray-500">Hatırlatma gönderildiğinde loglar burada görünecek.</p>
            </div>
        }
    </div>
</div>

<!-- Log Detay Modal -->
<div id="logDetailModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Log Detayları</h3>
            <div id="logDetailContent">
                <!-- Detay içeriği buraya yüklenecek -->
            </div>
            <div class="flex justify-end mt-4">
                <button onclick="closeLogDetailModal()" 
                        class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200">
                    Kapat
                </button>
            </div>
        </div>
    </div>
</div>

<script>
let currentPage = 1;
const pageSize = 50;

window.refreshLogs = function() {
    currentPage = 1;
    loadLogs(true);
};

// Global fonksiyon olarak tanımla
window.refreshLogsFromPartial = window.refreshLogs;

function loadLogs(refresh = false) {
    const logsContainer = document.querySelector('#logs-content .overflow-hidden');
    if (!logsContainer) return;

    if (refresh) {
        // Yükleme göstergesi ekle
        logsContainer.innerHTML = `
            <div class="flex justify-center items-center py-8">
                <svg class="animate-spin h-8 w-8 text-blue-600" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                <span class="ml-2 text-gray-600">Loglar yükleniyor...</span>
            </div>
        `;
    }

    const csrfToken = document.querySelector('input[name="__RequestVerificationToken"]');
    if (!csrfToken) {
        if (typeof showNotification === 'function') {
            showNotification('CSRF token bulunamadı. Sayfayı yenileyin.', 'error');
        }
        return;
    }

    fetch(`@Url.Action("GetLogs", "BasketReminder")?page=${currentPage}&pageSize=${pageSize}`, {
        method: 'GET',
        headers: {
            'RequestVerificationToken': csrfToken.value
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            updateLogsTable(data.logs);
        } else {
            if (typeof showNotification === 'function') {
                showNotification('Loglar yüklenirken hata oluştu: ' + data.message, 'error');
            }
        }
    })
    .catch(error => {
        console.error('Load logs error:', error);
        if (typeof showNotification === 'function') {
            showNotification('Loglar yüklenirken hata oluştu.', 'error');
        }
    });
}

function updateLogsTable(logs) {
    const logsContainer = document.querySelector('#logs-content .overflow-hidden');
    if (!logsContainer) return;

    if (!logs || logs.length === 0) {
        logsContainer.innerHTML = `
            <div class="text-center py-12">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                <h3 class="mt-2 text-sm font-medium text-gray-900">Henüz log yok</h3>
                <p class="mt-1 text-sm text-gray-500">Hatırlatma gönderildiğinde loglar burada görünecek.</p>
            </div>
        `;
        return;
    }

    // Logs tablosunu oluştur
    let tableHTML = `
        <table class="min-w-full divide-y divide-gray-300">
            <thead class="bg-gray-50">
                <tr>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tarih</th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Müşteri</th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Durum</th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Mesaj</th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
    `;

    logs.forEach(log => {
        const statusClass = log.IsSuccessful ? 'text-green-800 bg-green-100' : 'text-red-800 bg-red-100';
        const statusText = log.IsSuccessful ? 'Başarılı' : 'Başarısız';

        tableHTML += `
            <tr>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    ${new Date(log.CreatedAt).toLocaleDateString('tr-TR', {
                        year: 'numeric',
                        month: '2-digit',
                        day: '2-digit',
                        hour: '2-digit',
                        minute: '2-digit'
                    })}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    ${log.CustomerName || 'Bilinmiyor'}
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full ${statusClass}">
                        ${statusText}
                    </span>
                </td>
                <td class="px-6 py-4 text-sm text-gray-900 max-w-xs truncate">
                    ${log.ErrorMessage || log.NotificationContent || '-'}
                </td>
            </tr>
        `;
    });

    tableHTML += `
            </tbody>
        </table>
    `;

    logsContainer.innerHTML = tableHTML;
}

function loadMoreLogs() {
    currentPage++;
    loadLogs(false);
}

function loadLogs(replace = true) {
    fetch(`@Url.Action("GetLogs", "BasketReminder")?page=${currentPage}&pageSize=${pageSize}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                if (replace) {
                    // Sayfayı yenile
                    location.reload();
                } else {
                    // Daha fazla log ekle (bu durumda sayfa yenileme gerekebilir)
                    location.reload();
                }
            } else {
                showNotification('Loglar yüklenirken hata oluştu: ' + data.message, 'error');
            }
        })
        .catch(error => {
            showNotification('Bir hata oluştu: ' + error.message, 'error');
        });
}

function showLogDetail(logId) {
    // Bu örnekte basit bir detay gösterimi yapıyoruz
    // Gerçek implementasyonda log detaylarını API'den çekebilirsiniz
    
    const logRow = document.querySelector(`button[onclick="showLogDetail('${logId}')"]`).closest('tr');
    const cells = logRow.querySelectorAll('td');
    
    const basketId = cells[0].textContent.trim();
    const customerInfo = cells[1].querySelector('.text-sm.font-medium').textContent.trim();
    const customerEmail = cells[1].querySelector('.text-sm.text-gray-500').textContent.trim();
    const reminderTime = cells[2].textContent.trim();
    const status = cells[3].textContent.trim();
    const sentAt = cells[4].textContent.trim();
    
    const detailContent = `
        <div class="space-y-3">
            <div>
                <label class="block text-sm font-medium text-gray-700">Sepet ID</label>
                <p class="text-sm text-gray-900">${basketId}</p>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700">Müşteri</label>
                <p class="text-sm text-gray-900">${customerInfo}</p>
                <p class="text-sm text-gray-500">${customerEmail}</p>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700">Hatırlatma Zamanı</label>
                <p class="text-sm text-gray-900">${reminderTime}</p>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700">Durum</label>
                <p class="text-sm text-gray-900">${status}</p>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700">Gönderim Zamanı</label>
                <p class="text-sm text-gray-900">${sentAt}</p>
            </div>
        </div>
    `;
    
    document.getElementById('logDetailContent').innerHTML = detailContent;
    document.getElementById('logDetailModal').classList.remove('hidden');
}

function closeLogDetailModal() {
    document.getElementById('logDetailModal').classList.add('hidden');
}

// Modal dışına tıklayınca kapat
document.getElementById('logDetailModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeLogDetailModal();
    }
});
</script>
