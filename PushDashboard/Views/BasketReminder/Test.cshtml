<!DOCTYPE html>
<html>
<head>
    <title>Basket Reminder Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100">
    <div class="container mx-auto p-8">
        <h1 class="text-3xl font-bold mb-8">Basket Reminder Test Page</h1>
        
        <!-- Test Button -->
        <div class="mb-8">
            <button onclick="testOpenModal()" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Test Modal Open
            </button>
            <button onclick="testConsole()" class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded ml-4">
                Test Console
            </button>
        </div>

        <!-- Modal -->
        <div id="scheduleModal" class="hidden fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
            <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
                <div class="mt-3 text-center">
                    <h3 id="modalTitle" class="text-lg font-medium text-gray-900">Test Modal</h3>
                    <div class="mt-2 px-7 py-3">
                        <p class="text-sm text-gray-500">This is a test modal.</p>
                    </div>
                    <div class="items-center px-4 py-3">
                        <button onclick="testCloseModal()" class="px-4 py-2 bg-gray-500 text-white text-base font-medium rounded-md shadow-sm hover:bg-gray-600">
                            Close
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        console.log('Test page loaded');
        
        function testOpenModal() {
            console.log('testOpenModal called');
            const modal = document.getElementById('scheduleModal');
            if (modal) {
                modal.classList.remove('hidden');
                console.log('Modal opened successfully');
            } else {
                console.error('Modal not found');
            }
        }
        
        function testCloseModal() {
            console.log('testCloseModal called');
            const modal = document.getElementById('scheduleModal');
            if (modal) {
                modal.classList.add('hidden');
                console.log('Modal closed successfully');
            } else {
                console.error('Modal not found');
            }
        }
        
        function testConsole() {
            console.log('Console test - everything working!');
            alert('Console test completed - check browser console');
        }
        
        // Test if window functions work
        window.testGlobalFunction = function() {
            console.log('Global function test successful');
        };
        
        // Auto test
        setTimeout(() => {
            console.log('Auto test running...');
            if (typeof window.testGlobalFunction === 'function') {
                window.testGlobalFunction();
            }
        }, 1000);
    </script>
</body>
</html>
