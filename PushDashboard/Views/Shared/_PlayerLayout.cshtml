<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - Video Player</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    
    <style>
        body {
            margin: 0;
            padding: 0;
            background-color: #000;
            color: #fff;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        
        .video-container {
            width: 100%;
            height: 100vh;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }
        
        video {
            max-width: 100%;
            max-height: 80vh;
            width: auto;
            height: auto;
        }
        
        .video-info {
            padding: 20px;
            text-align: center;
            max-width: 800px;
        }
        
        .video-title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .video-description {
            font-size: 16px;
            color: #ccc;
            line-height: 1.5;
        }
        
        .video-meta {
            margin-top: 15px;
            font-size: 14px;
            color: #999;
        }
        
        .video-meta span {
            margin-right: 20px;
        }
        
    </style>
</head>
<body>
    <main>
        @RenderBody()
    </main>

    @await RenderSectionAsync("Scripts", required: false)
</body>
</html>
