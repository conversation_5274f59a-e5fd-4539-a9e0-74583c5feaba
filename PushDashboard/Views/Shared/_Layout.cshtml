@{
  var currentController = ViewContext.RouteData.Values["controller"]?.ToString();
}

<!DOCTYPE html>
<html lang="tr">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>@ViewData["Title"] - PushDashboard</title>
  <!-- Tailwind CSS CDN -->
  <script src="https://cdn.tailwindcss.com"></script>
  <script>
    tailwind.config = {
      theme: {
        extend: {
          colors: {
            primary: {
              DEFAULT: '#6366f1',
              dark: '#4f46e5',
            },
            green: {
              light: '#dcfce7',
              DEFAULT: '#22c55e',
            },
            blue: {
              light: '#dbeafe',
              DEFAULT: '#3b82f6',
            },
            purple: {
              light: '#f3e8ff',
              DEFAULT: '#a855f7',
            },
            yellow: {
              light: '#fef9c3',
              DEFAULT: '#eab308',
            },
            red: {
              light: '#fee2e2',
              DEFAULT: '#ef4444',
            },
          }
        }
      }
    }
  </script>
  <style>
    /* Minimal custom styles that can't be easily done with Tailwind */
    .bg-gradient-text {
      background: linear-gradient(to right, #6366f1, #4f46e5);
      -webkit-background-clip: text;
      background-clip: text;
      color: transparent;
    }

    .tooltip {
      display: none;
      position: fixed;
      z-index: 50;
      pointer-events: none;
    }

    .tooltip.visible {
      display: block;
    }
    .drawer {
      transition: transform 0.3s ease-in-out;
    }
    .drawer.open {
      transform: translateX(0);
    }
    .drawer.closed {
      transform: translateX(100%);
    }
    .drawer-overlay {
      transition: opacity 0.3s ease-in-out;
    }
    .drawer-overlay.open {
      opacity: 0.5;
      pointer-events: auto;
    }
    .drawer-overlay.closed {
      opacity: 0;
      pointer-events: none;
    }
    .tab-content {
      display: none;
    }
    .tab-content.active {
      display: block;
    }
    .tab-button.active {
      background-color: #6366f1;
      color: white;
    }
    .template-item.active {
      background-color: #eff6ff !important;
      border-left: 4px solid #3b82f6 !important;
    }

    .variable-tag {
      display: inline-block;
      background: #dbeafe;
      color: #1d4ed8;
      padding: 4px 8px;
      margin: 2px;
      border-radius: 12px;
      font-size: 0.75rem;
      cursor: pointer;
      border: 1px solid #93c5fd;
      transition: all 0.2s;
      width: 100%;
      text-align: left;
    }

    .variable-tag:hover {
      background: #bfdbfe;
      transform: translateY(-1px);
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .variable-tag.mb-2 {
      margin-bottom: 8px;
      padding: 8px 12px;
      border-radius: 8px;
    }
  </style>

  @await RenderSectionAsync("Styles", required: false)
</head>
<body class="bg-gray-50 text-gray-800">
  <div class="flex min-h-screen">
    <!-- Sidebar -->
    <div class="hidden md:block w-64 bg-white shadow-md">
      <a href="@Url.Action("Index", "Home")">
        <div class="p-4 border-b border-gray-200">
          <div class="flex items-center">
            <svg class="w-6 h-6 mr-2 text-primary" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M9 6v9a3 3 0 1 0 6 0V6"></path>
              <rect x="7" y="6" width="10" height="4" rx="1"></rect>
            </svg>
            <span class="text-xl font-bold bg-gradient-text">Pushonica</span>
          </div>
        </div>
      </a>
      <nav class="p-4">
        <ul class="space-y-2">
          <li>
            <a href="@Url.Action("Index", "Home")"
               class="flex items-center p-2 rounded-lg font-medium
               @(currentController == "Home" ? "text-primary bg-primary/10" : "text-gray-600 hover:bg-gray-100")">
              <svg class="w-5 h-5 mr-3" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <rect width="7" height="9" x="3" y="3" rx="1"></rect>
                <rect width="7" height="5" x="14" y="3" rx="1"></rect>
                <rect width="7" height="9" x="14" y="12" rx="1"></rect>
                <rect width="7" height="5" x="3" y="16" rx="1"></rect>
              </svg>
              Dashboard
            </a>
          </li>
          <li>
            <a href="@Url.Action("Index", "Store")"
               class="flex items-center p-2 rounded-lg font-medium
          @(currentController == "Store" ? "text-primary bg-primary/10" : "text-gray-600 hover:bg-gray-100")">
              <svg class="w-5 h-5 mr-3" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M6 8a6 6 0 0 1 12 0c0 7 3 9 3 9H3s3-2 3-9"></path>
                <path d="M10.3 21a1.94 1.94 0 0 0 3.4 0"></path>
              </svg>
              Mağaza
            </a>
          </li>
          <li>
            <a href="@Url.Action("Index", "Customer")"
               class="flex items-center p-2 rounded-lg font-medium
          @(currentController == "Customer" ? "text-primary bg-primary/10" : "text-gray-600 hover:bg-gray-100")">
              <svg class="w-5 h-5 mr-3" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"></path>
                <circle cx="9" cy="7" r="4"></circle>
                <path d="M22 21v-2a4 4 0 0 0-3-3.87"></path>
                <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
              </svg>
              Müşteriler
            </a>
          </li>
          <li>
            <a href="@Url.Action("Index", "Basket")"
               class="flex items-center p-2 rounded-lg font-medium
          @(currentController == "Basket" ? "text-primary bg-primary/10" : "text-gray-600 hover:bg-gray-100")">
              <svg class="w-5 h-5 mr-3" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <circle cx="8" cy="21" r="1"></circle>
                <circle cx="19" cy="21" r="1"></circle>
                <path d="M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43H5.12"></path>
              </svg>
              Sepetler
            </a>
          </li>
          <li>
            <a href="@Url.Action("Index", "Integration")"
               class="flex items-center p-2 rounded-lg font-medium
          @(currentController == "Integration" ? "text-primary bg-primary/10" : "text-gray-600 hover:bg-gray-100")">
              <svg class="w-5 h-5 mr-3" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="m2 7 4.41-4.41A2 2 0 0 1 7.83 2h8.34a2 2 0 0 1 1.42.59L22 7"></path>
                <path d="M4 12v8a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-8"></path>
                <path d="M15 22v-4a2 2 0 0 0-2-2h-2a2 2 0 0 0-2 2v4"></path>
                <path d="M2 7h20"></path>
                <path d="M22 7v3a2 2 0 0 1-2 2v0a2 2 0 0 1-2-2V7"></path>
                <path d="M6 7v3a2 2 0 0 1-2 2v0a2 2 0 0 1-2-2V7"></path>
              </svg>
              Entegrasyonlar
            </a>
          </li>
          <li>
            <a href="@Url.Action("Index", "ProductSlider")"
               class="flex items-center p-2 rounded-lg font-medium
          @(currentController == "ProductSlider" ? "text-primary bg-primary/10" : "text-gray-600 hover:bg-gray-100")">
              <svg class="w-5 h-5 mr-3" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3"></path>
              </svg>
              Ürün Slider
            </a>
          </li>

          @{
            var emailTemplateService = ViewContext.HttpContext.RequestServices.GetService<PushDashboard.Services.IEmailTemplateService>();
            var whatsAppTemplateService = ViewContext.HttpContext.RequestServices.GetService<PushDashboard.Services.IWhatsAppTemplateService>();
            var user = ViewContext.HttpContext.User;
            var userManager = ViewContext.HttpContext.RequestServices.GetService<Microsoft.AspNetCore.Identity.UserManager<PushDashboard.Models.ApplicationUser>>();
            var context = ViewContext.HttpContext.RequestServices.GetService<PushDashboard.Data.ApplicationDbContext>();

            bool showCommentModule = false;
            bool showEmailTemplates = false;
            bool showWhatsAppTemplates = false;
            bool showOrderStatusModule = false;
            bool showSocialProofModule = false;
            bool showCookieManagementModule = false;
            bool showBasketReminderModule = false;
            bool showExitSurveyModule = false;
            bool showGiftWheelModule = false;
            bool showVideoHostingModule = false;
            if (user.Identity?.IsAuthenticated == true && userManager != null && context != null)
            {
              var currentUser = userManager.GetUserAsync(user).Result;
              if (currentUser?.CompanyId != null)
              {

                showEmailTemplates = emailTemplateService.HasSmtpConfigurationAsync(currentUser.CompanyId.Value).Result;
                showWhatsAppTemplates = whatsAppTemplateService.HasWhatsAppConfigurationAsync(currentUser.CompanyId.Value).Result;

                // Yorum Taşıma modülünün satın alınıp alınmadığını kontrol et
                var commentModule = context.Modules.FirstOrDefault(m => m.Name == "Yorum Taşıma");
                if (commentModule != null)
                {
                  showCommentModule = context.CompanyModules.Any(cm =>
                    cm.CompanyId == currentUser.CompanyId.Value &&
                    cm.ModuleId == commentModule.Id &&
                    cm.IsActive);
                }

                // Sipariş Durumu Bildirimleri modülünün satın alınıp alınmadığını kontrol et
                var orderStatusModule = context.Modules.FirstOrDefault(m => m.Name == "Sipariş Durumu Bildirimleri");
                if (orderStatusModule != null)
                {
                  showOrderStatusModule = context.CompanyModules.Any(cm =>
                    cm.CompanyId == currentUser.CompanyId.Value &&
                    cm.ModuleId == orderStatusModule.Id &&
                    cm.IsActive);
                }

                // Social Proof modülünün satın alınıp alınmadığını kontrol et
                var socialProofModule = context.Modules.FirstOrDefault(m => m.Name == "Social Proof");
                if (socialProofModule != null)
                {
                  showSocialProofModule = context.CompanyModules.Any(cm =>
                    cm.CompanyId == currentUser.CompanyId.Value &&
                    cm.ModuleId == socialProofModule.Id &&
                    cm.IsActive);
                }

                // Çerez Yönetimi modülünün satın alınıp alınmadığını kontrol et
                var cookieManagementModule = context.Modules.FirstOrDefault(m => m.Name == "Çerez Yönetimi");
                if (cookieManagementModule != null)
                {
                  showCookieManagementModule = context.CompanyModules.Any(cm =>
                    cm.CompanyId == currentUser.CompanyId.Value &&
                    cm.ModuleId == cookieManagementModule.Id &&
                    cm.IsActive);
                }

                // Sepet Hatırlatma modülünün satın alınıp alınmadığını kontrol et
                var basketReminderModule = context.Modules.FirstOrDefault(m => m.Name == "Sepet Hatırlatma");
                if (basketReminderModule != null)
                {
                  showBasketReminderModule = context.CompanyModules.Any(cm =>
                    cm.CompanyId == currentUser.CompanyId.Value &&
                    cm.ModuleId == basketReminderModule.Id &&
                    cm.IsActive);
                }

                // Çıkış Anketi modülünün satın alınıp alınmadığını kontrol et
                var exitSurveyModule = context.Modules.FirstOrDefault(m => m.Name == "Çıkış Anketi");
                if (exitSurveyModule != null)
                {
                  showExitSurveyModule = context.CompanyModules.Any(cm =>
                    cm.CompanyId == currentUser.CompanyId.Value &&
                    cm.ModuleId == exitSurveyModule.Id &&
                    cm.IsActive);
                }

                // Video Hosting modülünün satın alınıp alınmadığını kontrol et
                var videoHostingModule = context.Modules.FirstOrDefault(m => m.Name == "Video Hosting");
                if (videoHostingModule != null)
                {
                  showVideoHostingModule = context.CompanyModules.Any(cm =>
                    cm.CompanyId == currentUser.CompanyId.Value &&
                    cm.ModuleId == videoHostingModule.Id &&
                    cm.IsActive);
                }

                // Hediye Çarkı modülünün satın alınıp alınmadığını kontrol et
                var giftWheelModule = context.Modules.FirstOrDefault(m => m.Name == "Hediye Çarkı");
                if (giftWheelModule != null)
                {
                  showGiftWheelModule = context.CompanyModules.Any(cm =>
                    cm.CompanyId == currentUser.CompanyId.Value &&
                    cm.ModuleId == giftWheelModule.Id &&
                    cm.IsActive);
                }
              }
            }
          }
          @if (showOrderStatusModule)
          {
            <li>
              <a href="@Url.Action("Index", "OrderStatus")"
                 class="flex items-center p-2 rounded-lg font-medium
            @(currentController == "OrderStatus" ? "text-primary bg-primary/10" : "text-gray-600 hover:bg-gray-100")">
                <svg class="w-5 h-5 mr-3" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2"></path>
                  <rect x="8" y="2" width="8" height="4" rx="1" ry="1"></rect>
                  <path d="M9 14l2 2 4-4"></path>
                </svg>
                Sipariş Durumu
              </a>
            </li>
          }
          @if (showCommentModule)
          {
            <li>
              <a href="@Url.Action("Index", "Comment")"
                 class="flex items-center p-2 rounded-lg font-medium
            @(currentController == "Comment" ? "text-primary bg-primary/10" : "text-gray-600 hover:bg-gray-100")">
                <svg class="w-5 h-5 mr-3" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
                </svg>
                Yorum Taşıma
              </a>
            </li>
          }
          @if (showSocialProofModule)
          {
            <li>
              <a href="@Url.Action("Index", "SocialProof")"
                 class="flex items-center p-2 rounded-lg font-medium
            @(currentController == "SocialProof" ? "text-primary bg-primary/10" : "text-gray-600 hover:bg-gray-100")">
                <svg class="w-5 h-5 mr-3" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
                  <circle cx="9" cy="7" r="4"></circle>
                  <path d="M23 21v-2a4 4 0 0 0-3-3.87"></path>
                  <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
                </svg>
                Social Proof
              </a>
            </li>
          }
          @if (showCookieManagementModule)
          {
            <li>
              <a href="@Url.Action("Index", "CookieManagement")"
                 class="flex items-center p-2 rounded-lg font-medium
            @(currentController == "CookieManagement" ? "text-primary bg-primary/10" : "text-gray-600 hover:bg-gray-100")">
                <svg class="w-5 h-5 mr-3" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <circle cx="12" cy="12" r="10"></circle>
                  <circle cx="12" cy="8" r="1"></circle>
                  <circle cx="8" cy="16" r="1"></circle>
                  <circle cx="16" cy="16" r="1"></circle>
                  <circle cx="7" cy="11" r="1"></circle>
                  <circle cx="17" cy="11" r="1"></circle>
                </svg>
                Çerez Yönetimi
              </a>
            </li>
          }
          @if (showBasketReminderModule)
          {
            <li>
              <a href="@Url.Action("Index", "BasketReminder")"
                 class="flex items-center p-2 rounded-lg font-medium
            @(currentController == "BasketReminder" ? "text-primary bg-primary/10" : "text-gray-600 hover:bg-gray-100")">
                <svg class="w-5 h-5 mr-3" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M3 3h2l.4 2M7 13h10l4-8H5.4m1.6 8L5 3H2m5 10v6a1 1 0 0 0 1 1h1m0 0a1 1 0 1 0 2 0m-2 0a1 1 0 1 0-2 0m9 0a1 1 0 1 0 2 0m-2 0a1 1 0 1 0-2 0"></path>
                  <path d="M12 8v4l2 2"></path>
                </svg>
                Sepet Hatırlatma
              </a>
            </li>
          }
          @if (showGiftWheelModule)
          {
            <li>
              <a href="@Url.Action("Index", "GiftWheel")"
                 class="flex items-center p-2 rounded-lg font-medium
            @(currentController == "GiftWheel" ? "text-primary bg-primary/10" : "text-gray-600 hover:bg-gray-100")">
                <svg class="w-5 h-5 mr-3" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <circle cx="12" cy="12" r="10"></circle>
                  <path d="M12 2a10 10 0 0 1 10 10"></path>
                  <path d="M12 2a10 10 0 0 0-10 10"></path>
                  <path d="M12 2v10l8 8"></path>
                </svg>
                Hediye Çarkı
              </a>
            </li>
          }
          @if (showExitSurveyModule)
          {
            <li>
              <a href="@Url.Action("Index", "ExitSurvey")"
                 class="flex items-center p-2 rounded-lg font-medium
            @(currentController == "ExitSurvey" ? "text-primary bg-primary/10" : "text-gray-600 hover:bg-gray-100")">
                <svg class="w-5 h-5 mr-3" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M9 12l2 2 4-4"></path>
                  <path d="M21 12c.552 0 1-.448 1-1V5c0-.552-.448-1-1-1H3c-.552 0-1 .448-1 1v6c0 .552.448 1 1 1h9l4 4V12z"></path>
                </svg>
                Çıkış Anketi
              </a>
            </li>
          }
          @if (showEmailTemplates)
          {
            <li>
              <a href="@Url.Action("Index", "EmailTemplate")"
                 class="flex items-center p-2 rounded-lg font-medium
            @(currentController == "EmailTemplate" ? "text-primary bg-primary/10" : "text-gray-600 hover:bg-gray-100")">
                <svg class="w-5 h-5 mr-3" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"></path>
                  <polyline points="22,6 12,13 2,6"></polyline>
                </svg>
                Mail Şablonları
              </a>
            </li>
          }
          @if (showWhatsAppTemplates)
          {
            <li>
              <a href="@Url.Action("Index", "WhatsAppTemplate")"
                 class="flex items-center p-2 rounded-lg font-medium
            @(currentController == "WhatsAppTemplate" ? "text-primary bg-primary/10" : "text-gray-600 hover:bg-gray-100")">
                <svg class="w-5 h-5 mr-3" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M21 11.5a8.38 8.38 0 0 1-.9 3.8 8.5 8.5 0 0 1-7.6 4.7 8.38 8.38 0 0 1-3.8-.9L3 21l1.9-5.7a8.38 8.38 0 0 1-.9-3.8 8.5 8.5 0 0 1 4.7-7.6 8.38 8.38 0 0 1 3.8-.9h.5a8.48 8.48 0 0 1 8 8v.5z"></path>
                </svg>
                WhatsApp Şablonları
              </a>
            </li>
          }
          @if (showVideoHostingModule)
          {
            <li>
              <a href="@Url.Action("Index", "VideoHosting")"
                 class="flex items-center p-2 rounded-lg font-medium
            @(currentController == "VideoHosting" ? "text-primary bg-primary/10" : "text-gray-600 hover:bg-gray-100")">
                <i class="fas fa-video w-5 h-5 mr-3"></i>
                Video Hosting
              </a>
            </li>
          }
          <li>
            <a href="@Url.Action("Index", "UsageHistory")"
               class="flex items-center p-2 rounded-lg font-medium
          @(currentController == "UsageHistory" ? "text-primary bg-primary/10" : "text-gray-600 hover:bg-gray-100")">
              <svg class="w-5 h-5 mr-3" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M3 3v5h5"></path>
                <path d="M21 21v-5h-5"></path>
                <path d="M21 3a16 16 0 0 0-13.8 8"></path>
                <path d="M3 21a16 16 0 0 1 13.8-8"></path>
                <circle cx="12" cy="12" r="2"></circle>
              </svg>
              Harcama Geçmişi
            </a>
          </li>
          <li>
            <a href="@Url.Action("Index", "Settings")"
               class="flex items-center p-2 rounded-lg font-medium
          @(currentController == "Settings" ? "text-primary bg-primary/10" : "text-gray-600 hover:bg-gray-100")">
              <svg class="w-5 h-5 mr-3" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z"></path>
                <circle cx="12" cy="12" r="3"></circle>
              </svg>
              Ayarlar
            </a>
          </li>
        </ul>
      </nav>
    </div>

    <!-- Main Content -->
    <div class="flex-1 flex flex-col overflow-hidden">
      <!-- Top Header -->
      <header class="bg-white shadow-sm p-4 flex items-center justify-between">
        <button class="md:hidden text-gray-600" id="menuButton">
          <svg class="w-6 h-6" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <line x1="4" x2="20" y1="12" y2="12"></line>
            <line x1="4" x2="20" y1="6" y2="6"></line>
            <line x1="4" x2="20" y1="18" y2="18"></line>
          </svg>
        </button>
        <div class="md:hidden flex items-center">
          <svg class="w-5 h-5 mr-2 text-primary" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M9 6v9a3 3 0 1 0 6 0V6"></path>
            <rect x="7" y="6" width="10" height="4" rx="1"></rect>
          </svg>
          <span class="font-bold text-primary">Pushonica</span>
        </div>
        <div class="flex items-center ml-auto">
          <div class="relative">
            <button class="p-2 text-gray-600 hover:text-gray-900" id="notificationButton">
              <svg class="w-5 h-5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M6 8a6 6 0 0 1 12 0c0 7 3 9 3 9H3s3-2 3-9"></path>
                <path d="M10.3 21a1.94 1.94 0 0 0 3.4 0"></path>
              </svg>
              <span class="absolute top-1 right-1 bg-red-500 text-white text-xs rounded-full w-4 h-4 flex items-center justify-center">
                3
              </span>
            </button>
          </div>
          <div class="ml-4 flex items-center">
            @if (User.Identity?.IsAuthenticated == true)
            {
              <div class="relative" id="profileDropdown">
                <div class="flex items-center cursor-pointer" id="profileButton">
                  <div class="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center hover:bg-gray-300 transition-colors">
                    <svg class="w-5 h-5 text-gray-600" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                      <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                      <circle cx="12" cy="7" r="4"></circle>
                    </svg>
                  </div>
                  <span class="ml-2 text-sm font-medium text-gray-700 hidden sm:inline-block">@User.Identity.Name</span>
                </div>
                <div class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50 hidden border border-gray-200" id="profileMenu">
                  <a asp-controller="Settings" asp-action="Index" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition duration-150 ease-in-out">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 inline-block mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                    </svg>
                    Profil
                  </a>
                  <form asp-controller="Account" asp-action="Logout" method="post">
                    <button type="submit" class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition duration-150 ease-in-out">
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 inline-block mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                      </svg>
                      Çıkış Yap
                    </button>
                  </form>
                </div>
              </div>
            }
            else
            {
              <div class="flex space-x-2">
                <a asp-controller="Account" asp-action="Login" class="px-3 py-1 text-sm bg-primary text-white rounded-md hover:bg-primary-dark">Giriş Yap</a>
                <a asp-controller="Account" asp-action="Register" class="px-3 py-1 text-sm border border-primary text-primary rounded-md hover:bg-primary hover:text-white">Kayıt Ol</a>
              </div>
            }
          </div>
        </div>
      </header>

      <!-- Mobile Sidebar -->
      <div class="fixed inset-0 z-40 md:hidden hidden" id="mobileSidebar">
        <div class="fixed inset-0 bg-gray-600 bg-opacity-75" id="sidebarBackdrop"></div>
        <div class="relative flex-1 flex flex-col max-w-xs w-full bg-white">
          <div class="absolute top-0 right-0 -mr-12 pt-2">
            <button class="ml-1 flex items-center justify-center h-10 w-10 rounded-full focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white" id="closeSidebarButton">
              <span class="sr-only">Kapat</span>
              <svg class="h-6 w-6 text-white" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M18 6 6 18"></path>
                <path d="m6 6 12 12"></path>
              </svg>
            </button>
          </div>
          <div class="p-4 border-b border-gray-200">
            <div class="flex items-center">
              <svg class="w-6 h-6 mr-2 text-primary" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M9 6v9a3 3 0 1 0 6 0V6"></path>
                <rect x="7" y="6" width="10" height="4" rx="1"></rect>
              </svg>
              <span class="text-xl font-bold bg-gradient-text">Pushonica</span>
            </div>
          </div>
          <nav class="p-4">
            <ul class="space-y-2">
              <li>
                <a href="#" class="flex items-center p-2 rounded-lg text-primary bg-primary/10 font-medium">
                  <svg class="w-5 h-5 mr-3" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <rect width="7" height="9" x="3" y="3" rx="1"></rect>
                    <rect width="7" height="5" x="14" y="3" rx="1"></rect>
                    <rect width="7" height="9" x="14" y="12" rx="1"></rect>
                    <rect width="7" height="5" x="3" y="16" rx="1"></rect>
                  </svg>
                  Dashboard
                </a>
              </li>
              <li>
                <a href="@Url.Action("Index", "Basket")" class="flex items-center p-2 rounded-lg text-gray-600 hover:bg-gray-100">
                  <svg class="w-5 h-5 mr-3" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <circle cx="8" cy="21" r="1"></circle>
                    <circle cx="19" cy="21" r="1"></circle>
                    <path d="M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43H5.12"></path>
                  </svg>
                  Sepetler
                </a>
              </li>
              <li>
                <a href="#" class="flex items-center p-2 rounded-lg text-gray-600 hover:bg-gray-100">
                  <svg class="w-5 h-5 mr-3" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M6 8a6 6 0 0 1 12 0c0 7 3 9 3 9H3s3-2 3-9"></path>
                    <path d="M10.3 21a1.94 1.94 0 0 0 3.4 0"></path>
                  </svg>
                  Kampanyalar
                </a>
              </li>
              <li>
                <a href="#" class="flex items-center p-2 rounded-lg text-gray-600 hover:bg-gray-100">
                  <svg class="w-5 h-5 mr-3" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"></path>
                    <circle cx="9" cy="7" r="4"></circle>
                    <path d="M22 21v-2a4 4 0 0 0-3-3.87"></path>
                    <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
                  </svg>
                  Müşteriler
                </a>
              </li>
              <li>
                <a href="@Url.Action("Index", "Integration")" class="flex items-center p-2 rounded-lg text-gray-600 hover:bg-gray-100">
                  <svg class="w-5 h-5 mr-3" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="m2 7 4.41-4.41A2 2 0 0 1 7.83 2h8.34a2 2 0 0 1 1.42.59L22 7"></path>
                    <path d="M4 12v8a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-8"></path>
                    <path d="M15 22v-4a2 2 0 0 0-2-2h-2a2 2 0 0 0-2 2v4"></path>
                    <path d="M2 7h20"></path>
                    <path d="M22 7v3a2 2 0 0 1-2 2v0a2 2 0 0 1-2-2V7"></path>
                    <path d="M6 7v3a2 2 0 0 1-2 2v0a2 2 0 0 1-2-2V7"></path>
                  </svg>
                  Entegrasyonlar
                </a>
              </li>

              @if (showOrderStatusModule)
              {
                <li>
                  <a href="@Url.Action("Index", "OrderStatus")" class="flex items-center p-2 rounded-lg text-gray-600 hover:bg-gray-100">
                    <svg class="w-5 h-5 mr-3" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                      <path d="M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2"></path>
                      <rect x="8" y="2" width="8" height="4" rx="1" ry="1"></rect>
                      <path d="M9 14l2 2 4-4"></path>
                    </svg>
                    Sipariş Durumu
                  </a>
                </li>
              }
              @if (showCommentModule)
              {
                <li>
                  <a href="@Url.Action("Index", "Comment")" class="flex items-center p-2 rounded-lg text-gray-600 hover:bg-gray-100">
                    <svg class="w-5 h-5 mr-3" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                      <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
                    </svg>
                    Yorum Taşıma
                  </a>
                </li>
              }
              @if (showCookieManagementModule)
              {
                <li>
                  <a href="@Url.Action("Index", "CookieManagement")" class="flex items-center p-2 rounded-lg text-gray-600 hover:bg-gray-100">
                    <svg class="w-5 h-5 mr-3" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                      <circle cx="12" cy="12" r="10"></circle>
                      <circle cx="12" cy="8" r="1"></circle>
                      <circle cx="8" cy="16" r="1"></circle>
                      <circle cx="16" cy="16" r="1"></circle>
                      <circle cx="7" cy="11" r="1"></circle>
                      <circle cx="17" cy="11" r="1"></circle>
                    </svg>
                    Çerez Yönetimi
                  </a>
                </li>
              }
              @if (showBasketReminderModule)
              {
                <li>
                  <a href="@Url.Action("Index", "BasketReminder")" class="flex items-center p-2 rounded-lg text-gray-600 hover:bg-gray-100">
                    <svg class="w-5 h-5 mr-3" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                      <path d="M3 3h2l.4 2M7 13h10l4-8H5.4m1.6 8L5 3H2m5 10v6a1 1 0 0 0 1 1h1m0 0a1 1 0 1 0 2 0m-2 0a1 1 0 1 0-2 0m9 0a1 1 0 1 0 2 0m-2 0a1 1 0 1 0-2 0"></path>
                      <path d="M12 8v4l2 2"></path>
                    </svg>
                    Sepet Hatırlatma
                  </a>
                </li>
              }
              @if (showGiftWheelModule)
              {
                <li>
                  <a href="@Url.Action("Index", "GiftWheel")" class="flex items-center p-2 rounded-lg text-gray-600 hover:bg-gray-100">
                    <svg class="w-5 h-5 mr-3" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                      <circle cx="12" cy="12" r="10"></circle>
                      <path d="M12 2a10 10 0 0 1 10 10"></path>
                      <path d="M12 2a10 10 0 0 0-10 10"></path>
                      <path d="M12 2v10l8 8"></path>
                    </svg>
                    Hediye Çarkı
                  </a>
                </li>
              }
              @if (showExitSurveyModule)
              {
                <li>
                  <a href="@Url.Action("Index", "ExitSurvey")" class="flex items-center p-2 rounded-lg text-gray-600 hover:bg-gray-100">
                    <svg class="w-5 h-5 mr-3" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                      <path d="M9 12l2 2 4-4"></path>
                      <path d="M21 12c.552 0 1-.448 1-1V5c0-.552-.448-1-1-1H3c-.552 0-1 .448-1 1v6c0 .552.448 1 1 1h9l4 4V12z"></path>
                    </svg>
                    Çıkış Anketi
                  </a>
                </li>
              }
              @if (showEmailTemplates)
              {
                <li>
                  <a href="@Url.Action("Index", "EmailTemplate")" class="flex items-center p-2 rounded-lg text-gray-600 hover:bg-gray-100">
                    <svg class="w-5 h-5 mr-3" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                      <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"></path>
                      <polyline points="22,6 12,13 2,6"></polyline>
                    </svg>
                    Mail Şablonları
                  </a>
                </li>
              }
              @if (showWhatsAppTemplates)
              {
                <li>
                  <a href="@Url.Action("Index", "WhatsAppTemplate")" class="flex items-center p-2 rounded-lg text-gray-600 hover:bg-gray-100">
                    <svg class="w-5 h-5 mr-3" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                      <path d="M21 11.5a8.38 8.38 0 0 1-.9 3.8 8.5 8.5 0 0 1-7.6 4.7 8.38 8.38 0 0 1-3.8-.9L3 21l1.9-5.7a8.38 8.38 0 0 1-.9-3.8 8.5 8.5 0 0 1 4.7-7.6 8.38 8.38 0 0 1 3.8-.9h.5a8.48 8.48 0 0 1 8 8v.5z"></path>
                    </svg>
                    WhatsApp Şablonları
                  </a>
                </li>
              }
              <li>
                <a href="#" class="flex items-center p-2 rounded-lg text-gray-600 hover:bg-gray-100">
                  <svg class="w-5 h-5 mr-3" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z"></path>
                    <circle cx="12" cy="12" r="3"></circle>
                  </svg>
                  Ayarlar
                </a>
              </li>
            </ul>
          </nav>
        </div>
      </div>

      <!-- Main Content Area -->
      <main class="flex-1 overflow-y-auto p-4 bg-gray-50">
        @Html.AntiForgeryToken()
        @RenderBody()
      </main>
    </div>
  </div>

  <!-- Bildirim Container -->
  <div id="notification-container" class="fixed bottom-4 right-4 z-50 space-y-2"></div>

  <script>
    // Mobile sidebar toggle
    const menuButton = document.getElementById('menuButton');
    const mobileSidebar = document.getElementById('mobileSidebar');
    const closeSidebarButton = document.getElementById('closeSidebarButton');
    const sidebarBackdrop = document.getElementById('sidebarBackdrop');

    menuButton.addEventListener('click', () => {
      mobileSidebar.classList.remove('hidden');
    });

    closeSidebarButton.addEventListener('click', () => {
      mobileSidebar.classList.add('hidden');
    });

    sidebarBackdrop.addEventListener('click', () => {
      mobileSidebar.classList.add('hidden');
    });

    // Profile dropdown toggle
    document.addEventListener('DOMContentLoaded', function() {
      const profileButton = document.getElementById('profileButton');
      const profileMenu = document.getElementById('profileMenu');

      if (profileButton && profileMenu) {
        // Toggle dropdown when clicking the profile button
        profileButton.addEventListener('click', function(e) {
          e.stopPropagation();
          profileMenu.classList.toggle('hidden');
        });

        // Keep dropdown open when clicking inside it
        profileMenu.addEventListener('click', function(e) {
          e.stopPropagation();
        });

        // Close dropdown when clicking outside
        document.addEventListener('click', function() {
          profileMenu.classList.add('hidden');
        });
      }
    });

    // Bildirim sistemi
    function showNotification(message, type = 'success', duration = 5000) {
      const container = document.getElementById('notification-container');
      const notification = document.createElement('div');

      // Bildirim türüne göre stil belirleme
      let bgColor, borderColor, textColor, icon;
      switch(type) {
        case 'success':
          bgColor = 'bg-green-50';
          borderColor = 'border-green-200';
          textColor = 'text-green-800';
          icon = `<svg class="w-5 h-5 text-green-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                  </svg>`;
          break;
        case 'error':
          bgColor = 'bg-red-50';
          borderColor = 'border-red-200';
          textColor = 'text-red-800';
          icon = `<svg class="w-5 h-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                  </svg>`;
          break;
        case 'warning':
          bgColor = 'bg-yellow-50';
          borderColor = 'border-yellow-200';
          textColor = 'text-yellow-800';
          icon = `<svg class="w-5 h-5 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                  </svg>`;
          break;
        case 'info':
          bgColor = 'bg-blue-50';
          borderColor = 'border-blue-200';
          textColor = 'text-blue-800';
          icon = `<svg class="w-5 h-5 text-blue-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                  </svg>`;
          break;
      }

      notification.className = `${bgColor} ${borderColor} ${textColor} border rounded-lg p-4 shadow-lg max-w-sm transform transition-all duration-300 ease-in-out translate-x-full opacity-0`;
      notification.innerHTML = `
        <div class="flex items-start">
          <div class="flex-shrink-0">
            ${icon}
          </div>
          <div class="ml-3 flex-1">
            <p class="text-sm font-medium">${message}</p>
          </div>
          <div class="ml-4 flex-shrink-0">
            <button class="inline-flex ${textColor} hover:${textColor.replace('800', '600')} focus:outline-none" onclick="this.parentElement.parentElement.parentElement.remove()">
              <svg class="w-4 h-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
              </svg>
            </button>
          </div>
        </div>
      `;

      container.appendChild(notification);

      // Animasyon ile göster
      setTimeout(() => {
        notification.classList.remove('translate-x-full', 'opacity-0');
        notification.classList.add('translate-x-0', 'opacity-100');
      }, 100);

      // Belirtilen süre sonra kaldır
      setTimeout(() => {
        notification.classList.add('translate-x-full', 'opacity-0');
        setTimeout(() => {
          if (notification.parentNode) {
            notification.remove();
          }
        }, 300);
      }, duration);
    }

    // Global olarak erişilebilir hale getir
    window.showNotification = showNotification;
  </script>
  <!-- jQuery -->
  <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>

  <!-- Axios -->
  <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
  <script src="https://unpkg.com/@@microsoft/signalr@latest/dist/browser/signalr.min.js"></script>
  <!-- Chart.js for beautiful charts -->
  <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.min.js"></script>
  <!-- CountUp.js for animated counters -->
  <script src="https://cdn.jsdelivr.net/npm/countup.js@2.8.0/dist/countUp.min.js"></script>  <!-- Site JS -->
  <script src="~/js/site.js"></script>

  @await RenderSectionAsync("Scripts", required: false)
  @await Html.PartialAsync("_SignalRConnectionPartial")
</body>
</html>