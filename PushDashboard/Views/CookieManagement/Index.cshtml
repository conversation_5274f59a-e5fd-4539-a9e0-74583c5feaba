@model PushDashboard.Models.CookieManagement

@{
    ViewData["Title"] = "Çerez Yönetimi";
}

<!-- Anti-forgery token for AJAX requests -->
@Html.AntiForgeryToken()

<main class="p-4 bg-gray-50 relative">
    <div class="mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Çerez Yönetimi</h2>
        <p class="text-gray-600">KVKK ve GDPR uyumlu çerez yönetimi sisteminizi yapılandırın</p>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Sol Panel - Ayarlar -->
        <div class="lg:col-span-2 space-y-6">
            <!-- Banner Ayarları -->
            <div class="bg-white rounded-lg shadow-sm p-6">
                <h3 class="text-lg font-semibold mb-4 flex items-center">
                    <svg class="w-5 h-5 mr-2 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2h4a1 1 0 110 2h-1v12a2 2 0 01-2 2H6a2 2 0 01-2-2V6H3a1 1 0 110-2h4z"></path>
                    </svg>
                    Banner Ayarları
                </h3>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="md:col-span-2">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Banner Başlığı</label>
                        <input type="text" id="bannerTitle" value="@Model.BannerTitle" 
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                    
                    <div class="md:col-span-2">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Banner Açıklaması</label>
                        <textarea id="bannerDescription" rows="3" 
                                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">@Model.BannerDescription</textarea>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Kabul Butonu Metni</label>
                        <input type="text" id="acceptButtonText" value="@Model.AcceptButtonText" 
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Reddet Butonu Metni</label>
                        <input type="text" id="rejectButtonText" value="@Model.RejectButtonText" 
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Ayarlar Butonu Metni</label>
                        <input type="text" id="settingsButtonText" value="@Model.SettingsButtonText" 
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Kaydet Butonu Metni</label>
                        <input type="text" id="saveButtonText" value="@Model.SaveButtonText" 
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                </div>
            </div>

            <!-- Tasarım Ayarları -->
            <div class="bg-white rounded-lg shadow-sm p-6">
                <h3 class="text-lg font-semibold mb-4 flex items-center">
                    <svg class="w-5 h-5 mr-2 text-purple-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM21 5a2 2 0 00-2-2h-4a2 2 0 00-2 2v12a4 4 0 004 4h4a2 2 0 002-2V5z"></path>
                    </svg>
                    Tasarım Ayarları
                </h3>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Banner Pozisyonu</label>
                        <select id="bannerPosition" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="bottom" selected="@(Model.BannerPosition == "bottom")">Alt</option>
                            <option value="top" selected="@(Model.BannerPosition == "top")">Üst</option>
                            <option value="left" selected="@(Model.BannerPosition == "left")">Sol</option>
                            <option value="right" selected="@(Model.BannerPosition == "right")">Sağ</option>
                        </select>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Köşe Yuvarlaklığı</label>
                        <input type="text" id="borderRadius" value="@Model.BorderRadius" 
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                               placeholder="8px">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Arka Plan Rengi</label>
                        <input type="color" id="bannerBackgroundColor" value="@Model.BannerBackgroundColor" 
                               class="w-full h-10 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Metin Rengi</label>
                        <input type="color" id="bannerTextColor" value="@Model.BannerTextColor" 
                               class="w-full h-10 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Kabul Butonu Rengi</label>
                        <input type="color" id="acceptButtonColor" value="@Model.AcceptButtonColor" 
                               class="w-full h-10 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Reddet Butonu Rengi</label>
                        <input type="color" id="rejectButtonColor" value="@Model.RejectButtonColor" 
                               class="w-full h-10 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Ayarlar Butonu Rengi</label>
                        <input type="color" id="settingsButtonColor" value="@Model.SettingsButtonColor" 
                               class="w-full h-10 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Çerez Süresi (Gün)</label>
                        <input type="number" id="cookieExpiryDays" value="@Model.CookieExpiryDays" min="1" max="3650"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                </div>
                
                <div class="mt-4 space-y-3">
                    <div class="flex items-center">
                        <input type="checkbox" id="showSettingsButton" @(Model.ShowSettingsButton ? "checked" : "") 
                               class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                        <label for="showSettingsButton" class="ml-2 text-sm text-gray-700">Ayarlar butonunu göster</label>
                    </div>
                    
                    <div class="flex items-center">
                        <input type="checkbox" id="enableAnimation" @(Model.EnableAnimation ? "checked" : "")
                               class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                        <label for="enableAnimation" class="ml-2 text-sm text-gray-700">Animasyonları etkinleştir</label>
                    </div>
                </div>
            </div>

            <!-- Çerez Kategorileri -->
            <div class="bg-white rounded-lg shadow-sm p-6">
                <h3 class="text-lg font-semibold mb-4 flex items-center">
                    <svg class="w-5 h-5 mr-2 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"></path>
                    </svg>
                    Çerez Kategorileri
                </h3>
                
                <div id="cookieCategories" class="space-y-4">
                    @foreach (var category in Model.Categories)
                    {
                        <div class="border border-gray-200 rounded-lg p-4" data-category-id="@category.Id">
                            <div class="flex items-center justify-between mb-2">
                                <div class="flex items-center">
                                    <h4 class="font-medium text-gray-900">@category.Name</h4>
                                    @if (category.IsRequired)
                                    {
                                        <span class="text-xs bg-red-100 text-red-800 px-2 py-1 rounded ml-2">Gerekli</span>
                                    }
                                </div>
                                <div class="flex items-center">
                                    @if (category.IsRequired)
                                    {
                                        <input type="checkbox" checked disabled class="h-4 w-4 text-blue-600 border-gray-300 rounded opacity-50 cursor-not-allowed">
                                    }
                                    else
                                    {
                                        <input type="checkbox" @(category.IsEnabled ? "checked" : "")
                                               class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded category-enabled">
                                    }
                                </div>
                            </div>
                            <div>
                                <label class="block text-sm text-gray-600 mb-1">Açıklama</label>
                                <textarea class="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 category-description"
                                          rows="2" @(category.IsRequired ? "readonly" : "")>@category.Description</textarea>
                            </div>
                        </div>
                    }
                </div>
            </div>

            <!-- Kaydet Butonu -->
            <div class="bg-white rounded-lg shadow-sm p-6">
                <button id="saveSettings" class="w-full bg-blue-600 text-white py-3 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 font-medium">
                    Ayarları Kaydet
                </button>
            </div>
        </div>

        <!-- Sağ Panel - Önizleme ve Script -->
        <div class="space-y-6">
            <!-- Önizleme -->
            <div class="bg-white rounded-lg shadow-sm p-6">
                <h3 class="text-lg font-semibold mb-4 flex items-center">
                    <svg class="w-5 h-5 mr-2 text-orange-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                    </svg>
                    Önizleme
                </h3>
                
                <div id="previewContainer" class="relative bg-gray-100 rounded-lg p-4 min-h-[350px] overflow-hidden">
                    <div class="text-center text-gray-500 py-8">
                        <p>Önizleme yükleniyor...</p>
                    </div>
                </div>
                
                <button id="refreshPreview" class="mt-3 w-full bg-gray-600 text-white py-2 px-4 rounded-md hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 text-sm">
                    Önizlemeyi Yenile
                </button>
            </div>

            <!-- Script URL -->
            <div class="bg-white rounded-lg shadow-sm p-6">
                <h3 class="text-lg font-semibold mb-4 flex items-center">
                    <svg class="w-5 h-5 mr-2 text-indigo-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4"></path>
                    </svg>
                    Script URL
                </h3>
                
                <div class="space-y-3">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Script Linki</label>
                        <div class="flex">
                            <input type="text" id="scriptUrl" value="@ViewData["ScriptUrl"]" readonly 
                                   class="flex-1 px-3 py-2 border border-gray-300 rounded-l-md bg-gray-50 text-sm">
                            <button id="copyScript" class="px-4 py-2 bg-indigo-600 text-white rounded-r-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                                Kopyala
                            </button>
                        </div>
                    </div>
                    
                    <div class="bg-gray-50 rounded-lg p-4">
                        <h4 class="text-sm font-medium text-gray-700 mb-2">Kullanım:</h4>
                        <code class="text-xs text-gray-600 block bg-white p-2 rounded border mb-3">
                            &lt;script src="@ViewData["ScriptUrl"]"&gt;&lt;/script&gt;
                        </code>

                        <h4 class="text-sm font-medium text-gray-700 mb-2">JavaScript Methodları:</h4>
                        <div class="bg-white rounded border p-3 text-xs">
                            <div class="space-y-2">
                                <div>
                                    <code class="text-blue-600 font-mono">CookieManagement.hasConsent('category')</code>
                                    <p class="text-gray-600 mt-1">Belirli kategori için onay kontrolü</p>
                                </div>
                                <div>
                                    <code class="text-blue-600 font-mono">CookieManagement.getPreferences()</code>
                                    <p class="text-gray-600 mt-1">Tüm çerez tercihlerini getirir</p>
                                </div>
                                <div>
                                    <code class="text-blue-600 font-mono">CookieManagement.openSettings()</code>
                                    <p class="text-gray-600 mt-1">Çerez ayarları modalını açar</p>
                                </div>
                                <div>
                                    <code class="text-blue-600 font-mono">window.addEventListener('cookieConsentChanged', fn)</code>
                                    <p class="text-gray-600 mt-1">Çerez tercihi değişikliklerini dinler</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</main>

@section Scripts {
    <script src="~/js/cookie-management.js"></script>
}
