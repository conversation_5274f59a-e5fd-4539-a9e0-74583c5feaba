@model PushDashboard.ViewModels.ChangePasswordViewModel

@{
    ViewData["Title"] = "<PERSON><PERSON><PERSON>";
}

<div class="container mx-auto px-4 py-8">
    <div class="max-w-3xl mx-auto">
        <h1 class="text-2xl font-bold mb-6"><PERSON><PERSON><PERSON></h1>
        
        @if (TempData["StatusMessage"] != null)
        {
            <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
                <span>@TempData["StatusMessage"]</span>
            </div>
        }
        
        <div class="bg-white rounded-lg shadow-sm p-6 mb-6">
            <form asp-action="ChangePassword" method="post">
                <div asp-validation-summary="ModelOnly" class="text-red-500 text-sm mb-4"></div>
                
                <div class="space-y-4">
                    <div>
                        <label asp-for="CurrentPassword" class="block text-sm font-medium text-gray-700 mb-1"></label>
                        <input asp-for="CurrentPassword" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                        <span asp-validation-for="CurrentPassword" class="text-red-500 text-sm"></span>
                    </div>
                    
                    <div>
                        <label asp-for="NewPassword" class="block text-sm font-medium text-gray-700 mb-1"></label>
                        <input asp-for="NewPassword" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                        <span asp-validation-for="NewPassword" class="text-red-500 text-sm"></span>
                    </div>
                    
                    <div>
                        <label asp-for="ConfirmPassword" class="block text-sm font-medium text-gray-700 mb-1"></label>
                        <input asp-for="ConfirmPassword" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                        <span asp-validation-for="ConfirmPassword" class="text-red-500 text-sm"></span>
                    </div>
                </div>
                
                <div class="mt-6 flex items-center justify-between">
                    <button type="submit" class="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark">
                        Şifremi Değiştir
                    </button>
                    
                    <a asp-action="Profile" class="text-primary hover:text-primary-dark">
                        Profil Bilgilerime Dön
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>

@section Scripts {
    @await Html.PartialAsync("_ValidationScriptsPartial")
}
