@model PushDashboard.Models.SocialProofSettings

<form id="socialProofForm" onsubmit="event.preventDefault(); updateSettings();" class="space-y-6">
    @Html.AntiForgeryToken()

    <!-- <PERSON><PERSON> -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex items-center space-x-2">
                <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                    <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    </svg>
                </div>
                <h3 class="text-lg font-semibold text-gray-900">Temel Ayarlar</h3>
            </div>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div class="md:col-span-1">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Modül Durumu</label>
                    <div class="flex items-center">
                        <button type="button"
                                class="relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 @(Model.IsActive ? "bg-blue-600" : "bg-gray-200")"
                                onclick="toggleSwitch(this, 'IsActive')"
                                role="switch"
                                aria-checked="@(Model.IsActive ? "true" : "false")">
                            <span class="pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out @(Model.IsActive ? "translate-x-5" : "translate-x-0")"></span>
                        </button>
                        <input type="hidden" name="IsActive" id="IsActive" value="@(Model.IsActive ? "true" : "false")">
                        <span class="ml-3 text-sm text-gray-600">@(Model.IsActive ? "Aktif" : "Pasif")</span>
                    </div>
                </div>
                <div>
                    <label for="UpdateInterval" class="block text-sm font-medium text-gray-700 mb-2">Güncelleme Sıklığı</label>
                    <div class="relative">
                        <input type="number"
                               class="block w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                               name="UpdateInterval"
                               id="UpdateInterval"
                               value="@Model.UpdateInterval"
                               min="30" max="300"
                               placeholder="60">
                        <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                            <span class="text-gray-500 sm:text-sm">saniye</span>
                        </div>
                    </div>
                </div>
                <div>
                    <label for="DisplayDuration" class="block text-sm font-medium text-gray-700 mb-2">Gösterim Süresi</label>
                    <div class="relative">
                        <input type="number"
                               class="block w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                               name="DisplayDuration"
                               id="DisplayDuration"
                               value="@Model.DisplayDuration"
                               min="3" max="15"
                               placeholder="5">
                        <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                            <span class="text-gray-500 sm:text-sm">saniye</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Sayı Aralıkları -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex items-center space-x-2">
                <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                    <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 20l4-16m2 16l4-16M6 9h14M4 15h14"></path>
                    </svg>
                </div>
                <h3 class="text-lg font-semibold text-gray-900">Sayı Aralıkları</h3>
            </div>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <!-- İnceleyenler -->
                <div class="space-y-4">
                    <div class="flex items-center space-x-2 mb-4">
                        <span class="text-2xl">👀</span>
                        <h4 class="text-base font-medium text-gray-900">İnceleyenler</h4>
                    </div>
                    <div class="grid grid-cols-2 gap-3">
                        <div>
                            <label for="ViewersMin" class="block text-sm font-medium text-gray-700 mb-1">Minimum</label>
                            <input type="number"
                                   class="block w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                                   name="ViewersMin"
                                   id="ViewersMin"
                                   value="@Model.ViewersMin"
                                   min="1" max="1000"
                                   placeholder="15">
                        </div>
                        <div>
                            <label for="ViewersMax" class="block text-sm font-medium text-gray-700 mb-1">Maksimum</label>
                            <input type="number"
                                   class="block w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                                   name="ViewersMax"
                                   id="ViewersMax"
                                   value="@Model.ViewersMax"
                                   min="1" max="1000"
                                   placeholder="45">
                        </div>
                    </div>
                </div>

                <!-- Takip Edenler -->
                <div class="space-y-4">
                    <div class="flex items-center space-x-2 mb-4">
                        <span class="text-2xl">❤️</span>
                        <h4 class="text-base font-medium text-gray-900">Takip Edenler</h4>
                    </div>
                    <div class="grid grid-cols-2 gap-3">
                        <div>
                            <label for="FollowersMin" class="block text-sm font-medium text-gray-700 mb-1">Minimum</label>
                            <input type="number"
                                   class="block w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                                   name="FollowersMin"
                                   id="FollowersMin"
                                   value="@Model.FollowersMin"
                                   min="1" max="1000"
                                   placeholder="5">
                        </div>
                        <div>
                            <label for="FollowersMax" class="block text-sm font-medium text-gray-700 mb-1">Maksimum</label>
                            <input type="number"
                                   class="block w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                                   name="FollowersMax"
                                   id="FollowersMax"
                                   value="@Model.FollowersMax"
                                   min="1" max="1000"
                                   placeholder="20">
                        </div>
                    </div>
                </div>

                <!-- Satın Alanlar -->
                <div class="space-y-4">
                    <div class="flex items-center space-x-2 mb-4">
                        <span class="text-2xl">🛒</span>
                        <h4 class="text-base font-medium text-gray-900">Satın Alanlar</h4>
                    </div>
                    <div class="grid grid-cols-2 gap-3">
                        <div>
                            <label for="BuyersMin" class="block text-sm font-medium text-gray-700 mb-1">Minimum</label>
                            <input type="number"
                                   class="block w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                                   name="BuyersMin"
                                   id="BuyersMin"
                                   value="@Model.BuyersMin"
                                   min="1" max="1000"
                                   placeholder="2">
                        </div>
                        <div>
                            <label for="BuyersMax" class="block text-sm font-medium text-gray-700 mb-1">Maksimum</label>
                            <input type="number"
                                   class="block w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                                   name="BuyersMax"
                                   id="BuyersMax"
                                   value="@Model.BuyersMax"
                                   min="1" max="1000"
                                   placeholder="8">
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Metin Şablonları -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex items-center space-x-2">
                <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                    <svg class="w-4 h-4 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z"></path>
                    </svg>
                </div>
                <h3 class="text-lg font-semibold text-gray-900">Metin Şablonları</h3>
            </div>
        </div>
        <div class="p-6">
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                <div class="flex items-start space-x-3">
                    <div class="flex-shrink-0">
                        <svg class="w-5 h-5 text-blue-600 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <div>
                        <h4 class="text-sm font-medium text-blue-900">Önemli Not</h4>
                        <p class="text-sm text-blue-700 mt-1">
                            Metinlerde <code class="bg-blue-100 px-1 py-0.5 rounded text-blue-800">{count}</code> placeholder'ını kullanarak dinamik sayıları gösterebilirsiniz.
                        </p>
                    </div>
                </div>
            </div>

            <div class="space-y-6">
                <div>
                    <label for="ViewersTemplate" class="block text-sm font-medium text-gray-700 mb-2">
                        <span class="flex items-center space-x-2">
                            <span>👀</span>
                            <span>İnceleyenler Metni</span>
                        </span>
                    </label>
                    <input type="text"
                           class="block w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                           name="ViewersTemplate"
                           id="ViewersTemplate"
                           value="@Model.TextTemplates.ViewersTemplate"
                           placeholder="{count} kişi şu anda bu ürünü inceliyor">
                </div>

                <div>
                    <label for="FollowersTemplate" class="block text-sm font-medium text-gray-700 mb-2">
                        <span class="flex items-center space-x-2">
                            <span>❤️</span>
                            <span>Takip Edenler Metni</span>
                        </span>
                    </label>
                    <input type="text"
                           class="block w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                           name="FollowersTemplate"
                           id="FollowersTemplate"
                           value="@Model.TextTemplates.FollowersTemplate"
                           placeholder="{count} kişi bu ürünü takip ediyor">
                </div>

                <div>
                    <label for="BuyersTemplate" class="block text-sm font-medium text-gray-700 mb-2">
                        <span class="flex items-center space-x-2">
                            <span>🛒</span>
                            <span>Satın Alanlar Metni</span>
                        </span>
                    </label>
                    <input type="text"
                           class="block w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                           name="BuyersTemplate"
                           id="BuyersTemplate"
                           value="@Model.TextTemplates.BuyersTemplate"
                           placeholder="{count} kişi satın almaya hazırlanıyor">
                </div>
            </div>
        </div>
    </div>

    <!-- Görsel Ayarlar -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex items-center space-x-2">
                <div class="w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center">
                    <svg class="w-4 h-4 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM21 5a2 2 0 00-2-2h-4a2 2 0 00-2 2v12a4 4 0 004 4h4a2 2 0 002-2V5z"></path>
                    </svg>
                </div>
                <h3 class="text-lg font-semibold text-gray-900">Görsel Ayarlar</h3>
            </div>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
                <div>
                    <label for="Position" class="block text-sm font-medium text-gray-700 mb-2">Pozisyon</label>
                    <select class="block w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                            name="Position" id="Position">
                        <option value="bottom-left" selected="@(Model.DisplaySettings.Position == "bottom-left")">Sol Alt</option>
                        <option value="bottom-right" selected="@(Model.DisplaySettings.Position == "bottom-right")">Sağ Alt</option>
                        <option value="top-left" selected="@(Model.DisplaySettings.Position == "top-left")">Sol Üst</option>
                        <option value="top-right" selected="@(Model.DisplaySettings.Position == "top-right")">Sağ Üst</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Canlı Önizleme</label>
                    <div id="colorPreview"
                         class="border-2 rounded-lg p-4 text-center font-medium transition-all duration-200"
                         style="background-color: @Model.DisplaySettings.BackgroundColor;
                                color: @Model.DisplaySettings.TextColor;
                                border-color: @Model.DisplaySettings.PrimaryColor;">
                        25 kişi şu anda bu ürünü inceliyor
                    </div>
                </div>
            </div>

            <!-- Renk Ayarları -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                <div>
                    <label for="PrimaryColor" class="block text-sm font-medium text-gray-700 mb-2">Ana Renk</label>
                    <div class="flex items-center space-x-3">
                        <input type="color"
                               class="h-10 w-16 rounded-lg border border-gray-300 cursor-pointer"
                               name="PrimaryColor"
                               id="PrimaryColor"
                               value="@Model.DisplaySettings.PrimaryColor">
                        <input type="text"
                               class="flex-1 rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                               value="@Model.DisplaySettings.PrimaryColor"
                               readonly>
                    </div>
                </div>
                <div>
                    <label for="BackgroundColor" class="block text-sm font-medium text-gray-700 mb-2">Arka Plan Rengi</label>
                    <div class="flex items-center space-x-3">
                        <input type="color"
                               class="h-10 w-16 rounded-lg border border-gray-300 cursor-pointer"
                               name="BackgroundColor"
                               id="BackgroundColor"
                               value="@Model.DisplaySettings.BackgroundColor">
                        <input type="text"
                               class="flex-1 rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                               value="@Model.DisplaySettings.BackgroundColor"
                               readonly>
                    </div>
                </div>
                <div>
                    <label for="TextColor" class="block text-sm font-medium text-gray-700 mb-2">Metin Rengi</label>
                    <div class="flex items-center space-x-3">
                        <input type="color"
                               class="h-10 w-16 rounded-lg border border-gray-300 cursor-pointer"
                               name="TextColor"
                               id="TextColor"
                               value="@Model.DisplaySettings.TextColor">
                        <input type="text"
                               class="flex-1 rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                               value="@Model.DisplaySettings.TextColor"
                               readonly>
                    </div>
                </div>
            </div>

            <!-- Diğer Ayarlar -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                    <label for="BorderRadius" class="block text-sm font-medium text-gray-700 mb-2">Köşe Yuvarlaklığı</label>
                    <div class="relative">
                        <input type="number"
                               class="block w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                               name="BorderRadius"
                               id="BorderRadius"
                               value="@Model.DisplaySettings.BorderRadius"
                               min="0" max="50"
                               placeholder="8">
                        <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                            <span class="text-gray-500 sm:text-sm">px</span>
                        </div>
                    </div>
                </div>
                <div>
                    <label for="FontSize" class="block text-sm font-medium text-gray-700 mb-2">Font Boyutu</label>
                    <div class="relative">
                        <input type="number"
                               class="block w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                               name="FontSize"
                               id="FontSize"
                               value="@Model.DisplaySettings.FontSize"
                               min="10" max="24"
                               placeholder="14">
                        <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                            <span class="text-gray-500 sm:text-sm">px</span>
                        </div>
                    </div>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Efektler</label>
                    <div class="space-y-3">
                        <div class="flex items-center">
                            <input type="hidden" name="EnableAnimation" value="false">
                            <input type="checkbox"
                                   class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                   name="EnableAnimation"
                                   id="EnableAnimation"
                                   value="true"
                                   @(Model.DisplaySettings.EnableAnimation ? "checked" : "")>
                            <label for="EnableAnimation" class="ml-2 block text-sm text-gray-700">
                                Animasyon Efekti
                            </label>
                        </div>
                        <div class="flex items-center">
                            <input type="hidden" name="EnableShadow" value="false">
                            <input type="checkbox"
                                   class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                   name="EnableShadow"
                                   id="EnableShadow"
                                   value="true"
                                   @(Model.DisplaySettings.EnableShadow ? "checked" : "")>
                            <label for="EnableShadow" class="ml-2 block text-sm text-gray-700">
                                Gölge Efekti
                            </label>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Kaydet Butonu -->
    <div class="flex justify-end">
        <button type="submit"
                class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-lg shadow-sm text-white bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200"
                id="submitBtn">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3-3m0 0l-3 3m3-3v12"></path>
            </svg>
            Ayarları Kaydet
        </button>
    </div>
</form>
