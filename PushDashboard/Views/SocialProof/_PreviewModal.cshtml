<!-- <PERSON><PERSON><PERSON><PERSON> -->
<div id="previewModal" class="fixed inset-0 z-50 hidden overflow-y-auto">
    <div class="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
        <!-- Overlay -->
        <div class="fixed inset-0 transition-opacity bg-gray-500 bg-opacity-75" onclick="closePreviewModal()"></div>

        <!-- Modal -->
        <div class="inline-block w-full max-w-4xl p-6 my-8 overflow-hidden text-left align-middle transition-all transform bg-white shadow-xl rounded-2xl">
            <!-- Header -->
            <div class="flex items-center justify-between mb-6">
                <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                        <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900">Social Proof Widget Önizlemesi</h3>
                </div>
                <button type="button"
                        class="text-gray-400 hover:text-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500 rounded-lg p-1"
                        onclick="closePreviewModal()">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>

            <!-- Info -->
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                <div class="flex items-start space-x-3">
                    <svg class="w-5 h-5 text-blue-600 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    <p class="text-sm text-blue-700">
                        Bu önizleme widget'ın sitenizde nasıl görüneceğini gösterir. Gerçek sitede sayılar otomatik olarak değişecektir.
                    </p>
                </div>
            </div>

            <!-- Önizleme Alanı -->
            <div id="previewArea" class="relative bg-gray-100 border-2 border-dashed border-gray-300 rounded-xl" style="height: 400px; overflow: hidden;">
                <div class="absolute inset-0 flex items-center justify-center text-gray-500">
                    <div class="text-center">
                        <svg class="w-16 h-16 mx-auto mb-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9"></path>
                        </svg>
                        <div class="text-lg font-medium">Ürün Sayfası Simülasyonu</div>
                    </div>
                </div>

                <!-- Widget burada görünecek -->
                <div id="previewWidget" class="absolute" style="display: none;">
                    <!-- JavaScript ile doldurulacak -->
                </div>
            </div>

            <!-- Kontroller -->
            <div class="flex items-center justify-between mt-6">
                <div class="flex items-center space-x-3">
                    <button type="button"
                            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                            onclick="startPreview()">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        Önizlemeyi Başlat
                    </button>
                    <button type="button"
                            class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                            onclick="stopPreview()">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        Durdur
                    </button>
                </div>

                <div class="flex items-center space-x-2">
                    <label class="inline-flex items-center">
                        <input type="radio" name="previewType" value="viewers" checked class="form-radio h-4 w-4 text-blue-600">
                        <span class="ml-2 text-sm">👀 İnceleyenler</span>
                    </label>
                    <label class="inline-flex items-center">
                        <input type="radio" name="previewType" value="followers" class="form-radio h-4 w-4 text-blue-600">
                        <span class="ml-2 text-sm">❤️ Takip Edenler</span>
                    </label>
                    <label class="inline-flex items-center">
                        <input type="radio" name="previewType" value="buyers" class="form-radio h-4 w-4 text-blue-600">
                        <span class="ml-2 text-sm">🛒 Satın Alanlar</span>
                    </label>
                </div>
            </div>

            <!-- Footer -->
            <div class="flex justify-end mt-6 pt-6 border-t border-gray-200">
                <button type="button"
                        class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                        onclick="closePreviewModal()">
                    Kapat
                </button>
            </div>
        </div>
    </div>
</div>

<script>
    let previewConfig = null;
    let previewTimer = null;
    let currentPreviewType = 'viewers';

    function showPreviewModal(config, scriptUrl) {
        previewConfig = config;

        // Modal'ı göster
        const modal = document.getElementById('previewModal');
        modal.classList.remove('hidden');

        // Önizlemeyi başlat
        setTimeout(() => {
            startPreview();
        }, 500);
    }

    function closePreviewModal() {
        const modal = document.getElementById('previewModal');
        modal.classList.add('hidden');
        stopPreview();
    }

    function startPreview() {
        if (!previewConfig || !previewConfig.isActive) {
            showNotification('Modül aktif değil!', 'warning');
            return;
        }
        
        stopPreview(); // Önceki timer'ı temizle
        
        const previewArea = document.getElementById('previewArea');
        const previewWidget = document.getElementById('previewWidget');
        
        // Widget'ı oluştur
        updatePreviewWidget();
        
        // Periyodik güncelleme
        previewTimer = setInterval(() => {
            updatePreviewWidget();
        }, 3000); // 3 saniyede bir güncelle (demo için hızlı)
    }

    function stopPreview() {
        if (previewTimer) {
            clearInterval(previewTimer);
            previewTimer = null;
        }
        
        const previewWidget = document.getElementById('previewWidget');
        previewWidget.style.display = 'none';
    }

    function updatePreviewWidget() {
        const previewWidget = document.getElementById('previewWidget');
        const selectedType = document.querySelector('input[name="previewType"]:checked').value;
        
        let config, template;
        
        switch(selectedType) {
            case 'viewers':
                config = { min: previewConfig.viewersMin, max: previewConfig.viewersMax };
                template = previewConfig.textTemplates.viewersTemplate;
                break;
            case 'followers':
                config = { min: previewConfig.followersMin, max: previewConfig.followersMax };
                template = previewConfig.textTemplates.followersTemplate;
                break;
            case 'buyers':
                config = { min: previewConfig.buyersMin, max: previewConfig.buyersMax };
                template = previewConfig.textTemplates.buyersTemplate;
                break;
        }
        
        // Rastgele sayı üret
        const count = Math.floor(Math.random() * (config.max - config.min + 1)) + config.min;
        const text = template.replace('{count}', count);
        
        // Widget stilini uygula
        const displaySettings = previewConfig.displaySettings;
        
        previewWidget.innerHTML = text;
        previewWidget.className = `position-absolute ${displaySettings.position}`;
        previewWidget.style.cssText = `
            display: block;
            padding: 12px 16px;
            background-color: ${displaySettings.backgroundColor};
            color: ${displaySettings.textColor};
            border-radius: ${displaySettings.borderRadius}px;
            font-size: ${displaySettings.fontSize}px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 300px;
            word-wrap: break-word;
            transition: all 0.3s ease;
            ${displaySettings.enableShadow ? 'box-shadow: 0 4px 12px rgba(0,0,0,0.15);' : ''}
            z-index: 10;
        `;
        
        // Pozisyon ayarla
        switch(displaySettings.position) {
            case 'bottom-left':
                previewWidget.style.bottom = '20px';
                previewWidget.style.left = '20px';
                break;
            case 'bottom-right':
                previewWidget.style.bottom = '20px';
                previewWidget.style.right = '20px';
                break;
            case 'top-left':
                previewWidget.style.top = '20px';
                previewWidget.style.left = '20px';
                break;
            case 'top-right':
                previewWidget.style.top = '20px';
                previewWidget.style.right = '20px';
                break;
        }
        
        // Animasyon efekti
        if (displaySettings.enableAnimation) {
            previewWidget.style.opacity = '0';
            previewWidget.style.transform = 'translateY(20px)';
            
            setTimeout(() => {
                previewWidget.style.opacity = '1';
                previewWidget.style.transform = 'translateY(0)';
            }, 100);
        }
    }

    // Tip değiştiğinde önizlemeyi güncelle
    document.addEventListener('DOMContentLoaded', function() {
        document.querySelectorAll('input[name="previewType"]').forEach(radio => {
            radio.addEventListener('change', function() {
                if (previewTimer) {
                    updatePreviewWidget();
                }
            });
        });
    });

    // ESC tuşu ile modal'ı kapat
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            const modal = document.getElementById('previewModal');
            if (!modal.classList.contains('hidden')) {
                closePreviewModal();
            }
        }
    });
</script>
