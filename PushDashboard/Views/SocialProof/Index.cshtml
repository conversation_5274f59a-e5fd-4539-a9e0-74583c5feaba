@model PushDashboard.ViewModels.SocialProof.SocialProofIndexViewModel
@{
    ViewData["Title"] = "Social Proof Modülü";
}

<div class="min-h-screen bg-gray-50 p-6">
    <div class="max-w-7xl mx-auto">
        <!-- Header -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 mb-6">
            <div class="px-6 py-4 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                        <div class="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                            </svg>
                        </div>
                        <div>
                            <h1 class="text-2xl font-bold text-gray-900">Social Proof Modülü</h1>
                            <p class="text-sm text-gray-500">Ürün sayfalarında sosyal kanıt göstererek satışları artırın</p>
                        </div>
                    </div>
                    <div class="flex items-center space-x-3">
                        <button type="button"
                                class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
                                onclick="previewWidget()">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                            </svg>
                            Önizleme
                        </button>
                        <button type="button"
                                class="inline-flex items-center px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200 @(Model.Settings.IsActive ? "bg-green-100 text-green-800 hover:bg-green-200" : "bg-gray-100 text-gray-800 hover:bg-gray-200")"
                                onclick="toggleStatus()" id="statusToggleBtn">
                            <div class="w-2 h-2 rounded-full mr-2 @(Model.Settings.IsActive ? "bg-green-500" : "bg-gray-400")"></div>
                            @(Model.Settings.IsActive ? "Aktif" : "Pasif")
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- Sol Panel - Ayarlar -->
            <div class="lg:col-span-2">
                @await Html.PartialAsync("_SettingsForm", Model.Settings)
            </div>

            <!-- Sağ Panel - Script ve Bilgi -->
            <div class="lg:col-span-1">
                @await Html.PartialAsync("_ScriptInfo", Model)
            </div>
        </div>
    </div>
</div>

<!-- Önizleme Modal -->
@await Html.PartialAsync("_PreviewModal")

@section Scripts {
    <script>
        // Global değişkenler
        let isUpdating = false;

        // Toggle switch fonksiyonu
        function toggleSwitch(button, inputId) {
            const input = document.getElementById(inputId);
            const isActive = input.value === 'true';
            const newValue = !isActive;

            // Hidden input değerini güncelle
            input.value = newValue.toString();

            // Button görünümünü güncelle
            if (newValue) {
                button.classList.remove('bg-gray-200');
                button.classList.add('bg-blue-600');
                button.setAttribute('aria-checked', 'true');
                button.querySelector('span').classList.remove('translate-x-0');
                button.querySelector('span').classList.add('translate-x-5');
            } else {
                button.classList.remove('bg-blue-600');
                button.classList.add('bg-gray-200');
                button.setAttribute('aria-checked', 'false');
                button.querySelector('span').classList.remove('translate-x-5');
                button.querySelector('span').classList.add('translate-x-0');
            }

            // Yanındaki metni güncelle
            const statusText = button.nextElementSibling.nextElementSibling;
            if (statusText) {
                statusText.textContent = newValue ? 'Aktif' : 'Pasif';
            }
        }

        // Ayarları güncelle
        async function updateSettings() {
            if (isUpdating) return;

            const form = document.getElementById('socialProofForm');
            const formData = new FormData(form);

            // Form verilerini JSON'a çevir
            const data = {};

            // IsActive için özel işlem (hidden input)
            const isActiveInput = form.querySelector('input[name="IsActive"]');
            data.IsActive = isActiveInput ? isActiveInput.value === 'true' : false;

            // Checkbox'lar için özel işlem
            const checkboxes = ['EnableAnimation', 'EnableShadow'];
            checkboxes.forEach(name => {
                const checkbox = form.querySelector(`input[name="${name}"][type="checkbox"]`);
                data[name] = checkbox ? checkbox.checked : false;
            });

            // Diğer alanlar için normal işlem
            for (let [key, value] of formData.entries()) {
                // IsActive ve checkbox'ları atla, zaten yukarıda işledik
                if (key === 'IsActive' || checkboxes.includes(key)) continue;

                if (!isNaN(value) && value !== '') {
                    data[key] = parseInt(value);
                } else {
                    data[key] = value;
                }
            }

            isUpdating = true;
            const submitBtn = document.getElementById('submitBtn');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = `
                <svg class="animate-spin w-5 h-5 mr-2" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Kaydediliyor...
            `;
            submitBtn.disabled = true;

            try {
                const response = await fetch('@Url.Action("UpdateSettings")', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]').value
                    },
                    body: JSON.stringify(data)
                });

                const result = await response.json();

                if (result.success) {
                    showNotification('Ayarlar başarıyla güncellendi!', 'success');
                } else {
                    showNotification(result.message || 'Bir hata oluştu!', 'error');
                }
            } catch (error) {
                console.error('Error:', error);
                showNotification('Bir hata oluştu!', 'error');
            } finally {
                isUpdating = false;
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            }
        }

        // Durumu değiştir
        async function toggleStatus() {
            try {
                const response = await fetch('@Url.Action("ToggleStatus")', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]').value
                    }
                });
                
                const result = await response.json();
                
                if (result.success) {
                    location.reload(); // Sayfayı yenile
                } else {
                    showNotification(result.message || 'Bir hata oluştu!', 'error');
                }
            } catch (error) {
                console.error('Error:', error);
                showNotification('Bir hata oluştu!', 'error');
            }
        }

        // Önizleme
        async function previewWidget() {
            try {
                const response = await fetch('@Url.Action("Preview")');
                const result = await response.json();
                
                if (result.success) {
                    showPreviewModal(result.config, result.scriptUrl);
                } else {
                    showNotification(result.message || 'Önizleme yüklenemedi!', 'error');
                }
            } catch (error) {
                console.error('Error:', error);
                showNotification('Önizleme yüklenemedi!', 'error');
            }
        }

        // Bildirim göster
        function showNotification(message, type = 'info') {
            const toast = document.createElement('div');
            const bgColor = type === 'success' ? 'bg-green-500' : type === 'error' ? 'bg-red-500' : 'bg-blue-500';
            const icon = type === 'success' ?
                '<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path></svg>' :
                type === 'error' ?
                '<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path></svg>' :
                '<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>';

            toast.className = `fixed top-4 right-4 z-50 flex items-center p-4 text-white ${bgColor} rounded-lg shadow-lg transform transition-all duration-300 translate-x-full`;
            toast.innerHTML = `
                <div class="flex items-center">
                    ${icon}
                    <span class="ml-2">${message}</span>
                </div>
                <button type="button" class="ml-4 text-white hover:text-gray-200" onclick="this.parentElement.remove()">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            `;

            document.body.appendChild(toast);

            // Animasyon için kısa gecikme
            setTimeout(() => {
                toast.classList.remove('translate-x-full');
            }, 100);

            // 5 saniye sonra otomatik kaldır
            setTimeout(() => {
                toast.classList.add('translate-x-full');
                setTimeout(() => {
                    if (toast.parentElement) {
                        toast.remove();
                    }
                }, 300);
            }, 5000);
        }

        // Script URL'yi kopyala
        function copyScriptUrl() {
            const scriptUrl = document.getElementById('scriptUrl');
            scriptUrl.select();
            document.execCommand('copy');
            showNotification('Script URL kopyalandı!', 'success');
        }

        // Renk değişikliklerini canlı önizleme
        function updateColorPreview() {
            const primaryColor = document.getElementById('PrimaryColor').value;
            const backgroundColor = document.getElementById('BackgroundColor').value;
            const textColor = document.getElementById('TextColor').value;

            const preview = document.getElementById('colorPreview');
            if (preview) {
                preview.style.backgroundColor = backgroundColor;
                preview.style.color = textColor;
                preview.style.borderColor = primaryColor;
            }

            // Renk input'larının yanındaki text input'ları güncelle
            const primaryText = document.querySelector('input[value="' + primaryColor + '"][readonly]');
            const backgroundText = document.querySelector('input[value="' + backgroundColor + '"][readonly]');
            const textColorText = document.querySelector('input[value="' + textColor + '"][readonly]');

            if (primaryText) primaryText.value = primaryColor;
            if (backgroundText) backgroundText.value = backgroundColor;
            if (textColorText) textColorText.value = textColor;
        }

        // Sayfa yüklendiğinde
        document.addEventListener('DOMContentLoaded', function() {
            // Renk inputlarına event listener ekle
            ['PrimaryColor', 'BackgroundColor', 'TextColor'].forEach(id => {
                const element = document.getElementById(id);
                if (element) {
                    element.addEventListener('input', function() {
                        updateColorPreview();
                        // Yanındaki text input'u güncelle
                        const textInput = this.parentElement.querySelector('input[readonly]');
                        if (textInput) {
                            textInput.value = this.value;
                        }
                    });
                }
            });

            // İlk renk önizlemesini göster
            updateColorPreview();
        });
    </script>
}
