@model PushDashboard.ViewModels.SocialProof.SocialProofIndexViewModel

<!-- Script Bilgileri -->
<div class="bg-white rounded-xl shadow-sm border border-gray-200 mb-6">
    <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex items-center space-x-2">
            <div class="w-8 h-8 bg-indigo-100 rounded-lg flex items-center justify-center">
                <svg class="w-4 h-4 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4"></path>
                </svg>
            </div>
            <h3 class="text-lg font-semibold text-gray-900">Script Entegrasyonu</h3>
        </div>
    </div>
    <div class="p-6">
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
            <div class="flex items-start space-x-3">
                <div class="flex-shrink-0">
                    <svg class="w-5 h-5 text-blue-600 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <div>
                    <h4 class="text-sm font-medium text-blue-900">Kurulum Talimatı</h4>
                    <p class="text-sm text-blue-700 mt-1">
                        Aşağıdaki script kodunu sitenizin <code class="bg-blue-100 px-1 py-0.5 rounded text-blue-800">&lt;head&gt;</code> bölümüne ekleyin.
                    </p>
                </div>
            </div>
        </div>

        <div class="space-y-4">
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Script URL</label>
                <div class="flex rounded-lg shadow-sm">
                    <input type="text"
                           class="flex-1 rounded-l-lg border-gray-300 focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                           id="scriptUrl"
                           value="@Model.ScriptUrl"
                           readonly>
                    <button type="button"
                            class="inline-flex items-center px-3 py-2 border border-l-0 border-gray-300 rounded-r-lg bg-gray-50 text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                            onclick="copyScriptUrl()">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                        </svg>
                    </button>
                </div>
            </div>

            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">HTML Kodu</label>
                <div class="relative">
                    <textarea class="block w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                              rows="3"
                              readonly
                              id="htmlCode">&lt;script src="@Model.ScriptUrl" async&gt;&lt;/script&gt;</textarea>
                    <button type="button"
                            class="absolute top-2 right-2 inline-flex items-center px-2 py-1 border border-gray-300 rounded text-xs font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
                            onclick="copyHtmlCode()">
                        <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                        </svg>
                        Kopyala
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Durum Bilgileri -->
<div class="bg-white rounded-xl shadow-sm border border-gray-200 mb-6">
    <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex items-center space-x-2">
            <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
            </div>
            <h3 class="text-lg font-semibold text-gray-900">Durum Bilgileri</h3>
        </div>
    </div>
    <div class="p-6">
        <div class="space-y-4">
            <div class="flex items-center justify-between py-2">
                <span class="text-sm font-medium text-gray-700">Modül Durumu</span>
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium @(Model.Settings.IsActive ? "bg-green-100 text-green-800" : "bg-gray-100 text-gray-800")">
                    <div class="w-1.5 h-1.5 rounded-full mr-1.5 @(Model.Settings.IsActive ? "bg-green-400" : "bg-gray-400")"></div>
                    @(Model.Settings.IsActive ? "Aktif" : "Pasif")
                </span>
            </div>
            <div class="flex items-center justify-between py-2 border-t border-gray-100">
                <span class="text-sm font-medium text-gray-700">Son Güncelleme</span>
                <span class="text-sm text-gray-500">@Model.Settings.UpdatedAt.ToString("dd.MM.yyyy HH:mm")</span>
            </div>
            <div class="flex items-center justify-between py-2 border-t border-gray-100">
                <span class="text-sm font-medium text-gray-700">Güncelleyen</span>
                <span class="text-sm text-gray-500">@Model.Settings.UpdatedByUser?.FullName</span>
            </div>
        </div>
    </div>
</div>

<!-- Mevcut Ayarlar -->
<div class="bg-white rounded-xl shadow-sm border border-gray-200 mb-6">
    <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex items-center space-x-2">
            <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2-2V7a2 2 0 012-2h2a2 2 0 002 2v2a2 2 0 002 2h2a2 2 0 002-2V7a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 00-2 2h-2a2 2 0 00-2 2v6a2 2 0 01-2 2H9z"></path>
                </svg>
            </div>
            <h3 class="text-lg font-semibold text-gray-900">Mevcut Ayarlar</h3>
        </div>
    </div>
    <div class="p-6">
        <!-- Sayı Aralıkları -->
        <div class="grid grid-cols-3 gap-4 mb-6">
            <div class="bg-gray-50 rounded-lg p-4 text-center">
                <div class="text-2xl mb-2">👀</div>
                <div class="text-xs text-gray-500 mb-1">İnceleyenler</div>
                <div class="text-lg font-bold text-gray-900">@<EMAIL></div>
            </div>
            <div class="bg-gray-50 rounded-lg p-4 text-center">
                <div class="text-2xl mb-2">❤️</div>
                <div class="text-xs text-gray-500 mb-1">Takip Edenler</div>
                <div class="text-lg font-bold text-gray-900">@<EMAIL></div>
            </div>
            <div class="bg-gray-50 rounded-lg p-4 text-center">
                <div class="text-2xl mb-2">🛒</div>
                <div class="text-xs text-gray-500 mb-1">Satın Alanlar</div>
                <div class="text-lg font-bold text-gray-900">@<EMAIL></div>
            </div>
        </div>

        <!-- Zaman Ayarları -->
        <div class="border-t border-gray-200 pt-4">
            <div class="grid grid-cols-2 gap-4">
                <div>
                    <div class="text-sm text-gray-500">Güncelleme Sıklığı</div>
                    <div class="text-base font-semibold text-gray-900">@Model.Settings.UpdateInterval saniye</div>
                </div>
                <div>
                    <div class="text-sm text-gray-500">Gösterim Süresi</div>
                    <div class="text-base font-semibold text-gray-900">@Model.Settings.DisplayDuration saniye</div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Yardım -->
<div class="bg-white rounded-xl shadow-sm border border-gray-200">
    <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex items-center space-x-2">
            <div class="w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center">
                <svg class="w-4 h-4 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
            </div>
            <h3 class="text-lg font-semibold text-gray-900">Yardım</h3>
        </div>
    </div>
    <div class="p-6">
        <div class="space-y-4">
            <!-- Nasıl Kurulur -->
            <div class="border border-gray-200 rounded-lg">
                <button type="button"
                        class="w-full px-4 py-3 text-left flex items-center justify-between focus:outline-none focus:ring-2 focus:ring-blue-500 rounded-lg"
                        onclick="toggleAccordion('help1')">
                    <span class="font-medium text-gray-900">Nasıl Kurulur?</span>
                    <svg class="w-5 h-5 text-gray-500 transform transition-transform duration-200" id="help1-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                    </svg>
                </button>
                <div id="help1" class="hidden px-4 pb-3">
                    <ol class="list-decimal list-inside space-y-2 text-sm text-gray-600">
                        <li>Yukarıdaki HTML kodunu kopyalayın</li>
                        <li>Ticimax admin paneline giriş yapın</li>
                        <li>Tasarım → Tema Düzenle → Header bölümüne gidin</li>
                        <li>Kodu <code class="bg-gray-100 px-1 py-0.5 rounded text-gray-800">&lt;/head&gt;</code> etiketinden önce yapıştırın</li>
                        <li>Değişiklikleri kaydedin</li>
                    </ol>
                </div>
            </div>

            <!-- Nasıl Çalışır -->
            <div class="border border-gray-200 rounded-lg">
                <button type="button"
                        class="w-full px-4 py-3 text-left flex items-center justify-between focus:outline-none focus:ring-2 focus:ring-blue-500 rounded-lg"
                        onclick="toggleAccordion('help2')">
                    <span class="font-medium text-gray-900">Nasıl Çalışır?</span>
                    <svg class="w-5 h-5 text-gray-500 transform transition-transform duration-200" id="help2-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                    </svg>
                </button>
                <div id="help2" class="hidden px-4 pb-3">
                    <ul class="list-disc list-inside space-y-2 text-sm text-gray-600">
                        <li>Widget ürün sayfalarında otomatik olarak görünür</li>
                        <li>Belirlediğiniz aralıklarda rastgele sayılar gösterir</li>
                        <li>Her güncelleme sıklığında farklı mesaj türü gösterir</li>
                        <li>Mobil cihazlarda responsive olarak çalışır</li>
                    </ul>
                </div>
            </div>

            <!-- Sorun Giderme -->
            <div class="border border-gray-200 rounded-lg">
                <button type="button"
                        class="w-full px-4 py-3 text-left flex items-center justify-between focus:outline-none focus:ring-2 focus:ring-blue-500 rounded-lg"
                        onclick="toggleAccordion('help3')">
                    <span class="font-medium text-gray-900">Sorun Giderme</span>
                    <svg class="w-5 h-5 text-gray-500 transform transition-transform duration-200" id="help3-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                    </svg>
                </button>
                <div id="help3" class="hidden px-4 pb-3">
                    <ul class="list-disc list-inside space-y-2 text-sm text-gray-600">
                        <li><strong>Widget görünmüyor:</strong> Modülün aktif olduğundan emin olun</li>
                        <li><strong>Yanlış renkler:</strong> Tarayıcı cache'ini temizleyin</li>
                        <li><strong>Mobilde sorun:</strong> Responsive ayarları kontrol edin</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    function copyHtmlCode() {
        const htmlCode = document.getElementById('htmlCode');
        htmlCode.select();
        document.execCommand('copy');
        showNotification('HTML kodu kopyalandı!', 'success');
    }

    function toggleAccordion(id) {
        const content = document.getElementById(id);
        const icon = document.getElementById(id + '-icon');

        if (content.classList.contains('hidden')) {
            content.classList.remove('hidden');
            icon.style.transform = 'rotate(180deg)';
        } else {
            content.classList.add('hidden');
            icon.style.transform = 'rotate(0deg)';
        }
    }
</script>
