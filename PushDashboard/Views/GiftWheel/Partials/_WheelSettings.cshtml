@using PushDashboard.ViewModels
@model GiftWheelViewModel

<div class="space-y-8">
    <!-- Header -->
    <div>
        <h3 class="text-lg font-medium text-gray-900">Çark Ayarları</h3>
        <p class="text-sm text-gray-600">Çarkın<PERSON><PERSON>ın görünümünü ve davranışını özelleştirin</p>
    </div>

    <form id="wheelSettingsForm" class="space-y-8">
        <!-- Appearance Settings -->
        <div class="bg-gray-50 rounded-lg p-6">
            <h4 class="text-md font-medium text-gray-900 mb-4">
                <i class="fas fa-palette mr-2 text-indigo-600"></i>
                Görünüm Ayarları
            </h4>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Wheel Title -->
                <div>
                    <label for="wheelTitle" class="block text-sm font-medium text-gray-700 mb-2">
                        Çark Başlığı
                    </label>
                    <input type="text" id="wheelTitle" name="wheelTitle" 
                           value="@(Model.Settings?.WheelTitle ?? "Çarkı Çevir, Hediyeni Kazan!")"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                           maxlength="200" required>
                </div>

                <!-- Wheel Subtitle -->
                <div>
                    <label for="wheelSubtitle" class="block text-sm font-medium text-gray-700 mb-2">
                        Alt Başlık
                    </label>
                    <input type="text" id="wheelSubtitle" name="wheelSubtitle" 
                           value="@(Model.Settings?.WheelSubtitle ?? "Şansını dene ve harika hediyeler kazan")"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                           maxlength="300">
                </div>

                <!-- Button Text -->
                <div>
                    <label for="buttonText" class="block text-sm font-medium text-gray-700 mb-2">
                        Buton Metni
                    </label>
                    <input type="text" id="buttonText" name="buttonText" 
                           value="@(Model.Settings?.ButtonText ?? "Çarkı Çevir")"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                           maxlength="50" required>
                </div>

                <!-- Wheel Size -->
                <div>
                    <label for="wheelSize" class="block text-sm font-medium text-gray-700 mb-2">
                        Çark Boyutu (piksel)
                    </label>
                    <input type="range" id="wheelSize" name="wheelSize" 
                           value="@(Model.Settings?.WheelSize ?? 300)"
                           min="200" max="500" step="10"
                           class="w-full">
                    <div class="flex justify-between text-xs text-gray-500 mt-1">
                        <span>200px</span>
                        <span id="wheelSizeValue">@(Model.Settings?.WheelSize ?? 300)px</span>
                        <span>500px</span>
                    </div>
                </div>
            </div>

            <!-- Color Settings -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
                <div>
                    <label for="primaryColor" class="block text-sm font-medium text-gray-700 mb-2">
                        Ana Renk
                    </label>
                    <div class="flex items-center space-x-3">
                        <input type="color" id="primaryColor" name="primaryColor" 
                               value="@(Model.Settings?.PrimaryColor ?? "#6366f1")"
                               class="h-10 w-16 border border-gray-300 rounded-lg cursor-pointer">
                        <input type="text" id="primaryColorText" 
                               value="@(Model.Settings?.PrimaryColor ?? "#6366f1")"
                               class="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                               pattern="^#[0-9A-Fa-f]{6}$">
                    </div>
                </div>

                <div>
                    <label for="secondaryColor" class="block text-sm font-medium text-gray-700 mb-2">
                        İkincil Renk
                    </label>
                    <div class="flex items-center space-x-3">
                        <input type="color" id="secondaryColor" name="secondaryColor" 
                               value="@(Model.Settings?.SecondaryColor ?? "#f3f4f6")"
                               class="h-10 w-16 border border-gray-300 rounded-lg cursor-pointer">
                        <input type="text" id="secondaryColorText" 
                               value="@(Model.Settings?.SecondaryColor ?? "#f3f4f6")"
                               class="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                               pattern="^#[0-9A-Fa-f]{6}$">
                    </div>
                </div>
            </div>
        </div>

        <!-- Behavior Settings -->
        <div class="bg-gray-50 rounded-lg p-6">
            <h4 class="text-md font-medium text-gray-900 mb-4">
                <i class="fas fa-cogs mr-2 text-indigo-600"></i>
                Davranış Ayarları
            </h4>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Max Spins Per Day -->
                <div>
                    <label for="maxSpinsPerDay" class="block text-sm font-medium text-gray-700 mb-2">
                        Günlük Maksimum Çevirme
                    </label>
                    <select id="maxSpinsPerDay" name="maxSpinsPerDay" 
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
                        @for (int i = 1; i <= 10; i++)
                        {
                            var isSelected = Model.Settings?.MaxSpinsPerDay == i;
                            <option value="@i" selected="@isSelected">@i kez</option>
                        }
                    </select>
                    <p class="text-xs text-gray-500 mt-1">Aynı telefon numarası günde kaç kez çevirebilir</p>
                </div>

                <!-- Show Confetti -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        Konfeti Efekti
                    </label>
                    <div class="flex items-center">
                        <button type="button" id="showConfettiToggle" 
                                class="relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 @(Model.Settings?.ShowConfetti != false ? "bg-indigo-600" : "bg-gray-200")"
                                data-enabled="@(Model.Settings?.ShowConfetti != false ? "true" : "false")">
                            <span class="inline-block h-4 w-4 transform rounded-full bg-white transition-transform @(Model.Settings?.ShowConfetti != false ? "translate-x-6" : "translate-x-1")"></span>
                        </button>
                        <span class="ml-3 text-sm text-gray-700">Kazanınca konfeti göster</span>
                    </div>
                </div>
            </div>

            <!-- Required Fields -->
            <div class="mt-6">
                <label class="block text-sm font-medium text-gray-700 mb-3">
                    Gerekli Bilgiler
                </label>
                <div class="space-y-3">
                    <div class="flex items-center">
                        <input type="checkbox" id="requirePhone" name="requirePhone" 
                               @(Model.Settings?.RequirePhone != false ? "checked" : "")
                               class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                        <label for="requirePhone" class="ml-3 text-sm text-gray-700">
                            Telefon numarası zorunlu
                        </label>
                    </div>
                    <div class="flex items-center">
                        <input type="checkbox" id="requireEmail" name="requireEmail" 
                               @(Model.Settings?.RequireEmail == true ? "checked" : "")
                               class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                        <label for="requireEmail" class="ml-3 text-sm text-gray-700">
                            E-posta adresi zorunlu
                        </label>
                    </div>
                </div>
            </div>
        </div>

        <!-- Message Settings -->
        <div class="bg-gray-50 rounded-lg p-6">
            <h4 class="text-md font-medium text-gray-900 mb-4">
                <i class="fas fa-comment mr-2 text-indigo-600"></i>
                Mesaj Ayarları
            </h4>
            
            <div class="space-y-4">
                <!-- Win Message -->
                <div>
                    <label for="winMessage" class="block text-sm font-medium text-gray-700 mb-2">
                        Kazanma Mesajı
                    </label>
                    <input type="text" id="winMessage" name="winMessage" 
                           value="@(Model.Settings?.WinMessage ?? "Tebrikler! {prize} kazandınız!")"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                           maxlength="200" required>
                    <p class="text-xs text-gray-500 mt-1">
                        <code>{prize}</code> yer tutucusunu kullanabilirsiniz
                    </p>
                </div>

                <!-- Lose Message -->
                <div>
                    <label for="loseMessage" class="block text-sm font-medium text-gray-700 mb-2">
                        Kaybetme Mesajı
                    </label>
                    <input type="text" id="loseMessage" name="loseMessage" 
                           value="@(Model.Settings?.LoseMessage ?? "Bu sefer olmadı, tekrar deneyin!")"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                           maxlength="200" required>
                </div>

                <!-- Notification Template -->
                <div>
                    <label for="notificationTemplate" class="block text-sm font-medium text-gray-700 mb-2">
                        WhatsApp Bildirim Şablonu
                    </label>
                    <textarea id="notificationTemplate" name="notificationTemplate" rows="3"
                              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                              maxlength="500" required>@(Model.Settings?.NotificationTemplate ?? "🎉 Tebrikler {name}! {prize} kazandınız. Hemen alışverişe başlayın: {siteUrl}")</textarea>
                    <p class="text-xs text-gray-500 mt-1">
                        Kullanılabilir yer tutucular: <code>{name}</code>, <code>{prize}</code>, <code>{siteUrl}</code>, <code>{voucherCode}</code>
                    </p>
                </div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="flex items-center justify-between pt-6 border-t border-gray-200">
            <button type="button" id="previewWheelBtn" class="bg-gray-600 hover:bg-gray-700 text-white px-6 py-2 rounded-lg font-medium transition-colors">
                <i class="fas fa-eye mr-2"></i>
                Önizleme
            </button>
            
            <div class="flex items-center space-x-3">
                <button type="button" id="resetSettingsBtn" class="text-gray-600 hover:text-gray-800 px-4 py-2 font-medium">
                    Sıfırla
                </button>
                <button type="submit" id="saveSettingsBtn" class="bg-indigo-600 hover:bg-indigo-700 text-white px-6 py-2 rounded-lg font-medium transition-colors">
                    <i class="fas fa-save mr-2"></i>
                    Kaydet
                </button>
            </div>
        </div>
    </form>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Wheel size slider
    const wheelSizeSlider = document.getElementById('wheelSize');
    const wheelSizeValue = document.getElementById('wheelSizeValue');
    
    wheelSizeSlider.addEventListener('input', function() {
        wheelSizeValue.textContent = this.value + 'px';
    });
    
    // Color picker sync
    const primaryColor = document.getElementById('primaryColor');
    const primaryColorText = document.getElementById('primaryColorText');
    const secondaryColor = document.getElementById('secondaryColor');
    const secondaryColorText = document.getElementById('secondaryColorText');
    
    primaryColor.addEventListener('change', function() {
        primaryColorText.value = this.value;
    });
    
    primaryColorText.addEventListener('change', function() {
        if (/^#[0-9A-Fa-f]{6}$/.test(this.value)) {
            primaryColor.value = this.value;
        }
    });
    
    secondaryColor.addEventListener('change', function() {
        secondaryColorText.value = this.value;
    });
    
    secondaryColorText.addEventListener('change', function() {
        if (/^#[0-9A-Fa-f]{6}$/.test(this.value)) {
            secondaryColor.value = this.value;
        }
    });
    
    // Confetti toggle
    const confettiToggle = document.getElementById('showConfettiToggle');
    confettiToggle.addEventListener('click', function() {
        const isEnabled = this.dataset.enabled === 'true';
        const newState = !isEnabled;
        
        this.dataset.enabled = newState.toString();
        
        if (newState) {
            this.classList.remove('bg-gray-200');
            this.classList.add('bg-indigo-600');
            this.querySelector('span').classList.remove('translate-x-1');
            this.querySelector('span').classList.add('translate-x-6');
        } else {
            this.classList.remove('bg-indigo-600');
            this.classList.add('bg-gray-200');
            this.querySelector('span').classList.remove('translate-x-6');
            this.querySelector('span').classList.add('translate-x-1');
        }
    });
});
</script>
