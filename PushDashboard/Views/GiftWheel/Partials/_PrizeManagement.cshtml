@using PushDashboard.ViewModels
@model GiftWheelViewModel

<div class="space-y-6">
    <!-- Header -->
    <div class="flex items-center justify-between">
        <div>
            <h3 class="text-lg font-medium text-gray-900"><PERSON><PERSON><PERSON><PERSON> Yönetimi</h3>
            <p class="text-sm text-gray-600">Çarkınızdaki ödülleri ekleyin, düzenleyin ve yönetin</p>
        </div>
        <button id="addPrizeBtn" class="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
            <i class="fas fa-plus mr-2"></i>
            <PERSON>d<PERSON><PERSON>
        </button>
    </div>

    <!-- Probability Warning -->
    <div id="probabilityWarning" class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 hidden">
        <div class="flex items-start">
            <i class="fas fa-exclamation-triangle text-yellow-500 mt-1 mr-3"></i>
            <div>
                <h4 class="text-yellow-800 font-medium"><PERSON>lasılık Uyarısı</h4>
                <p class="text-yellow-700 text-sm mt-1">
                    Toplam olasılık <span id="totalProbability">0</span>%. Optimal sonuçlar için %100'e yakın olması önerilir.
                </p>
            </div>
        </div>
    </div>

    <!-- Prizes List -->
    <div class="space-y-4" id="prizesList">
        @if (Model.Prizes.Any())
        {
            @foreach (var prize in Model.Prizes.OrderBy(p => p.SortOrder))
            {
                <div class="prize-item bg-gray-50 rounded-lg p-4 border border-gray-200" data-prize-id="@prize.Id">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-4">
                            <!-- Drag Handle -->
                            <div class="drag-handle cursor-move text-gray-400 hover:text-gray-600">
                                <i class="fas fa-grip-vertical"></i>
                            </div>
                            
                            <!-- Prize Color -->
                            <div class="prize-color-preview" style="background-color: @prize.Color"></div>
                            
                            <!-- Prize Info -->
                            <div>
                                <h4 class="font-medium text-gray-900">@prize.Name</h4>
                                <div class="flex items-center space-x-4 text-sm text-gray-600">
                                    <span>
                                        <i class="fas fa-percentage mr-1"></i>
                                        @prize.Probability% şans
                                    </span>
                                    @if (prize.PrizeType == "voucher" && prize.DiscountAmount.HasValue)
                                    {
                                        <span>
                                            <i class="fas fa-gift mr-1"></i>
                                            @if (prize.DiscountType == 1)
                                            {
                                                <text>@(prize.DiscountAmount.Value.ToString("N0")) TL</text>
                                            }
                                            else
                                            {
                                                <text>%@(prize.DiscountAmount.Value.ToString("N0")) indirim</text>
                                            }
                                        </span>
                                    }
                                    @if (prize.ValidityDays.HasValue)
                                    {
                                        <span>
                                            <i class="fas fa-calendar mr-1"></i>
                                            @prize.ValidityDays gün geçerli
                                        </span>
                                    }
                                </div>
                            </div>
                        </div>
                        
                        <!-- Actions -->
                        <div class="flex items-center space-x-2">
                            <!-- Status Toggle -->
                            <button class="toggle-prize-status relative inline-flex h-5 w-9 items-center rounded-full transition-colors focus:outline-none @(prize.IsActive ? "bg-green-600" : "bg-gray-200")"
                                    data-prize-id="@prize.Id" data-active="@prize.IsActive.ToString().ToLower()">
                                <span class="inline-block h-3 w-3 transform rounded-full bg-white transition-transform @(prize.IsActive ? "translate-x-5" : "translate-x-1")"></span>
                            </button>
                            
                            <!-- Edit Button -->
                            <button class="edit-prize-btn text-indigo-600 hover:text-indigo-800 p-2" data-prize-id="@prize.Id">
                                <i class="fas fa-edit"></i>
                            </button>
                            
                            <!-- Delete Button -->
                            <button class="delete-prize-btn text-red-600 hover:text-red-800 p-2" data-prize-id="@prize.Id">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
            }
        }
        else
        {
            <div class="text-center py-12">
                <div class="mx-auto h-24 w-24 text-gray-400">
                    <i class="fas fa-trophy text-6xl"></i>
                </div>
                <h3 class="mt-4 text-lg font-medium text-gray-900">Henüz ödül eklenmemiş</h3>
                <p class="mt-2 text-sm text-gray-600">Çarkınıza ödüller ekleyerek başlayın</p>
                <button id="addFirstPrizeBtn" class="mt-4 bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    <i class="fas fa-plus mr-2"></i>
                    İlk Ödülü Ekle
                </button>
            </div>
        }
    </div>

    <!-- Quick Actions -->
    @if (Model.Prizes.Any())
    {
        <div class="flex items-center justify-between pt-4 border-t border-gray-200">
            <div class="flex items-center space-x-4">
                <button id="enableAllPrizes" class="text-green-600 hover:text-green-800 text-sm font-medium">
                    <i class="fas fa-check-circle mr-1"></i>
                    Tümünü Aktif Et
                </button>
                <button id="disableAllPrizes" class="text-gray-600 hover:text-gray-800 text-sm font-medium">
                    <i class="fas fa-times-circle mr-1"></i>
                    Tümünü Pasif Et
                </button>
            </div>
            
            <div class="text-sm text-gray-600">
                Toplam <span class="font-medium">@Model.Prizes.Count</span> ödül
                • <span class="font-medium">@Model.Prizes.Count(p => p.IsActive)</span> aktif
                • <span class="font-medium" id="totalProbabilityDisplay">@Model.Prizes.Where(p => p.IsActive).Sum(p => p.Probability)</span>% toplam şans
            </div>
        </div>
    }

    <!-- Prize Templates (Quick Add) -->
    <div class="bg-blue-50 rounded-lg p-4">
        <h4 class="font-medium text-blue-900 mb-3">
            <i class="fas fa-magic mr-2"></i>
            Hızlı Ödül Şablonları
        </h4>
        <div class="grid grid-cols-2 md:grid-cols-4 gap-3">
            <button class="prize-template-btn bg-white hover:bg-blue-50 border border-blue-200 rounded-lg p-3 text-left transition-colors"
                    data-template='{"name":"10 TL İndirim","prizeType":"voucher","discountAmount":10,"discountType":1,"probability":25,"color":"#ef4444"}'>
                <div class="flex items-center">
                    <div class="w-4 h-4 bg-red-500 rounded-full mr-2"></div>
                    <span class="text-sm font-medium">10 TL İndirim</span>
                </div>
                <div class="text-xs text-gray-600 mt-1">%25 şans</div>
            </button>
            
            <button class="prize-template-btn bg-white hover:bg-blue-50 border border-blue-200 rounded-lg p-3 text-left transition-colors"
                    data-template='{"name":"20% İndirim","prizeType":"voucher","discountAmount":20,"discountType":2,"probability":15,"color":"#f59e0b"}'>
                <div class="flex items-center">
                    <div class="w-4 h-4 bg-yellow-500 rounded-full mr-2"></div>
                    <span class="text-sm font-medium">%20 İndirim</span>
                </div>
                <div class="text-xs text-gray-600 mt-1">%15 şans</div>
            </button>
            
            <button class="prize-template-btn bg-white hover:bg-blue-50 border border-blue-200 rounded-lg p-3 text-left transition-colors"
                    data-template='{"name":"Ücretsiz Kargo","prizeType":"voucher","discountAmount":15,"discountType":1,"probability":30,"color":"#10b981"}'>
                <div class="flex items-center">
                    <div class="w-4 h-4 bg-green-500 rounded-full mr-2"></div>
                    <span class="text-sm font-medium">Ücretsiz Kargo</span>
                </div>
                <div class="text-xs text-gray-600 mt-1">%30 şans</div>
            </button>
            
            <button class="prize-template-btn bg-white hover:bg-blue-50 border border-blue-200 rounded-lg p-3 text-left transition-colors"
                    data-template='{"name":"Tekrar Dene","prizeType":"message","discountAmount":null,"discountType":null,"probability":30,"color":"#6b7280"}'>
                <div class="flex items-center">
                    <div class="w-4 h-4 bg-gray-500 rounded-full mr-2"></div>
                    <span class="text-sm font-medium">Tekrar Dene</span>
                </div>
                <div class="text-xs text-gray-600 mt-1">%30 şans</div>
            </button>
        </div>
        <p class="text-xs text-blue-700 mt-3">
            <i class="fas fa-info-circle mr-1"></i>
            Şablonları tıklayarak hızlıca ödül ekleyebilirsiniz. Daha sonra düzenleyebilirsiniz.
        </p>
    </div>
</div>

<script>
// Prize management specific JavaScript
document.addEventListener('DOMContentLoaded', function() {
    // Update probability display
    function updateProbabilityDisplay() {
        const activeCheckboxes = document.querySelectorAll('.toggle-prize-status[data-active="true"]');
        let totalProbability = 0;
        
        activeCheckboxes.forEach(checkbox => {
            const prizeItem = checkbox.closest('.prize-item');
            const probabilityText = prizeItem.querySelector('.fa-percentage').parentNode.textContent;
            const probability = parseInt(probabilityText.match(/\d+/)[0]);
            totalProbability += probability;
        });
        
        document.getElementById('totalProbabilityDisplay').textContent = totalProbability;
        document.getElementById('totalProbability').textContent = totalProbability;
        
        const warningDiv = document.getElementById('probabilityWarning');
        if (totalProbability < 90 || totalProbability > 100) {
            warningDiv.classList.remove('hidden');
        } else {
            warningDiv.classList.add('hidden');
        }
    }
    
    // Initialize probability display
    updateProbabilityDisplay();
    
    // Prize template buttons
    document.querySelectorAll('.prize-template-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const template = JSON.parse(this.dataset.template);
            // This will be handled by the main gift-wheel-admin.js
            window.addPrizeFromTemplate(template);
        });
    });
});
</script>
