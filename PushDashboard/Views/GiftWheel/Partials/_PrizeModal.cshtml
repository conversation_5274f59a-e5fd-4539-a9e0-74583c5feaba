<!-- Prize Add/Edit Modal -->
<div id="prizeModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
        <!-- <PERSON><PERSON> Header -->
        <div class="flex items-center justify-between pb-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900" id="prizeModalTitle">
                <i class="fas fa-trophy mr-2 text-yellow-500"></i>
                Ödül Ekle
            </h3>
            <button id="closePrizeModal" class="text-gray-400 hover:text-gray-600">
                <i class="fas fa-times text-xl"></i>
            </button>
        </div>

        <!-- Modal Body -->
        <form id="prizeForm" class="mt-6 space-y-6">
            <input type="hidden" id="prizeId" name="prizeId">
            
            <!-- Prize Name -->
            <div>
                <label for="prizeName" class="block text-sm font-medium text-gray-700 mb-2">
                    Ödül Adı <span class="text-red-500">*</span>
                </label>
                <input type="text" id="prizeName" name="prizeName" required
                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                       placeholder="Örn: 10 TL İndirim" maxlength="100">
                <div class="text-red-500 text-sm mt-1 hidden" id="prizeNameError"></div>
            </div>

            <!-- Prize Type -->
            <div>
                <label for="prizeType" class="block text-sm font-medium text-gray-700 mb-2">
                    Ödül Türü <span class="text-red-500">*</span>
                </label>
                <select id="prizeType" name="prizeType" required
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
                    <option value="voucher">Hediye Çeki</option>
                    <option value="message">Sadece Mesaj</option>
                    <option value="none">Boş (Tekrar Dene)</option>
                </select>
            </div>

            <!-- Voucher Settings (shown only for voucher type) -->
            <div id="voucherSettings" class="space-y-4">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <!-- Discount Amount -->
                    <div>
                        <label for="discountAmount" class="block text-sm font-medium text-gray-700 mb-2">
                            İndirim Miktarı <span class="text-red-500">*</span>
                        </label>
                        <input type="number" id="discountAmount" name="discountAmount" 
                               min="0.01" max="999999.99" step="0.01"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                               placeholder="10.00">
                        <div class="text-red-500 text-sm mt-1 hidden" id="discountAmountError"></div>
                    </div>

                    <!-- Discount Type -->
                    <div>
                        <label for="discountType" class="block text-sm font-medium text-gray-700 mb-2">
                            İndirim Türü <span class="text-red-500">*</span>
                        </label>
                        <select id="discountType" name="discountType"
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
                            <option value="1">Tutar (TL)</option>
                            <option value="2">Yüzde (%)</option>
                        </select>
                    </div>
                </div>

                <!-- Validity Days -->
                <div>
                    <label for="validityDays" class="block text-sm font-medium text-gray-700 mb-2">
                        Geçerlilik Süresi (Gün)
                    </label>
                    <input type="number" id="validityDays" name="validityDays" 
                           min="1" max="365" value="30"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
                    <p class="text-xs text-gray-500 mt-1">Hediye çekinin kaç gün geçerli olacağını belirtin</p>
                </div>
            </div>

            <!-- Probability -->
            <div>
                <label for="probability" class="block text-sm font-medium text-gray-700 mb-2">
                    Kazanma Olasılığı (%) <span class="text-red-500">*</span>
                </label>
                <div class="flex items-center space-x-4">
                    <input type="range" id="probabilitySlider" name="probabilitySlider" 
                           min="1" max="100" value="10"
                           class="flex-1">
                    <input type="number" id="probability" name="probability" 
                           min="1" max="100" value="10" required
                           class="w-20 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
                    <span class="text-sm text-gray-500">%</span>
                </div>
                <div class="text-red-500 text-sm mt-1 hidden" id="probabilityError"></div>
                <div id="probabilityWarning" class="text-yellow-600 text-sm mt-1 hidden">
                    <i class="fas fa-exclamation-triangle mr-1"></i>
                    <span id="probabilityWarningText"></span>
                </div>
            </div>

            <!-- Color -->
            <div>
                <label for="prizeColor" class="block text-sm font-medium text-gray-700 mb-2">
                    Çark Rengi
                </label>
                <div class="flex items-center space-x-4">
                    <input type="color" id="prizeColor" name="prizeColor" value="#3B82F6"
                           class="h-10 w-16 border border-gray-300 rounded-lg cursor-pointer">
                    <input type="text" id="prizeColorText" 
                           value="#3B82F6" pattern="^#[0-9A-Fa-f]{6}$"
                           class="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                           placeholder="#3B82F6">
                    <div class="flex space-x-2">
                        <button type="button" class="color-preset w-8 h-8 rounded-full border-2 border-gray-300" style="background-color: #ef4444" data-color="#ef4444"></button>
                        <button type="button" class="color-preset w-8 h-8 rounded-full border-2 border-gray-300" style="background-color: #f59e0b" data-color="#f59e0b"></button>
                        <button type="button" class="color-preset w-8 h-8 rounded-full border-2 border-gray-300" style="background-color: #10b981" data-color="#10b981"></button>
                        <button type="button" class="color-preset w-8 h-8 rounded-full border-2 border-gray-300" style="background-color: #3b82f6" data-color="#3b82f6"></button>
                        <button type="button" class="color-preset w-8 h-8 rounded-full border-2 border-gray-300" style="background-color: #8b5cf6" data-color="#8b5cf6"></button>
                        <button type="button" class="color-preset w-8 h-8 rounded-full border-2 border-gray-300" style="background-color: #ec4899" data-color="#ec4899"></button>
                    </div>
                </div>
            </div>

            <!-- Sort Order -->
            <div>
                <label for="sortOrder" class="block text-sm font-medium text-gray-700 mb-2">
                    Sıralama
                </label>
                <input type="number" id="sortOrder" name="sortOrder" 
                       min="0" max="100" value="0"
                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
                <p class="text-xs text-gray-500 mt-1">Çarktaki ödüllerin sıralamasını belirler (0 = en üstte)</p>
            </div>

            <!-- Active Status -->
            <div class="flex items-center">
                <input type="checkbox" id="isActive" name="isActive" checked
                       class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                <label for="isActive" class="ml-3 text-sm text-gray-700">
                    Ödül aktif
                </label>
            </div>

            <!-- Preview -->
            <div class="bg-gray-50 rounded-lg p-4">
                <h4 class="text-sm font-medium text-gray-900 mb-3">Önizleme</h4>
                <div class="flex items-center space-x-4">
                    <div id="prizePreviewColor" class="w-8 h-8 rounded-full border-2 border-gray-300" style="background-color: #3B82F6"></div>
                    <div>
                        <p class="font-medium text-gray-900" id="prizePreviewName">Ödül Adı</p>
                        <p class="text-sm text-gray-600" id="prizePreviewDetails">%10 şans</p>
                    </div>
                </div>
            </div>
        </form>

        <!-- Modal Footer -->
        <div class="flex items-center justify-end space-x-3 pt-6 border-t border-gray-200 mt-6">
            <button type="button" id="cancelPrizeBtn" class="px-4 py-2 text-gray-700 bg-gray-200 hover:bg-gray-300 rounded-lg font-medium transition-colors">
                İptal
            </button>
            <button type="submit" form="prizeForm" id="savePrizeBtn" class="px-6 py-2 bg-indigo-600 hover:bg-indigo-700 text-white rounded-lg font-medium transition-colors">
                <i class="fas fa-save mr-2"></i>
                Kaydet
            </button>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const modal = document.getElementById('prizeModal');
    const form = document.getElementById('prizeForm');
    const prizeTypeSelect = document.getElementById('prizeType');
    const voucherSettings = document.getElementById('voucherSettings');
    const probabilitySlider = document.getElementById('probabilitySlider');
    const probabilityInput = document.getElementById('probability');
    const colorInput = document.getElementById('prizeColor');
    const colorTextInput = document.getElementById('prizeColorText');
    
    // Prize type change handler
    prizeTypeSelect.addEventListener('change', function() {
        if (this.value === 'voucher') {
            voucherSettings.classList.remove('hidden');
            document.getElementById('discountAmount').required = true;
        } else {
            voucherSettings.classList.add('hidden');
            document.getElementById('discountAmount').required = false;
        }
        updatePreview();
    });
    
    // Probability slider sync
    probabilitySlider.addEventListener('input', function() {
        probabilityInput.value = this.value;
        updatePreview();
        checkProbabilityWarning();
    });
    
    probabilityInput.addEventListener('input', function() {
        probabilitySlider.value = this.value;
        updatePreview();
        checkProbabilityWarning();
    });
    
    // Color picker sync
    colorInput.addEventListener('change', function() {
        colorTextInput.value = this.value;
        document.getElementById('prizePreviewColor').style.backgroundColor = this.value;
    });
    
    colorTextInput.addEventListener('change', function() {
        if (/^#[0-9A-Fa-f]{6}$/.test(this.value)) {
            colorInput.value = this.value;
            document.getElementById('prizePreviewColor').style.backgroundColor = this.value;
        }
    });
    
    // Color presets
    document.querySelectorAll('.color-preset').forEach(btn => {
        btn.addEventListener('click', function() {
            const color = this.dataset.color;
            colorInput.value = color;
            colorTextInput.value = color;
            document.getElementById('prizePreviewColor').style.backgroundColor = color;
        });
    });
    
    // Form inputs change handler for preview
    ['prizeName', 'discountAmount', 'discountType', 'validityDays'].forEach(id => {
        document.getElementById(id).addEventListener('input', updatePreview);
    });
    
    // Update preview function
    function updatePreview() {
        const name = document.getElementById('prizeName').value || 'Ödül Adı';
        const probability = document.getElementById('probability').value;
        const prizeType = document.getElementById('prizeType').value;
        const discountAmount = document.getElementById('discountAmount').value;
        const discountType = document.getElementById('discountType').value;
        const validityDays = document.getElementById('validityDays').value;
        
        document.getElementById('prizePreviewName').textContent = name;
        
        let details = `%${probability} şans`;
        
        if (prizeType === 'voucher' && discountAmount) {
            if (discountType === '1') {
                details += ` • ${discountAmount} TL`;
            } else {
                details += ` • %${discountAmount} indirim`;
            }
            
            if (validityDays) {
                details += ` • ${validityDays} gün geçerli`;
            }
        }
        
        document.getElementById('prizePreviewDetails').textContent = details;
    }
    
    // Check probability warning
    function checkProbabilityWarning() {
        // This will be implemented in the main gift-wheel-admin.js
        if (window.checkTotalProbability) {
            window.checkTotalProbability();
        }
    }
    
    // Modal close handlers
    document.getElementById('closePrizeModal').addEventListener('click', closePrizeModal);
    document.getElementById('cancelPrizeBtn').addEventListener('click', closePrizeModal);
    
    function closePrizeModal() {
        modal.classList.add('hidden');
        form.reset();
        clearErrors();
    }
    
    // Clear form errors
    function clearErrors() {
        document.querySelectorAll('.text-red-500').forEach(el => {
            if (el.id.endsWith('Error')) {
                el.classList.add('hidden');
                el.textContent = '';
            }
        });
    }
    
    // Form validation
    form.addEventListener('submit', function(e) {
        e.preventDefault();
        clearErrors();
        
        let isValid = true;
        
        // Validate prize name
        const prizeName = document.getElementById('prizeName').value.trim();
        if (!prizeName) {
            showError('prizeNameError', 'Ödül adı gereklidir');
            isValid = false;
        }
        
        // Validate voucher settings
        if (prizeTypeSelect.value === 'voucher') {
            const discountAmount = parseFloat(document.getElementById('discountAmount').value);
            if (!discountAmount || discountAmount <= 0) {
                showError('discountAmountError', 'Geçerli bir indirim miktarı giriniz');
                isValid = false;
            }
        }
        
        // Validate probability
        const probability = parseInt(document.getElementById('probability').value);
        if (!probability || probability < 1 || probability > 100) {
            showError('probabilityError', 'Olasılık 1-100 arasında olmalıdır');
            isValid = false;
        }
        
        if (isValid) {
            // Submit form via AJAX (implemented in main gift-wheel-admin.js)
            if (window.submitPrizeForm) {
                window.submitPrizeForm(new FormData(form));
            }
        }
    });
    
    function showError(elementId, message) {
        const errorElement = document.getElementById(elementId);
        errorElement.textContent = message;
        errorElement.classList.remove('hidden');
    }
    
    // Initialize preview
    updatePreview();
});
</script>
