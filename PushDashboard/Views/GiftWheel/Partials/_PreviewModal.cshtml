<!-- Preview Modal -->
<div id="previewModal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg shadow-xl max-w-6xl w-full max-h-[90vh] overflow-y-auto">
            <!-- Modal Header -->
            <div class="flex justify-between items-center p-6 border-b border-gray-200">
                <h3 class="text-xl font-semibold text-gray-900">Çark Önizlemesi</h3>
                <div class="flex items-center space-x-4">
                    <!-- Device Toggle -->
                    <div class="flex bg-gray-100 rounded-lg p-1">
                        <button id="desktopPreview" class="px-4 py-2 text-sm font-medium rounded-md bg-white shadow-sm text-gray-900">
                            Ma<PERSON>üstü
                        </button>
                        <button id="mobilePreview" class="px-4 py-2 text-sm font-medium rounded-md text-gray-600">
                            <PERSON>bil
                        </button>
                    </div>
                    <button id="closePreviewModal" class="text-gray-400 hover:text-gray-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
            </div>

            <!-- Modal Body -->
            <div class="p-6">
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    <!-- Preview Container -->
                    <div id="previewContainer" class="bg-gray-100 rounded-lg p-8 transition-all duration-300">
                        <div id="wheelPreviewContent" class="max-w-md mx-auto bg-white rounded-lg shadow-lg p-6">
                            <!-- Wheel Title -->
                            <h2 id="previewTitle" class="text-2xl font-bold text-center text-gray-800 mb-2">
                                Çarkı Çevir, Hediyeni Kazan!
                            </h2>
                            
                            <!-- Wheel Subtitle -->
                            <p id="previewSubtitle" class="text-center text-gray-600 mb-6">
                                Şansını dene ve harika hediyeler kazan
                            </p>
                            
                            <!-- Wheel Canvas Container -->
                            <div class="flex justify-center mb-6">
                                <div id="previewWheelCanvas" class="relative inline-block bg-gray-100 rounded-full" style="width: 300px; height: 300px;">
                                    <canvas id="wheelCanvas" width="300" height="300" 
                                            class="rounded-full shadow-lg border-2 border-gray-300" 
                                            style="display: block; width: 300px; height: 300px;">
                                    </canvas>
                                    <!-- Wheel Pointer -->
                                    <div class="absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-2 z-10">
                                        <div class="w-0 h-0 border-l-4 border-r-4 border-b-8 border-l-transparent border-r-transparent border-b-red-600 drop-shadow-md"></div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Customer Form -->
                            <div id="previewForm" class="space-y-4 mb-6">
                                <input type="text" placeholder="Adınız" 
                                       class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
                                <input type="tel" placeholder="Telefon Numaranız" 
                                       class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
                                <input type="email" placeholder="E-posta Adresiniz" id="previewEmailField"
                                       class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 hidden">
                            </div>
                            
                            <!-- Spin Button -->
                            <div class="text-center">
                                <button id="previewSpinButton" class="bg-indigo-600 hover:bg-indigo-700 text-white px-8 py-4 rounded-full font-bold text-lg transition-all duration-200 transform hover:scale-105 shadow-lg">
                                    Çarkı Çevir
                                </button>
                            </div>
                            
                            <!-- Result Message (Hidden by default) -->
                            <div id="previewResult" class="mt-6 p-4 rounded-lg text-center hidden">
                                <div id="previewResultIcon" class="text-4xl mb-2"></div>
                                <p id="previewResultMessage" class="font-medium"></p>
                            </div>
                        </div>
                    </div>

                    <!-- Controls Panel -->
                    <div class="space-y-6">
                        <!-- Test Controls -->
                        <div class="bg-white rounded-lg border border-gray-200 p-6">
                            <h4 class="text-lg font-semibold text-gray-900 mb-4">Test Kontrolleri</h4>
                            <div class="space-y-4">
                                <div class="flex space-x-3">
                                    <button id="testSpinBtn" class="flex-1 bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                                        Test Çevirme
                                    </button>
                                    <button id="resetPreviewBtn" class="flex-1 bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                                        Sıfırla
                                    </button>
                                </div>
                                <div class="flex justify-between items-center text-sm text-gray-600 bg-gray-50 p-3 rounded-lg">
                                    <span id="previewPrizeCount">0</span> ödül • 
                                    <span id="previewTotalProbability">0</span>% toplam şans
                                    <button id="refreshPreviewBtn" class="text-indigo-600 hover:text-indigo-800 font-medium">
                                        Yenile
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Simple and working preview modal script
(function() {
    console.log('Preview modal script loading...');
    
    let canvas, ctx;
    let currentPrizes = [];
    let isSpinning = false;
    let currentRotation = 0;
    
    // Initialize when DOM is ready
    function initPreview() {
        console.log('Initializing preview...');
        
        canvas = document.getElementById('wheelCanvas');
        if (!canvas) {
            console.error('Canvas not found');
            return false;
        }
        
        ctx = canvas.getContext('2d');
        if (!ctx) {
            console.error('Canvas context not available');
            return false;
        }
        
        console.log('Canvas initialized successfully');
        
        // Draw initial empty wheel
        drawEmptyWheel();
        
        return true;
    }
    
    // Draw empty wheel
    function drawEmptyWheel() {
        if (!ctx || !canvas) return;
        
        const centerX = canvas.width / 2;
        const centerY = canvas.height / 2;
        const radius = Math.min(centerX, centerY) - 20;
        
        // Clear canvas
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        
        // Draw background circle
        ctx.beginPath();
        ctx.arc(centerX, centerY, radius, 0, 2 * Math.PI);
        ctx.fillStyle = '#f3f4f6';
        ctx.fill();
        ctx.strokeStyle = '#d1d5db';
        ctx.lineWidth = 3;
        ctx.stroke();
        
        // Draw text
        ctx.fillStyle = '#6b7280';
        ctx.font = 'bold 16px Arial';
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        ctx.fillText('Ödül Yükleniyor...', centerX, centerY);
        
        // Draw center circle
        ctx.beginPath();
        ctx.arc(centerX, centerY, 15, 0, 2 * Math.PI);
        ctx.fillStyle = '#ffffff';
        ctx.fill();
        ctx.strokeStyle = '#333333';
        ctx.lineWidth = 2;
        ctx.stroke();
    }
    
    // Draw wheel with prizes
    function drawWheel() {
        if (!ctx || !canvas || !currentPrizes || currentPrizes.length === 0) {
            drawEmptyWheel();
            return;
        }
        
        console.log('Drawing wheel with', currentPrizes.length, 'prizes');
        
        const centerX = canvas.width / 2;
        const centerY = canvas.height / 2;
        const radius = Math.min(centerX, centerY) - 20;
        
        // Clear canvas
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        
        // Filter active prizes
        const activePrizes = currentPrizes.filter(p => p.isActive);
        if (activePrizes.length === 0) {
            drawEmptyWheel();
            return;
        }
        
        // Calculate segments
        const totalProbability = activePrizes.reduce((sum, prize) => sum + prize.probability, 0);
        let currentAngle = 0;
        
        // Colors for segments
        const colors = [
            '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', 
            '#FFEAA7', '#DDA0DD', '#98D8C8', '#F7DC6F',
            '#BB8FCE', '#85C1E9', '#F8C471', '#82E0AA'
        ];
        
        // Draw segments
        activePrizes.forEach((prize, index) => {
            const segmentAngle = (prize.probability / totalProbability) * 2 * Math.PI;
            const color = colors[index % colors.length];
            
            // Draw segment
            ctx.beginPath();
            ctx.moveTo(centerX, centerY);
            ctx.arc(centerX, centerY, radius, currentAngle, currentAngle + segmentAngle);
            ctx.closePath();
            ctx.fillStyle = color;
            ctx.fill();
            ctx.strokeStyle = '#ffffff';
            ctx.lineWidth = 2;
            ctx.stroke();
            
            // Draw text
            const textAngle = currentAngle + segmentAngle / 2;
            const textRadius = radius * 0.7;
            const textX = centerX + Math.cos(textAngle) * textRadius;
            const textY = centerY + Math.sin(textAngle) * textRadius;
            
            ctx.save();
            ctx.translate(textX, textY);
            ctx.rotate(textAngle + Math.PI / 2);
            ctx.fillStyle = '#ffffff';
            ctx.font = 'bold 12px Arial';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.shadowColor = 'rgba(0,0,0,0.5)';
            ctx.shadowBlur = 2;
            ctx.fillText(prize.name, 0, 0);
            ctx.restore();
            
            currentAngle += segmentAngle;
        });
        
        // Draw center circle
        ctx.beginPath();
        ctx.arc(centerX, centerY, 15, 0, 2 * Math.PI);
        ctx.fillStyle = '#ffffff';
        ctx.fill();
        ctx.strokeStyle = '#333333';
        ctx.lineWidth = 2;
        ctx.stroke();
    }
    
    // Update preview with data
    function updatePreview(data) {
        console.log('updatePreview called with data:', data);
        if (!data) {
            console.error('No data provided to updatePreview');
            return;
        }

        // Update text content
        const titleElement = document.getElementById('previewTitle');
        const subtitleElement = document.getElementById('previewSubtitle');
        const buttonElement = document.getElementById('previewSpinButton');
        
        if (titleElement) titleElement.textContent = data.title || 'Çarkı Çevir, Hediyeni Kazan!';
        if (subtitleElement) subtitleElement.textContent = data.subtitle || 'Şansını dene ve harika hediyeler kazan';
        if (buttonElement) buttonElement.textContent = data.buttonText || 'Çarkı Çevir';

        // Update email field visibility
        const emailField = document.getElementById('previewEmailField');
        if (emailField) {
            if (data.requireEmail) {
                emailField.classList.remove('hidden');
            } else {
                emailField.classList.add('hidden');
            }
        }

        // Update prizes
        currentPrizes = data.prizes || [];
        console.log('Current prizes set to:', currentPrizes);
        console.log('Active prizes:', currentPrizes.filter(p => p.isActive));

        // Update stats
        const prizeCountElement = document.getElementById('previewPrizeCount');
        const totalProbabilityElement = document.getElementById('previewTotalProbability');
        
        if (prizeCountElement) prizeCountElement.textContent = currentPrizes.length;
        
        const totalProbability = currentPrizes.reduce((sum, prize) => sum + (prize.isActive ? prize.probability : 0), 0);
        if (totalProbabilityElement) totalProbabilityElement.textContent = totalProbability;

        // Update colors
        if (data.primaryColor && buttonElement) {
            buttonElement.style.backgroundColor = data.primaryColor;
        }

        // Initialize canvas if needed
        if (!canvas || !ctx) {
            if (!initPreview()) {
                console.error('Failed to initialize preview');
                return;
            }
        }

        // Redraw wheel
        console.log('Calling drawWheel with', currentPrizes.length, 'prizes');
        drawWheel();
    }

    // Spin animation function
    function animateWheel(targetRotation, duration, callback) {
        const startRotation = currentRotation;
        const totalRotation = targetRotation - startRotation;
        const startTime = Date.now();

        function animate() {
            const elapsed = Date.now() - startTime;
            const progress = Math.min(elapsed / duration, 1);

            // Easing function (ease-out)
            const easeProgress = 1 - Math.pow(1 - progress, 3);

            currentRotation = startRotation + (totalRotation * easeProgress);

            // Redraw wheel with rotation
            drawWheelWithRotation();

            if (progress < 1) {
                requestAnimationFrame(animate);
            } else {
                currentRotation = targetRotation;
                drawWheelWithRotation();
                if (callback) callback();
            }
        }

        animate();
    }

    // Draw wheel with rotation
    function drawWheelWithRotation() {
        if (!ctx || !canvas || !currentPrizes || currentPrizes.length === 0) {
            drawEmptyWheel();
            return;
        }

        const centerX = canvas.width / 2;
        const centerY = canvas.height / 2;
        const radius = Math.min(centerX, centerY) - 20;

        // Clear canvas
        ctx.clearRect(0, 0, canvas.width, canvas.height);

        // Filter active prizes
        const activePrizes = currentPrizes.filter(p => p.isActive);
        if (activePrizes.length === 0) {
            drawEmptyWheel();
            return;
        }

        // Calculate segments
        const totalProbability = activePrizes.reduce((sum, prize) => sum + prize.probability, 0);
        let currentAngle = currentRotation; // Apply rotation

        // Colors for segments
        const colors = [
            '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4',
            '#FFEAA7', '#DDA0DD', '#98D8C8', '#F7DC6F',
            '#BB8FCE', '#85C1E9', '#F8C471', '#82E0AA'
        ];

        // Draw segments
        activePrizes.forEach((prize, index) => {
            const segmentAngle = (prize.probability / totalProbability) * 2 * Math.PI;
            const color = colors[index % colors.length];

            // Draw segment
            ctx.beginPath();
            ctx.moveTo(centerX, centerY);
            ctx.arc(centerX, centerY, radius, currentAngle, currentAngle + segmentAngle);
            ctx.closePath();
            ctx.fillStyle = color;
            ctx.fill();
            ctx.strokeStyle = '#ffffff';
            ctx.lineWidth = 2;
            ctx.stroke();

            // Draw text
            const textAngle = currentAngle + segmentAngle / 2;
            const textRadius = radius * 0.7;
            const textX = centerX + Math.cos(textAngle) * textRadius;
            const textY = centerY + Math.sin(textAngle) * textRadius;

            ctx.save();
            ctx.translate(textX, textY);
            ctx.rotate(textAngle + Math.PI / 2);
            ctx.fillStyle = '#ffffff';
            ctx.font = 'bold 12px Arial';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.shadowColor = 'rgba(0,0,0,0.5)';
            ctx.shadowBlur = 2;
            ctx.fillText(prize.name, 0, 0);
            ctx.restore();

            currentAngle += segmentAngle;
        });

        // Draw center circle
        ctx.beginPath();
        ctx.arc(centerX, centerY, 15, 0, 2 * Math.PI);
        ctx.fillStyle = '#ffffff';
        ctx.fill();
        ctx.strokeStyle = '#333333';
        ctx.lineWidth = 2;
        ctx.stroke();
    }

    // Perform spin
    function performSpin() {
        if (isSpinning || !currentPrizes || currentPrizes.length === 0) {
            console.log('Cannot spin:', { isSpinning, prizesLength: currentPrizes?.length });
            return;
        }

        const activePrizes = currentPrizes.filter(p => p.isActive);
        if (activePrizes.length === 0) {
            console.log('No active prizes');
            return;
        }

        isSpinning = true;
        const button = document.getElementById('previewSpinButton');
        if (button) {
            button.disabled = true;
            button.textContent = 'Çevriliyor...';
        }

        // Hide previous result
        const resultDiv = document.getElementById('previewResult');
        if (resultDiv) {
            resultDiv.classList.add('hidden');
        }

        // Calculate winning prize
        const totalProbability = activePrizes.reduce((sum, prize) => sum + prize.probability, 0);
        const randomValue = Math.random() * totalProbability;

        let currentSum = 0;
        let winningPrize = null;
        let targetSegmentIndex = 0;

        for (let i = 0; i < activePrizes.length; i++) {
            currentSum += activePrizes[i].probability;
            if (randomValue <= currentSum) {
                winningPrize = activePrizes[i];
                targetSegmentIndex = i;
                break;
            }
        }

        if (!winningPrize) {
            winningPrize = activePrizes[0];
            targetSegmentIndex = 0;
        }

        console.log('Winning prize:', winningPrize.name, 'Index:', targetSegmentIndex);

        // Calculate target rotation
        const segmentAngle = (2 * Math.PI) / activePrizes.length;
        const targetAngle = targetSegmentIndex * segmentAngle + segmentAngle / 2;

        // Add multiple rotations for effect
        const spinRotations = 5 + Math.random() * 3; // 5-8 rotations
        const finalRotation = currentRotation + (spinRotations * 2 * Math.PI) + (2 * Math.PI - targetAngle);

        // Animate spin
        animateWheel(finalRotation, 4000, () => {
            showResult(winningPrize);

            isSpinning = false;
            if (button) {
                button.disabled = false;
                button.textContent = 'Tekrar Çevir';
            }
        });
    }

    // Show result
    function showResult(prize) {
        const resultDiv = document.getElementById('previewResult');
        const messageDiv = document.getElementById('previewResultMessage');

        if (!resultDiv || !messageDiv) return;

        if (prize.prizeType === 'voucher') {
            resultDiv.className = 'mt-6 p-4 rounded-lg text-center bg-green-50 border border-green-200';
            messageDiv.innerHTML = `<span class="text-green-800">Tebrikler! <strong>${prize.name}</strong> kazandınız!</span>`;
        } else if (prize.prizeType === 'message') {
            resultDiv.className = 'mt-6 p-4 rounded-lg text-center bg-blue-50 border border-blue-200';
            messageDiv.innerHTML = `<span class="text-blue-800">${prize.name}</span>`;
        } else {
            resultDiv.className = 'mt-6 p-4 rounded-lg text-center bg-gray-50 border border-gray-200';
            messageDiv.innerHTML = `<span class="text-gray-800">Bu sefer olmadı, tekrar deneyin!</span>`;
        }

        resultDiv.classList.remove('hidden');
    }

    // Reset preview
    function resetPreview() {
        currentRotation = 0;
        isSpinning = false;

        const resultDiv = document.getElementById('previewResult');
        if (resultDiv) {
            resultDiv.classList.add('hidden');
        }

        const button = document.getElementById('previewSpinButton');
        if (button) {
            button.disabled = false;
            button.textContent = 'Çarkı Çevir';
        }

        drawWheel();
    }

    // Event listeners
    function setupEventListeners() {
        // Spin button
        const spinButton = document.getElementById('previewSpinButton');
        if (spinButton) {
            spinButton.addEventListener('click', performSpin);
        }

        // Test spin button
        const testSpinButton = document.getElementById('testSpinBtn');
        if (testSpinButton) {
            testSpinButton.addEventListener('click', performSpin);
        }

        // Reset button
        const resetButton = document.getElementById('resetPreviewBtn');
        if (resetButton) {
            resetButton.addEventListener('click', resetPreview);
        }

        // Refresh button
        const refreshButton = document.getElementById('refreshPreviewBtn');
        if (refreshButton) {
            refreshButton.addEventListener('click', function() {
                if (window.loadWheelPreviewData) {
                    window.loadWheelPreviewData().then(data => {
                        if (data) {
                            updatePreview(data);
                        }
                    });
                }
            });
        }

        // Close button
        const closeButton = document.getElementById('closePreviewModal');
        if (closeButton) {
            closeButton.addEventListener('click', function() {
                document.getElementById('previewModal').classList.add('hidden');
            });
        }
    }

    // Make updatePreview globally accessible
    window.updatePreview = updatePreview;

    // Listen for custom updatePreview event
    document.addEventListener('updatePreview', function(event) {
        console.log('updatePreview event received:', event.detail);
        updatePreview(event.detail);
    });

    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Preview modal DOM ready');
            initPreview();
            setupEventListeners();
        });
    } else {
        console.log('Preview modal DOM already ready');
        initPreview();
        setupEventListeners();
    }

})(); // End of IIFE
</script>
