@using PushDashboard.ViewModels
@model GiftWheelViewModel

<div class="space-y-8">
    <!-- Header -->
    <div class="flex items-center justify-between">
        <div>
            <h3 class="text-lg font-medium text-gray-900"><PERSON><PERSON><PERSON><PERSON><PERSON></h3>
            <p class="text-sm text-gray-600">Çarkınızın performansını ve kullanım verilerini görüntüleyin</p>
        </div>
        
        <!-- Date Range Filter -->
        <div class="flex items-center space-x-3">
            <select id="statsDateRange" class="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
                <option value="7">Son 7 gün</option>
                <option value="30" selected>Son 30 gün</option>
                <option value="90">Son 90 gün</option>
                <option value="365">Son 1 yıl</option>
                <option value="custom"><PERSON>zel tarih</option>
            </select>
            <button id="refreshStatsBtn" class="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                <i class="fas fa-sync-alt mr-2"></i>
                Yenile
            </button>
        </div>
    </div>

    <!-- Custom Date Range (Hidden by default) -->
    <div id="customDateRange" class="bg-gray-50 rounded-lg p-4 hidden">
        <div class="flex items-center space-x-4">
            <div>
                <label for="startDate" class="block text-sm font-medium text-gray-700 mb-1">Başlangıç</label>
                <input type="date" id="startDate" class="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
            </div>
            <div>
                <label for="endDate" class="block text-sm font-medium text-gray-700 mb-1">Bitiş</label>
                <input type="date" id="endDate" class="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
            </div>
            <div class="pt-6">
                <button id="applyCustomDateBtn" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    Uygula
                </button>
            </div>
        </div>
    </div>

    <!-- Overview Stats -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div class="bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg p-6 text-white">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-blue-100 text-sm">Toplam Çevirme</p>
                    <p class="text-3xl font-bold" id="totalSpinsDisplay">@Model.Stats.TotalSpins</p>
                    <p class="text-blue-100 text-xs mt-1">
                        Bugün: <span id="spinsToday">@Model.Stats.SpinsToday</span>
                    </p>
                </div>
                <div class="text-blue-200">
                    <i class="fas fa-mouse-pointer text-3xl"></i>
                </div>
            </div>
        </div>

        <div class="bg-gradient-to-r from-green-500 to-green-600 rounded-lg p-6 text-white">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-green-100 text-sm">Hediye Çeki</p>
                    <p class="text-3xl font-bold" id="totalVouchersDisplay">@Model.Stats.TotalVouchersCreated</p>
                    <p class="text-green-100 text-xs mt-1">
                        Başarı oranı: <span id="conversionRate">@(Model.Stats.TotalSpins > 0 ? Math.Round((decimal)Model.Stats.TotalVouchersCreated / Model.Stats.TotalSpins * 100, 1) : 0)%</span>
                    </p>
                </div>
                <div class="text-green-200">
                    <i class="fas fa-gift text-3xl"></i>
                </div>
            </div>
        </div>

        <div class="bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg p-6 text-white">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-purple-100 text-sm">Bildirim</p>
                    <p class="text-3xl font-bold" id="totalNotificationsDisplay">@Model.Stats.TotalNotificationsSent</p>
                    <p class="text-purple-100 text-xs mt-1">
                        Bu hafta: <span id="notificationsThisWeek">@Model.Stats.SpinsThisWeek</span>
                    </p>
                </div>
                <div class="text-purple-200">
                    <i class="fas fa-bell text-3xl"></i>
                </div>
            </div>
        </div>

        <div class="bg-gradient-to-r from-yellow-500 to-yellow-600 rounded-lg p-6 text-white">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-yellow-100 text-sm">Toplam Maliyet</p>
                    <p class="text-3xl font-bold" id="totalCostDisplay">@Model.Stats.TotalCost.ToString("C")</p>
                    <p class="text-yellow-100 text-xs mt-1">
                        Bu ay: <span id="costThisMonth">@Model.Stats.TotalCost.ToString("C")</span>
                    </p>
                </div>
                <div class="text-yellow-200">
                    <i class="fas fa-lira-sign text-3xl"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Section -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <!-- Daily Activity Chart -->
        <div class="bg-white rounded-lg shadow-sm p-6">
            <h4 class="text-lg font-medium text-gray-900 mb-4">
                <i class="fas fa-chart-line mr-2 text-indigo-600"></i>
                Günlük Aktivite
            </h4>
            <div class="h-64">
                <canvas id="dailyActivityChart"></canvas>
            </div>
        </div>

        <!-- Prize Distribution Chart -->
        <div class="bg-white rounded-lg shadow-sm p-6">
            <h4 class="text-lg font-medium text-gray-900 mb-4">
                <i class="fas fa-chart-pie mr-2 text-indigo-600"></i>
                Ödül Dağılımı
            </h4>
            <div class="h-64">
                <canvas id="prizeDistributionChart"></canvas>
            </div>
        </div>
    </div>

    <!-- Prize Performance Table -->
    <div class="bg-white rounded-lg shadow-sm">
        <div class="px-6 py-4 border-b border-gray-200">
            <h4 class="text-lg font-medium text-gray-900">
                <i class="fas fa-trophy mr-2 text-indigo-600"></i>
                Ödül Performansı
            </h4>
        </div>
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Ödül</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Kazanma Sayısı</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Kazanma Oranı</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Olasılık</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Performans</th>
                    </tr>
                </thead>
                <tbody id="prizePerformanceTable" class="bg-white divide-y divide-gray-200">
                    @foreach (var prizeStats in Model.Stats.PrizeStats)
                    {
                        var prize = Model.Prizes.FirstOrDefault(p => p.Id == prizeStats.PrizeId);
                        if (prize != null)
                        {
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="prize-color-preview" style="background-color: @prize.Color"></div>
                                        <span class="font-medium text-gray-900">@prize.Name</span>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    @prizeStats.TimesWon
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    @prizeStats.WinRate.ToString("F1")%
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    @prize.Probability%
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    @{
                                        var performance = prize.Probability > 0 ? prizeStats.WinRate / prize.Probability : 0;
                                        var performanceClass = performance > 1.2m ? "text-red-600" : performance < 0.8m ? "text-yellow-600" : "text-green-600";
                                        var performanceIcon = performance > 1.2m ? "fa-arrow-up" : performance < 0.8m ? "fa-arrow-down" : "fa-check";
                                    }
                                    <span class="@performanceClass text-sm font-medium">
                                        <i class="fas @performanceIcon mr-1"></i>
                                        @performance.ToString("F2")x
                                    </span>
                                </td>
                            </tr>
                        }
                    }
                </tbody>
            </table>
        </div>
    </div>

    <!-- Recent Activity -->
    <div class="bg-white rounded-lg shadow-sm">
        <div class="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
            <h4 class="text-lg font-medium text-gray-900">
                <i class="fas fa-history mr-2 text-indigo-600"></i>
                Son Aktiviteler
            </h4>
            <button id="loadMoreActivities" class="text-indigo-600 hover:text-indigo-800 text-sm font-medium">
                Daha fazla göster
            </button>
        </div>
        <div class="divide-y divide-gray-200" id="recentActivitiesList">
            <!-- Activities will be loaded here via JavaScript -->
            <div class="px-6 py-4 text-center text-gray-500">
                <i class="fas fa-spinner fa-spin mr-2"></i>
                Yükleniyor...
            </div>
        </div>
    </div>

    <!-- Export Options -->
    <div class="bg-gray-50 rounded-lg p-6">
        <h4 class="text-md font-medium text-gray-900 mb-4">
            <i class="fas fa-download mr-2 text-indigo-600"></i>
            Veri Dışa Aktarma
        </h4>
        <div class="flex items-center space-x-4">
            <button id="exportExcelBtn" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                <i class="fas fa-file-excel mr-2"></i>
                Excel
            </button>
            <button id="exportCsvBtn" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                <i class="fas fa-file-csv mr-2"></i>
                CSV
            </button>
            <button id="exportPdfBtn" class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                <i class="fas fa-file-pdf mr-2"></i>
                PDF Rapor
            </button>
        </div>
        <p class="text-xs text-gray-500 mt-2">
            Seçili tarih aralığındaki tüm veriler dışa aktarılacaktır.
        </p>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Date range selector
    const dateRangeSelect = document.getElementById('statsDateRange');
    const customDateRange = document.getElementById('customDateRange');
    
    dateRangeSelect.addEventListener('change', function() {
        if (this.value === 'custom') {
            customDateRange.classList.remove('hidden');
        } else {
            customDateRange.classList.add('hidden');
            // Auto-refresh stats for predefined ranges
            refreshStats();
        }
    });
    
    // Refresh stats function
    function refreshStats() {
        // This will be implemented in the main gift-wheel-admin.js
        if (window.refreshStatistics) {
            window.refreshStatistics();
        }
    }
    
    // Refresh button
    document.getElementById('refreshStatsBtn').addEventListener('click', refreshStats);
    
    // Custom date apply button
    document.getElementById('applyCustomDateBtn').addEventListener('click', function() {
        const startDate = document.getElementById('startDate').value;
        const endDate = document.getElementById('endDate').value;
        
        if (startDate && endDate) {
            refreshStats();
        } else {
            alert('Lütfen başlangıç ve bitiş tarihlerini seçin.');
        }
    });
    
    // Load recent activities
    loadRecentActivities();
    
    function loadRecentActivities() {
        // This will be implemented in the main gift-wheel-admin.js
        if (window.loadRecentActivities) {
            window.loadRecentActivities();
        }
    }
    
    // Load more activities button
    document.getElementById('loadMoreActivities').addEventListener('click', function() {
        loadRecentActivities(50); // Load more activities
    });
});
</script>
