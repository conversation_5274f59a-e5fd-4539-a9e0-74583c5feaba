@using PushDashboard.ViewModels
@model GiftWheelViewModel
@{
    ViewData["Title"] = "<PERSON><PERSON><PERSON>";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

@section Styles {
    <style>
        .gift-wheel-container {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .wheel-preview {
            width: 200px;
            height: 200px;
            border-radius: 50%;
            background: conic-gradient(
                #ff6b6b 0deg 60deg,
                #4ecdc4 60deg 120deg,
                #45b7d1 120deg 180deg,
                #96ceb4 180deg 240deg,
                #feca57 240deg 300deg,
                #ff9ff3 300deg 360deg
            );
            position: relative;
            margin: 0 auto;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }
        
        .wheel-center {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 40px;
            height: 40px;
            background: white;
            border-radius: 50%;
            box-shadow: 0 2px 10px rgba(0,0,0,0.2);
        }
        
        .stats-card {
            transition: transform 0.2s ease-in-out;
        }
        
        .stats-card:hover {
            transform: translateY(-2px);
        }
        
        .prize-color-preview {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
            border: 2px solid #fff;
            box-shadow: 0 1px 3px rgba(0,0,0,0.2);
        }
    </style>
}

<!-- Anti-forgery token for AJAX requests -->
@Html.AntiForgeryToken()

<main class="p-4 bg-gray-50 min-h-screen">
    <!-- Header Section -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-800 flex items-center">
                    <i class="fas fa-dharmachakra text-yellow-500 mr-3"></i>
                    Hediye Çarkı
                </h1>
                <p class="text-gray-600 mt-2">Müşterileriniz için etkileşimli hediye çarkı oluşturun ve yönetin</p>
            </div>
            
            @if (Model.Wheel != null)
            {
                <div class="flex items-center space-x-4">
                    <div class="flex items-center">
                        <span class="text-sm text-gray-600 mr-2">Durum:</span>
                        <button id="toggleWheelStatus" 
                                class="relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 @(Model.Wheel.IsActive ? "bg-green-600" : "bg-gray-200")"
                                data-active="@Model.Wheel.IsActive.ToString().ToLower()">
                            <span class="inline-block h-4 w-4 transform rounded-full bg-white transition-transform @(Model.Wheel.IsActive ? "translate-x-6" : "translate-x-1")"></span>
                        </button>
                        <span class="ml-2 text-sm font-medium @(Model.Wheel.IsActive ? "text-green-600" : "text-gray-500")">
                            @(Model.Wheel.IsActive ? "Aktif" : "Pasif")
                        </span>
                    </div>

                    <!-- R2 Export Button -->
                    <form method="post" action="@Url.Action("ExportToR2")" class="inline">
                        <button type="submit"
                                class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors text-sm"
                                title="Hediye çarkını R2 storage'a aktar">
                            <i class="fas fa-cloud-upload-alt mr-2"></i>
                            R2'ye Aktar
                        </button>
                    </form>
                </div>
            }
        </div>
    </div>

    @if (Model.Wheel == null)
    {
        <!-- Create Wheel Section -->
        <div class="bg-white rounded-lg shadow-sm p-8 text-center">
            <div class="wheel-preview mb-6">
                <div class="wheel-center"></div>
            </div>
            <h2 class="text-2xl font-bold text-gray-800 mb-4">Hediye Çarkınızı Oluşturun</h2>
            <p class="text-gray-600 mb-6">Müşterileriniz için etkileşimli bir hediye çarkı oluşturun ve otomatik hediye çeki gönderimi başlatın.</p>
            <button id="createWheelBtn" class="bg-indigo-600 hover:bg-indigo-700 text-white px-6 py-3 rounded-lg font-medium transition-colors">
                <i class="fas fa-plus mr-2"></i>
                Çark Oluştur
            </button>
        </div>
    }
    else
    {
        <!-- Integration Status Check -->
        @if (Model.IntegrationStatus.MissingIntegrations.Any() || Model.IntegrationStatus.ConfigurationIssues.Any())
        {
            <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
                <div class="flex items-start">
                    <i class="fas fa-exclamation-triangle text-yellow-500 mt-1 mr-3"></i>
                    <div>
                        <h3 class="text-yellow-800 font-medium mb-2">Entegrasyon Uyarısı</h3>
                        @if (Model.IntegrationStatus.MissingIntegrations.Any())
                        {
                            <p class="text-yellow-700 text-sm mb-2">Eksik entegrasyonlar:</p>
                            <ul class="text-yellow-700 text-sm list-disc list-inside mb-2">
                                @foreach (var missing in Model.IntegrationStatus.MissingIntegrations)
                                {
                                    <li>@missing</li>
                                }
                            </ul>
                        }
                        @if (Model.IntegrationStatus.ConfigurationIssues.Any())
                        {
                            <p class="text-yellow-700 text-sm mb-2">Yapılandırma sorunları:</p>
                            <ul class="text-yellow-700 text-sm list-disc list-inside">
                                @foreach (var issue in Model.IntegrationStatus.ConfigurationIssues)
                                {
                                    <li>@issue</li>
                                }
                            </ul>
                        }
                        <a href="/Integration" class="text-yellow-800 underline text-sm mt-2 inline-block">
                            Entegrasyonları Düzenle
                        </a>
                    </div>
                </div>
            </div>
        }

        <!-- Statistics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div class="stats-card bg-white rounded-lg shadow-sm p-6">
                <div class="flex items-center">
                    <div class="p-3 bg-blue-100 rounded-lg">
                        <i class="fas fa-mouse-pointer text-blue-600 text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm text-gray-600">Toplam Çevirme</p>
                        <p class="text-2xl font-bold text-gray-800">@Model.Stats.TotalSpins</p>
                    </div>
                </div>
            </div>

            <div class="stats-card bg-white rounded-lg shadow-sm p-6">
                <div class="flex items-center">
                    <div class="p-3 bg-green-100 rounded-lg">
                        <i class="fas fa-gift text-green-600 text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm text-gray-600">Hediye Çeki</p>
                        <p class="text-2xl font-bold text-gray-800">@Model.Stats.TotalVouchersCreated</p>
                    </div>
                </div>
            </div>

            <div class="stats-card bg-white rounded-lg shadow-sm p-6">
                <div class="flex items-center">
                    <div class="p-3 bg-purple-100 rounded-lg">
                        <i class="fas fa-bell text-purple-600 text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm text-gray-600">Bildirim</p>
                        <p class="text-2xl font-bold text-gray-800">@Model.Stats.TotalNotificationsSent</p>
                    </div>
                </div>
            </div>

            <div class="stats-card bg-white rounded-lg shadow-sm p-6">
                <div class="flex items-center">
                    <div class="p-3 bg-yellow-100 rounded-lg">
                        <i class="fas fa-lira-sign text-yellow-600 text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm text-gray-600">Toplam Maliyet</p>
                        <p class="text-2xl font-bold text-gray-800">@Model.Stats.TotalCost.ToString("C")</p>
                    </div>
                </div>
            </div>
        </div>

  
    }

    <!-- Main Content Tabs -->
    <div class="bg-white rounded-lg shadow-sm">
        <!-- Tab Navigation -->
        <div class="border-b border-gray-200">
            <nav class="flex space-x-8 px-6" aria-label="Tabs">
                <button class="tab-button active border-b-2 border-indigo-500 py-4 px-1 text-sm font-medium text-indigo-600" data-tab="prizes">
                    <i class="fas fa-trophy mr-2"></i>
                    Ödüller
                </button>
                <button class="tab-button border-b-2 border-transparent py-4 px-1 text-sm font-medium text-gray-500 hover:text-gray-700 hover:border-gray-300" data-tab="settings">
                    <i class="fas fa-cog mr-2"></i>
                    Ayarlar
                </button>
                <button class="tab-button border-b-2 border-transparent py-4 px-1 text-sm font-medium text-gray-500 hover:text-gray-700 hover:border-gray-300" data-tab="statistics">
                    <i class="fas fa-chart-bar mr-2"></i>
                    İstatistikler
                </button>
                <button class="tab-button border-b-2 border-transparent py-4 px-1 text-sm font-medium text-gray-500 hover:text-gray-700 hover:border-gray-300" data-tab="script">
                    <i class="fas fa-code mr-2"></i>
                    Entegrasyon
                </button>
            </nav>
        </div>

        <!-- Tab Content -->
        <div class="p-6">
            <!-- Prizes Tab -->
            <div id="prizes-tab" class="tab-content" style="display: none;">
                <div class="p-4 bg-blue-100 rounded mb-4">
                    <h4 class="text-lg font-bold">Prizes Tab Content</h4>
                    <p>This is a test to see if tab content is visible</p>
                </div>
                @await Html.PartialAsync("Partials/_PrizeManagement", Model)
            </div>

            <!-- Settings Tab -->
            <div id="settings-tab" class="tab-content hidden" style="display: none;">
                <div class="p-4 bg-yellow-100 rounded mb-4">
                    <h4 class="text-lg font-bold">Settings Tab Content</h4>
                    <p>This is a test to see if tab content is visible</p>
                </div>
                @await Html.PartialAsync("Partials/_WheelSettings", Model)
            </div>

            <!-- Statistics Tab -->
            <div id="statistics-tab" class="tab-content hidden" style="display: none;">
                <div class="p-4 bg-purple-100 rounded mb-4">
                    <h4 class="text-lg font-bold">Statistics Tab Content</h4>
                    <p>This is a test to see if tab content is visible</p>
                </div>
                @await Html.PartialAsync("Partials/_Statistics", Model)
            </div>

            <!-- Script Tab -->
            <div id="script-tab" class="tab-content hidden" style="display: none;">
                <div class="p-4 bg-green-100 rounded mb-4">
                    <h4 class="text-lg font-bold">Script Tab Content</h4>
                    <p>This is a test to see if tab content is visible</p>
                </div>
                @await Html.PartialAsync("Partials/_ScriptGenerator", Model)
            </div>
        </div>
    </div>
</main>

<!-- Modals -->
@await Html.PartialAsync("Partials/_PrizeModal")
@await Html.PartialAsync("Partials/_PreviewModal")

@section Scripts {
    <script src="~/js/gift-wheel-admin.js"></script>
}
