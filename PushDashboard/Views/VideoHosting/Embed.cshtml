@model PushDashboard.DTOs.VideoPlayerDto
@{
    Layout = null; // No layout for embed
}

<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>@Model.Title</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        html, body {
            width: 100%;
            height: 100%;
            background-color: #000;
            overflow: hidden;
        }
        
        .embed-container {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: #000;
        }
        
        #videoPlayer {
            width: 100%;
            height: 100%;
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
        }
        
        .video-overlay {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: linear-gradient(transparent, rgba(0,0,0,0.7));
            color: white;
            padding: 10px;
            font-family: Arial, sans-serif;
            font-size: 12px;
            opacity: 0;
            transition: opacity 0.3s;
        }
        
        .embed-container:hover .video-overlay {
            opacity: 1;
        }
        
        .video-title {
            font-weight: bold;
            margin-bottom: 2px;
        }
        
        .video-branding {
            font-size: 10px;
            opacity: 0.8;
        }
        
        /* Theme-specific styles */
        @if (Model.PlayerTheme == "dark")
        {
            <text>
            .embed-container { background-color: #1a1a1a; }
            </text>
        }
        else if (Model.PlayerTheme == "light")
        {
            <text>
            .embed-container { background-color: #f8f9fa; }
            </text>
        }
        
        /* Custom CSS */
        @if (!string.IsNullOrEmpty(Model.CustomPlayerCss))
        {
            @Html.Raw(Model.CustomPlayerCss)
        }
    </style>
</head>
<body>
    <div class="embed-container">
        <video 
            id="videoPlayer"
            controls="@(Model.ShowControls ? "controls" : "")"
            @(Model.AutoPlay ? "autoplay" : "")
            poster="@Model.ThumbnailUrl"
            preload="metadata"
            playsinline>
            <source src="@Model.VideoUrl" type="video/mp4">
            <p>Tarayıcınız video oynatmayı desteklemiyor.</p>
        </video>
        
        <div class="video-overlay">
            <div class="video-title">@Model.Title</div>
            <div class="video-branding">Powered by Video Hosting</div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const video = document.getElementById('videoPlayer');
            let hasStartedPlaying = false;
            let watchStartTime = null;
            
            // Track video events for analytics
            video.addEventListener('play', function() {
                if (!hasStartedPlaying) {
                    hasStartedPlaying = true;
                    watchStartTime = Date.now();
                    recordVideoEvent('play');
                }
            });
            
            video.addEventListener('pause', function() {
                if (watchStartTime) {
                    const watchDuration = (Date.now() - watchStartTime) / 1000;
                    recordVideoEvent('pause', video.currentTime, watchDuration);
                }
            });
            
            video.addEventListener('ended', function() {
                if (watchStartTime) {
                    const watchDuration = (Date.now() - watchStartTime) / 1000;
                    recordVideoEvent('complete', video.currentTime, watchDuration);
                }
            });
            
            // Track when user leaves the page
            window.addEventListener('beforeunload', function() {
                if (watchStartTime && !video.paused && !video.ended) {
                    const watchDuration = (Date.now() - watchStartTime) / 1000;
                    recordVideoEvent('exit', video.currentTime, watchDuration);
                }
            });
            
            function recordVideoEvent(eventType, currentTime = null, watchDuration = null) {
                // Send analytics data to parent window or server
                try {
                    const data = {
                        videoId: @Model.Id,
                        event: eventType,
                        currentTime: currentTime,
                        watchDuration: watchDuration,
                        timestamp: Date.now(),
                        referrer: document.referrer
                    };
                    
                    // Try to send to parent window (if embedded)
                    if (window.parent && window.parent !== window) {
                        window.parent.postMessage({
                            type: 'videoEvent',
                            data: data
                        }, '*');
                    }
                    
                    // Also send to server for analytics
                    fetch('/VideoHosting/RecordView', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify(data)
                    }).catch(function(error) {
                        console.log('Analytics error:', error);
                    });
                } catch (error) {
                    console.log('Event tracking error:', error);
                }
            }
            
            // Disable right-click context menu if download is not allowed
            @if (!Model.AllowDownload)
            {
                <text>
                video.addEventListener('contextmenu', function(e) {
                    e.preventDefault();
                    return false;
                });
                
                // Disable keyboard shortcuts for downloading
                document.addEventListener('keydown', function(e) {
                    if (e.ctrlKey && (e.key === 's' || e.key === 'S')) {
                        e.preventDefault();
                        return false;
                    }
                    // Disable F12 (Developer Tools)
                    if (e.key === 'F12') {
                        e.preventDefault();
                        return false;
                    }
                    // Disable Ctrl+Shift+I (Developer Tools)
                    if (e.ctrlKey && e.shiftKey && e.key === 'I') {
                        e.preventDefault();
                        return false;
                    }
                    // Disable Ctrl+U (View Source)
                    if (e.ctrlKey && e.key === 'u') {
                        e.preventDefault();
                        return false;
                    }
                });
                
                // Disable drag and drop
                video.addEventListener('dragstart', function(e) {
                    e.preventDefault();
                    return false;
                });
                </text>
            }
            
            // Auto-resize for responsive embedding
            function resizeVideo() {
                const container = document.querySelector('.embed-container');
                const video = document.getElementById('videoPlayer');
                
                if (container && video) {
                    const containerAspect = container.clientWidth / container.clientHeight;
                    const videoAspect = @Model.Width / @Model.Height;
                    
                    if (containerAspect > videoAspect) {
                        video.style.width = 'auto';
                        video.style.height = '100%';
                    } else {
                        video.style.width = '100%';
                        video.style.height = 'auto';
                    }
                }
            }
            
            window.addEventListener('resize', resizeVideo);
            resizeVideo(); // Initial resize
        });
    </script>
</body>
</html>
