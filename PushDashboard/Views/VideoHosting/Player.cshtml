@model PushDashboard.DTOs.VideoPlayerDto
@{
    ViewData["Title"] = Model.Title;
    Layout = "_PlayerLayout";
}

<div class="video-container">
    <div class="w-full flex justify-center">
        <video
            id="videoPlayer"
            controls="@(Model.ShowControls ? "controls" : "")"
            @(Model.AutoPlay ? "autoplay" : "")
            poster="@Model.ThumbnailUrl"
            preload="metadata"
            class="max-w-full max-h-screen">
            <source src="@Model.VideoUrl" type="video/mp4">
            <p class="text-white">Tarayıcınız video oynatmayı desteklemiyor.</p>
        </video>
    </div>

    <div class="video-info">
        <h1 class="video-title">@Model.Title</h1>
        @if (!string.IsNullOrEmpty(Model.Description))
        {
            <p class="video-description">@Model.Description</p>
        }

        <div class="video-meta">
            <span>Süre: @Model.Duration.ToString(@"mm\:ss")</span>
            <span>@Model.Width x @Model.Height</span>
        </div>
    </div>
</div>



@section Scripts {
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const video = document.getElementById('videoPlayer');
            let hasStartedPlaying = false;

            // Apply theme styles
            @if (Model.PlayerTheme == "dark")
            {
                <text>
                document.body.style.backgroundColor = '#1a1a1a';
                </text>
            }
            else if (Model.PlayerTheme == "light")
            {
                <text>
                document.body.style.backgroundColor = '#f8f9fa';
                document.body.style.color = '#333';
                </text>
            }

            // Apply custom CSS
            @if (!string.IsNullOrEmpty(Model.CustomPlayerCss))
            {
                <text>
                const style = document.createElement('style');
                style.textContent = '@Html.Raw(Model.CustomPlayerCss)';
                document.head.appendChild(style);
                </text>
            }

            // Track video events for analytics
            video.addEventListener('play', function() {
                if (!hasStartedPlaying) {
                    hasStartedPlaying = true;
                    recordVideoEvent('play');
                }
            });

            video.addEventListener('ended', function() {
                recordVideoEvent('complete');
            });

            // Track watch duration every 10 seconds
            setInterval(function() {
                if (!video.paused && !video.ended) {
                    recordVideoEvent('progress', video.currentTime);
                }
            }, 10000);

            function recordVideoEvent(eventType, currentTime = null) {
                console.log('Video event:', eventType, currentTime);
                // TODO: Send analytics data to server
            }

            // Disable right-click context menu if download is not allowed
            @if (!Model.AllowDownload)
            {
                <text>
                video.addEventListener('contextmenu', function(e) {
                    e.preventDefault();
                    return false;
                });

                // Disable keyboard shortcuts for downloading
                document.addEventListener('keydown', function(e) {
                    if (e.ctrlKey && (e.key === 's' || e.key === 'S')) {
                        e.preventDefault();
                        return false;
                    }
                });
                </text>
            }
        });
    </script>
}
