@model PushDashboard.DTOs.VideoSettingsDto
@{
    ViewData["Title"] = "Video Hosting Ayarları";
}

<div class="p-6">
    <!-- Header -->
    <div class="flex justify-between items-center mb-6">
        <div>
            <h1 class="text-3xl font-bold text-gray-900">Video Hosting Ayarları</h1>
            <p class="text-gray-600 mt-1">Video hosting modülü ayarlarını yönetin</p>
        </div>
        <div>
            <a href="@Url.Action("Index")" class="bg-gray-100 hover:bg-gray-200 text-gray-700 px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors">
                <i class="fas fa-arrow-left"></i>
                <span>Geri <PERSON></span>
            </a>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
        <!-- Settings Form -->
        <div class="lg:col-span-3">
            <form id="settingsForm" class="space-y-6">
                <!-- Pricing Settings -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-semibold text-gray-900">Fiyatlandırma Ayarları</h3>
                    </div>
                    <div class="p-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label for="costPerMinute" class="block text-sm font-medium text-gray-700 mb-2">Dakika Başına Maliyet (₺)</label>
                                <input type="number" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                       id="costPerMinute" name="CostPerMinute"
                                       value="@(Model?.CostPerMinute ?? 0.5m)" step="0.01" min="0.01" max="100" required>
                                <p class="text-sm text-gray-500 mt-1">Video yükleme maliyeti dakika başına</p>
                            </div>
                            <div>
                                <label for="storageCostPerGBPerMonth" class="block text-sm font-medium text-gray-700 mb-2">GB Başına Aylık Depolama Maliyeti (₺)</label>
                                <input type="number" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                       id="storageCostPerGBPerMonth" name="StorageCostPerGBPerMonth"
                                       value="@(Model?.StorageCostPerGBPerMonth ?? 0.1m)" step="0.01" min="0.01" max="10" required>
                                <p class="text-sm text-gray-500 mt-1">Aylık depolama maliyeti GB başına</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Upload Limits -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-semibold text-gray-900">Yükleme Limitleri</h3>
                    </div>
                    <div class="p-6">
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                            <div>
                                <label for="maxFileSizeMB" class="block text-sm font-medium text-gray-700 mb-2">Maksimum Dosya Boyutu (MB)</label>
                                <input type="number" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                       id="maxFileSizeMB" name="MaxFileSizeMB"
                                       value="@(Model?.MaxFileSizeMB ?? 500)" min="1" max="5000" required>
                                <p class="text-sm text-gray-500 mt-1">1 MB - 5000 MB arası</p>
                            </div>
                            <div>
                                <label for="maxDurationMinutes" class="block text-sm font-medium text-gray-700 mb-2">Maksimum Video Süresi (Dakika)</label>
                                <input type="number" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                       id="maxDurationMinutes" name="MaxDurationMinutes"
                                       value="@(Model?.MaxDurationMinutes ?? 60)" min="1" max="300" required>
                                <p class="text-sm text-gray-500 mt-1">1 - 300 dakika arası</p>
                            </div>
                            <div>
                                <label for="maxVideosPerCompany" class="block text-sm font-medium text-gray-700 mb-2">Maksimum Video Sayısı</label>
                                <input type="number" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                       id="maxVideosPerCompany" name="MaxVideosPerCompany"
                                       value="@(Model?.MaxVideosPerCompany ?? 100)" min="1" max="1000" required>
                                <p class="text-sm text-gray-500 mt-1">Şirket başına maksimum video sayısı</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Player Settings -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">Video Player Ayarları</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="playerTheme">Player Teması</label>
                                    <select class="form-control" id="playerTheme" name="PlayerTheme">
                                        <option value="default" selected="@(Model?.PlayerTheme == "default")">Varsayılan</option>
                                        <option value="dark" selected="@(Model?.PlayerTheme == "dark")">Koyu</option>
                                        <option value="light" selected="@(Model?.PlayerTheme == "light")">Açık</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <div class="form-check mt-4">
                                        <input type="checkbox" class="form-check-input" id="autoPlay" name="AutoPlay" 
                                               @(Model?.AutoPlay == true ? "checked" : "")>
                                        <label class="form-check-label" for="autoPlay">
                                            Otomatik oynatma
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input type="checkbox" class="form-check-input" id="showControls" name="ShowControls" 
                                               @(Model?.ShowControls != false ? "checked" : "")>
                                        <label class="form-check-label" for="showControls">
                                            Player kontrollerini göster
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input type="checkbox" class="form-check-input" id="allowDownload" name="AllowDownload" 
                                               @(Model?.AllowDownload == true ? "checked" : "")>
                                        <label class="form-check-label" for="allowDownload">
                                            Video indirmeye izin ver
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="customPlayerCss">Özel Player CSS</label>
                            <textarea class="form-control" id="customPlayerCss" name="CustomPlayerCss" rows="4" 
                                      maxlength="5000" placeholder="Özel CSS kodları buraya yazın...">@Model?.CustomPlayerCss</textarea>
                            <small class="form-text text-muted">Player görünümünü özelleştirmek için CSS kodları</small>
                        </div>
                    </div>
                </div>

                <!-- Security Settings -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">Güvenlik ve Erişim Ayarları</h6>
                    </div>
                    <div class="card-body">
                        <div class="form-check mb-3">
                            <input type="checkbox" class="form-check-input" id="requireAuthentication" name="RequireAuthentication" 
                                   @(Model?.RequireAuthentication == true ? "checked" : "")>
                            <label class="form-check-label" for="requireAuthentication">
                                <strong>Kimlik doğrulama gerektir</strong>
                                <small class="d-block text-muted">Video izlemek için giriş yapma zorunluluğu</small>
                            </label>
                        </div>

                        <div class="form-check mb-3">
                            <input type="checkbox" class="form-check-input" id="allowEmbedding" name="AllowEmbedding" 
                                   @(Model?.AllowEmbedding != false ? "checked" : "")>
                            <label class="form-check-label" for="allowEmbedding">
                                <strong>Embed edilmeye izin ver</strong>
                                <small class="d-block text-muted">Videoların iframe ile gömülmesine izin ver</small>
                            </label>
                        </div>

                        <div class="form-group">
                            <label for="allowedDomains">İzin Verilen Domainler (JSON)</label>
                            <textarea class="form-control" id="allowedDomains" name="AllowedDomains" rows="3" 
                                      maxlength="2000" placeholder='["example.com", "subdomain.example.com"]'>@Model?.AllowedDomains</textarea>
                            <small class="form-text text-muted">
                                Embed için izin verilen domain listesi (JSON formatında). Boş bırakılırsa tüm domainler izinli olur.
                            </small>
                        </div>
                    </div>
                </div>

                <!-- Save Button -->
                <div class="card shadow mb-4">
                    <div class="card-body">
                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="fas fa-save"></i> Ayarları Kaydet
                        </button>
                        <button type="button" class="btn btn-outline-secondary btn-lg ml-2" onclick="resetToDefaults()">
                            <i class="fas fa-undo"></i> Varsayılanlara Sıfırla
                        </button>
                    </div>
                </div>
            </form>
        </div>

        <!-- Info Panel -->
        <div class="col-lg-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Ayar Bilgileri</h6>
                </div>
                <div class="card-body">
                    <h6>Fiyatlandırma</h6>
                    <p class="small text-muted">
                        Video yükleme maliyeti video süresine göre hesaplanır. 
                        Depolama maliyeti ise dosya boyutuna göre aylık olarak hesaplanır.
                    </p>

                    <h6>Yükleme Limitleri</h6>
                    <p class="small text-muted">
                        Bu limitler şirketinizin video yükleme kapasitesini belirler. 
                        Limitler aşıldığında yeni video yüklenemez.
                    </p>

                    <h6>Player Ayarları</h6>
                    <p class="small text-muted">
                        Video player'ın görünümü ve davranışını kontrol eder. 
                        Özel CSS ile tamamen özelleştirebilirsiniz.
                    </p>

                    <h6>Güvenlik</h6>
                    <p class="small text-muted">
                        Video erişim kontrolü ve embed güvenlik ayarları. 
                        Domain kısıtlaması ile sadece belirli sitelerden embed edilmesine izin verebilirsiniz.
                    </p>
                </div>
            </div>

            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Desteklenen Formatlar</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-6">
                            <ul class="list-unstyled mb-0">
                                <li><i class="fas fa-check text-success"></i> MP4</li>
                                <li><i class="fas fa-check text-success"></i> AVI</li>
                                <li><i class="fas fa-check text-success"></i> MOV</li>
                                <li><i class="fas fa-check text-success"></i> WMV</li>
                            </ul>
                        </div>
                        <div class="col-6">
                            <ul class="list-unstyled mb-0">
                                <li><i class="fas fa-check text-success"></i> FLV</li>
                                <li><i class="fas fa-check text-success"></i> WebM</li>
                                <li><i class="fas fa-check text-success"></i> MKV</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function() {
            $('#settingsForm').on('submit', function(e) {
                e.preventDefault();
                saveSettings();
            });

            // JSON validation for allowed domains
            $('#allowedDomains').on('blur', function() {
                const value = $(this).val().trim();
                if (value && value !== '') {
                    try {
                        JSON.parse(value);
                        $(this).removeClass('is-invalid').addClass('is-valid');
                    } catch (e) {
                        $(this).removeClass('is-valid').addClass('is-invalid');
                        showToast('warning', 'Geçersiz JSON formatı. Örnek: ["example.com", "test.com"]');
                    }
                } else {
                    $(this).removeClass('is-invalid is-valid');
                }
            });
        });

        function saveSettings() {
            const formData = {
                CostPerMinute: parseFloat($('#costPerMinute').val()),
                StorageCostPerGBPerMonth: parseFloat($('#storageCostPerGBPerMonth').val()),
                MaxFileSizeMB: parseInt($('#maxFileSizeMB').val()),
                MaxDurationMinutes: parseInt($('#maxDurationMinutes').val()),
                MaxVideosPerCompany: parseInt($('#maxVideosPerCompany').val()),
                AutoPlay: $('#autoPlay').is(':checked'),
                ShowControls: $('#showControls').is(':checked'),
                AllowDownload: $('#allowDownload').is(':checked'),
                PlayerTheme: $('#playerTheme').val(),
                CustomPlayerCss: $('#customPlayerCss').val(),
                RequireAuthentication: $('#requireAuthentication').is(':checked'),
                AllowEmbedding: $('#allowEmbedding').is(':checked'),
                AllowedDomains: $('#allowedDomains').val().trim()
            };

            // Validate JSON if provided
            if (formData.AllowedDomains) {
                try {
                    JSON.parse(formData.AllowedDomains);
                } catch (e) {
                    showToast('error', 'İzin verilen domainler geçersiz JSON formatında.');
                    return;
                }
            }

            $.post('@Url.Action("UpdateSettings")', formData)
                .done(function(response) {
                    if (response.success) {
                        showToast('success', response.message);
                    } else {
                        showToast('error', response.message);
                    }
                })
                .fail(function() {
                    showToast('error', 'Ayarlar kaydedilirken hata oluştu.');
                });
        }

        function resetToDefaults() {
            if (confirm('Tüm ayarları varsayılan değerlere sıfırlamak istediğinizden emin misiniz?')) {
                $('#costPerMinute').val(0.5);
                $('#storageCostPerGBPerMonth').val(0.1);
                $('#maxFileSizeMB').val(500);
                $('#maxDurationMinutes').val(60);
                $('#maxVideosPerCompany').val(100);
                $('#autoPlay').prop('checked', false);
                $('#showControls').prop('checked', true);
                $('#allowDownload').prop('checked', false);
                $('#playerTheme').val('default');
                $('#customPlayerCss').val('');
                $('#requireAuthentication').prop('checked', false);
                $('#allowEmbedding').prop('checked', true);
                $('#allowedDomains').val('');
                
                showToast('info', 'Ayarlar varsayılan değerlere sıfırlandı. Kaydetmeyi unutmayın.');
            }
        }

        function showToast(type, message) {
            const toastClass = type === 'success' ? 'alert-success' : 
                              type === 'warning' ? 'alert-warning' : 
                              type === 'info' ? 'alert-info' : 'alert-danger';
            const toast = `
                <div class="alert ${toastClass} alert-dismissible fade show position-fixed" 
                     style="top: 20px; right: 20px; z-index: 9999; min-width: 300px;">
                    ${message}
                    <button type="button" class="close" data-dismiss="alert">
                        <span>&times;</span>
                    </button>
                </div>
            `;
            $('body').append(toast);
            
            setTimeout(() => {
                $('.alert').alert('close');
            }, 5000);
        }
    </script>
}
