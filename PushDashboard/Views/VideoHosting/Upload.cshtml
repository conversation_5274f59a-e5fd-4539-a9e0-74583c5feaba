@{
    ViewData["Title"] = "Video Yükle";
    var settings = ViewBag.Settings as PushDashboard.DTOs.VideoSettingsDto;
}

<div class="p-6">
    <!-- Header -->
    <div class="flex justify-between items-center mb-6">
        <div>
            <h1 class="text-3xl font-bold text-gray-900">Video Yükle</h1>
            <p class="text-gray-600 mt-1">Yeni video yükleyin ve paylaşın</p>
        </div>
        <div>
            <a href="@Url.Action("Index")" class="bg-gray-100 hover:bg-gray-200 text-gray-700 px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors">
                <i class="fas fa-arrow-left"></i>
                <span>Geri Dö<PERSON></span>
            </a>
        </div>
    </div>

    <!-- Upload Limits Info -->
    @if (settings != null)
    {
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
            <h3 class="flex items-center text-lg font-semibold text-blue-900 mb-3">
                <i class="fas fa-info-circle mr-2"></i>
                Yükleme Limitleri
            </h3>
            <ul class="space-y-2 text-blue-800">
                <li><strong>Maksimum dosya boyutu:</strong> @settings.MaxFileSizeMB MB</li>
                <li><strong>Maksimum video süresi:</strong> @settings.MaxDurationMinutes dakika</li>
                <li><strong>Dakika başına maliyet:</strong> ₺@settings.CostPerMinute.ToString("F2")</li>
                <li><strong>Desteklenen formatlar:</strong> MP4, AVI, MOV, WMV, FLV, WebM, MKV</li>
            </ul>
        </div>
    }

    <!-- Upload Form -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div class="lg:col-span-2">
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-semibold text-gray-900">Video Bilgileri</h3>
                </div>
                <div class="p-6">
                    <form id="videoUploadForm" enctype="multipart/form-data">
                        <div class="mb-6">
                            <label for="videoTitle" class="block text-sm font-medium text-gray-700 mb-2">
                                Video Başlığı <span class="text-red-500">*</span>
                            </label>
                            <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                   id="videoTitle" name="Title" required maxlength="200"
                                   placeholder="Videonuz için açıklayıcı bir başlık girin">
                        </div>

                        <div class="mb-6">
                            <label for="videoDescription" class="block text-sm font-medium text-gray-700 mb-2">Açıklama</label>
                            <textarea class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                      id="videoDescription" name="Description" rows="4" maxlength="1000"
                                      placeholder="Video hakkında kısa bir açıklama yazın (isteğe bağlı)"></textarea>
                        </div>

                        <div class="mb-6">
                            <label for="videoFile" class="block text-sm font-medium text-gray-700 mb-2">
                                Video Dosyası <span class="text-red-500">*</span>
                            </label>
                            <div class="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md hover:border-gray-400 transition-colors">
                                <div class="space-y-1 text-center">
                                    <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                                        <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                                    </svg>
                                    <div class="flex text-sm text-gray-600">
                                        <label for="videoFile" class="relative cursor-pointer bg-white rounded-md font-medium text-blue-600 hover:text-blue-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-blue-500">
                                            <span>Video dosyası seçin</span>
                                            <input id="videoFile" name="VideoFile" type="file" class="sr-only" accept="video/*" required>
                                        </label>
                                        <p class="pl-1">veya sürükleyip bırakın</p>
                                    </div>
                                    <p class="text-xs text-gray-500">MP4, AVI, MOV, WMV, FLV, WebM, MKV</p>
                                </div>
                            </div>
                        </div>

                        <div class="mb-6">
                            <label class="flex items-start">
                                <input type="checkbox" class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50 mt-1"
                                       id="isPublic" name="IsPublic">
                                <div class="ml-3">
                                    <span class="font-medium text-gray-900">Herkese açık video</span>
                                    <p class="text-sm text-gray-500">
                                        İşaretlenirse video herkese açık olur ve paylaşılabilir.
                                        İşaretlenmezse sadece özel link ile erişilebilir.
                                    </p>
                                </div>
                            </label>
                        </div>

                        <div class="flex space-x-3">
                            <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg flex items-center space-x-2 transition-colors" id="uploadBtn">
                                <i class="fas fa-upload"></i>
                                <span>Video Yükle</span>
                            </button>
                            <button type="button" class="bg-gray-100 hover:bg-gray-200 text-gray-700 px-6 py-3 rounded-lg flex items-center space-x-2 transition-colors" onclick="resetForm()">
                                <i class="fas fa-redo"></i>
                                <span>Sıfırla</span>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="space-y-6">
            <!-- Upload Progress -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 hidden" id="progressCard">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-semibold text-gray-900">Yükleme Durumu</h3>
                </div>
                <div class="p-6">
                    <div class="mb-4">
                        <div class="flex justify-between text-sm font-medium text-gray-700 mb-2">
                            <span id="progressText">Hazırlanıyor...</span>
                            <span id="progressPercent">0%</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2">
                            <div class="bg-blue-600 h-2 rounded-full transition-all duration-300" id="progressBar" style="width: 0%"></div>
                        </div>
                    </div>
                    <div id="progressDetails" class="text-sm text-gray-500"></div>
                </div>
            </div>

            <!-- Video Preview -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 hidden" id="previewCard">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-semibold text-gray-900">Video Önizleme</h3>
                </div>
                <div class="p-6">
                    <video id="videoPreview" controls class="w-full rounded-lg" style="max-height: 200px;">
                        Tarayıcınız video önizlemeyi desteklemiyor.
                    </video>
                    <div id="videoInfo" class="mt-4 text-sm text-gray-600"></div>
                </div>
            </div>

            <!-- Cost Estimation -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 hidden" id="costCard">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-semibold text-gray-900">Maliyet Tahmini</h3>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-2 gap-4 mb-4">
                        <div class="text-center">
                            <div class="text-2xl font-bold text-blue-600" id="estimatedCost">₺0.00</div>
                            <div class="text-sm text-gray-500">Yükleme Maliyeti</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold text-purple-600" id="storageCost">₺0.00</div>
                            <div class="text-sm text-gray-500">Aylık Depolama</div>
                        </div>
                    </div>
                    <div class="border-t border-gray-200 pt-4">
                        <div class="text-sm text-gray-600 space-y-1">
                            <div>Video süresi: <span id="videoDuration" class="font-medium">-</span></div>
                            <div>Dosya boyutu: <span id="videoSize" class="font-medium">-</span></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        let selectedFile = null;
        let uploadInProgress = false;

        $(document).ready(function() {
            // File input change handler
            $('#videoFile').on('change', function() {
                const file = this.files[0];
                if (file) {
                    selectedFile = file;
                    handleFileSelection(file);
                }
            });

            // File input label update
            $('#videoFile').on('change', function() {
                const fileName = this.files[0]?.name || 'Dosya seçin...';
                $(this).closest('.border-dashed').find('span').first().text(fileName);
            });

            // Form submit handler
            $('#videoUploadForm').on('submit', function(e) {
                e.preventDefault();
                if (!uploadInProgress) {
                    uploadVideo();
                }
            });
        });

        function handleFileSelection(file) {
            // Validate file type
            const allowedTypes = ['video/mp4', 'video/avi', 'video/quicktime', 'video/x-msvideo', 'video/x-flv', 'video/webm', 'video/x-matroska'];
            if (!allowedTypes.includes(file.type) && !file.name.match(/\.(mp4|avi|mov|wmv|flv|webm|mkv)$/i)) {
                showToast('error', 'Desteklenmeyen dosya formatı. Lütfen video dosyası seçin.');
                resetFileInput();
                return;
            }

            // Validate file size
            @if (settings != null)
            {
                <text>
                const maxSize = @settings.MaxFileSizeMB * 1024 * 1024;
                if (file.size > maxSize) {
                    showToast('error', `Dosya boyutu çok büyük. Maksimum @settings.MaxFileSizeMB MB olabilir.`);
                    resetFileInput();
                    return;
                }
                </text>
            }

            // Show preview
            showVideoPreview(file);
            
            // Validate with server
            validateFileWithServer(file);
        }

        function showVideoPreview(file) {
            const video = document.getElementById('videoPreview');
            const url = URL.createObjectURL(file);
            
            video.src = url;
            video.onloadedmetadata = function() {
                const duration = video.duration;
                const size = file.size;
                
                // Update video info
                $('#videoInfo').html(`
                    <div><strong>Süre:</strong> ${formatDuration(duration)}</div>
                    <div><strong>Boyut:</strong> ${formatFileSize(size)}</div>
                    <div><strong>Format:</strong> ${file.type}</div>
                `);
                
                // Calculate costs
                calculateCosts(duration, size);
                
                // Show cards
                $('#previewCard').removeClass('hidden');
                $('#costCard').removeClass('hidden');
                
                URL.revokeObjectURL(url);
            };
        }

        function calculateCosts(durationSeconds, fileSizeBytes) {
            @if (settings != null)
            {
                <text>
                const durationMinutes = Math.ceil(durationSeconds / 60);
                const uploadCost = durationMinutes * @settings.CostPerMinute;
                const fileSizeGB = fileSizeBytes / (1024 * 1024 * 1024);
                const storageCost = fileSizeGB * @settings.StorageCostPerGBPerMonth;
                
                $('#estimatedCost').text('₺' + uploadCost.toFixed(2));
                $('#storageCost').text('₺' + storageCost.toFixed(2));
                $('#videoDuration').text(formatDuration(durationSeconds));
                $('#videoSize').text(formatFileSize(fileSizeBytes));
                </text>
            }
        }

        function validateFileWithServer(file) {
            const formData = new FormData();
            formData.append('videoFile', file);
            
            $.ajax({
                url: '@Url.Action("ValidateUpload")',
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                success: function(response) {
                    if (!response.success) {
                        showToast('error', response.message);
                        resetFileInput();
                    } else {
                        showToast('success', 'Video dosyası geçerli. Yüklemeye hazır.');
                    }
                },
                error: function() {
                    showToast('warning', 'Dosya doğrulama yapılamadı, ancak yüklemeyi deneyebilirsiniz.');
                }
            });
        }

        function uploadVideo() {
            if (!selectedFile) {
                showToast('error', 'Lütfen bir video dosyası seçin.');
                return;
            }

            const title = $('#videoTitle').val().trim();
            if (!title) {
                showToast('error', 'Lütfen video başlığı girin.');
                return;
            }

            uploadInProgress = true;
            $('#uploadBtn').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> <span>Yükleniyor...</span>');
            $('#progressCard').removeClass('hidden');

            const formData = new FormData();
            formData.append('Title', title);
            formData.append('Description', $('#videoDescription').val());
            formData.append('VideoFile', selectedFile);
            formData.append('IsPublic', $('#isPublic').is(':checked'));

            $.ajax({
                url: '@Url.Action("UploadVideo")',
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                xhr: function() {
                    const xhr = new window.XMLHttpRequest();
                    xhr.upload.addEventListener('progress', function(evt) {
                        if (evt.lengthComputable) {
                            const percentComplete = Math.round((evt.loaded / evt.total) * 100);
                            updateProgress(percentComplete, 'Yükleniyor...');
                        }
                    }, false);
                    return xhr;
                },
                success: function(response) {
                    if (response.success) {
                        updateProgress(100, 'Tamamlandı!');
                        showToast('success', response.message);
                        
                        setTimeout(() => {
                            window.location.href = '@Url.Action("Index")';
                        }, 2000);
                    } else {
                        uploadInProgress = false;
                        $('#uploadBtn').prop('disabled', false).html('<i class="fas fa-upload"></i> <span>Video Yükle</span>');
                        showToast('error', response.message);
                        updateProgress(0, 'Hata oluştu');
                    }
                },
                error: function() {
                    uploadInProgress = false;
                    $('#uploadBtn').prop('disabled', false).html('<i class="fas fa-upload"></i> <span>Video Yükle</span>');
                    showToast('error', 'Video yükleme sırasında hata oluştu.');
                    updateProgress(0, 'Hata oluştu');
                }
            });
        }

        function updateProgress(percent, text) {
            $('#progressBar').css('width', percent + '%');
            $('#progressPercent').text(percent + '%');
            $('#progressText').text(text);

            if (percent === 100) {
                $('#progressBar').removeClass('bg-blue-600').addClass('bg-green-500');
                $('#progressDetails').text('Video başarıyla yüklendi ve işleniyor...');
            } else if (percent === 0 && text === 'Hata oluştu') {
                $('#progressBar').removeClass('bg-blue-600').addClass('bg-red-500');
                $('#progressDetails').text('Yükleme başarısız oldu.');
            } else {
                $('#progressDetails').text(`${percent}% tamamlandı`);
            }
        }

        function resetForm() {
            if (uploadInProgress) {
                if (!confirm('Yükleme devam ediyor. İptal etmek istediğinizden emin misiniz?')) {
                    return;
                }
            }
            
            $('#videoUploadForm')[0].reset();
            $(this).closest('.border-dashed').find('span').first().text('Video dosyası seçin');
            $('#previewCard').addClass('hidden');
            $('#costCard').addClass('hidden');
            $('#progressCard').addClass('hidden');
            selectedFile = null;
            uploadInProgress = false;
            $('#uploadBtn').prop('disabled', false).html('<i class="fas fa-upload"></i> <span>Video Yükle</span>');
        }

        function resetFileInput() {
            $('#videoFile').val('');
            $('#videoFile').closest('.border-dashed').find('span').first().text('Video dosyası seçin');
            $('#previewCard').addClass('hidden');
            $('#costCard').addClass('hidden');
            selectedFile = null;
        }

        function formatDuration(seconds) {
            const hours = Math.floor(seconds / 3600);
            const minutes = Math.floor((seconds % 3600) / 60);
            const secs = Math.floor(seconds % 60);
            
            if (hours > 0) {
                return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
            } else {
                return `${minutes}:${secs.toString().padStart(2, '0')}`;
            }
        }

        function formatFileSize(bytes) {
            const sizes = ['B', 'KB', 'MB', 'GB'];
            if (bytes === 0) return '0 B';
            const i = Math.floor(Math.log(bytes) / Math.log(1024));
            return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
        }

        function showToast(type, message) {
            const bgColor = type === 'success' ? 'bg-green-500' :
                           type === 'warning' ? 'bg-yellow-500' : 'bg-red-500';
            const toast = document.createElement('div');
            toast.className = `fixed top-4 right-4 ${bgColor} text-white px-6 py-3 rounded-lg shadow-lg z-50 transition-opacity duration-300`;
            toast.innerHTML = `
                <div class="flex items-center justify-between">
                    <span>${message}</span>
                    <button onclick="this.parentElement.parentElement.remove()" class="ml-4 text-white hover:text-gray-200">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `;
            document.body.appendChild(toast);

            setTimeout(() => {
                toast.remove();
            }, 5000);
        }
    </script>
}
