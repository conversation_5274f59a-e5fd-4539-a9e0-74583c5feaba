@model List<PushDashboard.DTOs.VideoListDto>
@{
    ViewData["Title"] = "Video Hosting";
    var stats = ViewBag.Stats as PushDashboard.DTOs.VideoStatsDto;
}

<div class="p-6">
    <!-- Header -->
    <div class="flex justify-between items-center mb-6">
        <div>
            <h1 class="text-3xl font-bold text-gray-900">Video Hosting</h1>
            <p class="text-gray-600 mt-1"><PERSON>ların<PERSON><PERSON><PERSON> yükleyin, yönetin ve paylaşın</p>
        </div>
        <div class="flex space-x-3">
            <a href="@Url.Action("Upload")" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors">
                <i class="fas fa-upload"></i>
                <span>Video Yükle</span>
            </a>
            <a href="@Url.Action("Settings")" class="bg-gray-100 hover:bg-gray-200 text-gray-700 px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors">
                <i class="fas fa-cog"></i>
                <span>Ayarlar</span>
            </a>
        </div>
    </div>

    <!-- Stats Cards -->
    @if (stats != null)
    {
        <div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-6 mb-8">
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600 uppercase tracking-wide">Toplam Video</p>
                        <p class="text-3xl font-bold text-gray-900 mt-2">@stats.TotalVideos</p>
                    </div>
                    <div class="bg-blue-100 p-3 rounded-full">
                        <i class="fas fa-video text-blue-600 text-xl"></i>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600 uppercase tracking-wide">Toplam Görüntüleme</p>
                        <p class="text-3xl font-bold text-gray-900 mt-2">@stats.TotalViews.ToString("N0")</p>
                    </div>
                    <div class="bg-green-100 p-3 rounded-full">
                        <i class="fas fa-eye text-green-600 text-xl"></i>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600 uppercase tracking-wide">Toplam Depolama</p>
                        <p class="text-3xl font-bold text-gray-900 mt-2">@stats.TotalStorageFormatted</p>
                    </div>
                    <div class="bg-purple-100 p-3 rounded-full">
                        <i class="fas fa-hdd text-purple-600 text-xl"></i>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600 uppercase tracking-wide">Toplam Süre</p>
                        <p class="text-3xl font-bold text-gray-900 mt-2">@stats.TotalDurationFormatted</p>
                    </div>
                    <div class="bg-yellow-100 p-3 rounded-full">
                        <i class="fas fa-clock text-yellow-600 text-xl"></i>
                    </div>
                </div>
            </div>
        </div>
    }

    <!-- Videos Table -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900">Videolarım</h3>
        </div>
        <div class="p-6">
            @if (Model.Any())
            {
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200" id="videosTable">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Thumbnail</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Başlık</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Dosya</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Boyut</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Süre</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Çözünürlük</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Durum</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Görüntüleme</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Maliyet</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tarih</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">İşlemler</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            @foreach (var video in Model)
                            {
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        @if (!string.IsNullOrEmpty(video.ThumbnailUrl))
                                        {
                                            <img src="@video.ThumbnailUrl" alt="Thumbnail" class="w-16 h-10 object-cover rounded border">
                                        }
                                        else
                                        {
                                            <div class="w-16 h-10 bg-gray-100 flex items-center justify-center rounded border">
                                                <i class="fas fa-video text-gray-400"></i>
                                            </div>
                                        }
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex flex-col">
                                            <span class="font-medium text-gray-900">@video.Title</span>
                                            @if (video.IsPublic)
                                            {
                                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 mt-1 w-fit">Herkese Açık</span>
                                            }
                                            else
                                            {
                                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800 mt-1 w-fit">Özel</span>
                                            }
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">@video.OriginalFileName</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">@video.FileSizeFormatted</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">@video.DurationFormatted</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">@video.Resolution</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        @switch (video.Status)
                                        {
                                            case PushDashboard.Models.VideoStatus.Ready:
                                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">@video.StatusText</span>
                                                break;
                                            case PushDashboard.Models.VideoStatus.Processing:
                                            case PushDashboard.Models.VideoStatus.Uploading:
                                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">@video.StatusText</span>
                                                break;
                                            case PushDashboard.Models.VideoStatus.Failed:
                                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">@video.StatusText</span>
                                                break;
                                            default:
                                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">@video.StatusText</span>
                                                break;
                                        }
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">@video.ViewCount.ToString("N0")</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">₺@video.UploadCost.ToString("F2")</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">@video.CreatedAt.ToString("dd.MM.yyyy HH:mm")</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <div class="flex space-x-2">
                                            @if (video.Status == PushDashboard.Models.VideoStatus.Ready)
                                            {
                                                <button type="button" class="text-blue-600 hover:text-blue-900 p-1 rounded" onclick="viewVideo(@video.Id)" title="İzle">
                                                    <i class="fas fa-play"></i>
                                                </button>
                                                <button type="button" class="text-green-600 hover:text-green-900 p-1 rounded" onclick="generateEmbedCode(@video.Id)" title="Embed Kodu">
                                                    <i class="fas fa-code"></i>
                                                </button>
                                            }
                                            <button type="button" class="text-gray-600 hover:text-gray-900 p-1 rounded" onclick="editVideo(@video.Id)" title="Düzenle">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button type="button" class="text-red-600 hover:text-red-900 p-1 rounded" onclick="deleteVideo(@video.Id)" title="Sil">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            }
                        </tbody>
                    </table>
                </div>
            }
            else
            {
                <div class="text-center py-12">
                    <i class="fas fa-video text-6xl text-gray-300 mb-4"></i>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">Henüz video yüklenmemiş</h3>
                    <p class="text-gray-500 mb-6">İlk videonuzu yüklemek için aşağıdaki butonu kullanın.</p>
                    <a href="@Url.Action("Upload")" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg inline-flex items-center space-x-2 transition-colors">
                        <i class="fas fa-upload"></i>
                        <span>Video Yükle</span>
                    </a>
                </div>
            }
        </div>
    </div>
</div>

<!-- Video Player Modal -->
<div id="videoPlayerModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
        <div class="flex justify-between items-center pb-3">
            <h3 class="text-lg font-bold text-gray-900" id="videoPlayerTitle">Video Player</h3>
            <button type="button" class="text-gray-400 hover:text-gray-600" onclick="closeVideoModal()">
                <i class="fas fa-times text-xl"></i>
            </button>
        </div>
        <div class="mt-2">
            <div id="videoPlayerContainer"></div>
        </div>
    </div>
</div>

<!-- Edit Video Modal -->
<div id="editVideoModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-1/2 shadow-lg rounded-md bg-white">
        <div class="flex justify-between items-center pb-3">
            <h3 class="text-lg font-bold text-gray-900">Video Düzenle</h3>
            <button type="button" class="text-gray-400 hover:text-gray-600" onclick="closeEditModal()">
                <i class="fas fa-times text-xl"></i>
            </button>
        </div>
        <form id="editVideoForm">
            <div class="mt-2">
                <input type="hidden" id="editVideoId" />
                <div class="mb-4">
                    <label for="editVideoTitle" class="block text-sm font-medium text-gray-700 mb-2">Başlık</label>
                    <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" id="editVideoTitle" required maxlength="200">
                </div>
                <div class="mb-4">
                    <label for="editVideoDescription" class="block text-sm font-medium text-gray-700 mb-2">Açıklama</label>
                    <textarea class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" id="editVideoDescription" rows="3" maxlength="1000"></textarea>
                </div>
                <div class="mb-4">
                    <label class="flex items-center">
                        <input type="checkbox" class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50" id="editVideoIsPublic">
                        <span class="ml-2 text-sm text-gray-700">Herkese açık</span>
                    </label>
                </div>
            </div>
            <div class="flex justify-end space-x-3 pt-3 border-t border-gray-200">
                <button type="button" class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 transition-colors" onclick="closeEditModal()">İptal</button>
                <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">Kaydet</button>
            </div>
        </form>
    </div>
</div>

<!-- Embed Code Modal -->
<div id="embedCodeModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
        <div class="flex justify-between items-center pb-3">
            <h3 class="text-lg font-bold text-gray-900">Embed Kodu</h3>
            <button type="button" class="text-gray-400 hover:text-gray-600" onclick="closeEmbedModal()">
                <i class="fas fa-times text-xl"></i>
            </button>
        </div>
        <div class="mt-2">
            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700 mb-2">Embed Kodu (HTML)</label>
                <textarea class="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50" id="embedCodeText" rows="3" readonly></textarea>
                <p class="text-sm text-gray-500 mt-1">Bu kodu web sitenize kopyalayarak videoyu gömebilirsiniz.</p>
            </div>
            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700 mb-2">Direkt Link</label>
                <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50" id="embedDirectUrl" readonly>
            </div>
            <div class="grid grid-cols-2 gap-4 mb-4">
                <div>
                    <label for="embedWidth" class="block text-sm font-medium text-gray-700 mb-2">Genişlik</label>
                    <input type="number" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" id="embedWidth" value="640" min="200" max="1920">
                </div>
                <div>
                    <label for="embedHeight" class="block text-sm font-medium text-gray-700 mb-2">Yükseklik</label>
                    <input type="number" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" id="embedHeight" value="360" min="150" max="1080">
                </div>
            </div>
            <button type="button" class="mb-4 px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors" onclick="updateEmbedCode()">Kodu Güncelle</button>
        </div>
        <div class="flex justify-end space-x-3 pt-3 border-t border-gray-200">
            <button type="button" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors" onclick="copyEmbedCode()">Kodu Kopyala</button>
            <button type="button" class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 transition-colors" onclick="closeEmbedModal()">Kapat</button>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        let currentVideoId = null;

        $(document).ready(function() {
            // DataTable'ı kaldırdık, basit tablo kullanıyoruz
        });

        function viewVideo(videoId) {
            $.get('@Url.Action("GetVideo")', { id: videoId })
                .done(function(response) {
                    if (response.success) {
                        const video = response.data;
                        $('#videoPlayerTitle').text(video.title);
                        
                        const playerHtml = `
                            <video controls width="100%" height="400" poster="${video.thumbnailUrl || ''}">
                                <source src="${video.videoUrl}" type="video/mp4">
                                Tarayıcınız video oynatmayı desteklemiyor.
                            </video>
                            ${video.description ? `<p class="mt-3">${video.description}</p>` : ''}
                        `;

                        $('#videoPlayerContainer').html(playerHtml);
                        showVideoModal();
                    } else {
                        showToast('error', response.message);
                    }
                })
                .fail(function() {
                    showToast('error', 'Video bilgileri alınırken hata oluştu.');
                });
        }

        function editVideo(videoId) {
            $.get('@Url.Action("GetVideo")', { id: videoId })
                .done(function(response) {
                    if (response.success) {
                        const video = response.data;
                        $('#editVideoId').val(video.id);
                        $('#editVideoTitle').val(video.title);
                        $('#editVideoDescription').val(video.description || '');
                        $('#editVideoIsPublic').prop('checked', video.isPublic);
                        showEditModal();
                    } else {
                        showToast('error', response.message);
                    }
                })
                .fail(function() {
                    showToast('error', 'Video bilgileri alınırken hata oluştu.');
                });
        }

        function deleteVideo(videoId) {
            if (confirm('Bu videoyu silmek istediğinizden emin misiniz? Bu işlem geri alınamaz.')) {
                $.post('@Url.Action("DeleteVideo")', { id: videoId })
                    .done(function(response) {
                        if (response.success) {
                            showToast('success', response.message);
                            setTimeout(() => location.reload(), 1500);
                        } else {
                            showToast('error', response.message);
                        }
                    })
                    .fail(function() {
                        showToast('error', 'Video silme sırasında hata oluştu.');
                    });
            }
        }

        function generateEmbedCode(videoId) {
            currentVideoId = videoId;
            updateEmbedCode();
            showEmbedModal();
        }

        function updateEmbedCode() {
            if (!currentVideoId) return;
            
            const width = $('#embedWidth').val();
            const height = $('#embedHeight').val();
            
            $.get('@Url.Action("GenerateEmbedCode")', { 
                id: currentVideoId, 
                width: width, 
                height: height 
            })
                .done(function(response) {
                    if (response.success) {
                        $('#embedCodeText').val(response.data.embedCode);
                        $('#embedDirectUrl').val(response.data.directUrl);
                    } else {
                        showToast('error', response.message);
                    }
                })
                .fail(function() {
                    showToast('error', 'Embed kodu oluşturulurken hata oluştu.');
                });
        }

        function copyEmbedCode() {
            const embedCode = $('#embedCodeText').val();
            navigator.clipboard.writeText(embedCode).then(function() {
                showToast('success', 'Embed kodu kopyalandı!');
            });
        }

        $('#editVideoForm').on('submit', function(e) {
            e.preventDefault();
            
            const videoId = $('#editVideoId').val();
            const data = {
                title: $('#editVideoTitle').val(),
                description: $('#editVideoDescription').val(),
                isPublic: $('#editVideoIsPublic').is(':checked')
            };
            
            $.post('@Url.Action("UpdateVideo")', { id: videoId, ...data })
                .done(function(response) {
                    if (response.success) {
                        showToast('success', response.message);
                        closeEditModal();
                        setTimeout(() => location.reload(), 1500);
                    } else {
                        showToast('error', response.message);
                    }
                })
                .fail(function() {
                    showToast('error', 'Video güncelleme sırasında hata oluştu.');
                });
        });

        // Modal functions
        function showVideoModal() {
            document.getElementById('videoPlayerModal').classList.remove('hidden');
        }

        function closeVideoModal() {
            document.getElementById('videoPlayerModal').classList.add('hidden');
            $('#videoPlayerContainer').html('');
        }

        function showEditModal() {
            document.getElementById('editVideoModal').classList.remove('hidden');
        }

        function closeEditModal() {
            document.getElementById('editVideoModal').classList.add('hidden');
        }

        function showEmbedModal() {
            document.getElementById('embedCodeModal').classList.remove('hidden');
        }

        function closeEmbedModal() {
            document.getElementById('embedCodeModal').classList.add('hidden');
        }

        function showToast(type, message) {
            const bgColor = type === 'success' ? 'bg-green-500' : 'bg-red-500';
            const toast = document.createElement('div');
            toast.className = `fixed top-4 right-4 ${bgColor} text-white px-6 py-3 rounded-lg shadow-lg z-50 transition-opacity duration-300`;
            toast.innerHTML = `
                <div class="flex items-center justify-between">
                    <span>${message}</span>
                    <button onclick="this.parentElement.parentElement.remove()" class="ml-4 text-white hover:text-gray-200">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `;
            document.body.appendChild(toast);

            setTimeout(() => {
                toast.remove();
            }, 5000);
        }
    </script>
}
