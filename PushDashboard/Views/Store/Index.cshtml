@model PushDashboard.ViewModels.StoreIndexViewModel
@{
    ViewData["Title"] = "Mağaza";
}

@section Scripts
{
    @await Html.PartialAsync("Partials/_StoreScriptsPartial")
    @await Html.PartialAsync("Partials/_TransactionHistoryScriptsPartial")
    @await Html.PartialAsync("Partials/_ModuleSettingsScriptsPartial")
    @await Html.PartialAsync("Partials/_ModuleDetailsScriptsPartial")}

<!-- Anti-forgery token for AJAX requests -->
@Html.AntiForgeryToken()

<main class="p-4 bg-gray-50 relative">
    <div class="mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Mağaza</h2>
        <p class="text-gray-600">Modüllerinizi yönetin ve yeni modüller keşfedin</p>
    </div>

    @await Html.PartialAsync("Partials/_StoreStatsPartial", Model)

    @await Html.PartialAsync("Partials/_ModuleGridPartial", Model)

    @await Html.PartialAsync("Partials/_TransactionHistoryPartial", Model)
    
    <!-- Module Settings Sidebar -->
    @await Html.PartialAsync("Partials/_ModuleSettingsSidebarPartial")
</main>
    
    <!-- Module Details Drawer -->
    @await Html.PartialAsync("Partials/_ModuleDetailsDrawerPartial")
