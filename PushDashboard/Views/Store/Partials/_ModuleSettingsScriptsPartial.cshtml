<script>
  let currentModuleId = null;
  let currentModuleSettings = {};

  // Open module settings sidebar
  function openModuleSettings(moduleId, moduleName) {
    currentModuleId = moduleId;

    // Update title
    document.getElementById('module-settings-title').textContent = `${moduleName} - Ayarları`;

    // Show sidebar and overlay
    const sidebar = document.getElementById('module-settings-sidebar');
    const overlay = document.getElementById('module-settings-overlay');

    sidebar.classList.remove('hidden');
    overlay.classList.remove('hidden');

    // Animate sidebar in
    setTimeout(() => {
      sidebar.classList.remove('translate-x-full');
    }, 10);

    // Load settings
    loadModuleSettings(moduleId);
  }

  // Close module settings sidebar
  function closeModuleSettings() {
    const sidebar = document.getElementById('module-settings-sidebar');
    const overlay = document.getElementById('module-settings-overlay');

    // Animate sidebar out
    sidebar.classList.add('translate-x-full');

    setTimeout(() => {
      sidebar.classList.add('hidden');
      overlay.classList.add('hidden');
      currentModuleId = null;
      currentModuleSettings = {};
    }, 300);
  }

  // Load module settings
  async function loadModuleSettings(moduleId) {
    showLoadingState();

    try {
      const response = await axios.get(`/Store/GetModuleSettings?moduleId=${moduleId}`, {
        headers: {
          'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]').value
        }
      });

      if (response.data.success) {
        currentModuleSettings = response.data.settings || {};
        await renderModuleSettings(response.data.moduleInfo, currentModuleSettings);
        showContentState();
      } else {
        showErrorState();
      }
    } catch (error) {
      console.error('Error loading module settings:', error);
      showErrorState();
    }
  }

  // Get module info
  async function getModuleInfo(moduleId) {
    try {
      const response = await axios.get(`/Store/GetModuleSettings?moduleId=${moduleId}`, {
        headers: {
          'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]').value
        }
      });

      if (response.data.success) {
        return response.data.moduleInfo;
      }
      return null;
    } catch (error) {
      console.error('Error getting module info:', error);
      return null;
    }
  }

  // Render module settings form
  async function renderModuleSettings(moduleInfo, settings) {
    const content = document.getElementById('module-settings-content');

    // Generate settings form based on module type
    let settingsHtml = await generateSettingsForm(moduleInfo, settings);

    content.innerHTML = settingsHtml;

    // Add event listeners for gift voucher settings
    const giftVoucherCheckbox = document.getElementById('setting-enable-gift-voucher');
    if (giftVoucherCheckbox) {
      giftVoucherCheckbox.addEventListener('change', function() {
        const giftVoucherSettings = document.getElementById('gift-voucher-settings');
        if (giftVoucherSettings) {
          if (this.checked) {
            giftVoucherSettings.classList.remove('opacity-50', 'pointer-events-none');
          } else {
            giftVoucherSettings.classList.add('opacity-50', 'pointer-events-none');
          }
        }
      });
    }
  }

  // Generate settings form based on module
  async function generateSettingsForm(moduleInfo, settings) {
    // Default settings that apply to all modules
    let html = `
      <div class="space-y-6">
        <div class="bg-gray-50 p-4 rounded-lg">
          <h4 class="text-sm font-medium text-gray-900 mb-3">Genel Ayarlar</h4>

          <div class="space-y-4">
            <div>
              <label class="flex items-center">
                <input type="checkbox" id="setting-enabled" ${settings.enabled !== false ? 'checked' : ''}
                       class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                <span class="ml-2 text-sm text-gray-700">Modülü etkinleştir</span>
              </label>
            </div>

            <div>
              <label class="flex items-center">
                <input type="checkbox" id="setting-notifications" ${settings.notifications !== false ? 'checked' : ''}
                       class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                <span class="ml-2 text-sm text-gray-700">Bildirimler</span>
              </label>
            </div>
          </div>
        </div>
    `;

    // Module-specific settings
    switch (moduleInfo.name) {
      case 'Sepet Hatırlatma':
        html += generateCartReminderSettings(settings);
        break;
      case 'SMS Gönderimi':
        html += generateSmsSettings(settings);
        break;
      case 'Push Bildirim':
        html += generatePushNotificationSettings(settings.settings);
        break;
      case 'Email Otomasyonu':
        html += generateEmailAutomationSettings(settings);
        break;
      case 'Doğum Günü Hatırlatma':
        html += await generateBirthdayReminderSettings(settings);
        break;
      case 'Toplu Mesaj Gönderimi':
        html += await generateBulkMessagingSettings(settings);
        break;
      case 'Sipariş Durumu Bildirimleri':
        html += await generateOrderStatusNotificationSettings(settings);
        break;
      case 'İlk Alışveriş Mesajı':
        html += await generateFirstOrderSettings(settings);
        break;
      default:
        html += generateDefaultModuleSettings(settings);
        break;
    }

    html += '</div>';
    return html;
  }

  // Generate cart reminder specific settings
  function generateCartReminderSettings(settings) {
    const isEnabled = settings.isEnabled !== false; // Default to true if not specified

    return `
      <div class="bg-blue-50 p-4 rounded-lg">
        <h4 class="text-sm font-medium text-gray-900 mb-3">Sepet Hatırlatma Modülü</h4>

        <div class="space-y-4">
          <!-- Modül Aktif/Pasif Toggle -->
          <div class="flex items-center justify-between">
            <label class="text-sm font-medium text-gray-700">Modül Etkin</label>
            <label class="relative inline-flex items-center cursor-pointer">
              <input type="checkbox" id="setting-basket-reminder-enabled" ${isEnabled ? 'checked' : ''} class="sr-only peer">
              <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
            </label>
          </div>

          <div class="bg-blue-100 p-3 rounded-md">
            <p class="text-sm text-blue-800">
              <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
              Detaylı ayarlar için <strong>Sepet Hatırlatma</strong> modül sayfasını ziyaret edin.
            </p>
          </div>
        </div>
      </div>
    `;
  }

  // Generate SMS specific settings
  function generateSmsSettings(settings) {
    return `
      <div class="bg-green-50 p-4 rounded-lg">
        <h4 class="text-sm font-medium text-gray-900 mb-3">SMS Ayarları</h4>

        <div class="space-y-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Gönderici Adı</label>
            <input type="text" id="setting-sender-name" value="${settings.senderName || ''}"
                   maxlength="11" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
          </div>

          <div>
            <label class="flex items-center">
              <input type="checkbox" id="setting-auto-send" ${settings.autoSend ? 'checked' : ''}
                     class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
              <span class="ml-2 text-sm text-gray-700">Otomatik gönderim</span>
            </label>
          </div>
        </div>
      </div>
    `;
  }

  // Generate push notification specific settings
  function generatePushNotificationSettings(settings) {
    return `
      <div class="bg-orange-50 p-4 rounded-lg">
        <h4 class="text-sm font-medium text-gray-900 mb-3">Push Bildirim Ayarları</h4>

        <div class="space-y-4">
          <div>
            <label class="flex items-center">
              <input type="checkbox" id="setting-web-push" ${settings.webPush !== false ? 'checked' : ''}
                     class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
              <span class="ml-2 text-sm text-gray-700">Web push bildirimleri</span>
            </label>
          </div>

          <div>
            <label class="flex items-center">
              <input type="checkbox" id="setting-mobile-push" ${settings.mobilePush !== false ? 'checked' : ''}
                     class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
              <span class="ml-2 text-sm text-gray-700">Mobil push bildirimleri</span>
            </label>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Bildirim Sesi</label>
            <select id="setting-notification-sound" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
              <option value="default" ${settings.notificationSound == 'default' ? 'selected' : ''}>Varsayılan</option>
              <option value="chime" ${settings.notificationSound == 'chime' ? 'selected' : ''}>Chime</option>
              <option value="bell" ${settings.notificationSound == 'bell' ? 'selected' : ''}>Bell</option>
              <option value="none" ${settings.notificationSound == 'none' ? 'selected' : ''}>Sessiz</option>
            </select>
          </div>
        </div>
      </div>
    `;
  }

  // Generate email automation specific settings
  function generateEmailAutomationSettings(settings) {
    return `
      <div class="bg-purple-50 p-4 rounded-lg">
        <h4 class="text-sm font-medium text-gray-900 mb-3">Email Otomasyon Ayarları</h4>

        <div class="space-y-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Gönderici Email</label>
            <input type="email" id="setting-sender-email" value="${settings.senderEmail || ''}"
                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Gönderici Adı</label>
            <input type="text" id="setting-sender-display-name" value="${settings.senderDisplayName || ''}"
                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
          </div>

          <div>
            <label class="flex items-center">
              <input type="checkbox" id="setting-track-opens" ${settings.trackOpens !== false ? 'checked' : ''}
                     class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
              <span class="ml-2 text-sm text-gray-700">Email açılma takibi</span>
            </label>
          </div>
        </div>
      </div>
    `;
  }

  // Generate birthday reminder specific settings
  async function generateBirthdayReminderSettings(settings) {
    // Get active communication channels
    const activeChannels = await getActiveChannels();

    return `
      <div class="bg-pink-50 p-4 rounded-lg">
        <h4 class="text-sm font-medium text-gray-900 mb-3">Doğum Günü Hatırlatma Ayarları</h4>

        <div class="space-y-4">
          <div>
            <label class="flex items-center">
              <input type="checkbox" id="setting-auto-send" ${settings.autoSend !== false ? 'checked' : ''}
                     class="rounded border-gray-300 text-pink-600 focus:ring-pink-500">
              <span class="ml-2 text-sm text-gray-700">Otomatik gönderim</span>
            </label>
            <p class="text-xs text-gray-500 mt-1">Etkinleştirildiğinde her gün otomatik olarak doğum günü bildirimleri gönderilir</p>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Gönderim Saati</label>
            <input type="time" id="setting-send-time" value="${settings.sendTime || '09:00'}"
                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-pink-500">
            <p class="text-xs text-gray-500 mt-1">Doğum günü bildirimlerinin gönderileceği saat</p>
          </div>

          <div>
            <h5 class="text-sm font-medium text-gray-900 mb-3">İletişim Kanalları</h5>
            <div class="space-y-3">
              ${generateChannelSettings('email', 'Email', activeChannels.email, settings)}
              ${generateChannelSettings('sms', 'SMS', activeChannels.sms, settings)}
              ${generateChannelSettings('whatsapp', 'WhatsApp', activeChannels.whatsapp, settings)}
            </div>
          </div>
        </div>
      </div>

      <!-- Hediye Çeki Ayarları -->
      <div class="bg-yellow-50 p-4 rounded-lg">
        <h4 class="text-sm font-medium text-gray-900 mb-3">🎁 Hediye Çeki Ayarları</h4>
        ${settings.hasEcommerceIntegration ?
          `<p class="text-xs text-gray-600 mb-4">${settings.activePlatformName} entegrasyonu aktif olduğunda doğum günü maillerine otomatik hediye çeki eklenebilir.</p>` :
          `<p class="text-xs text-yellow-700 mb-4">⚠️ Hediye çeki özelliği için aktif bir e-ticaret entegrasyonu gereklidir. Entegrasyonlar sayfasından bir e-ticaret platformu entegrasyonu ekleyin.</p>`
        }

        <div class="space-y-4 ${!settings.hasEcommerceIntegration ? 'opacity-50 pointer-events-none' : ''}">
          <div>
            <label class="flex items-center">
              <input type="checkbox" id="setting-enable-gift-voucher" ${settings.enableGiftVoucher === true ? 'checked' : ''}
                     ${!settings.hasEcommerceIntegration ? 'disabled' : ''}
                     class="rounded border-gray-300 text-yellow-600 focus:ring-yellow-500">
              <span class="ml-2 text-sm text-gray-700">Hediye çeki özelliğini etkinleştir</span>
            </label>
            <p class="text-xs text-gray-500 mt-1">Etkinleştirildiğinde her doğum günü mailine otomatik hediye çeki eklenir</p>
          </div>

          <div id="gift-voucher-settings" class="space-y-4 ${settings.enableGiftVoucher !== true ? 'opacity-50 pointer-events-none' : ''}">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Hediye Çeki Tutarı</label>
              <div class="flex items-center space-x-2">
                <input type="number" id="setting-gift-voucher-amount" value="${settings.giftVoucherAmount || 50}"
                       min="1" max="10000" step="1"
                       class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-yellow-500">
                <select id="setting-gift-voucher-discount-type" class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-yellow-500">
                  <option value="1" ${(settings.giftVoucherDiscountType || 1) == 1 ? 'selected' : ''}>TL</option>
                  <option value="2" ${settings.giftVoucherDiscountType == 2 ? 'selected' : ''}>%</option>
                </select>
              </div>
              <p class="text-xs text-gray-500 mt-1">Hediye çekinin değeri (sabit tutar veya yüzde indirim)</p>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Geçerlilik Süresi (Gün)</label>
              <select id="setting-gift-voucher-validity-days" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-yellow-500">
                <option value="30" ${settings.giftVoucherValidityDays == 30 ? 'selected' : ''}>30 Gün</option>
                <option value="60" ${settings.giftVoucherValidityDays == 60 ? 'selected' : ''}>60 Gün</option>
                <option value="90" ${settings.giftVoucherValidityDays == 90 ? 'selected' : ''}>90 Gün</option>
                <option value="180" ${settings.giftVoucherValidityDays == 180 ? 'selected' : ''}>6 Ay</option>
                <option value="365" ${(settings.giftVoucherValidityDays || 365) == 365 ? 'selected' : ''}>1 Yıl</option>
              </select>
              <p class="text-xs text-gray-500 mt-1">Hediye çekinin ne kadar süre geçerli olacağı</p>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Açıklama</label>
              <input type="text" id="setting-gift-voucher-description" value="${settings.giftVoucherDescription || 'Doğum Günü Hediye Çeki'}"
                     maxlength="100" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-yellow-500">
              <p class="text-xs text-gray-500 mt-1">Hediye çekinin açıklaması (mağazada görünecek)</p>
            </div>
          </div>
        </div>
      </div>
    `;
  }

  // Generate channel settings
  function generateChannelSettings(channelType, channelName, isActive, settings) {
    const isEnabled = settings[`enable${channelType.charAt(0).toUpperCase() + channelType.slice(1)}`] !== false;
    const cost = settings[`${channelType}Cost`] || 0;

    return `
      <div class="border border-gray-200 rounded-lg p-3 ${!isActive ? 'bg-gray-50 opacity-60' : ''}">
        <div class="flex items-center justify-between mb-2">
          <label class="flex items-center">
            <input type="checkbox" id="setting-enable-${channelType}"
                   ${isEnabled && isActive ? 'checked' : ''}
                   ${!isActive ? 'disabled' : ''}
                   class="rounded border-gray-300 text-pink-600 focus:ring-pink-500">
            <span class="ml-2 text-sm font-medium text-gray-700">${channelName}</span>
          </label>
          <span class="text-xs px-2 py-1 rounded-full ${isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}">
            ${isActive ? 'Aktif' : 'Pasif'}
          </span>
        </div>

        <div class="text-xs text-gray-600">
          <div class="flex justify-between">
            <span>Maliyet:</span>
            <span class="font-medium">₺${cost.toFixed(2)} / mesaj</span>
          </div>
        </div>

        ${!isActive ? `
          <div class="mt-2 text-xs text-red-600">
            <svg class="w-3 h-3 inline mr-1" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
            </svg>
            Bu kanal yapılandırılmamış. Entegrasyonlar → İletişim Kanalları bölümünden yapılandırabilirsiniz.
          </div>
        ` : ''}
      </div>
    `;
  }

  // Generate bulk messaging specific settings
  async function generateBulkMessagingSettings(settings) {
    // Get available communication channels
    const availableChannels = await getBulkMessagingChannels();

    return `
      <div class="bg-blue-50 p-4 rounded-lg">
        <h4 class="text-sm font-medium text-gray-900 mb-3">📧 Aktif İletişim Kanalları</h4>
        <p class="text-xs text-gray-600 mb-4">Toplu mesaj gönderiminde kullanılacak iletişim kanallarını seçin. Sadece yapılandırılmış kanallar kullanılabilir.</p>

        <div class="space-y-3">
          ${generateBulkChannelSetting('email', 'Email', availableChannels.email, settings)}
          ${generateBulkChannelSetting('whatsapp', 'WhatsApp', availableChannels.whatsapp, settings)}
        </div>
      </div>



      <div class="bg-purple-50 p-4 rounded-lg">
        <h4 class="text-sm font-medium text-gray-900 mb-3">💰 Maliyet Bilgileri</h4>
        <div class="space-y-3">
          <div class="flex justify-between items-center">
            <span class="text-sm text-gray-700">Email Başına Maliyet:</span>
            <span class="text-sm font-medium text-gray-900">₺0.50</span>
          </div>
          <div class="flex justify-between items-center">
            <span class="text-sm text-gray-700">WhatsApp Başına Maliyet:</span>
            <span class="text-sm font-medium text-gray-900">₺1.00</span>
          </div>
          <div class="flex justify-between items-center">
            <span class="text-sm text-gray-700">SMS Başına Maliyet:</span>
            <span class="text-sm font-medium text-gray-900">₺0.75</span>
          </div>
          <p class="text-xs text-gray-500 mt-2">
            <svg class="w-3 h-3 inline mr-1" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
            </svg>
            Maliyet oranları sistem tarafından belirlenir ve güncellenemez.
          </p>
        </div>
      </div>


    `;
  }

  // Generate bulk messaging channel setting
  function generateBulkChannelSetting(channelType, channelName, isAvailable, settings) {
    const enabledChannels = settings.enabledChannels || [];
    const isEnabled = enabledChannels.includes(channelType);

    return `
      <div class="border border-gray-200 rounded-lg p-3 ${!isAvailable ? 'bg-gray-50 opacity-60' : ''}">
        <div class="flex items-center justify-between mb-2">
          <label class="flex items-center">
            <input type="checkbox" id="setting-enable-bulk-${channelType}"
                   ${isEnabled && isAvailable ? 'checked' : ''}
                   ${!isAvailable ? 'disabled' : ''}
                   class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                   onchange="updateBulkChannelSettings()">
            <span class="ml-2 text-sm font-medium text-gray-700">${channelName}</span>
          </label>
          <span class="text-xs px-2 py-1 rounded-full ${isAvailable ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}">
            ${isAvailable ? 'Kullanılabilir' : 'Yapılandırılmamış'}
          </span>
        </div>

        ${!isAvailable ? `
          <div class="text-xs text-red-600">
            <svg class="w-3 h-3 inline mr-1" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
            </svg>
            Bu kanal yapılandırılmamış. Entegrasyonlar sayfasından ${channelName} entegrasyonunu yapılandırın.
          </div>
        ` : `
          <div class="text-xs text-gray-600">
            Bu kanal toplu mesaj gönderiminde kullanılabilir.
          </div>
        `}
      </div>
    `;
  }

  // Get available channels for bulk messaging
  async function getBulkMessagingChannels() {
    try {
      const response = await fetch('/BulkMessaging/GetAvailableChannels', {
        headers: {
          'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]').value
        }
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          const channels = { email: false, whatsapp: false };
          data.data.forEach(channel => {
            channels[channel.channelType] = channel.isConfigured;
          });
          return channels;
        }
      }
    } catch (error) {
      console.error('Error getting bulk messaging channels:', error);
    }

    // Fallback
    return { email: false, whatsapp: false };
  }

  // Update bulk channel settings
  function updateBulkChannelSettings() {
    // This function will be called when channel checkboxes change
    // The actual saving will happen when the user clicks save
  }

  // Get active communication channels
  async function getActiveChannels() {
    try {
      const response = await fetch('/Settings/GetActiveChannels', {
        headers: {
          'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]').value
        }
      });

      if (response.ok) {
        const data = await response.json();
        return data.success ? data.channels : { email: false, sms: false, whatsapp: false };
      }
    } catch (error) {
      console.error('Error getting active channels:', error);
    }

    // Fallback - assume only email is active
    return { email: true, sms: false, whatsapp: false };
  }

  // Generate default module settings
  function generateDefaultModuleSettings(settings) {
    return `
      <div class="bg-gray-50 p-4 rounded-lg">
        <h4 class="text-sm font-medium text-gray-900 mb-3">Özel Ayarlar</h4>
        <p class="text-sm text-gray-600">Bu modül için özel ayarlar henüz tanımlanmamış.</p>
      </div>
    `;
  }

  // Save module settings
  async function saveModuleSettings() {
    if (!currentModuleId) return;

    // Check if this is the First Order module
    const moduleInfo = await getModuleInfo(currentModuleId);

    if (moduleInfo && moduleInfo.name === 'İlk Alışveriş Mesajı') {
      await saveFirstOrderSettings();
      return;
    }

    // Check if this is the Basket Reminder module
    if (moduleInfo && moduleInfo.name === 'Sepet Hatırlatma') {
      await saveBasketReminderSettings();
      return;
    }

    const settings = collectSettingsFromForm();

    try {
      const response = await axios.post(`/Store/SaveModuleSettings?moduleId=${currentModuleId}`, {
        settings: settings
      }, {
        headers: {
          'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]').value,
          'Content-Type': 'application/json'
        }
      });

      if (response.data.success) {
        showSuccessMessage('Ayarlar başarıyla kaydedildi!');
        currentModuleSettings = settings;
      } else {
        showErrorMessage(response.data.message || 'Ayarlar kaydedilemedi.');
      }
    } catch (error) {
      console.error('Error saving module settings:', error);
      showErrorMessage('Ayarlar kaydedilirken bir hata oluştu.');
    }
  }

  // Save basket reminder module settings
  async function saveBasketReminderSettings() {
    try {
      // Get the actual toggle state from the UI
      const isEnabledCheckbox = document.getElementById('setting-enabled');
      const isEnabled = isEnabledCheckbox ? isEnabledCheckbox.checked : true;

      const settings = {
        enabled: isEnabled
      };

      const response = await axios.post(`/Store/SaveModuleSettings?moduleId=${currentModuleId}`, {
        settings: settings
      }, {
        headers: {
          'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]').value,
          'Content-Type': 'application/json'
        }
      });

      if (response.data.success) {
        const statusText = isEnabled ? 'aktif' : 'pasif';
        showSuccessMessage(`Sepet hatırlatma modülü ${statusText} yapıldı! Detaylı ayarlar için modül sayfasını ziyaret edin.`);
        currentModuleSettings = settings;
      } else {
        showErrorMessage(response.data.message || 'Ayarlar kaydedilemedi.');
      }
    } catch (error) {
      console.error('Error saving basket reminder settings:', error);
      showErrorMessage('Ayarlar kaydedilirken bir hata oluştu.');
    }
  }

  // Save first order module settings
  async function saveFirstOrderSettings() {
    try {
      const settings = {
        enabled: document.getElementById('setting-first-order-enabled')?.checked || false,
        emailEnabled: document.getElementById('setting-email-enabled')?.checked || false,
        smsEnabled: document.getElementById('setting-sms-enabled')?.checked || false,
        whatsAppEnabled: document.getElementById('setting-whatsapp-enabled')?.checked || false,
        pushEnabled: document.getElementById('setting-push-enabled')?.checked || false,
        giftVoucherEnabled: document.getElementById('setting-gift-voucher-enabled')?.checked || false,
        giftVoucherAmount: parseFloat(document.getElementById('setting-gift-voucher-amount')?.value) || 50,
        giftVoucherDiscountType: parseInt(document.getElementById('setting-gift-voucher-discount-type')?.value) || 1,
        giftVoucherValidityDays: parseInt(document.getElementById('setting-gift-voucher-validity-days')?.value) || 30
      };

      const response = await axios.post('/api/FirstOrderModule/settings', settings, {
        headers: {
          'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]').value,
          'Content-Type': 'application/json'
        }
      });

      if (response.status === 200) {
        showSuccessMessage('İlk alışveriş mesajı ayarları başarıyla kaydedildi!');
        currentModuleSettings = settings;
      } else {
        showErrorMessage(response.data.message || 'Ayarlar kaydedilemedi.');
      }
    } catch (error) {
      console.error('Error saving first order settings:', error);
      if (error.response && error.response.data && error.response.data.message) {
        showErrorMessage(error.response.data.message);
      } else {
        showErrorMessage('Ayarlar kaydedilirken bir hata oluştu.');
      }
    }
  }

  // Collect settings from form
  function collectSettingsFromForm() {
    const settings = {};

    // General settings
    settings.enabled = document.getElementById('setting-enabled')?.checked || false;
    settings.notifications = document.getElementById('setting-notifications')?.checked || false;
    
    // Collect enabled channels for bulk messaging
    const enabledChannels = [];
    const emailCheckbox = document.getElementById('setting-enable-bulk-email');
    const whatsappCheckbox = document.getElementById('setting-enable-bulk-whatsapp');

    if (emailCheckbox && emailCheckbox.checked) {
      enabledChannels.push('email');
    }
    if (whatsappCheckbox && whatsappCheckbox.checked) {
      enabledChannels.push('whatsapp');
    }

    if (enabledChannels.length > 0) {
      settings.enabledChannels = enabledChannels;
    }

    // Module-specific settings
    const inputs = document.querySelectorAll('#module-settings-content input, #module-settings-content select, #module-settings-content textarea');
    inputs.forEach(input => {
      if (input.id && input.id.startsWith('setting-')) {
        // Skip bulk channel checkboxes as they are handled above
        if (input.id.includes('enable-bulk-')) {
          return;
        }

        const key = input.id.replace('setting-', '').replace(/-([a-z])/g, (g) => g[1].toUpperCase());

        if (input.type === 'checkbox') {
          settings[key] = input.checked;
        } else if (input.type === 'number') {
          const value = parseFloat(input.value);
          settings[key] = isNaN(value) ? 0 : value;
        } else {
          settings[key] = input.value;
        }
      }
    });

    return settings;
  }

  // Reset module settings
async function resetModuleSettings() {
  if (!currentModuleId) return;

  if (confirm("Ayarları varsayılan değerlere sıfırlamak istediğinizden emin misiniz?")) {
    try {
      const response = await axios.post(`/Store/ResetModuleSettings?moduleId=${currentModuleId}`, {}, {
        headers: {
          "RequestVerificationToken": document.querySelector("input[name=\"__RequestVerificationToken\"]").value,
          "Content-Type": "application/json"
        }
      });

      if (response.data.success) {
        showSuccessMessage("Ayarlar varsayılan değerlere sıfırlandı!");
        // Reload settings to show default values
        loadModuleSettings(currentModuleId);
      } else {
        showErrorMessage(response.data.message || "Ayarlar sıfırlanamadı.");
      }
    } catch (error) {
      console.error("Error resetting module settings:", error);
      showErrorMessage("Ayarlar sıfırlanırken bir hata oluştu.");
    }
  }
}
  // Retry loading settings
  function retryLoadSettings() {
    if (currentModuleId) {
      loadModuleSettings(currentModuleId);
    }
  }

  // UI state management
  function showLoadingState() {
    document.getElementById('module-settings-loading').classList.remove('hidden');
    document.getElementById('module-settings-content').classList.add('hidden');
    document.getElementById('module-settings-error').classList.add('hidden');
  }

  function showContentState() {
    document.getElementById('module-settings-loading').classList.add('hidden');
    document.getElementById('module-settings-content').classList.remove('hidden');
    document.getElementById('module-settings-error').classList.add('hidden');
  }

  function showErrorState() {
    document.getElementById('module-settings-loading').classList.add('hidden');
    document.getElementById('module-settings-content').classList.add('hidden');
    document.getElementById('module-settings-error').classList.remove('hidden');
  }

  // Generate order status notification settings
  async function generateOrderStatusNotificationSettings(settings) {
    try {
      // Sipariş durumu eşleştirme ayarlarını yükle
      const response = await axios.get(`/Store/GetOrderStatusMappingSettings?moduleId=${currentModuleId}`, {
        headers: {
          'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]').value
        }
      });

      console.log('Order status mapping response:', response.data);

      if (!response.data.success) {
        console.error('Backend error:', response.data.message);
        return `
          <div class="bg-yellow-50 p-4 rounded-lg">
            <h4 class="text-sm font-medium text-yellow-900 mb-2">Sipariş Durumu Eşleştirme</h4>
            <p class="text-sm text-yellow-700">${response.data.message}</p>
          </div>
        `;
      }

      const mappingSettings = response.data.settings;

      // Null check'ler ekle
      if (!mappingSettings || !mappingSettings.externalStatuses || !mappingSettings.internalStatuses) {
        console.error('Missing data in mappingSettings:', mappingSettings);
        return `
          <div class="bg-red-50 p-4 rounded-lg">
            <h4 class="text-sm font-medium text-red-900 mb-2">Hata</h4>
            <p class="text-sm text-red-700">Sipariş durumu verileri eksik.</p>
          </div>
        `;
      }

      return `
        <div class="bg-blue-50 p-4 rounded-lg">
          <h4 class="text-sm font-medium text-blue-900 mb-3">Sipariş Durumu Eşleştirme</h4>
          <p class="text-sm text-blue-700 mb-4">
            ${mappingSettings.integrationType || 'Bilinmeyen'} platformundaki sipariş durumlarını sistem durumlarına eşleştirin.
          </p>

          <div class="space-y-3 mb-4">
            ${(mappingSettings.internalStatuses || []).map(internalStatus => {
              // Bu sistem durumu için mevcut eşleştirmeleri bul
              const currentMappings = (mappingSettings.currentMappings || []).filter(m => m.internalStatus === internalStatus.value);
              const selectedExternalStatuses = currentMappings.map(m => m.externalStatus);

              return `
                <div class="p-4 bg-white rounded border">
                  <div class="flex items-center justify-between mb-3">
                    <div class="flex-1">
                      <div class="text-sm font-medium text-gray-900">
                        ${internalStatus.text} (${internalStatus.value})
                      </div>
                      <div class="text-xs text-gray-500">Bu sistem durumu için ${mappingSettings.integrationType} durumlarını seçin</div>
                    </div>
                  </div>

                  <div class="grid grid-cols-2 gap-2 max-h-32 overflow-y-auto">
                    ${(mappingSettings.externalStatuses || []).map(externalStatus => `
                      <label class="flex items-center space-x-2 text-xs">
                        <input type="checkbox"
                               id="mapping-${internalStatus.value}-${externalStatus.statusCode}"
                               data-internal="${internalStatus.value}"
                               data-external="${externalStatus.statusCode}"
                               data-external-name="${externalStatus.statusName}"
                               ${selectedExternalStatuses.includes(externalStatus.statusCode) ? 'checked' : ''}
                               class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                        <span class="text-gray-700">${externalStatus.statusName} (${externalStatus.statusCode})</span>
                      </label>
                    `).join('')}
                  </div>
                </div>
              `;
            }).join('')}
          </div>

          <div class="flex space-x-2">
            <button onclick="createDefaultMappings()"
                    class="px-3 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 text-sm">
              Varsayılan Eşleştirmeler
            </button>
            <button onclick="saveOrderStatusMappings()"
                    class="px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 text-sm">
              Eşleştirmeleri Kaydet
            </button>
          </div>
        </div>
      `;
    } catch (error) {
      console.error('Error loading order status mapping settings:', error);
      console.error('Error details:', error.response?.data || error.message);
      return `
        <div class="bg-red-50 p-4 rounded-lg">
          <h4 class="text-sm font-medium text-red-900 mb-2">Hata</h4>
          <p class="text-sm text-red-700">Sipariş durumu eşleştirme ayarları yüklenemedi.</p>
          <p class="text-xs text-red-600 mt-1">Detay: ${error.message}</p>
        </div>
      `;
    }
  }

  // Save order status mappings
  async function saveOrderStatusMappings() {
    if (!currentModuleId) return;

    try {
      // Collect mapping data
      const mappings = [];
      const checkboxes = document.querySelectorAll('[id^="mapping-"][data-internal][data-external]');

      checkboxes.forEach(checkbox => {
        if (checkbox.checked) {
          const internalStatus = checkbox.getAttribute('data-internal');
          const externalStatus = checkbox.getAttribute('data-external');
          const externalStatusName = checkbox.getAttribute('data-external-name');

          mappings.push({
            ExternalStatus: externalStatus,
            ExternalStatusDisplayName: externalStatusName,
            InternalStatus: internalStatus,
            IsActive: true
          });
        }
      });

      const response = await axios.post(`/Store/SaveOrderStatusMappings?moduleId=${currentModuleId}`, mappings, {
        headers: {
          'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]').value,
          'Content-Type': 'application/json'
        }
      });

      if (response.data.success) {
        showSuccessMessage('Sipariş durumu eşleştirmeleri başarıyla kaydedildi!');
      } else {
        showErrorMessage(response.data.message || 'Eşleştirmeler kaydedilemedi.');
      }
    } catch (error) {
      console.error('Error saving order status mappings:', error);
      showErrorMessage('Eşleştirmeler kaydedilirken bir hata oluştu.');
    }
  }

  // Create default mappings
  async function createDefaultMappings() {
    if (!currentModuleId) return;

    if (!confirm('Varsayılan eşleştirmeler oluşturulacak. Mevcut eşleştirmeler silinecek. Devam etmek istiyor musunuz?')) {
      return;
    }

    try {
      const response = await axios.post(`/Store/CreateDefaultOrderStatusMappings?moduleId=${currentModuleId}`, {}, {
        headers: {
          'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]').value,
          'Content-Type': 'application/json'
        }
      });

      if (response.data.success) {
        showSuccessMessage('Varsayılan eşleştirmeler başarıyla oluşturuldu!');
        // Reload settings to show new mappings
        loadModuleSettings(currentModuleId);
      } else {
        showErrorMessage(response.data.message || 'Varsayılan eşleştirmeler oluşturulamadı.');
      }
    } catch (error) {
      console.error('Error creating default mappings:', error);
      showErrorMessage('Varsayılan eşleştirmeler oluşturulurken bir hata oluştu.');
    }
  }

  // Show success/error messages
  function showSuccessMessage(message) {
    // You can implement a toast notification here
    alert(message);
  }

  function showErrorMessage(message) {
    // You can implement a toast notification here
    alert(message);
  }

  // Generate channel settings for first order module
  function generateFirstOrderChannelSettings(channels, settings) {
    return channels.map(channel => {
      const isEnabled = settings[`${channel.channelType}Enabled`] || false;
      const statusColor = channel.isConfigured ? 'text-green-600' : 'text-red-600';
      const statusIcon = channel.isConfigured ? '✓' : '✗';

      return `
        <div class="flex items-center justify-between p-3 border border-gray-200 rounded-md bg-white">
          <div class="flex items-center">
            <span class="text-lg mr-3">${getChannelIcon(channel.channelType)}</span>
            <div>
              <div class="flex items-center">
                <span class="text-sm font-medium text-gray-900">${channel.displayName}</span>
                <span class="ml-2 text-xs ${statusColor}">${statusIcon} ${channel.configurationStatus}</span>
              </div>
              <div class="text-xs text-gray-500">Maliyet: ${channel.costPerMessage.toFixed(2)} TL/mesaj</div>
              ${channel.lastTestResult ? `<div class="text-xs text-gray-400">Son test: ${channel.lastTestResult}</div>` : ''}
            </div>
          </div>
          <label class="relative inline-flex items-center cursor-pointer">
            <input type="checkbox" id="setting-${channel.channelType}-enabled" ${isEnabled ? 'checked' : ''} class="sr-only peer">
            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
          </label>
        </div>
      `;
    }).join('');
  }

  // Get channel icon
  function getChannelIcon(channelType) {
    switch (channelType) {
      case 'email': return '📧';
      case 'sms': return '📱';
      case 'whatsapp': return '💬';
      case 'push': return '🔔';
      default: return '📢';
    }
  }

  // Generate first order messaging specific settings
  async function generateFirstOrderSettings(settings) {
    try {
      // Get first order module settings from API
      const response = await axios.get('/api/FirstOrderModule/settings', {
        headers: {
          'X-Requested-With': 'XMLHttpRequest',
          'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]').value
        }
      });

      const firstOrderSettings = response.data;

      return `
        <div class="bg-blue-50 p-4 rounded-lg mb-6">
          <h4 class="text-sm font-medium text-gray-900 mb-3">🎉 İlk Alışveriş Mesajı Ayarları</h4>
          <p class="text-xs text-gray-600 mb-4">Müşterilerinizin ilk alışverişlerinde otomatik olarak gönderilecek mesaj ayarlarını yapılandırın.</p>

          <div class="space-y-4">
            <div class="flex items-center justify-between">
              <label class="text-sm font-medium text-gray-700">Modül Etkin</label>
              <label class="relative inline-flex items-center cursor-pointer">
                <input type="checkbox" id="setting-first-order-enabled" ${firstOrderSettings.enabled ? 'checked' : ''} class="sr-only peer">
                <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
              </label>
            </div>

            <div>
              <h5 class="text-sm font-medium text-gray-900 mb-3">📱 İletişim Kanalları</h5>
              <div class="space-y-3">
                ${firstOrderSettings.availableChannels.length > 0 ?
                  generateFirstOrderChannelSettings(firstOrderSettings.availableChannels, firstOrderSettings) :
                  `<div class="bg-yellow-50 p-3 rounded-md">
                    <div class="flex items-center">
                      <svg class="w-4 h-4 text-yellow-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                      </svg>
                      <span class="text-sm text-yellow-800">Henüz aktif iletişim kanalı bulunmuyor. Lütfen önce Email veya WhatsApp entegrasyonunu yapılandırın.</span>
                    </div>
                  </div>`
                }
              </div>
            </div>

            <div class="border-t pt-4">
              <h5 class="text-sm font-medium text-gray-900 mb-3">🎁 Hediye Çeki Ayarları</h5>
              <div class="space-y-3">
                <div class="flex items-center justify-between">
                  <label class="text-sm font-medium text-gray-700">Hediye Çeki Etkin</label>
                  <label class="relative inline-flex items-center cursor-pointer">
                    <input type="checkbox" id="setting-gift-voucher-enabled" ${firstOrderSettings.giftVoucherEnabled ? 'checked' : ''} class="sr-only peer">
                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                  </label>
                </div>

                <div class="grid grid-cols-2 gap-3">
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Tutar</label>
                    <input type="number" id="setting-gift-voucher-amount" value="${firstOrderSettings.giftVoucherAmount}" min="1" max="10000"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                  </div>
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">İndirim Tipi</label>
                    <select id="setting-gift-voucher-discount-type" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                      <option value="1" ${firstOrderSettings.giftVoucherDiscountType === 1 ? 'selected' : ''}>Sabit Tutar (TL)</option>
                      <option value="2" ${firstOrderSettings.giftVoucherDiscountType === 2 ? 'selected' : ''}>Yüzde (%)</option>
                    </select>
                  </div>
                </div>

                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">Geçerlilik Süresi (Gün)</label>
                  <input type="number" id="setting-gift-voucher-validity-days" value="${firstOrderSettings.giftVoucherValidityDays}" min="1" max="365"
                         class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                  <p class="text-xs text-gray-500 mt-1">Hediye çekinin kaç gün geçerli olacağını belirtin</p>
                </div>

                ${firstOrderSettings.hasActiveEcommerceIntegration ?
                  `<div class="bg-green-50 p-3 rounded-md">
                    <div class="flex items-center">
                      <svg class="w-4 h-4 text-green-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                      </svg>
                      <span class="text-sm text-green-800">E-ticaret entegrasyonu aktif: ${firstOrderSettings.activeEcommercePlatform}</span>
                    </div>
                  </div>` :
                  `<div class="bg-yellow-50 p-3 rounded-md">
                    <div class="flex items-center">
                      <svg class="w-4 h-4 text-yellow-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                      </svg>
                      <span class="text-sm text-yellow-800">Hediye çeki oluşturmak için e-ticaret entegrasyonu gerekli</span>
                    </div>
                  </div>`
                }

                <button type="button" onclick="testGiftVoucher()" class="w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 text-sm font-medium">
                  Hediye Çeki Testi Yap
                </button>
              </div>
            </div>
          </div>
        </div>
      `;
    } catch (error) {
      console.error('Error loading first order settings:', error);
      return `
        <div class="bg-red-50 p-4 rounded-lg">
          <p class="text-sm text-red-800">Ayarlar yüklenirken hata oluştu. Lütfen sayfayı yenileyin.</p>
        </div>
      `;
    }
  }



  // Test gift voucher creation
  async function testGiftVoucher() {
    try {
      const response = await axios.post('/api/FirstOrderModule/test-gift-voucher', {}, {
        headers: {
          'X-Requested-With': 'XMLHttpRequest',
          'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]').value
        }
      });

      const result = response.data;

      if (result.success) {
        showSuccessMessage(`Hediye çeki testi başarılı! Kod: ${result.voucherCode}, Tutar: ${result.amount}, Platform: ${result.platform}`);
      } else {
        showErrorMessage(`Hediye çeki testi başarısız: ${result.errorMessage}`);
      }
    } catch (error) {
      console.error('Error testing gift voucher:', error);
      showErrorMessage('Hediye çeki testi sırasında hata oluştu.');
    }
  }



  // Close sidebar on escape key
  document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape' && !document.getElementById('module-settings-sidebar').classList.contains('hidden')) {
      closeModuleSettings();
    }
  });
</script>
