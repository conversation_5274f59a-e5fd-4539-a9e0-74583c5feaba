<script>
  let currentDetailsModuleId = null;
  let currentModuleDetails = {};

  // Open module details drawer
  function openModuleDetails(moduleId, moduleName) {
    currentDetailsModuleId = moduleId;
    
    // Update title
    document.getElementById('module-details-title').textContent = `${moduleName} - Detayları`;
    
    // Show drawer and overlay
    const drawer = document.getElementById('module-details-drawer');
    const overlay = document.getElementById('module-details-overlay');
    
    drawer.classList.remove('hidden');
    overlay.classList.remove('hidden');
    
    // Animate drawer in
    setTimeout(() => {
      drawer.classList.remove('translate-x-full');
    }, 10);
    
    // Load details
    loadModuleDetails(moduleId);
  }

  // Close module details drawer
  function closeModuleDetails() {
    const drawer = document.getElementById('module-details-drawer');
    const overlay = document.getElementById('module-details-overlay');
    
    // Animate drawer out
    drawer.classList.add('translate-x-full');
    
    setTimeout(() => {
      drawer.classList.add('hidden');
      overlay.classList.add('hidden');
      currentDetailsModuleId = null;
      currentModuleDetails = {};
    }, 300);
  }

  // Load module details
  async function loadModuleDetails(moduleId) {
    showDetailsLoadingState();
    
    try {
      const response = await axios.get(`/Store/GetModuleDetails?moduleId=${moduleId}`, {
        headers: {
          'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]').value
        }
      });
      
      if (response.data.success) {
        currentModuleDetails = response.data.moduleDetails;
        renderModuleDetails(currentModuleDetails);
        showDetailsContentState();
      } else {
        showDetailsErrorState();
      }
    } catch (error) {
      console.error('Error loading module details:', error);
      showDetailsErrorState();
    }
  }

  // Render module details
  function renderModuleDetails(moduleDetails) {
    const content = document.getElementById('module-details-content');
    
    const module = moduleDetails.module;
    const relatedModules = moduleDetails.relatedModules || [];
    
    let html = `
      <!-- Module Header -->
      <div class="text-center">
        <div class="w-16 h-16 mx-auto mb-4 ${module.backgroundColor || 'bg-blue-100'} rounded-lg flex items-center justify-center">
          ${module.iconClass ? 
            `<i class="${module.iconClass} text-2xl ${module.iconColor || 'text-blue-600'}"></i>` :
            `<svg class="w-8 h-8 ${module.iconColor || 'text-blue-600'}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
            </svg>`
          }
        </div>
        <h2 class="text-xl font-bold text-gray-900 mb-2">${module.name}</h2>
        <p class="text-gray-600 mb-4">${module.description}</p>
        
        <div class="flex items-center justify-center space-x-4 mb-6">
          <div class="text-center">
            <div class="text-2xl font-bold text-green-600">${module.formattedPrice}</div>
            <div class="text-sm text-gray-500">Fiyat</div>
          </div>
          <div class="text-center">
            <div class="text-lg font-semibold text-blue-600">${module.categoryName}</div>
            <div class="text-sm text-gray-500">Kategori</div>
          </div>
        </div>
        
        ${module.isNew ? '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 mb-4">Yeni</span>' : ''}
        ${module.isFeatured ? '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800 mb-4 ml-2">Öne Çıkan</span>' : ''}
      </div>

      <!-- Detailed Description -->
      ${module.detailedDescription ? `
        <div class="bg-gray-50 p-4 rounded-lg">
          <h3 class="text-lg font-semibold text-gray-900 mb-3">Açıklama</h3>
          <p class="text-gray-700 leading-relaxed">${module.detailedDescription}</p>
        </div>
      ` : ''}

      <!-- Features -->
      ${module.features && module.features.length > 0 ? `
        <div class="bg-green-50 p-4 rounded-lg">
          <h3 class="text-lg font-semibold text-gray-900 mb-3">Özellikler</h3>
          <ul class="space-y-2">
            ${module.features.map(feature => `
              <li class="flex items-start">
                <svg class="w-5 h-5 text-green-500 mr-2 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                </svg>
                <span class="text-gray-700">${feature}</span>
              </li>
            `).join('')}
          </ul>
        </div>
      ` : ''}

      <!-- Module Stats -->
      <div class="grid grid-cols-2 gap-4">
        <div class="bg-blue-50 p-3 rounded-lg text-center">
          <div class="text-lg font-semibold text-blue-600">${module.isActive ? 'Aktif' : 'Pasif'}</div>
          <div class="text-sm text-gray-600">Durum</div>
        </div>
        <div class="bg-orange-50 p-3 rounded-lg text-center">
          <div class="text-lg font-semibold text-orange-600">₺${module.price}</div>
          <div class="text-sm text-gray-600">Fiyat</div>
        </div>
      </div>
    `;
    
    content.innerHTML = html;
    
    // Update purchase button
    const purchaseBtn = document.getElementById('purchase-from-details');
    if (module.isOwned) {
      purchaseBtn.style.display = 'none';
    } else {
      purchaseBtn.style.display = 'block';
      purchaseBtn.setAttribute('data-module-id', module.id);
      purchaseBtn.setAttribute('data-module-name', module.name);
      purchaseBtn.setAttribute('data-module-price', module.formattedPrice);
    }
  }

  // Purchase from details
  function purchaseFromDetails() {
    const btn = document.getElementById('purchase-from-details');
    const moduleId = btn.getAttribute('data-module-id');
    const moduleName = btn.getAttribute('data-module-name');
    const modulePrice = btn.getAttribute('data-module-price');
    
    if (moduleId && moduleName && modulePrice) {
      closeModuleDetails();
      purchaseModule(moduleId, moduleName, modulePrice);
    }
  }

  // Retry loading details
  function retryLoadDetails() {
    if (currentDetailsModuleId) {
      loadModuleDetails(currentDetailsModuleId);
    }
  }

  // UI state management for details
  function showDetailsLoadingState() {
    document.getElementById('module-details-loading').classList.remove('hidden');
    document.getElementById('module-details-content').classList.add('hidden');
    document.getElementById('module-details-error').classList.add('hidden');
  }

  function showDetailsContentState() {
    document.getElementById('module-details-loading').classList.add('hidden');
    document.getElementById('module-details-content').classList.remove('hidden');
    document.getElementById('module-details-error').classList.add('hidden');
  }

  function showDetailsErrorState() {
    document.getElementById('module-details-loading').classList.add('hidden');
    document.getElementById('module-details-content').classList.add('hidden');
    document.getElementById('module-details-error').classList.remove('hidden');
  }

  // Close drawer on escape key
  document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape' && !document.getElementById('module-details-drawer').classList.contains('hidden')) {
      closeModuleDetails();
    }
  });
</script>
