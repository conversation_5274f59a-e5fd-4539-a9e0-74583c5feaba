@model PushDashboard.ViewModels.StoreIndexViewModel.ModuleViewModel

<div class="border rounded-lg p-6 relative module-card @(Model.IsOwned ? "owned" : "available")" data-category-id="@Model.CategoryId" data-category-name="@Model.CategoryName" data-status="@(Model.IsOwned ? "owned" : "available")" data-module-id="@Model.Id" data-module-name="@Model.Name">
  @if (!string.IsNullOrEmpty(Model.StatusBadge))
  {
    <div class="absolute top-4 right-4">
      <span class="px-2 py-1 text-xs @Model.StatusBadgeClass rounded-full flex items-center">
        @if (Model.IsOwned)
        {
          <svg class="w-3 h-3 mr-1" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
            <path d="m9 11 3 3L22 4"></path>
          </svg>
        }
        @Model.StatusBadge
      </span>
    </div>
  }

  <div class="flex items-center mb-4">
    <div class="w-12 h-12 bg-primary-light rounded-lg flex items-center justify-center mr-4">
      @if (!string.IsNullOrEmpty(Model.IconClass))
      {
        <i class="@Model.IconClass w-6 h-6 text-primary"></i>
      }
      else
      {
        <svg class="w-6 h-6 text-primary" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <path d="M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2"></path>
          <rect x="8" y="2" width="8" height="4" rx="1" ry="1"></rect>
        </svg>
      }
    </div>
    <div>
      <h4 class="font-semibold text-gray-900">@Model.Name</h4>
      <p class="text-sm text-gray-500">@Model.CategoryName</p>
    </div>
  </div>

  <p class="text-gray-600 text-sm mb-4 line-clamp-3">@Model.Description</p>

  <div class="flex items-center justify-between mb-4">
    @if (Model.IsOwned)
    {
      <span class="text-lg font-bold text-green-600">Sahip</span>
    }
    else
    {
      <span class="text-lg font-bold text-gray-900">@Model.FormattedPrice</span>
    }
    
    @if (Model.Price > 0 && !Model.IsOwned)
    {
      <span class="text-sm text-gray-500">@Model.Price.ToString("C")</span>
    }
  </div>

  @if (Model.Features?.Any() == true)
  {
    <div class="mb-4">
      <h5 class="text-sm font-medium text-gray-900 mb-2">Özellikler:</h5>
      <ul class="text-sm text-gray-600 space-y-1">
        @foreach (var feature in Model.Features.Take(3))
        {
          <li class="flex items-center">
            <svg class="w-3 h-3 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
            </svg>
            @feature
          </li>
        }
        @if (Model.Features.Count > 3)
        {
          <li class="text-xs text-gray-500">+@(Model.Features.Count - 3) özellik daha</li>
        }
      </ul>
    </div>
  }

  <div class="flex space-x-2">
    @if (Model.IsOwned)
    {
      <button onclick="openModuleSettings('@Model.Id', '@Model.Name')"
              class="flex-1 px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 text-sm font-medium">
        <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
        </svg>
        Ayarlar
      </button>
    }
    else
    {
      <button onclick="purchaseModule('@Model.Id', '@Model.Name', '@Model.FormattedPrice')"
              class="flex-1 px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark text-sm font-medium">
        Satın Al
      </button>
      <button onclick="openModuleDetails('@Model.Id', '@Model.Name')"
              class="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 text-sm font-medium">
        <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
        Detaylar
      </button>

    }
    </div>
    @if (Model.IsOwned && Model.LastUsedAt.HasValue)
    {
      <div class="mt-3 pt-3 border-t border-gray-200">
        <p class="text-xs text-gray-500">Son kullanım: @Model.LastUsedAt.Value.ToString("dd MMM yyyy")</p>
      </div>
    }
    else if (Model.IsOwned && Model.PurchasedAt.HasValue)
    {
      <div class="mt-3 pt-3 border-t border-gray-200">
        <p class="text-xs text-gray-500">Satın alındı: @Model.PurchasedAt.Value.ToString("dd MMM yyyy")</p>
      </div>
    }
</div>
