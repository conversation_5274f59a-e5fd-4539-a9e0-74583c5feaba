@model PushDashboard.Models.ViewModels.AllResponsesViewModel
@{
    ViewData["Title"] = "Tüm Cevaplar";
    ViewData["PageHeader"] = "Çıkış Anketi Cevapları";
    ViewData["PageDescription"] = "Ziyaretçilerinizin verdiği tüm cevapları görüntüleyin ve analiz edin";
}

<div class="p-6">

    <!-- Cevaplar -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
            <div>
                <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                    <i class="fas fa-list text-gray-500 mr-2"></i>
                    Tüm Cevaplar
                </h3>
                <p class="text-sm text-gray-500 mt-1">
                    Toplam @Model.TotalSessions session, @Model.TotalResponses cevap
                    @if (Model.TotalPages > 1)
                    {
                        <span class="ml-2">• Sayfa @Model.CurrentPage / @Model.TotalPages</span>
                    }
                </p>
            </div>
            <a href="@Url.Action("Index")" class="inline-flex items-center px-3 py-2 text-blue-600 hover:text-blue-800 hover:bg-blue-50 text-sm font-medium rounded-md transition-colors duration-200">
                <i class="fas fa-arrow-left mr-2"></i>
                Geri Dön
            </a>
        </div>
        <div class="p-6">
            @if (Model.SessionGroups.Any())
            {
                <div class="space-y-6">
                    @foreach (var session in Model.SessionGroups)
                    {
                        <div class="border border-gray-200 rounded-lg p-4">
                            <!-- Session Bilgileri -->
                            <div class="flex justify-between items-start mb-4 pb-3 border-b border-gray-100">
                                <div>
                                    <h4 class="font-medium text-gray-900">Session: @session.SessionId</h4>
                                    <p class="text-sm text-gray-500">@session.CreatedAt.ToString("dd.MM.yyyy HH:mm")</p>
                                </div>
                                <div class="text-right">
                                    <p class="text-sm text-gray-500">IP: @(session.IpAddress ?? "Bilinmiyor")</p>
                                    <p class="text-xs text-gray-400">@session.Responses.Count cevap</p>
                                </div>
                            </div>

                            <!-- Cevaplar -->
                            <div class="space-y-3">
                                @foreach (var response in session.Responses.OrderBy(r => r.Question.SortOrder))
                                {
                                    <div class="bg-gray-50 rounded-lg p-3">
                                        <div class="flex justify-between items-start">
                                            <div class="flex-1">
                                                <h5 class="font-medium text-gray-900 mb-1">@response.Question.QuestionText</h5>
                                                <div class="text-gray-700">
                                                    @if (response.Question.QuestionType == PushDashboard.Models.ExitSurveyQuestionType.Rating && response.RatingValue.HasValue)
                                                    {
                                                        <div class="flex items-center space-x-2">
                                                            <div class="flex space-x-1">
                                                                @for (int i = 1; i <= 5; i++)
                                                                {
                                                                    <i class="fas fa-star @(i <= response.RatingValue ? "text-yellow-400" : "text-gray-300")"></i>
                                                                }
                                                            </div>
                                                            <span class="font-medium">@response.RatingValue / 5</span>
                                                        </div>
                                                    }
                                                    else
                                                    {
                                                        <p class="font-medium">@response.ResponseText</p>
                                                    }
                                                </div>
                                            </div>
                                            <div class="ml-4">
                                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium 
                                                    @(response.Question.QuestionType == PushDashboard.Models.ExitSurveyQuestionType.MultipleChoice ? "bg-blue-100 text-blue-800" :
                                                      response.Question.QuestionType == PushDashboard.Models.ExitSurveyQuestionType.Text ? "bg-green-100 text-green-800" :
                                                      response.Question.QuestionType == PushDashboard.Models.ExitSurveyQuestionType.YesNo ? "bg-purple-100 text-purple-800" :
                                                      "bg-yellow-100 text-yellow-800")">
                                                    @(response.Question.QuestionType == PushDashboard.Models.ExitSurveyQuestionType.MultipleChoice ? "Çoktan Seçmeli" :
                                                      response.Question.QuestionType == PushDashboard.Models.ExitSurveyQuestionType.Text ? "Metin" :
                                                      response.Question.QuestionType == PushDashboard.Models.ExitSurveyQuestionType.YesNo ? "Evet/Hayır" :
                                                      "Puanlama")
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                }
                            </div>
                        </div>
                    }
                </div>
            }
            else
            {
                <div class="text-center py-12">
                    <i class="fas fa-inbox text-gray-400 text-6xl mb-4"></i>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">Henüz cevap yok</h3>
                    <p class="text-gray-500">Ziyaretçileriniz anket doldurdukça cevaplar burada görünecek.</p>
                </div>
            }
        </div>

        <!-- Sayfalama -->
        @if (Model.TotalPages > 1)
        {
            <div class="px-6 py-4 border-t border-gray-200 bg-gray-50">
                <div class="flex items-center justify-between">
                    <div class="flex items-center text-sm text-gray-700">
                        <span>
                            Sayfa <span class="font-medium">@Model.CurrentPage</span> / <span class="font-medium">@Model.TotalPages</span>
                        </span>
                        <span class="ml-4">
                            Toplam <span class="font-medium">@Model.TotalSessions</span> session
                        </span>
                    </div>

                    <div class="flex items-center space-x-2">
                        <!-- Önceki Sayfa -->
                        @if (Model.HasPreviousPage)
                        {
                            <a href="@Url.Action("AllResponses", new { page = Model.CurrentPage - 1, pageSize = Model.PageSize })"
                               class="inline-flex items-center px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 hover:text-gray-700">
                                <i class="fas fa-chevron-left mr-1"></i>
                                Önceki
                            </a>
                        }
                        else
                        {
                            <span class="inline-flex items-center px-3 py-2 text-sm font-medium text-gray-300 bg-gray-100 border border-gray-200 rounded-md cursor-not-allowed">
                                <i class="fas fa-chevron-left mr-1"></i>
                                Önceki
                            </span>
                        }

                        <!-- Sayfa Numaraları -->
                        @{
                            var startPage = Math.Max(1, Model.CurrentPage - 2);
                            var endPage = Math.Min(Model.TotalPages, Model.CurrentPage + 2);
                        }

                        @if (startPage > 1)
                        {
                            <a href="@Url.Action("AllResponses", new { page = 1, pageSize = Model.PageSize })"
                               class="inline-flex items-center px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 hover:text-gray-700">
                                1
                            </a>
                            @if (startPage > 2)
                            {
                                <span class="inline-flex items-center px-3 py-2 text-sm font-medium text-gray-500">...</span>
                            }
                        }

                        @for (int i = startPage; i <= endPage; i++)
                        {
                            @if (i == Model.CurrentPage)
                            {
                                <span class="inline-flex items-center px-3 py-2 text-sm font-medium text-white bg-blue-600 border border-blue-600 rounded-md">
                                    @i
                                </span>
                            }
                            else
                            {
                                <a href="@Url.Action("AllResponses", new { page = i, pageSize = Model.PageSize })"
                                   class="inline-flex items-center px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 hover:text-gray-700">
                                    @i
                                </a>
                            }
                        }

                        @if (endPage < Model.TotalPages)
                        {
                            @if (endPage < Model.TotalPages - 1)
                            {
                                <span class="inline-flex items-center px-3 py-2 text-sm font-medium text-gray-500">...</span>
                            }
                            <a href="@Url.Action("AllResponses", new { page = Model.TotalPages, pageSize = Model.PageSize })"
                               class="inline-flex items-center px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 hover:text-gray-700">
                                @Model.TotalPages
                            </a>
                        }

                        <!-- Sonraki Sayfa -->
                        @if (Model.HasNextPage)
                        {
                            <a href="@Url.Action("AllResponses", new { page = Model.CurrentPage + 1, pageSize = Model.PageSize })"
                               class="inline-flex items-center px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 hover:text-gray-700">
                                Sonraki
                                <i class="fas fa-chevron-right ml-1"></i>
                            </a>
                        }
                        else
                        {
                            <span class="inline-flex items-center px-3 py-2 text-sm font-medium text-gray-300 bg-gray-100 border border-gray-200 rounded-md cursor-not-allowed">
                                Sonraki
                                <i class="fas fa-chevron-right ml-1"></i>
                            </span>
                        }
                    </div>
                </div>
            </div>
        }
    </div>
</div>
