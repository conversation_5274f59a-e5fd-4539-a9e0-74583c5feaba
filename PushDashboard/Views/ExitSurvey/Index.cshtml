@model PushDashboard.Models.ViewModels.ExitSurveyViewModel
@{
    ViewData["Title"] = "Çıkış Anketi";
    ViewData["PageHeader"] = "Çıkış Anketi";
    ViewData["PageDescription"] = "Ziyaretçilerinizin siteyi terk etme nedenlerini öğrenin ve deneyimlerini iyileştirin";
}

<div class="p-6">
    <!-- İstatistik Kartları -->
    <div class="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-3 gap-6 mb-8">
        <div class="bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg p-6 text-white">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-blue-100 text-sm">Toplam Cevap</p>
                    <p class="text-2xl font-bold">@Model.Stats.TotalResponses</p>
                </div>
                <div class="text-blue-200">
                    <i class="fas fa-chart-bar text-3xl"></i>
                </div>
            </div>
        </div>
        <div class="bg-gradient-to-r from-yellow-500 to-yellow-600 rounded-lg p-6 text-white">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-yellow-100 text-sm">Bu Ay</p>
                    <p class="text-2xl font-bold">@Model.Stats.ResponsesThisMonth</p>
                </div>
                <div class="text-yellow-200">
                    <i class="fas fa-calendar-month text-3xl"></i>
                </div>
            </div>
        </div>
        <div class="bg-gradient-to-r from-green-500 to-green-600 rounded-lg p-6 text-white">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-green-100 text-sm">Bu Hafta</p>
                    <p class="text-2xl font-bold">@Model.Stats.ResponsesThisWeek</p>
                </div>
                <div class="text-green-200">
                    <i class="fas fa-calendar-week text-3xl"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- Ana İçerik -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Sol Panel - Ayarlar -->
        <div class="lg:col-span-2">
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-8">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                        <i class="fas fa-cog text-gray-500 mr-2"></i>
                        Anket Ayarları
                    </h3>
                </div>
                <div class="p-6">
                    <form id="exitSurveySettingsForm">
                        <!-- Genel Ayarlar -->
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                            <div>
                                <label for="title" class="block text-sm font-medium text-gray-700 mb-2">Anket Başlığı</label>
                                <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                       id="title" name="title" value="@(Model.ExitSurvey?.Title ?? "Görüşünüz Bizim İçin Önemli")" required>
                            </div>
                            <div>
                                <label for="isActive" class="block text-sm font-medium text-gray-700 mb-2">Durum</label>
                                <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" id="isActive" name="isActive">
                                    @if (Model.ExitSurvey?.IsActive == true)
                                    {
                                        <option value="true" selected>Aktif</option>
                                        <option value="false">Pasif</option>
                                    }
                                    else
                                    {
                                        <option value="true">Aktif</option>
                                        <option value="false" selected>Pasif</option>
                                    }
                                </select>
                            </div>
                        </div>

                        <div class="mb-6">
                            <label for="description" class="block text-sm font-medium text-gray-700 mb-2">Açıklama</label>
                            <textarea class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                      id="description" name="description" rows="3" required>@(Model.ExitSurvey?.Description ?? "Sitemizi terk etmeden önce deneyiminizi bizimle paylaşır mısınız? Bu sadece birkaç saniye sürecek.")</textarea>
                        </div>

                        <!-- Buton Metinleri -->
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                            <div>
                                <label for="submitButtonText" class="block text-sm font-medium text-gray-700 mb-2">Gönder Butonu</label>
                                <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                       id="submitButtonText" name="submitButtonText" value="@(Model.ExitSurvey?.SubmitButtonText ?? "Gönder")" required>
                            </div>
                            <div>
                                <label for="cancelButtonText" class="block text-sm font-medium text-gray-700 mb-2">İptal Butonu</label>
                                <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                       id="cancelButtonText" name="cancelButtonText" value="@(Model.ExitSurvey?.CancelButtonText ?? "Kapat")" required>
                            </div>
                            <div>
                                <label for="thankYouMessage" class="block text-sm font-medium text-gray-700 mb-2">Teşekkür Mesajı</label>
                                <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                       id="thankYouMessage" name="thankYouMessage" value="@(Model.ExitSurvey?.ThankYouMessage ?? "Geri bildiriminiz için teşekkür ederiz!")" required>
                            </div>
                        </div>

                        <!-- Tasarım Ayarları -->
                        <h4 class="text-lg font-semibold text-gray-900 mb-4">Tasarım Ayarları</h4>
                        <div class="grid grid-cols-2 md:grid-cols-4 gap-6 mb-6">
                            <div>
                                <label for="backgroundColor" class="block text-sm font-medium text-gray-700 mb-2">Arka Plan Rengi</label>
                                <input type="color" class="w-full h-10 border border-gray-300 rounded-md cursor-pointer"
                                       id="backgroundColor" name="backgroundColor" value="@(Model.ExitSurvey?.BackgroundColor ?? "#ffffff")">
                            </div>
                            <div>
                                <label for="textColor" class="block text-sm font-medium text-gray-700 mb-2">Metin Rengi</label>
                                <input type="color" class="w-full h-10 border border-gray-300 rounded-md cursor-pointer"
                                       id="textColor" name="textColor" value="@(Model.ExitSurvey?.TextColor ?? "#333333")">
                            </div>
                            <div>
                                <label for="submitButtonColor" class="block text-sm font-medium text-gray-700 mb-2">Gönder Buton Rengi</label>
                                <input type="color" class="w-full h-10 border border-gray-300 rounded-md cursor-pointer"
                                       id="submitButtonColor" name="submitButtonColor" value="@(Model.ExitSurvey?.SubmitButtonColor ?? "#4CAF50")">
                            </div>
                            <div>
                                <label for="cancelButtonColor" class="block text-sm font-medium text-gray-700 mb-2">İptal Buton Rengi</label>
                                <input type="color" class="w-full h-10 border border-gray-300 rounded-md cursor-pointer"
                                       id="cancelButtonColor" name="cancelButtonColor" value="@(Model.ExitSurvey?.CancelButtonColor ?? "#6c757d")">
                            </div>
                        </div>

                        <!-- Davranış Ayarları -->
                        <h4 class="text-lg font-semibold text-gray-900 mb-4">Davranış Ayarları</h4>
                        <div class="grid grid-cols-2 md:grid-cols-4 gap-6 mb-6">
                            <div class="flex items-center">
                                <input type="checkbox" id="showOnPageExit" name="showOnPageExit"
                                       class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                       @(Model.ExitSurvey?.ShowOnPageExit != false ? "checked" : "")>
                                <label for="showOnPageExit" class="ml-2 text-sm text-gray-700">
                                    Sayfa Çıkışında Göster
                                </label>
                            </div>
                            <div class="flex items-center">
                                <input type="checkbox" id="showOnTabClose" name="showOnTabClose"
                                       class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                       @(Model.ExitSurvey?.ShowOnTabClose != false ? "checked" : "")>
                                <label for="showOnTabClose" class="ml-2 text-sm text-gray-700">
                                    Sekme Kapatmada Göster
                                </label>
                            </div>
                            <div class="flex items-center">
                                <input type="checkbox" id="showOnMobile" name="showOnMobile"
                                       class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                       @(Model.ExitSurvey?.ShowOnMobile != false ? "checked" : "")>
                                <label for="showOnMobile" class="ml-2 text-sm text-gray-700">
                                    Mobilde Göster
                                </label>
                            </div>
                            <div class="flex items-center">
                                <input type="checkbox" id="enableAnimation" name="enableAnimation"
                                       class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                       @(Model.ExitSurvey?.EnableAnimation != false ? "checked" : "")>
                                <label for="enableAnimation" class="ml-2 text-sm text-gray-700">
                                    Animasyon Aktif
                                </label>
                            </div>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                            <div>
                                <label for="showFrequencyDays" class="block text-sm font-medium text-gray-700 mb-2">Gösterim Sıklığı (Gün)</label>
                                <input type="number" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                       id="showFrequencyDays" name="showFrequencyDays" value="@(Model.ExitSurvey?.ShowFrequencyDays ?? 30)" min="1" max="365">
                            </div>
                            <div>
                                <label for="delayBeforeShow" class="block text-sm font-medium text-gray-700 mb-2">Gösterim Gecikmesi (ms)</label>
                                <input type="number" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                       id="delayBeforeShow" name="delayBeforeShow" value="@(Model.ExitSurvey?.DelayBeforeShow ?? 0)" min="0" max="10000">
                            </div>
                        </div>

                        <div class="flex flex-col sm:flex-row gap-4 justify-between">
                            <button type="submit" class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-md transition-colors duration-200">
                                <i class="fas fa-save mr-2"></i>
                                Ayarları Kaydet
                            </button>
                            <button type="button" class="inline-flex items-center px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white font-medium rounded-md transition-colors duration-200" id="previewBtn">
                                <i class="fas fa-eye mr-2"></i>
                                Önizleme
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Sorular Bölümü -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                    <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                        <i class="fas fa-question-circle text-gray-500 mr-2"></i>
                        Anket Soruları
                    </h3>
                    <button type="button" class="inline-flex items-center px-3 py-2 bg-green-600 hover:bg-green-700 text-white text-sm font-medium rounded-md transition-colors duration-200" id="addQuestionBtn">
                        <i class="fas fa-plus mr-2"></i>
                        Soru Ekle
                    </button>
                </div>
                <div class="p-6">
                    <div id="questionsList">
                        @if (Model.Questions.Any())
                        {
                            @foreach (var question in Model.Questions.OrderBy(q => q.SortOrder))
                            {
                                <div class="question-item bg-gray-50 border border-gray-200 rounded-lg p-4 mb-4" data-question-id="@question.Id">
                                    <div class="flex justify-between items-start">
                                        <div class="flex-1">
                                            <h4 class="text-base font-medium text-gray-900 mb-2">@question.QuestionText</h4>
                                            <div class="flex items-center space-x-2">
                                                <span class="text-sm text-gray-500">Tip: @question.QuestionType.ToString()</span>
                                                @if (question.IsRequired)
                                                {
                                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">Zorunlu</span>
                                                }
                                            </div>
                                        </div>
                                        <div class="flex space-x-2">
                                            <button type="button" class="inline-flex items-center px-3 py-2 text-sm font-medium text-blue-600 bg-blue-50 hover:bg-blue-100 border border-blue-200 rounded-md transition-colors duration-200 edit-question-btn" data-question-id="@question.Id">
                                                <i class="fas fa-edit mr-1"></i>
                                                Düzenle
                                            </button>
                                            <button type="button" class="inline-flex items-center px-3 py-2 text-sm font-medium text-red-600 bg-red-50 hover:bg-red-100 border border-red-200 rounded-md transition-colors duration-200 delete-question-btn" data-question-id="@question.Id">
                                                <i class="fas fa-trash mr-1"></i>
                                                Sil
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            }
                        }
                        else
                        {
                            <div class="text-center py-12">
                                <i class="fas fa-question-circle text-gray-400 text-6xl mb-4"></i>
                                <p class="text-gray-500 text-lg">Henüz soru eklenmemiş.</p>
                                <p class="text-gray-400">İlk sorunuzu eklemek için "Soru Ekle" butonuna tıklayın.</p>
                            </div>
                        }
                    </div>
                </div>
            </div>
        </div>

        <!-- Sağ Panel - Script ve Son Cevaplar -->
        <div class="space-y-8">
            <!-- Script Bilgisi -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                        <i class="fas fa-code text-gray-500 mr-2"></i>
                        Script Entegrasyonu
                    </h3>
                </div>
                <div class="p-6">
                    <p class="text-sm text-gray-600 mb-4">
                        Aşağıdaki script kodunu sitenizin &lt;/body&gt; etiketinden hemen önce ekleyin:
                    </p>
                    <div class="relative">
                        <textarea class="w-full px-3 py-2 border border-gray-300 rounded-md font-mono text-sm bg-gray-50" id="scriptCode" rows="3" readonly></textarea>
                        <button type="button" class="absolute top-2 right-2 inline-flex items-center p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-md transition-colors duration-200" id="copyScriptBtn">
                            <i class="fas fa-copy"></i>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Soru İstatistikleri -->
            @if (Model.Stats.QuestionStats.Any())
            {
                <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                            <i class="fas fa-chart-pie text-gray-500 mr-2"></i>
                            Soru İstatistikleri
                        </h3>
                    </div>
                    <div class="p-6">
                        <div class="space-y-6">
                            @foreach (var questionStat in Model.Stats.QuestionStats.Where(q => q.QuestionType != PushDashboard.Models.ExitSurveyQuestionType.Text))
                            {
                                <div class="border border-gray-200 rounded-lg p-4">
                                    <div class="flex justify-between items-start mb-3">
                                        <h4 class="text-sm font-medium text-gray-900">@questionStat.QuestionText</h4>
                                        <span class="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">
                                            @questionStat.ResponseCount cevap
                                        </span>
                                    </div>

                                    @if (questionStat.QuestionType == PushDashboard.Models.ExitSurveyQuestionType.Rating && questionStat.AverageRating.HasValue)
                                    {
                                        <!-- Puanlama İstatistikleri -->
                                        <div class="mb-4">
                                            <div class="flex items-center mb-2">
                                                <span class="text-sm text-gray-600 mr-2">Ortalama:</span>
                                                <span class="text-lg font-bold text-blue-600">@questionStat.AverageRating.Value.ToString("F1")</span>
                                                <div class="flex ml-2">
                                                    @for (int i = 1; i <= 5; i++)
                                                    {
                                                        <i class="fas fa-star text-sm @(i <= Math.Round(questionStat.AverageRating.Value) ? "text-yellow-400" : "text-gray-300")"></i>
                                                    }
                                                </div>
                                            </div>
                                        </div>
                                    }
                                    else if (questionStat.QuestionType == PushDashboard.Models.ExitSurveyQuestionType.YesNo || questionStat.QuestionType == PushDashboard.Models.ExitSurveyQuestionType.MultipleChoice)
                                    {
                                        <!-- Evet/Hayır ve Çoktan Seçmeli İstatistikleri -->
                                        <div class="space-y-2">
                                            @foreach (var option in questionStat.OptionStats)
                                            {
                                                <div class="flex items-center justify-between">
                                                    <span class="text-sm text-gray-700">@option.OptionText:</span>
                                                    <div class="flex items-center">
                                                        <div class="w-24 bg-gray-200 rounded-full h-3 mr-3">
                                                            <div class="@(option.OptionText == "Evet" ? "bg-green-500" : option.OptionText == "Hayır" ? "bg-red-500" : "bg-blue-500") h-3 rounded-full" style="width: @(option.Percentage)%"></div>
                                                        </div>
                                                        <span class="text-sm font-medium text-gray-900 min-w-[80px] text-right">
                                                            @option.Count (@option.Percentage.ToString("F1")%)
                                                        </span>
                                                    </div>
                                                </div>
                                            }
                                        </div>
                                    }
                                    else if (questionStat.QuestionType == PushDashboard.Models.ExitSurveyQuestionType.Text)
                                    {
                                        <!-- Metin Cevapları -->
                                        <div class="max-h-32 overflow-y-auto">
                                            @if (questionStat.TextResponses.Any())
                                            {
                                                <div class="space-y-1">
                                                    @foreach (var textResponse in questionStat.TextResponses.Take(5))
                                                    {
                                                        <div class="text-xs text-gray-600 bg-gray-50 p-2 rounded">
                                                            "@textResponse"
                                                        </div>
                                                    }
                                                    @if (questionStat.TextResponses.Count > 5)
                                                    {
                                                        <div class="text-xs text-gray-500 text-center">
                                                            +@(questionStat.TextResponses.Count - 5) daha fazla cevap...
                                                        </div>
                                                    }
                                                </div>
                                            }
                                            else
                                            {
                                                <p class="text-xs text-gray-500">Henüz metin cevabı yok</p>
                                            }
                                        </div>
                                    }
                                </div>
                            }
                        </div>
                    </div>
                </div>
            }

            <!-- Tüm Cevapları Görüntüle -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="p-6 text-center">
                    <div class="mb-4">
                        <i class="fas fa-chart-line text-blue-500 text-4xl mb-3"></i>
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">Detaylı Analiz</h3>
                        <p class="text-gray-600 text-sm">Tüm anket cevaplarını görüntüleyin ve detaylı analiz yapın</p>
                    </div>
                    <a href="@Url.Action("AllResponses")" class="inline-flex items-center px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors duration-200">
                        <i class="fas fa-comments mr-2"></i>
                        Tüm Cevapları Görüntüle
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Soru Ekleme/Düzenleme Modal -->
<div class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden" id="questionModal">
    <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-1/2 lg:w-1/3 shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-semibold text-gray-900" id="questionModalTitle">Soru Ekle/Düzenle</h3>
                <button type="button" class="text-gray-400 hover:text-gray-600" id="closeModalBtn">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>

            <form id="questionForm" class="space-y-4">
                <input type="hidden" id="questionId" name="questionId" value="0">

                <div>
                    <label for="questionText" class="block text-sm font-medium text-gray-700 mb-2">Soru Metni</label>
                    <textarea class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                              id="questionText" name="questionText" rows="3" required></textarea>
                </div>

                <div>
                    <label for="questionType" class="block text-sm font-medium text-gray-700 mb-2">Soru Tipi</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            id="questionType" name="questionType" required>
                        <option value="0">Çoktan Seçmeli</option>
                        <option value="1">Serbest Metin</option>
                        <option value="2">Evet/Hayır</option>
                        <option value="3">Puanlama (1-5 Yıldız)</option>
                    </select>
                </div>

                <div id="optionsContainer" class="hidden">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Seçenekler</label>
                    <div id="optionsList" class="space-y-2"></div>
                    <button type="button" class="inline-flex items-center px-3 py-2 border border-blue-300 text-blue-700 bg-blue-50 hover:bg-blue-100 text-sm font-medium rounded-md transition-colors duration-200" id="addOptionBtn">
                        <i class="fas fa-plus mr-2"></i>
                        Seçenek Ekle
                    </button>
                </div>

                <div class="flex items-center">
                    <input type="checkbox" id="isRequired" name="isRequired"
                           class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                    <label for="isRequired" class="ml-2 text-sm text-gray-700">
                        Bu soru zorunlu
                    </label>
                </div>
            </form>

            <div class="flex justify-end space-x-3 mt-6">
                <button type="button" class="px-4 py-2 bg-gray-300 hover:bg-gray-400 text-gray-800 font-medium rounded-md transition-colors duration-200" id="cancelModalBtn">
                    İptal
                </button>
                <button type="button" class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-md transition-colors duration-200" id="saveQuestionBtn">
                    Kaydet
                </button>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script src="~/js/exit-survey.js"></script>
}
