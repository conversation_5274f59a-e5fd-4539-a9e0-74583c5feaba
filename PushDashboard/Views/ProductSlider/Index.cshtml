@model List<PushDashboard.DTOs.ProductSliderResponseDto>
@{
    ViewData["Title"] = "Ürün Slider Yönetimi";
    var stats = ViewBag.Stats as dynamic;
}

<!-- Anti-forgery token for AJAX requests -->
@Html.AntiForgeryToken()

<main class="p-4 bg-gray-50 relative">
    <div class="mb-6">
        <div class="flex justify-between items-center">
            <div>
                <h2 class="text-2xl font-bold text-gray-800">Ürün Slider Yönetimi</h2>
                <p class="text-gray-600">E-ticaret sitenize gömülebilir ürün slider'ları oluşturun ve yönetin</p>
            </div>
            <div class="flex gap-3">
                <button onclick="showR2EmbedCode()" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg flex items-center gap-2">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4"></path>
                    </svg>
                    Embed Kodu Al
                </button>
                <button onclick="exportToR2()" class="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg flex items-center gap-2">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10"></path>
                    </svg>
                    R2'ye Export Et
                </button>
                <button onclick="openCreateSliderModal()" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center gap-2">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                    </svg>
                    Yeni Slider Oluştur
                </button>
            </div>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="p-2 bg-blue-100 rounded-lg">
                    <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Toplam Slider</p>
                    <p class="text-2xl font-semibold text-gray-900">@(stats?.TotalSliders ?? 0)</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="p-2 bg-green-100 rounded-lg">
                    <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Aktif Slider</p>
                    <p class="text-2xl font-semibold text-gray-900">@(stats?.ActiveSliders ?? 0)</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="p-2 bg-purple-100 rounded-lg">
                    <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Toplam Ürün</p>
                    <p class="text-2xl font-semibold text-gray-900">@(stats?.TotalProducts ?? 0)</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="p-2 bg-yellow-100 rounded-lg">
                    <svg class="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Aktif Ürün</p>
                    <p class="text-2xl font-semibold text-gray-900">@(stats?.ActiveProducts ?? 0)</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Sliders Grid -->
    <div class="bg-white rounded-lg shadow">
        <div class="p-6 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Slider'larınız</h3>
        </div>
        
        @if (Model.Any())
        {
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 p-6">
                @foreach (var slider in Model)
                {
                    <div class="border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow">
                        <div class="flex justify-between items-start mb-4">
                            <div>
                                <h4 class="text-lg font-semibold text-gray-900">@slider.Name</h4>
                                @if (!string.IsNullOrEmpty(slider.Description))
                                {
                                    <p class="text-sm text-gray-600 mt-1">@slider.Description</p>
                                }
                            </div>
                            <div class="flex items-center gap-2">
                                @if (slider.IsActive)
                                {
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                        Aktif
                                    </span>
                                }
                                else
                                {
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                        Pasif
                                    </span>
                                }
                            </div>
                        </div>

                        <div class="space-y-2 mb-4">
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-600">Tip:</span>
                                <span class="font-medium">
                                    @(slider.DisplayType == "slider" ? "Slider" : 
                                      slider.DisplayType == "tabs" ? "Sekmeler" : 
                                      slider.DisplayType == "grid" ? "Grid" : slider.DisplayType)
                                </span>
                            </div>
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-600">Ürün Sayısı:</span>
                                <span class="font-medium">@slider.ItemCount</span>
                            </div>
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-600">Oluşturulma:</span>
                                <span class="font-medium">@slider.CreatedAt.ToString("dd.MM.yyyy")</span>
                            </div>
                        </div>

                        <div class="flex gap-2">
                            <a href="@Url.Action("Edit", new { id = slider.Id })" 
                               class="flex-1 bg-blue-600 hover:bg-blue-700 text-white text-center py-2 px-3 rounded text-sm">
                                Düzenle
                            </a>
                            <button onclick="showEmbedCode(@slider.Id, '@slider.Name')" 
                                    class="flex-1 bg-gray-600 hover:bg-gray-700 text-white py-2 px-3 rounded text-sm">
                                Embed Kodu
                            </button>
                            <button onclick="toggleSliderStatus(@slider.Id)" 
                                    class="bg-yellow-600 hover:bg-yellow-700 text-white py-2 px-3 rounded text-sm">
                                @(slider.IsActive ? "Pasif Yap" : "Aktif Yap")
                            </button>
                            <button onclick="deleteSlider(@slider.Id, '@slider.Name')" 
                                    class="bg-red-600 hover:bg-red-700 text-white py-2 px-3 rounded text-sm">
                                Sil
                            </button>
                        </div>
                    </div>
                }
            </div>
        }
        else
        {
            <div class="p-12 text-center">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3"></path>
                </svg>
                <h3 class="mt-2 text-sm font-medium text-gray-900">Henüz slider oluşturmadınız</h3>
                <p class="mt-1 text-sm text-gray-500">İlk ürün slider'ınızı oluşturarak başlayın.</p>
                <div class="mt-6">
                    <button onclick="openCreateSliderModal()" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg">
                        Yeni Slider Oluştur
                    </button>
                </div>
            </div>
        }
    </div>
</main>

<!-- Create Slider Modal -->
<div id="createSliderModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg max-w-md w-full p-6">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-medium text-gray-900">Yeni Slider Oluştur</h3>
                <button onclick="closeCreateSliderModal()" class="text-gray-400 hover:text-gray-600">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
            
            <form id="createSliderForm">
                <div class="space-y-4">
                    <div>
                        <label for="sliderName" class="block text-sm font-medium text-gray-700">Slider Adı</label>
                        <input type="text" id="sliderName" name="name" required 
                               class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                    </div>
                    
                    <div>
                        <label for="sliderDescription" class="block text-sm font-medium text-gray-700">Açıklama (Opsiyonel)</label>
                        <textarea id="sliderDescription" name="description" rows="3"
                                  class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"></textarea>
                    </div>
                    
                    <div>
                        <label for="displayType" class="block text-sm font-medium text-gray-700">Görüntüleme Tipi</label>
                        <select id="displayType" name="displayType" 
                                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                            <option value="slider">Slider</option>
                            <option value="tabs">Sekmeler</option>
                            <option value="grid">Grid</option>
                        </select>
                    </div>
                </div>
                
                <div class="mt-6 flex gap-3">
                    <button type="submit" class="flex-1 bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-md">
                        Oluştur
                    </button>
                    <button type="button" onclick="closeCreateSliderModal()" 
                            class="flex-1 bg-gray-300 hover:bg-gray-400 text-gray-700 py-2 px-4 rounded-md">
                        İptal
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Embed Code Modal -->
<div id="embedCodeModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg max-w-2xl w-full p-6">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-medium text-gray-900">Embed Kodu</h3>
                <button onclick="closeEmbedCodeModal()" class="text-gray-400 hover:text-gray-600">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
            
            <div class="space-y-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Bu kodu sitenizin HTML'ine yapıştırın:</label>
                    <div class="relative">
                        <textarea id="embedCodeText" readonly rows="3"
                                  class="w-full p-3 border border-gray-300 rounded-md bg-gray-50 font-mono text-sm"></textarea>
                        <button onclick="copyEmbedCode()" 
                                class="absolute top-2 right-2 bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm">
                            Kopyala
                        </button>
                    </div>
                </div>
                
                <div class="bg-blue-50 p-4 rounded-md">
                    <h4 class="font-medium text-blue-900 mb-2">Kullanım Talimatları:</h4>
                    <ul class="text-sm text-blue-800 space-y-1">
                        <li>• Bu kodu slider'ın görünmesini istediğiniz sayfanın HTML'ine yapıştırın</li>
                        <li>• Kod otomatik olarak slider'ı yükleyecek ve görüntüleyecektir</li>
                        <li>• Slider responsive tasarıma sahiptir ve tüm cihazlarda çalışır</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- R2 Embed Code Modal -->
<div id="r2EmbedCodeModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-medium text-gray-900">Pushonica Modülleri - Embed Kodu</h3>
                <button onclick="closeR2EmbedCodeModal()" class="text-gray-400 hover:text-gray-600">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>

            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700 mb-2">Embed Kodu:</label>
                <textarea id="r2EmbedCodeText" readonly class="w-full h-32 p-3 border border-gray-300 rounded-md bg-gray-50 font-mono text-sm resize-none"></textarea>
                <button onclick="copyR2EmbedCode()" class="mt-2 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm">
                    Kopyala
                </button>
            </div>

            <div class="bg-green-50 p-4 rounded-md">
                <h4 class="font-medium text-green-900 mb-2">Yeni Sistem Avantajları:</h4>
                <ul class="text-sm text-green-800 space-y-1">
                    <li>• <strong>Tek Script:</strong> Bu kodu sitenizin tüm sayfalarına ekleyin</li>
                    <li>• <strong>Otomatik Tanıma:</strong> Script hangi sayfalarda çalışacağını otomatik belirler</li>
                    <li>• <strong>CDN Hızı:</strong> R2 CDN üzerinden hızlı yükleme</li>
                    <li>• <strong>Responsive:</strong> Tüm cihazlarda mükemmel görünüm</li>
                    <li>• <strong>Ticimax Uyumlu:</strong> Ticimax e-ticaret yapısına özel optimize edilmiş</li>
                </ul>
            </div>

            <div class="bg-blue-50 p-4 rounded-md mt-4">
                <h4 class="font-medium text-blue-900 mb-2">Kurulum Talimatları:</h4>
                <ol class="text-sm text-blue-800 space-y-1 list-decimal list-inside">
                    <li>Yukarıdaki kodu kopyalayın</li>
                    <li>Ticimax admin panelinde "Tasarım > Script Yönetimi" bölümüne gidin</li>
                    <li>Kodu "Header" veya "Footer" bölümüne yapıştırın</li>
                    <li>Değişiklikleri kaydedin</li>
                    <li>Sitenizi ziyaret ederek slider'ların çalıştığını kontrol edin</li>
                </ol>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script src="~/js/product-slider-admin.js"></script>
}
