@model PushDashboard.DTOs.ProductSliderDetailResponseDto
@{
    ViewData["Title"] = "Slider Düzenle - " + Model.Name;
}

<!-- Anti-forgery token for AJAX requests -->
@Html.AntiForgeryToken()

<!-- Hidden slider ID for JavaScript -->
<input type="hidden" id="sliderId" value="@Model.Id" />

<main class="p-4 bg-gray-50 relative">
    <div class="mb-6">
        <div class="flex justify-between items-center">
            <div>
                <h2 class="text-2xl font-bold text-gray-800">Slider Düzenle</h2>
                <p class="text-gray-600">@Model.Name slider'ını düzenleyin ve ürünlerini yönetin</p>
            </div>
            <div class="flex gap-3">
                <button id="saveSliderBtn" onclick="saveSliderSettings()"
                        class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg flex items-center gap-2">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                    Kaydet
                </button>
                <a href="@Url.Action("Index")" class="bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded-lg">
                    Geri Dön
                </a>
            </div>
        </div>
    </div>

    <!-- Slider Settings -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Slider Info -->
        <div class="lg:col-span-2">
            <div class="bg-white rounded-lg shadow mb-6">
                <div class="p-6 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Slider Bilgileri</h3>
                </div>
                <div class="p-6">
                    <form id="sliderInfoForm">
                        <input type="hidden" id="sliderId" value="@Model.Id">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label for="sliderName" class="block text-sm font-medium text-gray-700">Slider Adı</label>
                                <input type="text" id="sliderName" name="name" value="@Model.Name" required 
                                       class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                            </div>
                            <div>
                                <label for="displayType" class="block text-sm font-medium text-gray-700">Görüntüleme Tipi</label>
                                <select id="displayType" name="displayType" 
                                        class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                                    <option value="slider" selected="@(Model.DisplayType == "slider")">Slider</option>
                                    <option value="tabs" selected="@(Model.DisplayType == "tabs")">Sekmeler</option>
                                    <option value="grid" selected="@(Model.DisplayType == "grid")">Grid</option>
                                </select>
                            </div>
                        </div>
                        <div class="mt-4">
                            <label for="sliderDescription" class="block text-sm font-medium text-gray-700">Açıklama</label>
                            <textarea id="sliderDescription" name="description" rows="3"
                                      class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">@Model.Description</textarea>
                        </div>
                        <div class="mt-4 flex items-center">
                            <input type="checkbox" id="isActive" name="isActive" @(Model.IsActive ? "checked" : "")
                                   class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                            <label for="isActive" class="ml-2 block text-sm text-gray-900">Aktif</label>
                        </div>
                        <div class="mt-6">
                            <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md">
                                Bilgileri Güncelle
                            </button>
                        </div>
                    </form>
                </div>
            </div>



            <!-- Products List -->
            <div class="bg-white rounded-lg shadow">
                <div class="p-6 border-b border-gray-200">
                    <div class="flex justify-between items-center">
                        <h3 class="text-lg font-medium text-gray-900">Ürünler (@Model.Items.Count)</h3>
                        <button onclick="openEcommerceProductModal()" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg flex items-center gap-2">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                            </svg>
                            Ürün Ekle
                        </button>
                    </div>
                </div>
                
                @if (Model.Items.Any())
                {
                    <div class="p-6">
                        <div id="productsList" class="space-y-4">
                            @foreach (var item in Model.Items.OrderBy(i => i.SortOrder))
                            {
                                <div class="border border-gray-200 rounded-lg p-4 product-item" data-item-id="@item.Id">
                                    <div class="flex items-center gap-4">
                                        <div class="drag-handle cursor-move text-gray-400">
                                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8h16M4 16h16"></path>
                                            </svg>
                                        </div>
                                        @if (!string.IsNullOrEmpty(item.ProductImage))
                                        {
                                            <img src="@item.ProductImage" alt="@item.ProductTitle" class="w-16 h-16 object-cover rounded">
                                        }
                                        else
                                        {
                                            <div class="w-16 h-16 bg-gray-200 rounded flex items-center justify-center">
                                                <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                                </svg>
                                            </div>
                                        }
                                        <div class="flex-1">
                                            <h4 class="font-medium text-gray-900">@item.ProductTitle</h4>
                                            @if (item.ProductPrice.HasValue)
                                            {
                                                <p class="text-sm text-blue-600 font-semibold">@item.ProductPrice.Value @item.Currency</p>
                                            }
                                            <p class="text-sm text-gray-500 truncate">@item.ProductUrl</p>
                                        </div>
                                        <div class="flex items-center gap-2">
                                            <button onclick="deleteProduct(@item.Id)" class="text-red-600 hover:text-red-800">
                                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                                </svg>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            }
                        </div>
                    </div>
                }
                else
                {
                    <div class="p-12 text-center">
                        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                        </svg>
                        <h3 class="mt-2 text-sm font-medium text-gray-900">Henüz ürün eklenmemiş</h3>
                        <p class="mt-1 text-sm text-gray-500">İlk ürününüzü ekleyerek başlayın.</p>
                        <div class="mt-6 flex justify-center">
                            <button onclick="openEcommerceProductModal()" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg">
                                Ürün Ekle
                            </button>
                        </div>
                    </div>
                }
            </div>
        </div>

        <!-- Preview Panel -->
        <div class="lg:col-span-1">
            <!-- Button Preview Panel -->
            <div class="bg-white rounded-lg shadow sticky top-4 mb-6" style="display:none;">
                <div class="p-6 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Buton Ön İzlemesi</h3>
                    <p class="text-sm text-gray-600 mt-1">Butonunuzun tasarımını özelleştirin ve önizleyin</p>
                </div>
                <div class="p-6">
                    <!-- Button Preview Container -->
                    <div class="mb-6">
                        <label class="block text-sm font-medium text-gray-700 mb-3">Buton Önizleme</label>
                        <div class="border border-gray-200 rounded-lg p-6 bg-gray-50 relative min-h-[120px]">
                            <div class="text-xs text-gray-500 mb-3">Gerçek sitede nasıl görüneceği:</div>
                            <div id="buttonPreviewContainer" class="relative h-16">
                                <button id="previewButton" onclick="simulateButtonClick()"
                                        class="absolute bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg shadow-lg transition-all duration-200 transform hover:scale-105"
                                        style="right: 10px; top: 50%; transform: translateY(-50%);">
                                    <div class="flex items-center space-x-2">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                        </svg>
                                        <span>Ürünleri Gör</span>
                                    </div>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Button Design Settings -->
                    <div class="space-y-4">
                        <h4 class="text-sm font-medium text-gray-700">Buton Tasarımı</h4>

                        <!-- Button Text -->
                        <div>
                            <label class="block text-xs text-gray-600 mb-1">Buton Metni</label>
                            <input type="text" id="buttonText" value="Ürünleri Gör"
                                   onchange="updateButtonPreview()" oninput="updateButtonPreview()"
                                   class="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                        </div>

                        <!-- Size and Position -->
                        <div class="grid grid-cols-2 gap-3">
                            <div>
                                <label class="block text-xs text-gray-600 mb-1">Buton Boyutu</label>
                                <select id="buttonSize" onchange="updateButtonPreview()"
                                        class="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                                    <option value="small">Küçük</option>
                                    <option value="medium" selected>Orta</option>
                                    <option value="large">Büyük</option>
                                </select>
                            </div>
                            <div>
                                <label class="block text-xs text-gray-600 mb-1">Pozisyon</label>
                                <select id="buttonPosition" onchange="updateButtonPreview()"
                                        class="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                                    <option value="top-left">Sol Üst</option>
                                    <option value="top-right">Sağ Üst</option>
                                    <option value="middle-left">Sol Orta</option>
                                    <option value="middle-right" selected>Sağ Orta</option>
                                    <option value="bottom-left">Sol Alt</option>
                                    <option value="bottom-right">Sağ Alt</option>
                                    <option value="center">Orta</option>
                                </select>
                            </div>
                        </div>

                        <!-- Colors -->
                        <div class="grid grid-cols-3 gap-3">
                            <div>
                                <label class="block text-xs text-gray-600 mb-1">Arka Plan</label>
                                <input type="color" id="buttonBgColor" value="#3B82F6"
                                       onchange="updateButtonPreview()"
                                       class="w-full h-10 border border-gray-300 rounded-md cursor-pointer">
                            </div>
                            <div>
                                <label class="block text-xs text-gray-600 mb-1">Metin Rengi</label>
                                <input type="color" id="buttonTextColor" value="#FFFFFF"
                                       onchange="updateButtonPreview()"
                                       class="w-full h-10 border border-gray-300 rounded-md cursor-pointer">
                            </div>
                            <div>
                                <label class="block text-xs text-gray-600 mb-1">Hover Rengi</label>
                                <input type="color" id="buttonHoverColor" value="#2563EB"
                                       onchange="updateButtonPreview()"
                                       class="w-full h-10 border border-gray-300 rounded-md cursor-pointer">
                            </div>
                        </div>

                        <!-- Border Radius and Icon -->
                        <div class="grid grid-cols-2 gap-3">
                            <div>
                                <label class="block text-xs text-gray-600 mb-1">İkon</label>
                                <select id="buttonIcon" onchange="updateButtonPreview()"
                                        class="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                                    <option value="eye" selected>Göz İkonu</option>
                                    <option value="cart">Sepet İkonu</option>
                                    <option value="arrow">Ok İkonu</option>
                                    <option value="none">İkon Yok</option>
                                </select>
                            </div>
                        </div>

                        <!-- Effects -->
                        <div class="flex space-x-6">
                            <div class="flex items-center">
                                <input type="checkbox" id="buttonShadow" checked onchange="updateButtonPreview()"
                                       class="mr-2 w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500">
                                <label class="text-xs text-gray-600">Gölge Efekti</label>
                            </div>
                            <div class="flex items-center">
                                <input type="checkbox" id="buttonPulse" onchange="updateButtonPreview()"
                                       class="mr-2 w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500">
                                <label class="text-xs text-gray-600">Pulse Animasyonu</label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Slider Preview Panel -->
            <div class="bg-white rounded-lg shadow sticky top-4">
                <div class="p-6 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Slider Ön İzlemesi</h3>
                    <p class="text-sm text-gray-600 mt-1">Slider'ınızın nasıl görüneceğini görün</p>
                </div>
                <div class="p-6">
                    <!-- Display Type Selector -->
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Görüntüleme Tipi</label>
                        <div class="flex space-x-2">
                            <button type="button" onclick="changePreviewType('slider')"
                                    class="preview-type-btn flex-1 px-3 py-2 text-sm border rounded-md transition-colors"
                                    data-type="slider">
                                Slider
                            </button>
                            <button type="button" onclick="changePreviewType('tabs')"
                                    class="preview-type-btn flex-1 px-3 py-2 text-sm border rounded-md transition-colors"
                                    data-type="tabs">
                                Sekmeler
                            </button>
                            <button type="button" onclick="changePreviewType('grid')"
                                    class="preview-type-btn flex-1 px-3 py-2 text-sm border rounded-md transition-colors"
                                    data-type="grid">
                                Grid
                            </button>
                        </div>
                    </div>

                    <!-- Slider Design Customization -->
                    <div class="mb-6">
                        <div class="border border-gray-200 rounded-lg">
                            <button type="button" onclick="toggleSliderDesignPanel()"
                                    class="w-full px-4 py-3 text-left bg-gray-50 hover:bg-gray-100 rounded-t-lg border-b border-gray-200 flex justify-between items-center">
                                <span class="text-sm font-medium text-gray-700">🎨 Tasarım Özelleştirme</span>
                                <svg id="designPanelIcon" class="w-4 h-4 text-gray-500 transform transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </button>

                            <div id="sliderDesignPanel" class="hidden p-4 space-y-4">
                                <!-- Slider Settings Form -->
                                <form id="sliderSettingsForm" class="space-y-4">
                                    <div>
                                        <h5 class="text-sm font-medium text-gray-700 mb-3">Slider Ayarları</h5>
                                        <div class="grid grid-cols-2 gap-3">
                                            <div>
                                                <label class="flex items-center">
                                                    <input type="checkbox" id="autoPlay" @(Model.Settings?.AutoPlay == true ? "checked" : "")
                                                           class="mr-2 rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                                    <span class="text-sm text-gray-700">Otomatik Oynat</span>
                                                </label>
                                            </div>
                                            <div>
                                                <label class="block text-xs text-gray-600 mb-1">Oynatma Aralığı (ms)</label>
                                                <input type="number" id="autoPlayInterval" value="@(Model.Settings?.AutoPlayInterval ?? 5000)"
                                                       min="1000" max="30000" step="1000"
                                                       class="w-full px-2 py-1 text-sm border border-gray-300 rounded">
                                            </div>
                                            <div>
                                                <label class="flex items-center">
                                                    <input type="checkbox" id="showArrows" @(Model.Settings?.ShowArrows == true ? "checked" : "")
                                                           class="mr-2 rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                                    <span class="text-sm text-gray-700">Okları Göster</span>
                                                </label>
                                            </div>
                                            <div>
                                                <label class="flex items-center">
                                                    <input type="checkbox" id="showDots" @(Model.Settings?.ShowDots == true ? "checked" : "")
                                                           class="mr-2 rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                                    <span class="text-sm text-gray-700">Noktaları Göster</span>
                                                </label>
                                            </div>
                                            <div>
                                                <label class="block text-xs text-gray-600 mb-1">Görünüm Başına Öğe</label>
                                                <input type="number" id="itemsPerView" value="@(Model.Settings?.ItemsPerView ?? 4)"
                                                       min="1" max="10"
                                                       class="w-full px-2 py-1 text-sm border border-gray-300 rounded">
                                            </div>
                                            <div>
                                                <label class="block text-xs text-gray-600 mb-1">Tablet Görünümü</label>
                                                <input type="number" id="itemsPerViewTablet" value="@(Model.Settings?.ItemsPerViewTablet ?? 2)"
                                                       min="1" max="8"
                                                       class="w-full px-2 py-1 text-sm border border-gray-300 rounded">
                                            </div>
                                            <div>
                                                <label class="block text-xs text-gray-600 mb-1">Mobil Görünümü</label>
                                                <input type="number" id="itemsPerViewMobile" value="@(Model.Settings?.ItemsPerViewMobile ?? 1)"
                                                       min="1" max="5"
                                                       class="w-full px-2 py-1 text-sm border border-gray-300 rounded">
                                            </div>
                                            <div>
                                                <label class="flex items-center">
                                                    <input type="checkbox" id="showProductPrice" @(Model.Settings?.ShowProductPrice == true ? "checked" : "")
                                                           class="mr-2 rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                                    <span class="text-sm text-gray-700">Fiyatları Göster</span>
                                                </label>
                                            </div>
                                            <div>
                                                <label class="flex items-center">
                                                    <input type="checkbox" id="showProductImage" @(Model.Settings?.ShowProductImage == true ? "checked" : "")
                                                           class="mr-2 rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                                    <span class="text-sm text-gray-700">Resimleri Göster</span>
                                                </label>
                                            </div>
                                            <div>
                                                <label class="flex items-center">
                                                    <input type="checkbox" id="showProductDescription" @(Model.Settings?.ShowProductDescription == true ? "checked" : "")
                                                           class="mr-2 rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                                    <span class="text-sm text-gray-700">Açıklamaları Göster</span>
                                                </label>
                                            </div>
                                            <div>
                                                <label class="block text-xs text-gray-600 mb-1">Resim Oranı</label>
                                                <select id="imageAspectRatio" class="w-full px-2 py-1 text-sm border border-gray-300 rounded">
                                                    <option value="square" selected="@(Model.Settings?.ImageAspectRatio == "square" ? "selected" : null)">Kare</option>
                                                    <option value="landscape" selected="@(Model.Settings?.ImageAspectRatio == "landscape" ? "selected" : null)">Yatay</option>
                                                    <option value="portrait" selected="@(Model.Settings?.ImageAspectRatio == "portrait" ? "selected" : null)">Dikey</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                </form>

                                <!-- Color Settings - Sadece Çalışan Ayarlar -->
                                <div>
                                    <h5 class="text-sm font-medium text-gray-700 mb-3">Renk Ayarları</h5>
                                    <div class="grid grid-cols-2 gap-3">
                                        <div>
                                            <label class="block text-xs text-gray-600 mb-1">Ana Renk (Primary)</label>
                                            <input type="color" id="sliderPrimaryColor" value="@(Model.Settings?.PrimaryColor ?? "#667eea")" onchange="updateSliderDesign()"
                                                   class="w-full h-8 border border-gray-300 rounded cursor-pointer">
                                        </div>
                                        <div>
                                            <label class="block text-xs text-gray-600 mb-1">İkinci Renk (Secondary)</label>
                                            <input type="color" id="sliderSecondaryColor" value="@(Model.Settings?.SecondaryColor ?? "#764ba2")" onchange="updateSliderDesign()"
                                                   class="w-full h-8 border border-gray-300 rounded cursor-pointer">
                                        </div>
                                        <div>
                                            <label class="block text-xs text-gray-600 mb-1">Arka Plan Rengi</label>
                                            <input type="color" id="sliderBackgroundColor" value="@(Model.Settings?.BackgroundColor ?? "#FFFFFF")" onchange="updateSliderDesign()"
                                                   class="w-full h-8 border border-gray-300 rounded cursor-pointer">
                                        </div>
                                        <div>
                                            <label class="block text-xs text-gray-600 mb-1">Metin Rengi</label>
                                            <input type="color" id="textColor" value="@(Model.Settings?.TextColor ?? "#1F2937")" onchange="updateSliderDesign()"
                                                   class="w-full h-8 border border-gray-300 rounded cursor-pointer">
                                        </div>
                                    </div>
                                </div>



                                <!-- Animation & Effects -->
                                <div>
                                    <h5 class="text-sm font-medium text-gray-700 mb-3">Animasyon & Efektler</h5>
                                    <div class="grid grid-cols-2 gap-3">
                                        <div>
                                            <label class="block text-xs text-gray-600 mb-1">Geçiş Süresi: <span id="transitionDurationValue">@(Model.Settings?.TransitionDuration ?? 300)</span>ms</label>
                                            <input type="range" id="sliderTransitionDuration" min="100" max="2000" value="@(Model.Settings?.TransitionDuration ?? 300)"
                                                   onchange="updateSliderDesign()" oninput="updateTransitionDurationValue(this.value)"
                                                   class="w-full">
                                        </div>
                                        <div>
                                            <label class="block text-xs text-gray-600 mb-1">Animasyon Tipi</label>
                                            <select id="animationType" onchange="updateSliderDesign()"
                                                    class="w-full px-2 py-1 text-sm border border-gray-300 rounded">
                                                <option value="slide" selected="@(Model.Settings?.AnimationType == "slide" ? "selected" : null)">Kaydırma</option>
                                                <option value="fade" selected="@(Model.Settings?.AnimationType == "fade" ? "selected" : null)">Solma</option>
                                                <option value="zoom" selected="@(Model.Settings?.AnimationType == "zoom" ? "selected" : null)">Yakınlaştırma</option>
                                            </select>
                                        </div>
                                        <div class="flex flex-col space-y-2">
                                            <div class="flex items-center">
                                                <input type="checkbox" id="enableAnimations" @(Model.Settings?.EnableAnimations == true ? "checked" : "") onchange="updateSliderDesign()"
                                                       class="mr-2 w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500">
                                                <label class="text-xs text-gray-600">Animasyonları Etkinleştir</label>
                                            </div>

                                        </div>
                                    </div>
                                </div>

                                <!-- Theme Settings -->
                                <div>
                                    <h5 class="text-sm font-medium text-gray-700 mb-3">Tema Ayarları</h5>
                                    <div class="grid grid-cols-1 gap-3">
                                        <div>
                                            <label class="block text-xs text-gray-600 mb-1">Tema</label>
                                            <select id="theme" onchange="updateSliderDesign()"
                                                    class="w-full px-2 py-1 text-sm border border-gray-300 rounded">
                                                <option value="default" selected="@(Model.Settings?.Theme == "default" || Model.Settings?.Theme == null ? "selected" : null)">Varsayılan</option>
                                                <option value="modern" selected="@(Model.Settings?.Theme == "modern" ? "selected" : null)">Modern</option>
                                                <option value="minimal" selected="@(Model.Settings?.Theme == "minimal" ? "selected" : null)">Minimal</option>
                                                <option value="colorful" selected="@(Model.Settings?.Theme == "colorful" ? "selected" : null)">Renkli</option>
                                            </select>
                                        </div>
                                        <div>
                                            <label class="block text-xs text-gray-600 mb-1">Özel CSS</label>
                                            <textarea id="customCSS" rows="3" placeholder="Özel CSS kodlarınızı buraya yazın..."
                                                      class="w-full px-2 py-1 text-sm border border-gray-300 rounded">@(Model.Settings?.CustomCSS ?? "")</textarea>
                                        </div>
                                    </div>
                                </div>

                                <!-- Quick Presets -->
                                <div>
                                    <h5 class="text-sm font-medium text-gray-700 mb-3">Hızlı Şablonlar</h5>
                                    <div class="flex space-x-2">
                                        <button type="button" onclick="applySliderPreset('modern')"
                                                class="px-3 py-1 text-xs bg-blue-100 text-blue-700 rounded hover:bg-blue-200">
                                            Modern
                                        </button>
                                        <button type="button" onclick="applySliderPreset('minimal')"
                                                class="px-3 py-1 text-xs bg-gray-100 text-gray-700 rounded hover:bg-gray-200">
                                            Minimal
                                        </button>
                                        <button type="button" onclick="applySliderPreset('colorful')"
                                                class="px-3 py-1 text-xs bg-purple-100 text-purple-700 rounded hover:bg-purple-200">
                                            Renkli
                                        </button>
                                        <button type="button" onclick="resetSliderDesign()"
                                                class="px-3 py-1 text-xs bg-red-100 text-red-700 rounded hover:bg-red-200">
                                            Sıfırla
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Preview Container -->
                    <div id="previewContainer" class="border border-gray-200 rounded-lg p-4 bg-gray-50 min-h-[300px] slider-preview">
                        <div id="previewContent">
                            <!-- Preview will be generated here -->
                        </div>
                    </div>

                    <!-- Preview Info -->
                    <div class="mt-4 text-xs text-gray-500">
                        <p>• Ön izleme gerçek boyutları yansıtmayabilir</p>
                        <p>• Değişiklikler otomatik olarak güncellenir</p>
                        <p>• Tasarım ayarları tüm görünüm tiplerinde geçerlidir</p>
                    </div>
                </div>
            </div>


        </div>
    </div>
</main>



<!-- E-commerce Product Selection Modal -->
<div id="ecommerceProductModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg max-w-4xl w-full p-6 max-h-[90vh] overflow-hidden flex flex-col">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-medium text-gray-900">E-ticaret Ürünü Seç</h3>
                <button onclick="closeEcommerceProductModal()" class="text-gray-400 hover:text-gray-600">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>

            <!-- Search and Filters -->
            <div class="mb-4 space-y-4">
                <div class="flex gap-4">
                    <div class="flex-1">
                        <input type="text" id="productSearch" placeholder="Ürün ara..."
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                    </div>
                    <button onclick="searchEcommerceProducts()" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md">
                        Ara
                    </button>
                </div>

                <div class="flex gap-4">
                    <div class="flex-1">
                        <select id="categoryFilter" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                            <option value="">Tüm Kategoriler</option>
                        </select>
                    </div>
                    <div class="flex-1">
                        <select id="brandFilter" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                            <option value="">Tüm Markalar</option>
                        </select>
                    </div>
                    <div class="flex-1">
                        <select id="stockFilter" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                            <option value="">Stok Durumu</option>
                            <option value="true">Stokta Var</option>
                            <option value="false">Stokta Yok</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- Products List -->
            <div class="flex-1 overflow-y-auto">
                <div id="ecommerceProductsList" class="space-y-2">
                    <!-- Products will be loaded here -->
                </div>

                <!-- Loading -->
                <div id="ecommerceProductsLoading" class="hidden text-center py-8">
                    <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                    <p class="mt-2 text-gray-600">Ürünler yükleniyor...</p>
                </div>

                <!-- No Results -->
                <div id="ecommerceProductsEmpty" class="hidden text-center py-8">
                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                    </svg>
                    <h3 class="mt-2 text-sm font-medium text-gray-900">Ürün bulunamadı</h3>
                    <p class="mt-1 text-sm text-gray-500">Arama kriterlerinizi değiştirip tekrar deneyin.</p>
                </div>
            </div>

            <!-- Pagination -->
            <div id="ecommercePagination" class="mt-4 flex justify-center">
                <!-- Pagination will be loaded here -->
            </div>

            <!-- Selected Products -->
            <div id="selectedProductsSection" class="hidden mt-4 pt-4 border-t">
                <h4 class="font-medium text-gray-900 mb-2">Seçilen Ürünler (<span id="selectedCount">0</span>)</h4>
                <div id="selectedProductsList" class="space-y-2 max-h-32 overflow-y-auto">
                    <!-- Selected products will be shown here -->
                </div>
            </div>

            <!-- Actions -->
            <div class="mt-6 flex gap-3">
                <button onclick="addSelectedProducts()" id="addSelectedBtn" disabled
                        class="flex-1 bg-green-600 hover:bg-green-700 disabled:bg-gray-300 disabled:cursor-not-allowed text-white py-2 px-4 rounded-md">
                    Seçilen Ürünleri Ekle
                </button>
                <button onclick="closeEcommerceProductModal()"
                        class="flex-1 bg-gray-300 hover:bg-gray-400 text-gray-700 py-2 px-4 rounded-md">
                    İptal
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Embed Code Modal -->
<div id="embedCodeModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg max-w-2xl w-full p-6">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-medium text-gray-900">Embed Kodu</h3>
                <button onclick="closeEmbedCodeModal()" class="text-gray-400 hover:text-gray-600">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
            
            <div class="space-y-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Bu kodu sitenizin HTML'ine yapıştırın:</label>
                    <div class="relative">
                        <textarea id="embedCodeText" readonly rows="3"
                                  class="w-full p-3 border border-gray-300 rounded-md bg-gray-50 font-mono text-sm">@Model.EmbedCode</textarea>
                        <button onclick="copyEmbedCode()" 
                                class="absolute top-2 right-2 bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm">
                            Kopyala
                        </button>
                    </div>
                </div>
                
                <div class="bg-blue-50 p-4 rounded-md">
                    <h4 class="font-medium text-blue-900 mb-2">Kullanım Talimatları:</h4>
                    <ul class="text-sm text-blue-800 space-y-1">
                        <li>• Bu kodu slider'ın görünmesini istediğiniz sayfanın HTML'ine yapıştırın</li>
                        <li>• Kod otomatik olarak slider'ı yükleyecek ve görüntüleyecektir</li>
                        <li>• Slider responsive tasarıma sahiptir ve tüm cihazlarda çalışır</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script src="~/js/product-slider-edit.js"></script>
}
