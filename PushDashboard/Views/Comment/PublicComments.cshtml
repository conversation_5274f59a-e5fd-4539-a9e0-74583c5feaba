@model PushDashboard.DTOs.CommentDetailsDto
@{
    ViewData["Title"] = "Yorumlar";
    Layout = null; // No layout for public view
}

<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - Pushonica</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3B82F6',
                        'primary-dark': '#2563EB'
                    }
                }
            }
        }
    </script>
    <style>
        .comment-card {
            background-color: white;
            border-radius: 0.5rem;
            box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            border: 1px solid #e5e7eb;
            padding: 1.5rem;
            transition: box-shadow 0.2s;
        }
        .comment-card:hover {
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }
        .rating-stars {
            display: flex;
            align-items: center;
            gap: 0.25rem;
        }
        .star-filled {
            color: #fbbf24;
        }
        .star-empty {
            color: #d1d5db;
        }
        .comment-photo {
            width: 4rem;
            height: 4rem;
            object-fit: cover;
            border-radius: 0.5rem;
            border: 1px solid #e5e7eb;
            cursor: pointer;
            transition: opacity 0.2s;
        }
        .comment-photo:hover {
            opacity: 0.8;
        }
    </style>
</head>
<body class="bg-gray-50">
    <div class="min-h-screen">
        <!-- Header -->
        <header class="bg-white shadow-sm border-b border-gray-200">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex items-center justify-between h-16">
                    <div class="flex items-center">
                        <h1 class="text-xl font-semibold text-gray-900">Ürün Yorumları</h1>
                    </div>
                    <div class="flex items-center space-x-4">
                        <span class="text-sm text-gray-600">Toplam @Model.TotalComments yorum</span>
                        <button onclick="window.print()" class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                            <svg class="-ml-1 mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z"></path>
                            </svg>
                            Yazdır
                        </button>
                    </div>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
            <!-- Product Info -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
                <div class="flex items-center justify-between">
                    <div>
                        <h2 class="text-lg font-semibold text-gray-900">Ürün Bilgileri</h2>
                        <p class="text-sm text-gray-600 mt-1">@Model.ProductUrl</p>
                        <p class="text-sm text-gray-500 mt-1">Ürün ID: @Model.ExternalProductId</p>
                    </div>
                    <div class="text-right">
                        <div class="text-2xl font-bold text-primary">@Model.TotalComments</div>
                        <div class="text-sm text-gray-600">Toplam Yorum</div>
                    </div>
                </div>
            </div>

            @if (Model.Comments != null && Model.Comments.Any())
            {
                <!-- Comments Grid -->
                <div class="space-y-6">
                    @foreach (var comment in Model.Comments)
                    {
                        <div class="comment-card">
                            <div class="flex items-start space-x-4">
                                <div class="flex-shrink-0">
                                    <div class="w-12 h-12 bg-gradient-to-br from-blue-400 to-blue-600 rounded-full flex items-center justify-center">
                                        <span class="text-white font-medium text-sm">
                                            @(comment.user.Length > 0 ? comment.user.Substring(0, 1).ToUpper() : "?")
                                        </span>
                                    </div>
                                </div>
                                
                                <div class="flex-1 min-w-0">
                                    <div class="flex items-center justify-between">
                                        <div class="flex items-center space-x-3">
                                            <h4 class="text-sm font-medium text-gray-900">@comment.user</h4>
                                            @if (!string.IsNullOrEmpty(comment.elit_customer))
                                            {
                                                <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                                    <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                                                    </svg>
                                                    Elite
                                                </span>
                                            }
                                        </div>
                                        <div class="flex items-center space-x-3">
                                            <div class="rating-stars">
                                                @for (int i = 1; i <= 5; i++)
                                                {
                                                    <svg class="w-4 h-4 @(i <= comment.rating ? "star-filled" : "star-empty")" fill="currentColor" viewBox="0 0 20 20">
                                                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                                                    </svg>
                                                }
                                            </div>
                                            <span class="text-sm text-gray-500">@comment.date</span>
                                        </div>
                                    </div>
                                    
                                    <div class="mt-3">
                                        <p class="text-gray-700 leading-relaxed">@comment.comment</p>
                                    </div>
                                    
                                    @if (comment.photos != null && comment.photos.Any())
                                    {
                                        <div class="mt-4">
                                            <div class="flex flex-wrap gap-3">
                                                @foreach (var photo in comment.photos)
                                                {
                                                    <img src="@photo" alt="Yorum fotoğrafı" class="comment-photo" onclick="showImageModal('@photo')">
                                                }
                                            </div>
                                        </div>
                                    }
                                    
                                    <div class="mt-4 flex items-center justify-between">
                                        @if (!string.IsNullOrEmpty(comment.like_count) && comment.like_count != "0")
                                        {
                                            <div class="flex items-center text-sm text-gray-500">
                                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 10h4.764a2 2 0 011.789 2.894l-3.5 7A2 2 0 0115.263 21h-4.017c-.163 0-.326-.02-.485-.06L7 20m7-10V5a2 2 0 00-2-2h-.095c-.5 0-.905.405-.905.905 0 .714-.211 1.412-.608 2.006L9 7v13m-3-4h-2m-2-2h2m0 0h2v2H4v-2z"></path>
                                                </svg>
                                                @comment.like_count beğeni
                                            </div>
                                        }
                                        else
                                        {
                                            <div></div>
                                        }
                                        
                                        <div class="text-xs text-gray-400">
                                            Yorum #@(Model.Comments.IndexOf(comment) + 1)
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    }
                </div>
            }
            else
            {
                <!-- No Comments -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-12 text-center">
                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                    </svg>
                    <h3 class="mt-2 text-sm font-medium text-gray-900">Yorum bulunamadı</h3>
                    <p class="mt-1 text-sm text-gray-500">Bu ürün için henüz yorum bulunmuyor.</p>
                </div>
            }
        </main>

        <!-- Footer -->
        <footer class="bg-white border-t border-gray-200 mt-12">
            <div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
                <div class="text-center text-sm text-gray-500">
                    <p>Bu yorumlar Pushonica tarafından otomatik olarak çekilmiştir.</p>
                    <p class="mt-1">Oluşturulma tarihi: @DateTime.Now.ToString("dd.MM.yyyy HH:mm")</p>
                </div>
            </div>
        </footer>
    </div>

    <!-- Image Modal -->
    <div id="imageModal" class="fixed inset-0 bg-gray-600 bg-opacity-75 overflow-y-auto h-full w-full hidden z-50">
        <div class="relative top-20 mx-auto p-5 border w-11/12 max-w-4xl shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold text-gray-900">Yorum Fotoğrafı</h3>
                    <button id="closeImageModal" class="text-gray-400 hover:text-gray-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
                <div class="text-center">
                    <img id="modalImage" src="" alt="Yorum fotoğrafı" class="max-w-full h-auto rounded-lg">
                </div>
            </div>
        </div>
    </div>

    <script>
        function showImageModal(imageUrl) {
            document.getElementById('modalImage').src = imageUrl;
            document.getElementById('imageModal').classList.remove('hidden');
        }

        document.getElementById('closeImageModal').addEventListener('click', function() {
            document.getElementById('imageModal').classList.add('hidden');
        });

        // Close modal when clicking outside
        document.getElementById('imageModal').addEventListener('click', function(e) {
            if (e.target === this) {
                this.classList.add('hidden');
            }
        });

        // Print styles
        const printStyles = `
            <style media="print">
                @@page { margin: 1cm; }
                .no-print { display: none !important; }
                .comment-card { break-inside: avoid; margin-bottom: 1rem; }
                .comment-photo { max-width: 100px; max-height: 100px; }
                header { background: white !important; box-shadow: none !important; }
            </style>
        `;
        document.head.insertAdjacentHTML('beforeend', printStyles);
    </script>
</body>
</html>
