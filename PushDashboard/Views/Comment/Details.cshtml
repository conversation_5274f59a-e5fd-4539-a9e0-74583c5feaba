@model PushDashboard.ViewModels.CommentRequestDetailsViewModel
@{
    ViewData["Title"] = "Yorum Detayları";
}

<style>
    .comment-card {
        background-color: white;
        border-radius: 0.5rem;
        box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
        border: 1px solid #e5e7eb;
        padding: 1.5rem;
        transition: box-shadow 0.2s;
    }
    .comment-card:hover {
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    }
    .rating-stars {
        display: flex;
        align-items: center;
        gap: 0.25rem;
    }
    .star-filled {
        color: #fbbf24;
    }
    .star-empty {
        color: #d1d5db;
    }
    .comment-photo {
        width: 4rem;
        height: 4rem;
        object-fit: cover;
        border-radius: 0.5rem;
        border: 1px solid #e5e7eb;
    }
    .pagination-btn {
        padding: 0.5rem 0.75rem;
        font-size: 0.875rem;
        font-weight: 500;
        color: #6b7280;
        background-color: white;
        border: 1px solid #d1d5db;
        border-radius: 0.375rem;
        transition: all 0.2s;
        cursor: pointer;
    }
    .pagination-btn:hover {
        background-color: #f9fafb;
        color: #374151;
    }
    .pagination-btn:focus {
        outline: none;
        ring: 2px;
        ring-offset: 2px;
        ring-color: #3b82f6;
    }
    .pagination-btn.active {
        background-color: #3b82f6;
        color: white;
        border-color: #3b82f6;
    }
    .pagination-btn.active:hover {
        background-color: #2563eb;
    }
    .pagination-btn:disabled {
        opacity: 0.5;
        cursor: not-allowed;
    }
    .pagination-btn:disabled:hover {
        background-color: white;
        color: #6b7280;
    }
</style>

<main class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">Yorum Detayları</h1>
                <p class="mt-2 text-sm text-gray-600">@Model.Request.ProductUrl</p>
            </div>
            <div class="flex items-center space-x-3">
                @if (Model.Request.HasComments)
                {
                    <button onclick="exportComments()" class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                        <svg class="-ml-1 mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        Dışa Aktar
                    </button>
                }
                <a href="@Url.Action("Index")" class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                    <svg class="-ml-1 mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                    </svg>
                    Geri Dön
                </a>
            </div>
        </div>
    </div>

    <!-- Request Info -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div>
                <h3 class="text-sm font-medium text-gray-500">Durum</h3>
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium @Model.Request.StatusBadgeClass mt-1">
                    <svg class="w-3 h-3 mr-1" data-feather="@Model.Request.StatusIcon"></svg>
                    @Model.Request.Status
                </span>
            </div>
            <div>
                <h3 class="text-sm font-medium text-gray-500">İstenilen Yorum Sayısı</h3>
                <p class="text-lg font-semibold text-gray-900 mt-1">@Model.Request.RequestedCommentsCount</p>
            </div>
            <div>
                <h3 class="text-sm font-medium text-gray-500">Çekilen Yorum Sayısı</h3>
                <p class="text-lg font-semibold text-gray-900 mt-1">@(Model.Request.ActualCommentsCount?.ToString() ?? "0")</p>
            </div>
            <div>
                <h3 class="text-sm font-medium text-gray-500">Oluşturulma Tarihi</h3>
                <p class="text-lg font-semibold text-gray-900 mt-1">@Model.Request.CreatedAt.ToString("dd.MM.yyyy HH:mm")</p>
            </div>
        </div>

        @if (!string.IsNullOrEmpty(Model.Request.ErrorMessage))
        {
            <div class="mt-6 bg-red-50 border border-red-200 rounded-lg p-4">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <div class="ml-3">
                        <h3 class="text-sm font-medium text-red-800">Hata</h3>
                        <div class="mt-2 text-sm text-red-700">
                            <p>@Model.Request.ErrorMessage</p>
                        </div>
                    </div>
                </div>
            </div>
        }
    </div>

    @if (Model.Details != null && Model.Details.Comments.Any())
    {
        <!-- Comments Section -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="px-6 py-4 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <h2 class="text-lg font-semibold text-gray-900">Yorumlar (@Model.Details.TotalComments)</h2>
                    <div class="flex items-center space-x-4">
                        <!-- Pagination Info -->
                        <span class="text-sm text-gray-600">
                            @((Model.Details.CurrentPage - 1) * Model.Details.PageSize + 1) - 
                            @(Math.Min(Model.Details.CurrentPage * Model.Details.PageSize, Model.Details.TotalComments)) / 
                            @Model.Details.TotalComments
                        </span>
                        
                        <!-- Pagination Controls -->
                        <div class="flex items-center space-x-1">
                            <button onclick="loadPage(@(Model.Details.CurrentPage - 1))" 
                                    class="pagination-btn" 
                                    @(!Model.Details.HasPreviousPage ? "disabled" : "")>
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                                </svg>
                            </button>
                            
                            @for (int i = Math.Max(1, Model.Details.CurrentPage - 2); i <= Math.Min(Model.Details.TotalPages, Model.Details.CurrentPage + 2); i++)
                            {
                                <button onclick="loadPage(@i)" 
                                        class="pagination-btn @(i == Model.Details.CurrentPage ? "active" : "")">
                                    @i
                                </button>
                            }
                            
                            <button onclick="loadPage(@(Model.Details.CurrentPage + 1))" 
                                    class="pagination-btn" 
                                    @(!Model.Details.HasNextPage ? "disabled" : "")>
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="p-6">
                <div class="space-y-6">
                    @foreach (var comment in Model.Details.Comments)
                    {
                        <div class="comment-card">
                            <div class="flex items-start space-x-4">
                                <div class="flex-shrink-0">
                                    <div class="w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center">
                                        <svg class="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                        </svg>
                                    </div>
                                </div>
                                
                                <div class="flex-1 min-w-0">
                                    <div class="flex items-center justify-between">
                                        <div class="flex items-center space-x-3">
                                            <h4 class="text-sm font-medium text-gray-900">@comment.user</h4>
                                            @if (!string.IsNullOrEmpty(comment.elit_customer))
                                            {
                                                <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-yellow-100 text-yellow-800">
                                                    Elite
                                                </span>
                                            }
                                        </div>
                                        <div class="flex items-center space-x-2">
                                            <div class="rating-stars">
                                                @for (int i = 1; i <= 5; i++)
                                                {
                                                    <svg class="w-4 h-4 @(i <= comment.rating ? "star-filled" : "star-empty")" fill="currentColor" viewBox="0 0 20 20">
                                                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                                                    </svg>
                                                }
                                            </div>
                                            <span class="text-sm text-gray-500">@comment.date</span>
                                        </div>
                                    </div>
                                    
                                    <div class="mt-3">
                                        <p class="text-gray-700 text-sm leading-relaxed">@comment.comment</p>
                                    </div>
                                    
                                    @if (comment.photos != null && comment.photos.Any())
                                    {
                                        <div class="mt-4">
                                            <div class="flex flex-wrap gap-2">
                                                @foreach (var photo in comment.photos)
                                                {
                                                    <img src="@photo" alt="Yorum fotoğrafı" class="comment-photo" onclick="showImageModal('@photo')">
                                                }
                                            </div>
                                        </div>
                                    }
                                    
                                    @if (!string.IsNullOrEmpty(comment.like_count) && comment.like_count != "0")
                                    {
                                        <div class="mt-3 flex items-center">
                                            <svg class="w-4 h-4 text-gray-400 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 10h4.764a2 2 0 011.789 2.894l-3.5 7A2 2 0 0115.263 21h-4.017c-.163 0-.326-.02-.485-.06L7 20m7-10V5a2 2 0 00-2-2h-.095c-.5 0-.905.405-.905.905 0 .714-.211 1.412-.608 2.006L9 7v13m-3-4h-2m-2-2h2m0 0h2v2H4v-2z"></path>
                                            </svg>
                                            <span class="text-sm text-gray-500">@comment.like_count beğeni</span>
                                        </div>
                                    }
                                </div>
                            </div>
                        </div>
                    }
                </div>
            </div>
        </div>
    }
    else if (Model.Request.Status == "Hazır")
    {
        <!-- No Comments -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-12 text-center">
            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
            </svg>
            <h3 class="mt-2 text-sm font-medium text-gray-900">Yorum bulunamadı</h3>
            <p class="mt-1 text-sm text-gray-500">Bu ürün için henüz yorum bulunmuyor.</p>
        </div>
    }
    else
    {
        <!-- Processing State -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-12 text-center">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-primary mb-4"></div>
            <h3 class="text-sm font-medium text-gray-900">İşleniyor</h3>
            <p class="mt-1 text-sm text-gray-500">Yorumlar henüz hazır değil. Lütfen bekleyin.</p>
        </div>
    }
</main>

<!-- Image Modal -->
<div id="imageModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-11/12 max-w-2xl shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-gray-900">Yorum Fotoğrafı</h3>
                <button id="closeImageModal" class="text-gray-400 hover:text-gray-600">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
            <div class="text-center">
                <img id="modalImage" src="" alt="Yorum fotoğrafı" class="max-w-full h-auto rounded-lg">
            </div>
        </div>
    </div>
</div>

@section Scripts {
<script>
function loadPage(page) {
    if (page < 1) return;
    
    const url = new URL(window.location);
    url.searchParams.set('page', page);
    window.location.href = url.toString();
}

function exportComments() {
    const requestId = @Model.Request.Id;
    window.open('@Url.Action("ExportHtml", new { id = Model.Request.Id })', '_blank');
}

function showImageModal(imageUrl) {
    $('#modalImage').attr('src', imageUrl);
    $('#imageModal').removeClass('hidden');
}

$(document).ready(function() {
    // Close image modal
    $('#closeImageModal').on('click', function() {
        $('#imageModal').addClass('hidden');
    });
    
    // Close modal when clicking outside
    $('#imageModal').on('click', function(e) {
        if (e.target === this) {
            $(this).addClass('hidden');
        }
    });
    
    // Initialize Feather icons
    feather.replace();
});
</script>
}
