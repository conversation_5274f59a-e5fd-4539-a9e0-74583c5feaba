@{
    ViewData["Title"] = "Yorum Yönetimi";
}

<!-- Anti-forgery token for AJAX requests -->
@Html.AntiForgeryToken()

<style>
    .tab-content {
        display: none;
    }
    .tab-content.active {
        display: block;
    }
    .tab-button {
        padding: 0.5rem 1rem;
        font-size: 0.875rem;
        font-weight: 500;
        color: #6b7280;
        border-bottom: 2px solid transparent;
        transition: all 0.2s;
    }
    .tab-button:hover {
        color: #374151;
        border-bottom-color: #d1d5db;
    }
    .tab-button.active {
        color: #3b82f6;
        border-bottom-color: #3b82f6;
    }
    .store-card {
        background-color: white;
        border-radius: 0.5rem;
        box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
        border: 1px solid #e5e7eb;
        padding: 1.5rem;
        transition: box-shadow 0.2s;
    }
    .store-card:hover {
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    }
    .product-card {
        background-color: white;
        border-radius: 0.5rem;
        box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
        border: 1px solid #e5e7eb;
        padding: 1rem;
        transition: box-shadow 0.2s;
    }
    .product-card:hover {
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    }
    .job-card {
        background-color: white;
        border-radius: 0.5rem;
        box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
        border: 1px solid #e5e7eb;
        padding: 1.5rem;
        transition: box-shadow 0.2s;
    }
    .job-card:hover {
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    }
</style>

<main class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
    <!-- Header -->
    <div class="mb-8">
        <div class="sm:flex sm:items-center sm:justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">Yorum Yönetimi</h1>
                <p class="mt-2 text-sm text-gray-600">Trendyol mağaza ürünlerinden toplu yorum çekme ve yönetme sistemi</p>
            </div>
        </div>
    </div>

    <!-- Tabs -->
    <div class="border-b border-gray-200 mb-6">
        <nav class="-mb-px flex space-x-8">
            <button class="tab-button active" data-tab="stores">
                <svg class="w-5 h-5 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-2 0H7m5 0v-9a2 2 0 00-2-2H8a2 2 0 00-2 2v9m8 0V9a2 2 0 012-2h2a2 2 0 012 2v9M7 16h6v4H7v-4z"></path>
                </svg>
                Mağaza Yönetimi
            </button>
            <button class="tab-button" data-tab="transfers">
                <svg class="w-5 h-5 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                </svg>
                Transfer İşleri
            </button>
            <button class="tab-button" data-tab="legacy">
                <svg class="w-5 h-5 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C20.168 18.477 18.582 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                </svg>
                Tekli Ürün
            </button>
        </nav>
    </div>

    <!-- Store Management Tab -->
    <div id="stores-tab" class="tab-content active">
        <!-- Add Store Form -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
            <h2 class="text-lg font-semibold text-gray-900 mb-4">Yeni Mağaza Ekle</h2>
            <form id="addStoreForm" class="space-y-4">
                <div>
                    <label for="storeUrl" class="block text-sm font-medium text-gray-700 mb-1">Mağaza URL'si</label>
                    <input type="url" id="storeUrl" name="storeUrl" required
                           placeholder="https://www.trendyol.com/magaza/magaza-adi"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                    <p class="mt-1 text-xs text-gray-500">Trendyol mağaza sayfasının tam URL'sini giriniz</p>
                </div>
                <div class="flex justify-end">
                    <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                        <svg class="-ml-1 mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        Mağaza Ekle
                    </button>
                </div>
            </form>
        </div>

        <!-- Stores List -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="px-6 py-4 border-b border-gray-200">
                <h2 class="text-lg font-semibold text-gray-900">Mağazalarım</h2>
            </div>
            <div id="storesContainer" class="p-6">
                <div id="storesLoading" class="text-center py-8">
                    <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                    <p class="mt-2 text-sm text-gray-600">Mağazalar yükleniyor...</p>
                </div>
                <div id="storesList" class="hidden space-y-4"></div>
                <div id="storesEmpty" class="hidden text-center py-8">
                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-2 0H7m5 0v-9a2 2 0 00-2-2H8a2 2 0 00-2 2v9m8 0V9a2 2 0 012-2h2a2 2 0 012 2v9M7 16h6v4H7v-4z"></path>
                    </svg>
                    <h3 class="mt-2 text-sm font-medium text-gray-900">Henüz mağaza eklenmemiş</h3>
                    <p class="mt-1 text-sm text-gray-500">Başlamak için yukarıdaki formu kullanarak bir mağaza ekleyin.</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Transfer Jobs Tab -->
    <div id="transfers-tab" class="tab-content">
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="px-6 py-4 border-b border-gray-200">
                <h2 class="text-lg font-semibold text-gray-900">Transfer İşleri</h2>
            </div>
            <div id="transfersContainer" class="p-6">
                <div id="transfersLoading" class="text-center py-8">
                    <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                    <p class="mt-2 text-sm text-gray-600">Transfer işleri yükleniyor...</p>
                </div>
                <div id="transfersList" class="hidden space-y-4"></div>
                <div id="transfersEmpty" class="hidden text-center py-8">
                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                    </svg>
                    <h3 class="mt-2 text-sm font-medium text-gray-900">Henüz transfer işi yok</h3>
                    <p class="mt-1 text-sm text-gray-500">Mağaza ürünlerini senkronize ettiğinizde veya yorum transferi başlattığınızda burada görünecek.</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Legacy Single Product Tab -->
    <div id="legacy-tab" class="tab-content">
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h2 class="text-lg font-semibold text-gray-900 mb-4">Tekli Ürün Yorum Çekme</h2>
            <p class="text-sm text-gray-600 mb-6">Tek bir ürün için yorum çekmek istiyorsanız bu formu kullanabilirsiniz.</p>
            
            <div class="text-center">
                <a href="@Url.Action("Create")" class="inline-flex items-center px-6 py-3 border border-transparent shadow-sm text-base font-medium rounded-md text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                    <svg class="-ml-1 mr-3 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    Yeni Yorum İsteği Oluştur
                </a>
            </div>
        </div>
    </div>
</main>

<!-- Store Products Modal -->
<div id="storeProductsModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-11/12 max-w-6xl shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-gray-900" id="modalStoreTitle">Mağaza Ürünleri</h3>
                <button id="closeProductsModal" class="text-gray-400 hover:text-gray-600">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
            
            <div id="productsModalContent">
                <!-- Products will be loaded here -->
            </div>
        </div>
    </div>
</div>

<!-- Transfer Settings Modal -->
<div id="transferModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-gray-900">Yorum Transfer Ayarları</h3>
                <button id="closeTransferModal" class="text-gray-400 hover:text-gray-600">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>

            <div class="mb-4">
                <p class="text-sm text-gray-600 mb-4">
                    <span id="transferProductCount">0</span> ürün için yorum transferi başlatılacak.
                </p>

                <div class="mb-4">
                    <label for="commentCount" class="block text-sm font-medium text-gray-700 mb-2">
                        Ürün başına çekilecek yorum sayısı
                    </label>
                    <input type="number"
                           id="commentCount"
                           name="commentCount"
                           value="100"
                           min="10"
                           max="1000"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                    <p class="mt-1 text-xs text-gray-500">Minimum: 10, Maksimum: 1000</p>
                </div>
            </div>

            <div class="flex items-center justify-end space-x-3">
                <button id="cancelTransfer" class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-500">
                    İptal
                </button>
                <button id="confirmTransfer" class="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-primary">
                    Transfer Başlat
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Notification Container -->
<div id="notificationContainer" class="fixed top-4 right-4 z-50"></div>

@section Scripts {
<script>
// Global variables
let currentStoreId = null;
let selectedProducts = new Set();

// Initialize page
$(document).ready(function() {
    initializeTabs();
    loadStores();
    loadTransferJobs();
    
    // Setup form handlers
    setupStoreForm();
    setupModalHandlers();
    
    // Auto-refresh transfer jobs every 30 seconds
    setInterval(loadTransferJobs, 30000);
});

// Tab functionality
function initializeTabs() {
    $('.tab-button').on('click', function() {
        const tabId = $(this).data('tab');
        
        // Update tab buttons
        $('.tab-button').removeClass('active');
        $(this).addClass('active');
        
        // Update tab content
        $('.tab-content').removeClass('active');
        $(`#${tabId}-tab`).addClass('active');
        
        // Load data for active tab
        if (tabId === 'stores') {
            loadStores();
        } else if (tabId === 'transfers') {
            loadTransferJobs();
        }
    });
}

// Store management functions
function setupStoreForm() {
    $('#addStoreForm').on('submit', function(e) {
        e.preventDefault();
        
        const formData = {
            storeUrl: $('#storeUrl').val()
        };
        
        $.ajax({
            url: '@Url.Action("CreateStore")',
            type: 'POST',
            data: JSON.stringify(formData),
            contentType: 'application/json',
            headers: {
                'RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()
            },
            success: function(response) {
                if (response.success) {
                    showNotification('Mağaza başarıyla eklendi.', 'success');
                    $('#addStoreForm')[0].reset();
                    loadStores();
                } else {
                    showNotification(response.message, 'error');
                }
            },
            error: function() {
                showNotification('Mağaza eklenirken hata oluştu.', 'error');
            }
        });
    });
}

function loadStores() {
    $('#storesLoading').show();
    $('#storesList').hide();
    $('#storesEmpty').hide();
    
    $.ajax({
        url: '@Url.Action("GetStores")',
        type: 'GET',
        success: function(response) {
            $('#storesLoading').hide();
            
            if (response.success && response.data.length > 0) {
                renderStores(response.data);
                $('#storesList').show();
            } else {
                $('#storesEmpty').show();
            }
        },
        error: function() {
            $('#storesLoading').hide();
            showNotification('Mağazalar yüklenirken hata oluştu.', 'error');
        }
    });
}

function renderStores(stores) {
    const container = $('#storesList');
    container.empty();
    
    stores.forEach(store => {
        const storeCard = `
            <div class="store-card">
                <div class="flex items-center justify-between">
                    <div class="flex-1">
                        <h3 class="text-lg font-medium text-gray-900">${store.storeName || 'Bilinmeyen Mağaza'}</h3>
                        <p class="text-sm text-gray-600 mt-1">${store.storeUrl}</p>
                        <div class="flex items-center mt-2 space-x-4">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${store.statusBadgeClass}">
                                <i data-feather="${store.statusIcon}" class="w-3 h-3 mr-1"></i>
                                ${store.syncStatus}
                            </span>
                            ${store.productCount ? `<span class="text-sm text-gray-500">${store.productCount} ürün</span>` : ''}
                            ${store.lastSyncAt ? `<span class="text-sm text-gray-500">Son sync: ${new Date(store.lastSyncAt).toLocaleDateString('tr-TR')}</span>` : ''}
                        </div>
                        ${store.errorMessage ? `<p class="text-sm text-red-600 mt-2">${store.errorMessage}</p>` : ''}
                    </div>
                    <div class="flex items-center space-x-2 ml-4">
                        <button onclick="syncStoreProducts(${store.id})" 
                                class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
                                ${store.syncStatus === 'Syncing' ? 'disabled' : ''}>
                            <i data-feather="refresh-cw" class="w-4 h-4 mr-1 ${store.syncStatus === 'Syncing' ? 'animate-spin' : ''}"></i>
                            ${store.syncStatus === 'Syncing' ? 'Senkronize ediliyor...' : 'Ürünleri Senkronize Et'}
                        </button>
                        ${store.productCount > 0 ? `
                            <button onclick="showStoreProducts(${store.id}, '${store.storeName || 'Mağaza'}')" 
                                    class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                                <i data-feather="package" class="w-4 h-4 mr-1"></i>
                                Ürünleri Görüntüle
                            </button>
                        ` : ''}
                        <button onclick="deleteStore(${store.id})" 
                                class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                            <i data-feather="trash-2" class="w-4 h-4"></i>
                        </button>
                    </div>
                </div>
            </div>
        `;
        container.append(storeCard);
    });
    
    // Re-initialize Feather icons
    if (typeof feather !== 'undefined') {
        feather.replace();
    }
}

function syncStoreProducts(storeId) {
    const button = event.target.closest('button');
    const originalText = button.innerHTML;

    button.disabled = true;
    button.innerHTML = '<i data-feather="refresh-cw" class="w-4 h-4 mr-1 animate-spin"></i>Senkronize ediliyor...';

    $.ajax({
        url: '@Url.Action("SyncStoreProducts")',
        type: 'POST',
        data: JSON.stringify({
            storeId: storeId,
            productCount: 100
        }),
        contentType: 'application/json',
        headers: {
            'RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()
        },
        success: function(response) {
            if (response.success) {
                showNotification('Ürün senkronizasyonu başlatıldı.', 'success');
                setTimeout(loadStores, 2000); // Reload stores after 2 seconds
            } else {
                showNotification(response.message, 'error');
                button.disabled = false;
                button.innerHTML = originalText;
            }
        },
        error: function() {
            showNotification('Senkronizasyon başlatılırken hata oluştu.', 'error');
            button.disabled = false;
            button.innerHTML = originalText;
        }
    });
}

function deleteStore(storeId) {
    if (!confirm('Bu mağazayı silmek istediğinizden emin misiniz? Bu işlem geri alınamaz.')) {
        return;
    }

    $.ajax({
        url: '@Url.Action("DeleteStore")',
        type: 'POST',
        data: JSON.stringify({ storeId: storeId }),
        contentType: 'application/json',
        headers: {
            'RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()
        },
        success: function(response) {
            if (response.success) {
                showNotification('Mağaza silindi.', 'success');
                loadStores();
            } else {
                showNotification(response.message, 'error');
            }
        },
        error: function() {
            showNotification('Mağaza silinirken hata oluştu.', 'error');
        }
    });
}

// Product management functions
function showStoreProducts(storeId, storeName) {
    currentStoreId = storeId;
    selectedProducts.clear();

    $('#modalStoreTitle').text(`${storeName} - Ürünler`);
    $('#storeProductsModal').removeClass('hidden');

    loadStoreProducts(storeId);
}

function loadStoreProducts(storeId) {
    $('#productsModalContent').html(`
        <div class="text-center py-8">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            <p class="mt-2 text-sm text-gray-600">Ürünler yükleniyor...</p>
        </div>
    `);

    $.ajax({
        url: '@Url.Action("GetStoreProducts")',
        type: 'GET',
        data: { storeId: storeId, pageSize: 50 },
        success: function(response) {
            if (response.success) {
                renderStoreProducts(response.data.products, response.data.stats);
            } else {
                $('#productsModalContent').html(`
                    <div class="text-center py-8">
                        <p class="text-red-600">${response.message}</p>
                    </div>
                `);
            }
        },
        error: function() {
            $('#productsModalContent').html(`
                <div class="text-center py-8">
                    <p class="text-red-600">Ürünler yüklenirken hata oluştu.</p>
                </div>
            `);
        }
    });
}

function renderStoreProducts(products, stats) {
    let content = `
        <div class="mb-6">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <span class="text-sm text-gray-600">Toplam: ${stats.totalProducts} ürün</span>
                    <span class="text-sm text-gray-600">Seçili: <span id="selectedCount">${stats.selectedProducts}</span> ürün</span>
                </div>
                <div class="flex items-center space-x-2">
                    <button id="selectAllProducts" class="text-sm text-primary hover:text-primary-dark">Tümünü Seç</button>
                    <button id="clearSelection" class="text-sm text-gray-600 hover:text-gray-800">Seçimi Temizle</button>
                    <button id="transferSelected" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary" disabled>
                        <i data-feather="download" class="w-4 h-4 mr-1"></i>
                        Seçilenleri Transfer Et
                    </button>
                </div>
            </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 max-h-96 overflow-y-auto">
    `;

    products.forEach(product => {
        content += `
            <div class="product-card">
                <div class="flex items-start">
                    <input type="checkbox" class="product-checkbox mt-1 mr-3" data-product-id="${product.id}" ${selectedProducts.has(product.id) ? 'checked' : ''}>
                    <div class="flex-1 min-w-0">
                        <h4 class="text-sm font-medium text-gray-900 truncate" title="${product.title}">${product.shortTitle}</h4>
                        <p class="text-xs text-gray-500 mt-1">ID: ${product.productId}</p>
                        <a href="${product.fullUrl}" target="_blank" class="text-xs text-primary hover:text-primary-dark mt-1 inline-block">
                            Ürünü Görüntüle
                            <i data-feather="external-link" class="w-3 h-3 inline ml-1"></i>
                        </a>
                    </div>
                </div>
            </div>
        `;
    });

    content += '</div>';

    $('#productsModalContent').html(content);

    // Setup event handlers
    setupProductSelectionHandlers();

    // Re-initialize Feather icons
    if (typeof feather !== 'undefined') {
        feather.replace();
    }
}

function setupProductSelectionHandlers() {
    // Individual product selection
    $('.product-checkbox').on('change', function() {
        const productId = parseInt($(this).data('product-id'));
        if ($(this).is(':checked')) {
            selectedProducts.add(productId);
        } else {
            selectedProducts.delete(productId);
        }
        updateSelectionUI();
    });

    // Select all products
    $('#selectAllProducts').on('click', function() {
        $('.product-checkbox').each(function() {
            const productId = parseInt($(this).data('product-id'));
            selectedProducts.add(productId);
            $(this).prop('checked', true);
        });
        updateSelectionUI();
    });

    // Clear selection
    $('#clearSelection').on('click', function() {
        selectedProducts.clear();
        $('.product-checkbox').prop('checked', false);
        updateSelectionUI();
    });

    // Transfer selected products
    $('#transferSelected').on('click', function() {
        if (selectedProducts.size === 0) {
            showNotification('Lütfen en az bir ürün seçin.', 'warning');
            return;
        }

        showTransferModal();
    });
}

function updateSelectionUI() {
    $('#selectedCount').text(selectedProducts.size);
    $('#transferSelected').prop('disabled', selectedProducts.size === 0);
}

function showTransferModal() {
    $('#transferProductCount').text(selectedProducts.size);
    $('#commentCount').val(100); // Reset to default
    $('#transferModal').removeClass('hidden');
}

function transferSelectedProducts(commentCount) {
    const button = $('#confirmTransfer');
    const originalText = button.html();

    button.prop('disabled', true);
    button.html('<i data-feather="loader" class="w-4 h-4 mr-1 animate-spin"></i>Transfer ediliyor...');

    $.ajax({
        url: '@Url.Action("TransferSelectedProducts")',
        type: 'POST',
        data: JSON.stringify({
            storeId: currentStoreId,
            productIds: Array.from(selectedProducts),
            commentCount: commentCount
        }),
        contentType: 'application/json',
        headers: {
            'RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()
        },
        success: function(response) {
            if (response.success) {
                showNotification('Yorum transfer işlemi başlatıldı.', 'success');
                $('#transferModal').addClass('hidden');
                $('#storeProductsModal').addClass('hidden');

                // Switch to transfers tab
                $('.tab-button[data-tab="transfers"]').click();
            } else {
                showNotification(response.message, 'error');
            }
            button.prop('disabled', false);
            button.html(originalText);
        },
        error: function() {
            showNotification('Transfer işlemi başlatılırken hata oluştu.', 'error');
            button.prop('disabled', false);
            button.html(originalText);
        }
    });
}

// Transfer jobs functions
function loadTransferJobs() {
    if (!$('#transfers-tab').hasClass('active')) return;

    $('#transfersLoading').show();
    $('#transfersList').hide();
    $('#transfersEmpty').hide();

    $.ajax({
        url: '@Url.Action("GetTransferJobs")',
        type: 'GET',
        success: function(response) {
            $('#transfersLoading').hide();

            if (response.success && response.data.length > 0) {
                renderTransferJobs(response.data);
                $('#transfersList').show();
            } else {
                $('#transfersEmpty').show();
            }
        },
        error: function() {
            $('#transfersLoading').hide();
            showNotification('Transfer işleri yüklenirken hata oluştu.', 'error');
        }
    });
}

function renderTransferJobs(jobs) {
    const container = $('#transfersList');
    container.empty();

    jobs.forEach(job => {
        const jobCard = `
            <div class="job-card">
                <div class="flex items-center justify-between">
                    <div class="flex-1">
                        <div class="flex items-center space-x-3">
                            <h3 class="text-lg font-medium text-gray-900">${job.jobTypeDisplay}</h3>
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${job.statusBadgeClass}">
                                <i data-feather="${job.statusIcon}" class="w-3 h-3 mr-1"></i>
                                ${job.status}
                            </span>
                        </div>
                        <p class="text-sm text-gray-600 mt-1">${job.storeName || 'Bilinmeyen Mağaza'}</p>
                        <div class="flex items-center mt-2 space-x-4 text-sm text-gray-500">
                            <span>İş ID: ${job.jobId}</span>
                            ${job.totalProducts ? `<span>${job.totalProducts} ürün</span>` : ''}
                            ${job.totalComments ? `<span>${job.totalComments} yorum</span>` : ''}
                            <span>${new Date(job.createdAt).toLocaleDateString('tr-TR')} ${new Date(job.createdAt).toLocaleTimeString('tr-TR')}</span>
                        </div>
                        ${job.progressPercent ? `
                            <div class="mt-3">
                                <div class="flex items-center justify-between text-sm">
                                    <span class="text-gray-600">${job.currentStep || 'İşleniyor...'}</span>
                                    <span class="text-gray-600">${job.progressPercent}%</span>
                                </div>
                                <div class="mt-1 bg-gray-200 rounded-full h-2">
                                    <div class="bg-primary h-2 rounded-full transition-all duration-300" style="width: ${job.progressPercent}%"></div>
                                </div>
                            </div>
                        ` : ''}
                        ${job.errorMessage ? `<p class="text-sm text-red-600 mt-2">${job.errorMessage}</p>` : ''}
                    </div>
                    <div class="flex items-center space-x-2 ml-4">
                        ${job.resultFileUrl ? `
                            <a href="${job.resultFileUrl}" target="_blank"
                               class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                                <i data-feather="download" class="w-4 h-4 mr-1"></i>
                                Sonuçları İndir
                            </a>
                        ` : ''}
                        ${job.status === 'Running' || job.status === 'Pending' ? `
                            <button onclick="cancelTransferJob(${job.id})"
                                    class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                                <i data-feather="x" class="w-4 h-4 mr-1"></i>
                                İptal Et
                            </button>
                        ` : ''}
                    </div>
                </div>
            </div>
        `;
        container.append(jobCard);
    });

    // Re-initialize Feather icons
    if (typeof feather !== 'undefined') {
        feather.replace();
    }
}

function cancelTransferJob(jobId) {
    if (!confirm('Bu transfer işini iptal etmek istediğinizden emin misiniz?')) {
        return;
    }

    $.ajax({
        url: '@Url.Action("CancelTransferJob")',
        type: 'POST',
        data: JSON.stringify({ jobId: jobId }),
        contentType: 'application/json',
        headers: {
            'RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()
        },
        success: function(response) {
            if (response.success) {
                showNotification('Transfer işi iptal edildi.', 'success');
                loadTransferJobs();
            } else {
                showNotification(response.message, 'error');
            }
        },
        error: function() {
            showNotification('İptal işlemi sırasında hata oluştu.', 'error');
        }
    });
}

// Modal handlers
function setupModalHandlers() {
    // Store Products Modal
    $('#closeProductsModal').on('click', function() {
        $('#storeProductsModal').addClass('hidden');
    });

    // Close modal when clicking outside
    $('#storeProductsModal').on('click', function(e) {
        if (e.target === this) {
            $(this).addClass('hidden');
        }
    });

    // Transfer Modal
    $('#closeTransferModal, #cancelTransfer').on('click', function() {
        $('#transferModal').addClass('hidden');
    });

    // Close transfer modal when clicking outside
    $('#transferModal').on('click', function(e) {
        if (e.target === this) {
            $(this).addClass('hidden');
        }
    });

    // Confirm transfer
    $('#confirmTransfer').on('click', function() {
        const commentCount = parseInt($('#commentCount').val());

        if (commentCount < 10 || commentCount > 1000) {
            showNotification('Yorum sayısı 10 ile 1000 arasında olmalıdır.', 'warning');
            return;
        }

        transferSelectedProducts(commentCount);
    });

    // Validate comment count input
    $('#commentCount').on('input', function() {
        const value = parseInt($(this).val());
        if (value < 10) {
            $(this).val(10);
        } else if (value > 1000) {
            $(this).val(1000);
        }
    });
}

// Notification system
function showNotification(message, type = 'info') {
    const bgColor = {
        'success': 'bg-green-500',
        'error': 'bg-red-500',
        'warning': 'bg-yellow-500',
        'info': 'bg-blue-500'
    }[type] || 'bg-blue-500';

    const notification = $(`
        <div class="notification ${bgColor} text-white px-6 py-4 rounded-lg shadow-lg mb-4 transform transition-all duration-300 translate-x-full">
            <div class="flex items-center justify-between">
                <span>${message}</span>
                <button class="ml-4 text-white hover:text-gray-200" onclick="$(this).closest('.notification').remove()">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
        </div>
    `);

    $('#notificationContainer').append(notification);

    // Animate in
    setTimeout(() => {
        notification.removeClass('translate-x-full');
    }, 100);

    // Auto remove after 5 seconds
    setTimeout(() => {
        notification.addClass('translate-x-full');
        setTimeout(() => {
            notification.remove();
        }, 300);
    }, 5000);
}
</script>

<!-- Feather Icons -->
<script src="https://unpkg.com/feather-icons"></script>
<script>
    // Initialize Feather icons when page loads
    document.addEventListener('DOMContentLoaded', function() {
        if (typeof feather !== 'undefined') {
            feather.replace();
        }
    });
</script>
}
