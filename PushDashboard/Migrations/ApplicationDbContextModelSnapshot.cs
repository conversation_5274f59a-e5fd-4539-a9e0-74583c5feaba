// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;
using PushDashboard.Data;

#nullable disable

namespace PushDashboard.Migrations
{
    [DbContext(typeof(ApplicationDbContext))]
    partial class ApplicationDbContextModelSnapshot : ModelSnapshot
    {
        protected override void BuildModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "8.0.8")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityRole", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasColumnType("text");

                    b.Property<string>("Name")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<string>("NormalizedName")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.HasKey("Id");

                    b.HasIndex("NormalizedName")
                        .IsUnique()
                        .HasDatabaseName("RoleNameIndex");

                    b.ToTable("AspNetRoles", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityRoleClaim<string>", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("ClaimType")
                        .HasColumnType("text");

                    b.Property<string>("ClaimValue")
                        .HasColumnType("text");

                    b.Property<string>("RoleId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("RoleId");

                    b.ToTable("AspNetRoleClaims", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserClaim<string>", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("ClaimType")
                        .HasColumnType("text");

                    b.Property<string>("ClaimValue")
                        .HasColumnType("text");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("AspNetUserClaims", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserLogin<string>", b =>
                {
                    b.Property<string>("LoginProvider")
                        .HasColumnType("text");

                    b.Property<string>("ProviderKey")
                        .HasColumnType("text");

                    b.Property<string>("ProviderDisplayName")
                        .HasColumnType("text");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("LoginProvider", "ProviderKey");

                    b.HasIndex("UserId");

                    b.ToTable("AspNetUserLogins", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserRole<string>", b =>
                {
                    b.Property<string>("UserId")
                        .HasColumnType("text");

                    b.Property<string>("RoleId")
                        .HasColumnType("text");

                    b.HasKey("UserId", "RoleId");

                    b.HasIndex("RoleId");

                    b.ToTable("AspNetUserRoles", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserToken<string>", b =>
                {
                    b.Property<string>("UserId")
                        .HasColumnType("text");

                    b.Property<string>("LoginProvider")
                        .HasColumnType("text");

                    b.Property<string>("Name")
                        .HasColumnType("text");

                    b.Property<string>("Value")
                        .HasColumnType("text");

                    b.HasKey("UserId", "LoginProvider", "Name");

                    b.ToTable("AspNetUserTokens", (string)null);
                });

            modelBuilder.Entity("PushDashboard.Models.ApplicationUser", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<int>("AccessFailedCount")
                        .HasColumnType("integer");

                    b.Property<Guid?>("CompanyId")
                        .HasColumnType("uuid");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<decimal>("CreditBalance")
                        .HasColumnType("decimal(10,2)");

                    b.Property<string>("Email")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<bool>("EmailConfirmed")
                        .HasColumnType("boolean");

                    b.Property<string>("FirstName")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime?>("LastLoginAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastName")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<bool>("LockoutEnabled")
                        .HasColumnType("boolean");

                    b.Property<DateTimeOffset?>("LockoutEnd")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("NormalizedEmail")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<string>("NormalizedUserName")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<string>("PasswordHash")
                        .HasColumnType("text");

                    b.Property<string>("PhoneNumber")
                        .HasColumnType("text");

                    b.Property<bool>("PhoneNumberConfirmed")
                        .HasColumnType("boolean");

                    b.Property<string>("SecurityStamp")
                        .HasColumnType("text");

                    b.Property<bool>("TwoFactorEnabled")
                        .HasColumnType("boolean");

                    b.Property<string>("TwoFactorSecretKey")
                        .HasColumnType("text");

                    b.Property<string>("UserName")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.HasKey("Id");

                    b.HasIndex("CompanyId");

                    b.HasIndex("NormalizedEmail")
                        .HasDatabaseName("EmailIndex");

                    b.HasIndex("NormalizedUserName")
                        .IsUnique()
                        .HasDatabaseName("UserNameIndex");

                    b.ToTable("AspNetUsers", (string)null);
                });

            modelBuilder.Entity("PushDashboard.Models.Basket", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("BasketDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid>("CompanyId")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Currency")
                        .IsRequired()
                        .HasMaxLength(10)
                        .HasColumnType("character varying(10)");

                    b.Property<string>("CustomerEmail")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<int>("CustomerId")
                        .HasColumnType("integer");

                    b.Property<string>("CustomerName")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<string>("ExternalId")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("GuidBasketId")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<bool>("IsAbandoned")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastSyncDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("LastUpdateDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("ProductCount")
                        .HasColumnType("integer");

                    b.Property<decimal>("ShippingCost")
                        .HasColumnType("decimal(10,2)");

                    b.Property<decimal>("TotalAmount")
                        .HasColumnType("decimal(10,2)");

                    b.Property<decimal>("TotalTax")
                        .HasColumnType("decimal(10,2)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.HasIndex("BasketDate")
                        .HasDatabaseName("IX_Baskets_BasketDate_Performance");

                    b.HasIndex("CompanyId")
                        .HasDatabaseName("IX_Baskets_CompanyId_Performance");

                    b.HasIndex("ExternalId", "CompanyId")
                        .HasDatabaseName("IX_Baskets_ExternalId_CompanyId_Performance");

                    b.ToTable("Baskets");
                });

            modelBuilder.Entity("PushDashboard.Models.BasketItem", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid>("BasketId")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Currency")
                        .IsRequired()
                        .HasMaxLength(10)
                        .HasColumnType("character varying(10)");

                    b.Property<int>("ExternalId")
                        .HasColumnType("integer");

                    b.Property<bool>("FreeShipping")
                        .HasColumnType("boolean");

                    b.Property<string>("GuidBasketItemId")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("ProductCode")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("ProductImage")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("ProductName")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<double>("Quantity")
                        .HasColumnType("double precision");

                    b.Property<decimal>("ShippingCost")
                        .HasColumnType("decimal(10,2)");

                    b.Property<decimal>("TaxAmount")
                        .HasColumnType("decimal(10,2)");

                    b.Property<double>("TaxRate")
                        .HasColumnType("double precision");

                    b.Property<decimal>("TotalPrice")
                        .HasColumnType("decimal(10,2)");

                    b.Property<decimal>("UnitPrice")
                        .HasColumnType("decimal(10,2)");

                    b.HasKey("Id");

                    b.HasIndex("BasketId")
                        .HasDatabaseName("IX_BasketItems_BasketId_Performance");

                    b.ToTable("BasketItems");
                });

            modelBuilder.Entity("PushDashboard.Models.BasketReminderLog", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("BasketExternalId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<Guid>("BasketId")
                        .HasColumnType("uuid");

                    b.Property<int?>("BasketReminderScheduleId")
                        .HasColumnType("integer");

                    b.Property<Guid>("CompanyId")
                        .HasColumnType("uuid");

                    b.Property<string>("CustomerEmail")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("CustomerId")
                        .HasColumnType("integer");

                    b.Property<string>("CustomerName")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("ErrorMessage")
                        .HasColumnType("text");

                    b.Property<bool>("IsSuccessful")
                        .HasColumnType("boolean");

                    b.Property<string>("NotificationContent")
                        .HasColumnType("text");

                    b.Property<int>("ReminderTimeForHours")
                        .HasColumnType("integer");

                    b.Property<DateTime>("SentAt")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.HasIndex("BasketId");

                    b.HasIndex("BasketReminderScheduleId");

                    b.HasIndex("CompanyId");

                    b.ToTable("BasketReminderLogs");
                });

            modelBuilder.Entity("PushDashboard.Models.BasketReminderSchedule", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("ChannelMessages")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)")
                        .HasDefaultValue("{}");

                    b.Property<string>("CommunicationChannels")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasDefaultValue("[]");

                    b.Property<Guid>("CompanyId")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedByUserId")
                        .IsRequired()
                        .HasMaxLength(450)
                        .HasColumnType("character varying(450)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("NotificationContent")
                        .IsRequired()
                        .HasMaxLength(300)
                        .HasColumnType("character varying(300)");

                    b.Property<int>("ReminderTimeForHours")
                        .HasColumnType("integer");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.HasIndex("CompanyId")
                        .HasDatabaseName("IX_BasketReminderSchedules_CompanyId");

                    b.HasIndex("CreatedByUserId");

                    b.HasIndex("CompanyId", "IsActive")
                        .HasDatabaseName("IX_BasketReminderSchedules_CompanyId_IsActive");

                    b.ToTable("BasketReminderSchedules");
                });

            modelBuilder.Entity("PushDashboard.Models.BasketReminderSettings", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<Guid>("CompanyId")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("IsEnabled")
                        .HasColumnType("boolean");

                    b.Property<int>("MaxNotificationsPerCustomer")
                        .HasColumnType("integer");

                    b.Property<string>("NotificationContent")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedByUserId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("CompanyId");

                    b.HasIndex("UpdatedByUserId");

                    b.ToTable("BasketReminderSettings");
                });

            modelBuilder.Entity("PushDashboard.Models.BulkMessage", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<decimal>("ActualCost")
                        .HasColumnType("decimal(10,2)");

                    b.Property<string>("ChannelSettingsJson")
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)");

                    b.Property<Guid>("CompanyId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("CompletedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("CurrentBatch")
                        .HasColumnType("integer");

                    b.Property<string>("CustomerFiltersJson")
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)");

                    b.Property<string>("Description")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<string>("ErrorMessage")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<decimal>("EstimatedCost")
                        .HasColumnType("decimal(10,2)");

                    b.Property<int>("FailedSends")
                        .HasColumnType("integer");

                    b.Property<int>("ProcessedRecipients")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("StartedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<int>("SuccessfulSends")
                        .HasColumnType("integer");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<int>("TotalBatches")
                        .HasColumnType("integer");

                    b.Property<int>("TotalRecipients")
                        .HasColumnType("integer");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasMaxLength(450)
                        .HasColumnType("character varying(450)");

                    b.HasKey("Id");

                    b.HasIndex("CompanyId")
                        .HasDatabaseName("IX_BulkMessages_CompanyId_Performance");

                    b.HasIndex("CreatedAt")
                        .HasDatabaseName("IX_BulkMessages_CreatedAt_Performance");

                    b.HasIndex("Status")
                        .HasDatabaseName("IX_BulkMessages_Status_Performance");

                    b.HasIndex("UserId");

                    b.HasIndex("CompanyId", "Status", "CreatedAt")
                        .HasDatabaseName("IX_BulkMessages_Company_Status_Date_Performance");

                    b.ToTable("BulkMessages");
                });

            modelBuilder.Entity("PushDashboard.Models.BulkMessageRecipient", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int>("BulkMessageId")
                        .HasColumnType("integer");

                    b.Property<decimal>("Cost")
                        .HasColumnType("decimal(10,2)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("CustomerId")
                        .HasColumnType("integer");

                    b.Property<string>("ErrorMessage")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<DateTime?>("ProcessedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("SentChannels")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.HasKey("Id");

                    b.HasIndex("BulkMessageId")
                        .HasDatabaseName("IX_BulkMessageRecipients_BulkMessageId_Performance");

                    b.HasIndex("CustomerId");

                    b.HasIndex("Status")
                        .HasDatabaseName("IX_BulkMessageRecipients_Status_Performance");

                    b.HasIndex("BulkMessageId", "Status")
                        .HasDatabaseName("IX_BulkMessageRecipients_BulkMessage_Status_Performance");

                    b.ToTable("BulkMessageRecipients");
                });

            modelBuilder.Entity("PushDashboard.Models.CommentRequest", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int?>("ActualCommentsCount")
                        .HasColumnType("integer");

                    b.Property<string>("CommentsFileUrl")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<Guid>("CompanyId")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ErrorMessage")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("ExportToken")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("ExternalProductId")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("ExternalProductUrl")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("ExternalRequestId")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<bool>("IsProcessing")
                        .HasColumnType("boolean");

                    b.Property<string>("LogsFileUrl")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<DateTime?>("ProcessedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ProductUrl")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<int>("RequestedCommentsCount")
                        .HasColumnType("integer");

                    b.Property<string>("ReviewSource")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("ScreenshotUrl")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("WebhookUrl")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.HasKey("Id");

                    b.HasIndex("CompanyId")
                        .HasDatabaseName("IX_CommentRequests_CompanyId_Performance");

                    b.HasIndex("CreatedAt")
                        .HasDatabaseName("IX_CommentRequests_CreatedAt_Performance");

                    b.HasIndex("Status")
                        .HasDatabaseName("IX_CommentRequests_Status_Performance");

                    b.ToTable("CommentRequests");
                });

            modelBuilder.Entity("PushDashboard.Models.CommentTransferJob", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<Guid>("CompanyId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("CompletedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CurrentStep")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<string>("ErrorMessage")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("ExternalBatchId")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("JobId")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("JobType")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("LogsFileUrl")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<int?>("ProcessedProducts")
                        .HasColumnType("integer");

                    b.Property<int?>("ProgressPercent")
                        .HasColumnType("integer");

                    b.Property<string>("ResultFileUrl")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<DateTime?>("StartedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<int?>("StoreId")
                        .HasColumnType("integer");

                    b.Property<int?>("TotalComments")
                        .HasColumnType("integer");

                    b.Property<int?>("TotalProducts")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("CompanyId")
                        .HasDatabaseName("IX_CommentTransferJobs_CompanyId_Performance");

                    b.HasIndex("CreatedAt")
                        .HasDatabaseName("IX_CommentTransferJobs_CreatedAt_Performance");

                    b.HasIndex("JobId")
                        .IsUnique()
                        .HasDatabaseName("IX_CommentTransferJobs_JobId_Unique");

                    b.HasIndex("Status")
                        .HasDatabaseName("IX_CommentTransferJobs_Status_Performance");

                    b.HasIndex("StoreId")
                        .HasDatabaseName("IX_CommentTransferJobs_StoreId_Performance");

                    b.ToTable("CommentTransferJobs");
                });

            modelBuilder.Entity("PushDashboard.Models.CommentTransferJobProduct", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int?>("CommentsCount")
                        .HasColumnType("integer");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ErrorMessage")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<int>("JobId")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("ProcessedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("ProductId")
                        .HasColumnType("integer");

                    b.Property<string>("ProductUrl")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.HasKey("Id");

                    b.HasIndex("JobId")
                        .HasDatabaseName("IX_CommentTransferJobProducts_JobId_Performance");

                    b.HasIndex("ProductId")
                        .HasDatabaseName("IX_CommentTransferJobProducts_ProductId_Performance");

                    b.HasIndex("Status")
                        .HasDatabaseName("IX_CommentTransferJobProducts_Status_Performance");

                    b.HasIndex("JobId", "ProductId")
                        .IsUnique()
                        .HasDatabaseName("IX_CommentTransferJobProducts_JobId_ProductId_Unique");

                    b.ToTable("CommentTransferJobProducts");
                });

            modelBuilder.Entity("PushDashboard.Models.Company", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Address")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<string>("BillingAddress")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("BillingType")
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<DateTime?>("BillingUpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CompanyName")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<decimal>("CreditBalance")
                        .HasColumnType("decimal(10,2)");

                    b.Property<string>("Email")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("FullName")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("IdentityNumber")
                        .HasMaxLength(11)
                        .HasColumnType("character varying(11)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("Phone")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("TaxNumber")
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<string>("TaxOffice")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Website")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.HasKey("Id");

                    b.ToTable("Companies");
                });

            modelBuilder.Entity("PushDashboard.Models.CompanyEmailTemplate", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<Guid>("CompanyId")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CustomContent")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("CustomSubject")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<int>("EmailTemplateId")
                        .HasColumnType("integer");

                    b.Property<bool>("IsEnabled")
                        .HasColumnType("boolean");

                    b.Property<string>("LastModifiedBy")
                        .HasMaxLength(450)
                        .HasColumnType("character varying(450)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.HasIndex("EmailTemplateId");

                    b.HasIndex("LastModifiedBy");

                    b.HasIndex("CompanyId", "EmailTemplateId")
                        .IsUnique();

                    b.ToTable("CompanyEmailTemplates");
                });

            modelBuilder.Entity("PushDashboard.Models.CompanyIntegration", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<Guid>("CompanyId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("ConfiguredAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("IntegrationId")
                        .HasColumnType("integer");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsConfigured")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastSyncAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("SettingsJson")
                        .HasMaxLength(4000)
                        .HasColumnType("character varying(4000)");

                    b.Property<string>("SyncStatsJson")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.HasKey("Id");

                    b.HasIndex("IntegrationId");

                    b.HasIndex("CompanyId", "IntegrationId")
                        .IsUnique();

                    b.ToTable("CompanyIntegrations");
                });

            modelBuilder.Entity("PushDashboard.Models.CompanyModule", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<Guid>("CompanyId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("ExpiresAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastUsedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("ModuleId")
                        .HasColumnType("integer");

                    b.Property<decimal>("PaidAmount")
                        .HasColumnType("decimal(10,2)");

                    b.Property<DateTime>("PurchasedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("PurchasedByUserId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("TransactionId")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.HasIndex("ModuleId");

                    b.HasIndex("PurchasedByUserId");

                    b.HasIndex("CompanyId", "ModuleId")
                        .IsUnique();

                    b.ToTable("CompanyModules");
                });

            modelBuilder.Entity("PushDashboard.Models.CompanyModuleSettings", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int>("CompanyModuleId")
                        .HasColumnType("integer");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("SettingsJson")
                        .HasMaxLength(4000)
                        .HasColumnType("character varying(4000)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedByUserId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("CompanyModuleId")
                        .IsUnique();

                    b.HasIndex("UpdatedByUserId");

                    b.ToTable("CompanyModuleSettings");
                });

            modelBuilder.Entity("PushDashboard.Models.CookieManagement", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("AcceptButtonColor")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("AcceptButtonText")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("BannerBackgroundColor")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("BannerDescription")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("BannerPosition")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("BannerTextColor")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("BannerTitle")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("BorderRadius")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("CategoriesJson")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<Guid>("CompanyId")
                        .HasColumnType("uuid");

                    b.Property<int>("CookieExpiryDays")
                        .HasColumnType("integer");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("EnableAnimation")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<string>("RejectButtonColor")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("RejectButtonText")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("SaveButtonText")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("SettingsButtonColor")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("SettingsButtonText")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<bool>("ShowSettingsButton")
                        .HasColumnType("boolean");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedByUserId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("CompanyId");

                    b.HasIndex("UpdatedByUserId");

                    b.ToTable("CookieManagements");
                });

            modelBuilder.Entity("PushDashboard.Models.Customer", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime?>("BirthDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("CashOnDeliveryBlocked")
                        .HasColumnType("boolean");

                    b.Property<string>("City")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<Guid>("CompanyId")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<decimal>("CreditLimit")
                        .HasColumnType("decimal(10,2)");

                    b.Property<string>("CustomerCode")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("District")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("EducationLevel")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<bool>("EmailPermission")
                        .HasColumnType("boolean");

                    b.Property<int>("ExternalId")
                        .HasColumnType("integer");

                    b.Property<string>("FirstName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<int?>("GenderId")
                        .HasColumnType("integer");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<bool>("KvkkApproval")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastLoginDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastLoginIp")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("LastName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime?>("LastSyncDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime>("LastUpdateDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("MembershipAgreementApproval")
                        .HasColumnType("boolean");

                    b.Property<DateTime>("MembershipDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("MembershipType")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("MobilePhone")
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<string>("Phone")
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<int>("PointBalance")
                        .HasColumnType("integer");

                    b.Property<string>("Profession")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<bool>("SmsPermission")
                        .HasColumnType("boolean");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.HasIndex("CompanyId");

                    b.HasIndex("ExternalId", "CompanyId")
                        .IsUnique();

                    b.ToTable("Customers");
                });

            modelBuilder.Entity("PushDashboard.Models.CustomerImportJob", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<Guid>("CompanyId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("CompletedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedByUserId")
                        .IsRequired()
                        .HasMaxLength(450)
                        .HasColumnType("character varying(450)");

                    b.Property<int>("ErrorCount")
                        .HasColumnType("integer");

                    b.Property<string>("ErrorDetails")
                        .HasColumnType("text");

                    b.Property<string>("FileName")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("FilePath")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<int>("ProcessedRows")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("StartedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<int>("SuccessCount")
                        .HasColumnType("integer");

                    b.Property<int>("TotalRows")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("CompanyId")
                        .HasDatabaseName("IX_CustomerImportJobs_CompanyId_Performance");

                    b.HasIndex("CreatedAt")
                        .HasDatabaseName("IX_CustomerImportJobs_CreatedAt_Performance");

                    b.HasIndex("CreatedByUserId");

                    b.HasIndex("Status")
                        .HasDatabaseName("IX_CustomerImportJobs_Status_Performance");

                    b.ToTable("CustomerImportJobs");
                });

            modelBuilder.Entity("PushDashboard.Models.EmailTemplate", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("Category")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("DefaultContent")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("DefaultSubject")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<int?>("ModuleId")
                        .HasColumnType("integer");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<int>("SortOrder")
                        .HasColumnType("integer");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Variables")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.HasKey("Id");

                    b.ToTable("EmailTemplates");
                });

            modelBuilder.Entity("PushDashboard.Models.ExitSurvey", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("BackgroundColor")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<string>("BorderRadius")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<string>("CancelButtonColor")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<string>("CancelButtonText")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<Guid>("CompanyId")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("DelayBeforeShow")
                        .HasColumnType("integer");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<bool>("EnableAnimation")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<int>("ShowFrequencyDays")
                        .HasColumnType("integer");

                    b.Property<bool>("ShowOnDesktop")
                        .HasColumnType("boolean");

                    b.Property<bool>("ShowOnMobile")
                        .HasColumnType("boolean");

                    b.Property<bool>("ShowOnPageExit")
                        .HasColumnType("boolean");

                    b.Property<bool>("ShowOnTabClose")
                        .HasColumnType("boolean");

                    b.Property<string>("SubmitButtonColor")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<string>("SubmitButtonText")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("TextColor")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<string>("ThankYouMessage")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedByUserId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("CompanyId");

                    b.HasIndex("IsActive");

                    b.HasIndex("UpdatedByUserId");

                    b.ToTable("ExitSurveys", (string)null);
                });

            modelBuilder.Entity("PushDashboard.Models.ExitSurveyQuestion", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("ExitSurveyId")
                        .HasColumnType("integer");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsRequired")
                        .HasColumnType("boolean");

                    b.Property<string>("OptionsJson")
                        .HasColumnType("text");

                    b.Property<string>("QuestionText")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<int>("QuestionType")
                        .HasColumnType("integer");

                    b.Property<int>("SortOrder")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("ExitSurveyId");

                    b.HasIndex("IsActive");

                    b.HasIndex("SortOrder");

                    b.ToTable("ExitSurveyQuestions", (string)null);
                });

            modelBuilder.Entity("PushDashboard.Models.ExitSurveyResponse", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("ExitSurveyId")
                        .HasColumnType("integer");

                    b.Property<string>("IpAddress")
                        .HasMaxLength(45)
                        .HasColumnType("character varying(45)");

                    b.Property<int>("QuestionId")
                        .HasColumnType("integer");

                    b.Property<int?>("RatingValue")
                        .HasColumnType("integer");

                    b.Property<string>("ResponseText")
                        .IsRequired()
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)");

                    b.Property<string>("SessionId")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("UserAgent")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.HasKey("Id");

                    b.HasIndex("CreatedAt");

                    b.HasIndex("ExitSurveyId");

                    b.HasIndex("IpAddress");

                    b.HasIndex("QuestionId");

                    b.HasIndex("SessionId");

                    b.ToTable("ExitSurveyResponses", (string)null);
                });

            modelBuilder.Entity("PushDashboard.Models.GiftWheel", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<Guid>("CompanyId")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedByUserId")
                        .IsRequired()
                        .HasMaxLength(450)
                        .HasColumnType("character varying(450)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedByUserId")
                        .HasMaxLength(450)
                        .HasColumnType("character varying(450)");

                    b.HasKey("Id");

                    b.HasIndex("CompanyId");

                    b.ToTable("GiftWheels");
                });

            modelBuilder.Entity("PushDashboard.Models.GiftWheelPrize", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("Color")
                        .IsRequired()
                        .HasMaxLength(7)
                        .HasColumnType("character varying(7)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<decimal?>("DiscountAmount")
                        .HasColumnType("decimal(10,2)");

                    b.Property<int?>("DiscountType")
                        .HasColumnType("integer");

                    b.Property<int>("GiftWheelId")
                        .HasColumnType("integer");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("PrizeType")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<int>("Probability")
                        .HasColumnType("integer");

                    b.Property<int>("SortOrder")
                        .HasColumnType("integer");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int?>("ValidityDays")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("GiftWheelId");

                    b.ToTable("GiftWheelPrizes");
                });

            modelBuilder.Entity("PushDashboard.Models.GiftWheelSettings", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("ButtonText")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("GiftWheelId")
                        .HasColumnType("integer");

                    b.Property<string>("LoseMessage")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<int>("MaxSpinsPerDay")
                        .HasColumnType("integer");

                    b.Property<string>("NotificationTemplate")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("PrimaryColor")
                        .IsRequired()
                        .HasMaxLength(7)
                        .HasColumnType("character varying(7)");

                    b.Property<bool>("RequireEmail")
                        .HasColumnType("boolean");

                    b.Property<bool>("RequirePhone")
                        .HasColumnType("boolean");

                    b.Property<string>("SecondaryColor")
                        .IsRequired()
                        .HasMaxLength(7)
                        .HasColumnType("character varying(7)");

                    b.Property<bool>("ShowConfetti")
                        .HasColumnType("boolean");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedByUserId")
                        .IsRequired()
                        .HasMaxLength(450)
                        .HasColumnType("character varying(450)");

                    b.Property<int>("WheelSize")
                        .HasColumnType("integer");

                    b.Property<string>("WheelSubtitle")
                        .IsRequired()
                        .HasMaxLength(300)
                        .HasColumnType("character varying(300)");

                    b.Property<string>("WheelTitle")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<string>("WinMessage")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.HasKey("Id");

                    b.HasIndex("GiftWheelId")
                        .IsUnique();

                    b.ToTable("GiftWheelSettings");
                });

            modelBuilder.Entity("PushDashboard.Models.GiftWheelSpin", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<decimal?>("Cost")
                        .HasColumnType("decimal(10,2)");

                    b.Property<string>("CustomerEmail")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("CustomerName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("CustomerPhone")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<string>("ErrorMessage")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<int>("GiftWheelId")
                        .HasColumnType("integer");

                    b.Property<string>("IpAddress")
                        .IsRequired()
                        .HasMaxLength(45)
                        .HasColumnType("character varying(45)");

                    b.Property<bool>("NotificationSent")
                        .HasColumnType("boolean");

                    b.Property<int>("PrizeId")
                        .HasColumnType("integer");

                    b.Property<DateTime>("SpinDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("VoucherCode")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<bool>("VoucherCreated")
                        .HasColumnType("boolean");

                    b.HasKey("Id");

                    b.HasIndex("GiftWheelId");

                    b.HasIndex("PrizeId");

                    b.ToTable("GiftWheelSpins");
                });

            modelBuilder.Entity("PushDashboard.Models.Integration", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("BackgroundColor")
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<string>("Category")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("DefaultSettingsTemplate")
                        .HasMaxLength(4000)
                        .HasColumnType("character varying(4000)");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("DetailedDescription")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<string>("Features")
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)");

                    b.Property<string>("IconClass")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("IconColor")
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsPopular")
                        .HasColumnType("boolean");

                    b.Property<string>("LogoUrl")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<int>("SortOrder")
                        .HasColumnType("integer");

                    b.Property<string>("Type")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.ToTable("Integrations");
                });

            modelBuilder.Entity("PushDashboard.Models.Module", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("BackgroundColor")
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<int>("CategoryId")
                        .HasColumnType("integer");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("DefaultSettings")
                        .HasColumnType("text");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("DetailedDescription")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<string>("Features")
                        .HasColumnType("text");

                    b.Property<string>("IconClass")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("IconColor")
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsFeatured")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsNew")
                        .HasColumnType("boolean");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<decimal>("Price")
                        .HasColumnType("decimal(10,2)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.HasIndex("CategoryId");

                    b.ToTable("Modules");
                });

            modelBuilder.Entity("PushDashboard.Models.ModuleCategory", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Description")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<string>("IconClass")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<int>("SortOrder")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.ToTable("ModuleCategories");
                });

            modelBuilder.Entity("PushDashboard.Models.ModuleUsageLog", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<decimal>("BalanceAfter")
                        .HasColumnType("decimal(10,2)");

                    b.Property<decimal>("BalanceBefore")
                        .HasColumnType("decimal(10,2)");

                    b.Property<string>("Channel")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<Guid?>("CompanyId")
                        .IsRequired()
                        .HasColumnType("uuid");

                    b.Property<decimal>("Cost")
                        .HasColumnType("decimal(10,2)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<string>("ErrorMessage")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<bool>("IsSuccessful")
                        .HasColumnType("boolean");

                    b.Property<string>("Metadata")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<int>("ModuleId")
                        .HasColumnType("integer");

                    b.Property<string>("ReferenceId")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("UsageType")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasMaxLength(450)
                        .HasColumnType("character varying(450)");

                    b.HasKey("Id");

                    b.HasIndex("CompanyId")
                        .HasDatabaseName("IX_ModuleUsageLogs_CompanyId_Performance");

                    b.HasIndex("CreatedAt")
                        .HasDatabaseName("IX_ModuleUsageLogs_CreatedAt_Performance");

                    b.HasIndex("ModuleId");

                    b.HasIndex("UserId");

                    b.HasIndex("CompanyId", "ModuleId", "CreatedAt")
                        .HasDatabaseName("IX_ModuleUsageLogs_Company_Module_Date_Performance");

                    b.ToTable("ModuleUsageLogs");
                });

            modelBuilder.Entity("PushDashboard.Models.NotificationPreferences", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("EmailCreditNotifications")
                        .HasColumnType("boolean");

                    b.Property<bool>("EmailInvoiceNotifications")
                        .HasColumnType("boolean");

                    b.Property<bool>("EmailMarketingNotifications")
                        .HasColumnType("boolean");

                    b.Property<bool>("SmsPaymentNotifications")
                        .HasColumnType("boolean");

                    b.Property<bool>("SmsSecurityAlerts")
                        .HasColumnType("boolean");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasMaxLength(450)
                        .HasColumnType("character varying(450)");

                    b.HasKey("Id");

                    b.HasIndex("UserId")
                        .IsUnique();

                    b.ToTable("NotificationPreferences");
                });

            modelBuilder.Entity("PushDashboard.Models.OrderStatusChangeLog", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<Guid>("CompanyId")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CustomerEmail")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("CustomerName")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<string>("CustomerPhone")
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<string>("NewStatus")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("NewStatusDisplayName")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<string>("NotificationChannels")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("NotificationError")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<bool>("NotificationSent")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("NotificationSentAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("OldStatus")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<decimal?>("OrderAmount")
                        .HasColumnType("decimal(10,2)");

                    b.Property<string>("OrderCurrency")
                        .HasMaxLength(10)
                        .HasColumnType("character varying(10)");

                    b.Property<string>("OrderId")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("OrderNumber")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime>("StatusChangedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("WebhookPayload")
                        .HasMaxLength(4000)
                        .HasColumnType("character varying(4000)");

                    b.HasKey("Id");

                    b.HasIndex("CompanyId")
                        .HasDatabaseName("IX_OrderStatusChangeLogs_CompanyId_Performance");

                    b.HasIndex("OrderId")
                        .HasDatabaseName("IX_OrderStatusChangeLogs_OrderId_Performance");

                    b.HasIndex("StatusChangedAt")
                        .HasDatabaseName("IX_OrderStatusChangeLogs_StatusChangedAt_Performance");

                    b.HasIndex("CompanyId", "StatusChangedAt")
                        .HasDatabaseName("IX_OrderStatusChangeLogs_Company_Date_Performance");

                    b.HasIndex("CompanyId", "NewStatus", "StatusChangedAt")
                        .HasDatabaseName("IX_OrderStatusChangeLogs_Company_Status_Date_Performance");

                    b.ToTable("OrderStatusChangeLogs");
                });

            modelBuilder.Entity("PushDashboard.Models.OrderStatusMapping", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<Guid>("CompanyId")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ExternalStatus")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("ExternalStatusDisplayName")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<string>("IntegrationType")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("InternalStatus")
                        .IsRequired()
                        .HasMaxLength(10)
                        .HasColumnType("character varying(10)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.HasIndex("CompanyId", "IntegrationType", "ExternalStatus")
                        .IsUnique()
                        .HasDatabaseName("IX_OrderStatusMappings_Company_Integration_ExternalStatus");

                    b.HasIndex("CompanyId", "IntegrationType", "IsActive")
                        .HasDatabaseName("IX_OrderStatusMappings_Company_Integration_Active");

                    b.ToTable("OrderStatusMappings");
                });

            modelBuilder.Entity("PushDashboard.Models.OrderStatusNotification", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("AdditionalSettings")
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)");

                    b.Property<Guid>("CompanyId")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("DelayMinutes")
                        .HasColumnType("integer");

                    b.Property<bool>("EmailNotificationEnabled")
                        .HasColumnType("boolean");

                    b.Property<int?>("EmailTemplateId")
                        .HasColumnType("integer");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastNotificationAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("NotificationOrder")
                        .HasColumnType("integer");

                    b.Property<string>("OrderStatus")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("OrderStatusDisplayName")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<bool>("SmsNotificationEnabled")
                        .HasColumnType("boolean");

                    b.Property<int?>("SmsTemplateId")
                        .HasColumnType("integer");

                    b.Property<int>("TotalNotificationsSent")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("WhatsAppNotificationEnabled")
                        .HasColumnType("boolean");

                    b.Property<string>("WhatsAppTemplateId")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.HasKey("Id");

                    b.HasIndex("CompanyId")
                        .HasDatabaseName("IX_OrderStatusNotifications_CompanyId_Performance");

                    b.HasIndex("IsActive")
                        .HasDatabaseName("IX_OrderStatusNotifications_IsActive_Performance");

                    b.HasIndex("CompanyId", "OrderStatus")
                        .IsUnique();

                    b.ToTable("OrderStatusNotifications");
                });

            modelBuilder.Entity("PushDashboard.Models.ProductSlider", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<Guid>("CompanyId")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedByUserId")
                        .IsRequired()
                        .HasMaxLength(450)
                        .HasColumnType("character varying(450)");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("DisplayType")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.HasIndex("CompanyId");

                    b.HasIndex("CreatedByUserId");

                    b.ToTable("ProductSliders");
                });

            modelBuilder.Entity("PushDashboard.Models.ProductSliderItem", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Currency")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<string>("ProductDescription")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<string>("ProductImage")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<decimal?>("ProductPrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("ProductTitle")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("ProductUrl")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<int>("SliderId")
                        .HasColumnType("integer");

                    b.Property<int>("SortOrder")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.HasIndex("SliderId");

                    b.ToTable("ProductSliderItems");
                });

            modelBuilder.Entity("PushDashboard.Models.ProductSliderSettings", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("AnimationType")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<bool>("AutoPlay")
                        .HasColumnType("boolean");

                    b.Property<int>("AutoPlayInterval")
                        .HasColumnType("integer");

                    b.Property<string>("BackgroundColor")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CustomCSS")
                        .HasColumnType("text");

                    b.Property<bool>("EnableAnimations")
                        .HasColumnType("boolean");

                    b.Property<string>("ImageAspectRatio")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<int>("ItemsPerView")
                        .HasColumnType("integer");

                    b.Property<int>("ItemsPerViewMobile")
                        .HasColumnType("integer");

                    b.Property<int>("ItemsPerViewTablet")
                        .HasColumnType("integer");

                    b.Property<string>("PrimaryColor")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<string>("ResponsiveBreakpoints")
                        .HasColumnType("text");

                    b.Property<string>("SecondaryColor")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<bool>("ShowArrows")
                        .HasColumnType("boolean");

                    b.Property<bool>("ShowDots")
                        .HasColumnType("boolean");

                    b.Property<bool>("ShowProductDescription")
                        .HasColumnType("boolean");

                    b.Property<bool>("ShowProductImage")
                        .HasColumnType("boolean");

                    b.Property<bool>("ShowProductPrice")
                        .HasColumnType("boolean");

                    b.Property<int>("SliderId")
                        .HasColumnType("integer");

                    b.Property<string>("TextColor")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<string>("Theme")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<int>("TransitionDuration")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.HasIndex("SliderId")
                        .IsUnique();

                    b.ToTable("ProductSliderSettings");
                });

            modelBuilder.Entity("PushDashboard.Models.SocialProofSettings", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int>("BuyersMax")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(8);

                    b.Property<int>("BuyersMin")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(2);

                    b.Property<Guid>("CompanyId")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<int>("DisplayDuration")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(5);

                    b.Property<string>("DisplaySettingsJson")
                        .HasColumnType("text");

                    b.Property<int>("FollowersMax")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(20);

                    b.Property<int>("FollowersMin")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(5);

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(true);

                    b.Property<string>("TextTemplatesJson")
                        .HasColumnType("text");

                    b.Property<int>("UpdateInterval")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(60);

                    b.Property<DateTime>("UpdatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<string>("UpdatedByUserId")
                        .IsRequired()
                        .HasMaxLength(450)
                        .HasColumnType("character varying(450)");

                    b.Property<int>("ViewersMax")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(45);

                    b.Property<int>("ViewersMin")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(15);

                    b.HasKey("Id");

                    b.HasIndex("CompanyId")
                        .IsUnique()
                        .HasDatabaseName("IX_SocialProofSettings_CompanyId_Unique");

                    b.HasIndex("IsActive")
                        .HasDatabaseName("IX_SocialProofSettings_IsActive_Performance");

                    b.HasIndex("UpdatedAt")
                        .HasDatabaseName("IX_SocialProofSettings_UpdatedAt_Performance");

                    b.HasIndex("UpdatedByUserId");

                    b.ToTable("SocialProofSettings");
                });

            modelBuilder.Entity("PushDashboard.Models.SyncLog", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<Guid>("CompanyId")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ErrorMessage")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<bool>("IsSuccessful")
                        .HasColumnType("boolean");

                    b.Property<int>("NewRecordsAdded")
                        .HasColumnType("integer");

                    b.Property<string>("Notes")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<int>("RecordsUpdated")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("SyncEndTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime>("SyncStartTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("SyncType")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<int>("TotalRecordsProcessed")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("CompanyId");

                    b.ToTable("SyncLogs");
                });

            modelBuilder.Entity("PushDashboard.Models.TrendyolProduct", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Href")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<bool>("IsSelected")
                        .HasColumnType("boolean");

                    b.Property<string>("ProductId")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<int>("StoreId")
                        .HasColumnType("integer");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.HasKey("Id");

                    b.HasIndex("IsSelected")
                        .HasDatabaseName("IX_TrendyolProducts_IsSelected_Performance");

                    b.HasIndex("ProductId")
                        .HasDatabaseName("IX_TrendyolProducts_ProductId_Performance");

                    b.HasIndex("StoreId")
                        .HasDatabaseName("IX_TrendyolProducts_StoreId_Performance");

                    b.HasIndex("StoreId", "ProductId")
                        .IsUnique()
                        .HasDatabaseName("IX_TrendyolProducts_StoreId_ProductId_Unique");

                    b.ToTable("TrendyolProducts");
                });

            modelBuilder.Entity("PushDashboard.Models.TrendyolStore", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<Guid>("CompanyId")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ErrorMessage")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("ExternalStoreId")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime?>("LastSyncAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int?>("ProductCount")
                        .HasColumnType("integer");

                    b.Property<string>("StoreName")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<string>("StoreUrl")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("SyncStatus")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.HasIndex("CompanyId")
                        .HasDatabaseName("IX_TrendyolStores_CompanyId_Performance");

                    b.HasIndex("SyncStatus")
                        .HasDatabaseName("IX_TrendyolStores_SyncStatus_Performance");

                    b.HasIndex("CompanyId", "StoreUrl")
                        .IsUnique()
                        .HasDatabaseName("IX_TrendyolStores_CompanyId_StoreUrl_Unique");

                    b.ToTable("TrendyolStores");
                });

            modelBuilder.Entity("PushDashboard.Models.UserInvitation", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<Guid>("CompanyId")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(450)
                        .HasColumnType("character varying(450)");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<DateTime>("ExpirationDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("InvitationToken")
                        .IsRequired()
                        .HasMaxLength(128)
                        .HasColumnType("character varying(128)");

                    b.Property<bool>("IsUsed")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("UsedAt")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.HasIndex("CompanyId");

                    b.HasIndex("CreatedBy");

                    b.HasIndex("InvitationToken")
                        .IsUnique();

                    b.HasIndex("Email", "CompanyId")
                        .IsUnique()
                        .HasFilter("\"IsUsed\" = false");

                    b.ToTable("UserInvitations");
                });

            modelBuilder.Entity("PushDashboard.Models.UserSession", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("Browser")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("DeviceInfo")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<string>("DeviceType")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<DateTime?>("EndedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("IpAddress")
                        .HasMaxLength(45)
                        .HasColumnType("character varying(45)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsCurrent")
                        .HasColumnType("boolean");

                    b.Property<DateTime>("LastActivityAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Location")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<string>("OperatingSystem")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("SessionId")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasMaxLength(450)
                        .HasColumnType("character varying(450)");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("UserSessions");
                });

            modelBuilder.Entity("PushDashboard.Models.VideoHosting", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("AccessToken")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("AudioCodec")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<Guid>("CompanyId")
                        .HasColumnType("uuid");

                    b.Property<string>("ContentType")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<string>("Description")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<TimeSpan>("Duration")
                        .HasColumnType("interval");

                    b.Property<long>("FileSizeBytes")
                        .HasColumnType("bigint");

                    b.Property<double>("FrameRate")
                        .HasColumnType("double precision");

                    b.Property<int>("Height")
                        .HasColumnType("integer");

                    b.Property<bool>("IsPublic")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastViewedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("OriginalFileName")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("ProcessingError")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<string>("S3Key")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("S3Url")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<decimal>("StorageCostPerMonth")
                        .HasColumnType("decimal(10,2)");

                    b.Property<string>("ThumbnailS3Key")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("ThumbnailS3Url")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedByUserId")
                        .HasMaxLength(450)
                        .HasColumnType("character varying(450)");

                    b.Property<decimal>("UploadCost")
                        .HasColumnType("decimal(10,2)");

                    b.Property<string>("UploadedByUserId")
                        .IsRequired()
                        .HasMaxLength(450)
                        .HasColumnType("character varying(450)");

                    b.Property<string>("VideoCodec")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<int>("ViewCount")
                        .HasColumnType("integer");

                    b.Property<int>("Width")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("AccessToken");

                    b.HasIndex("CreatedAt");

                    b.HasIndex("S3Key")
                        .IsUnique();

                    b.HasIndex("UpdatedByUserId");

                    b.HasIndex("UploadedByUserId");

                    b.HasIndex("CompanyId", "Status");

                    b.ToTable("VideoHostings");
                });

            modelBuilder.Entity("PushDashboard.Models.VideoHostingSettings", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<bool>("AllowDownload")
                        .HasColumnType("boolean");

                    b.Property<bool>("AllowEmbedding")
                        .HasColumnType("boolean");

                    b.Property<string>("AllowedDomains")
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)");

                    b.Property<bool>("AutoPlay")
                        .HasColumnType("boolean");

                    b.Property<Guid>("CompanyId")
                        .HasColumnType("uuid");

                    b.Property<decimal>("CostPerMinute")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("decimal(10,2)")
                        .HasDefaultValue(0.5m);

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<string>("CustomPlayerCss")
                        .HasMaxLength(5000)
                        .HasColumnType("character varying(5000)");

                    b.Property<int>("MaxDurationMinutes")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(60);

                    b.Property<long>("MaxFileSizeBytes")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasDefaultValue(524288000L);

                    b.Property<int>("MaxVideosPerCompany")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(100);

                    b.Property<string>("PlayerTheme")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasDefaultValue("default");

                    b.Property<bool>("RequireAuthentication")
                        .HasColumnType("boolean");

                    b.Property<bool>("ShowControls")
                        .HasColumnType("boolean");

                    b.Property<decimal>("StorageCostPerGBPerMonth")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("decimal(10,2)")
                        .HasDefaultValue(0.1m);

                    b.Property<DateTime>("UpdatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<string>("UpdatedByUserId")
                        .IsRequired()
                        .HasMaxLength(450)
                        .HasColumnType("character varying(450)");

                    b.HasKey("Id");

                    b.HasIndex("CompanyId")
                        .IsUnique();

                    b.HasIndex("UpdatedByUserId");

                    b.ToTable("VideoHostingSettings");
                });

            modelBuilder.Entity("PushDashboard.Models.VideoHostingView", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<Guid>("CompanyId")
                        .HasColumnType("uuid");

                    b.Property<bool>("CompletedView")
                        .HasColumnType("boolean");

                    b.Property<string>("EmbedUrl")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<string>("ReferrerUrl")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<int>("VideoHostingId")
                        .HasColumnType("integer");

                    b.Property<DateTime>("ViewedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<string>("ViewerCity")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("ViewerCountry")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("ViewerIpAddress")
                        .HasMaxLength(45)
                        .HasColumnType("character varying(45)");

                    b.Property<string>("ViewerUserAgent")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<TimeSpan?>("WatchDuration")
                        .HasColumnType("interval");

                    b.HasKey("Id");

                    b.HasIndex("VideoHostingId");

                    b.HasIndex("ViewerIpAddress");

                    b.HasIndex("CompanyId", "ViewedAt");

                    b.ToTable("VideoHostingViews");
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityRoleClaim<string>", b =>
                {
                    b.HasOne("Microsoft.AspNetCore.Identity.IdentityRole", null)
                        .WithMany()
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserClaim<string>", b =>
                {
                    b.HasOne("PushDashboard.Models.ApplicationUser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserLogin<string>", b =>
                {
                    b.HasOne("PushDashboard.Models.ApplicationUser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserRole<string>", b =>
                {
                    b.HasOne("Microsoft.AspNetCore.Identity.IdentityRole", null)
                        .WithMany()
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("PushDashboard.Models.ApplicationUser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserToken<string>", b =>
                {
                    b.HasOne("PushDashboard.Models.ApplicationUser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("PushDashboard.Models.ApplicationUser", b =>
                {
                    b.HasOne("PushDashboard.Models.Company", "Company")
                        .WithMany("Users")
                        .HasForeignKey("CompanyId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.Navigation("Company");
                });

            modelBuilder.Entity("PushDashboard.Models.Basket", b =>
                {
                    b.HasOne("PushDashboard.Models.Company", "Company")
                        .WithMany()
                        .HasForeignKey("CompanyId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Company");
                });

            modelBuilder.Entity("PushDashboard.Models.BasketItem", b =>
                {
                    b.HasOne("PushDashboard.Models.Basket", "Basket")
                        .WithMany("BasketItems")
                        .HasForeignKey("BasketId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Basket");
                });

            modelBuilder.Entity("PushDashboard.Models.BasketReminderLog", b =>
                {
                    b.HasOne("PushDashboard.Models.Basket", "Basket")
                        .WithMany()
                        .HasForeignKey("BasketId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("PushDashboard.Models.BasketReminderSchedule", "BasketReminderSchedule")
                        .WithMany()
                        .HasForeignKey("BasketReminderScheduleId");

                    b.HasOne("PushDashboard.Models.Company", "Company")
                        .WithMany()
                        .HasForeignKey("CompanyId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Basket");

                    b.Navigation("BasketReminderSchedule");

                    b.Navigation("Company");
                });

            modelBuilder.Entity("PushDashboard.Models.BasketReminderSchedule", b =>
                {
                    b.HasOne("PushDashboard.Models.Company", "Company")
                        .WithMany()
                        .HasForeignKey("CompanyId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("PushDashboard.Models.ApplicationUser", "CreatedByUser")
                        .WithMany()
                        .HasForeignKey("CreatedByUserId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Company");

                    b.Navigation("CreatedByUser");
                });

            modelBuilder.Entity("PushDashboard.Models.BasketReminderSettings", b =>
                {
                    b.HasOne("PushDashboard.Models.Company", "Company")
                        .WithMany()
                        .HasForeignKey("CompanyId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("PushDashboard.Models.ApplicationUser", "UpdatedByUser")
                        .WithMany()
                        .HasForeignKey("UpdatedByUserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Company");

                    b.Navigation("UpdatedByUser");
                });

            modelBuilder.Entity("PushDashboard.Models.BulkMessage", b =>
                {
                    b.HasOne("PushDashboard.Models.Company", "Company")
                        .WithMany()
                        .HasForeignKey("CompanyId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("PushDashboard.Models.ApplicationUser", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.Navigation("Company");

                    b.Navigation("User");
                });

            modelBuilder.Entity("PushDashboard.Models.BulkMessageRecipient", b =>
                {
                    b.HasOne("PushDashboard.Models.BulkMessage", "BulkMessage")
                        .WithMany("Recipients")
                        .HasForeignKey("BulkMessageId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("PushDashboard.Models.Customer", "Customer")
                        .WithMany()
                        .HasForeignKey("CustomerId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("BulkMessage");

                    b.Navigation("Customer");
                });

            modelBuilder.Entity("PushDashboard.Models.CommentRequest", b =>
                {
                    b.HasOne("PushDashboard.Models.Company", "Company")
                        .WithMany()
                        .HasForeignKey("CompanyId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Company");
                });

            modelBuilder.Entity("PushDashboard.Models.CommentTransferJob", b =>
                {
                    b.HasOne("PushDashboard.Models.Company", "Company")
                        .WithMany()
                        .HasForeignKey("CompanyId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("PushDashboard.Models.TrendyolStore", "Store")
                        .WithMany("TransferJobs")
                        .HasForeignKey("StoreId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.Navigation("Company");

                    b.Navigation("Store");
                });

            modelBuilder.Entity("PushDashboard.Models.CommentTransferJobProduct", b =>
                {
                    b.HasOne("PushDashboard.Models.CommentTransferJob", "Job")
                        .WithMany("JobProducts")
                        .HasForeignKey("JobId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("PushDashboard.Models.TrendyolProduct", "Product")
                        .WithMany("JobProducts")
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Job");

                    b.Navigation("Product");
                });

            modelBuilder.Entity("PushDashboard.Models.CompanyEmailTemplate", b =>
                {
                    b.HasOne("PushDashboard.Models.Company", "Company")
                        .WithMany()
                        .HasForeignKey("CompanyId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("PushDashboard.Models.EmailTemplate", "EmailTemplate")
                        .WithMany("CompanyTemplates")
                        .HasForeignKey("EmailTemplateId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("PushDashboard.Models.ApplicationUser", "LastModifiedByUser")
                        .WithMany()
                        .HasForeignKey("LastModifiedBy")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.Navigation("Company");

                    b.Navigation("EmailTemplate");

                    b.Navigation("LastModifiedByUser");
                });

            modelBuilder.Entity("PushDashboard.Models.CompanyIntegration", b =>
                {
                    b.HasOne("PushDashboard.Models.Company", "Company")
                        .WithMany()
                        .HasForeignKey("CompanyId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("PushDashboard.Models.Integration", "Integration")
                        .WithMany("CompanyIntegrations")
                        .HasForeignKey("IntegrationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Company");

                    b.Navigation("Integration");
                });

            modelBuilder.Entity("PushDashboard.Models.CompanyModule", b =>
                {
                    b.HasOne("PushDashboard.Models.Company", "Company")
                        .WithMany("CompanyModules")
                        .HasForeignKey("CompanyId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("PushDashboard.Models.Module", "Module")
                        .WithMany("CompanyModules")
                        .HasForeignKey("ModuleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("PushDashboard.Models.ApplicationUser", "PurchasedByUser")
                        .WithMany()
                        .HasForeignKey("PurchasedByUserId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Company");

                    b.Navigation("Module");

                    b.Navigation("PurchasedByUser");
                });

            modelBuilder.Entity("PushDashboard.Models.CompanyModuleSettings", b =>
                {
                    b.HasOne("PushDashboard.Models.CompanyModule", "CompanyModule")
                        .WithOne("Settings")
                        .HasForeignKey("PushDashboard.Models.CompanyModuleSettings", "CompanyModuleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("PushDashboard.Models.ApplicationUser", "UpdatedByUser")
                        .WithMany()
                        .HasForeignKey("UpdatedByUserId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("CompanyModule");

                    b.Navigation("UpdatedByUser");
                });

            modelBuilder.Entity("PushDashboard.Models.CookieManagement", b =>
                {
                    b.HasOne("PushDashboard.Models.Company", "Company")
                        .WithMany()
                        .HasForeignKey("CompanyId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("PushDashboard.Models.ApplicationUser", "UpdatedByUser")
                        .WithMany()
                        .HasForeignKey("UpdatedByUserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Company");

                    b.Navigation("UpdatedByUser");
                });

            modelBuilder.Entity("PushDashboard.Models.Customer", b =>
                {
                    b.HasOne("PushDashboard.Models.Company", "Company")
                        .WithMany()
                        .HasForeignKey("CompanyId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Company");
                });

            modelBuilder.Entity("PushDashboard.Models.CustomerImportJob", b =>
                {
                    b.HasOne("PushDashboard.Models.Company", "Company")
                        .WithMany()
                        .HasForeignKey("CompanyId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("PushDashboard.Models.ApplicationUser", "CreatedByUser")
                        .WithMany()
                        .HasForeignKey("CreatedByUserId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Company");

                    b.Navigation("CreatedByUser");
                });

            modelBuilder.Entity("PushDashboard.Models.ExitSurvey", b =>
                {
                    b.HasOne("PushDashboard.Models.Company", "Company")
                        .WithMany()
                        .HasForeignKey("CompanyId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("PushDashboard.Models.ApplicationUser", "UpdatedByUser")
                        .WithMany()
                        .HasForeignKey("UpdatedByUserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Company");

                    b.Navigation("UpdatedByUser");
                });

            modelBuilder.Entity("PushDashboard.Models.ExitSurveyQuestion", b =>
                {
                    b.HasOne("PushDashboard.Models.ExitSurvey", "ExitSurvey")
                        .WithMany("Questions")
                        .HasForeignKey("ExitSurveyId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("ExitSurvey");
                });

            modelBuilder.Entity("PushDashboard.Models.ExitSurveyResponse", b =>
                {
                    b.HasOne("PushDashboard.Models.ExitSurvey", "ExitSurvey")
                        .WithMany("Responses")
                        .HasForeignKey("ExitSurveyId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("PushDashboard.Models.ExitSurveyQuestion", "Question")
                        .WithMany("Responses")
                        .HasForeignKey("QuestionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("ExitSurvey");

                    b.Navigation("Question");
                });

            modelBuilder.Entity("PushDashboard.Models.GiftWheel", b =>
                {
                    b.HasOne("PushDashboard.Models.Company", "Company")
                        .WithMany()
                        .HasForeignKey("CompanyId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Company");
                });

            modelBuilder.Entity("PushDashboard.Models.GiftWheelPrize", b =>
                {
                    b.HasOne("PushDashboard.Models.GiftWheel", "GiftWheel")
                        .WithMany("Prizes")
                        .HasForeignKey("GiftWheelId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("GiftWheel");
                });

            modelBuilder.Entity("PushDashboard.Models.GiftWheelSettings", b =>
                {
                    b.HasOne("PushDashboard.Models.GiftWheel", "GiftWheel")
                        .WithOne("Settings")
                        .HasForeignKey("PushDashboard.Models.GiftWheelSettings", "GiftWheelId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("GiftWheel");
                });

            modelBuilder.Entity("PushDashboard.Models.GiftWheelSpin", b =>
                {
                    b.HasOne("PushDashboard.Models.GiftWheel", "GiftWheel")
                        .WithMany("Spins")
                        .HasForeignKey("GiftWheelId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("PushDashboard.Models.GiftWheelPrize", "Prize")
                        .WithMany("Spins")
                        .HasForeignKey("PrizeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("GiftWheel");

                    b.Navigation("Prize");
                });

            modelBuilder.Entity("PushDashboard.Models.Module", b =>
                {
                    b.HasOne("PushDashboard.Models.ModuleCategory", "Category")
                        .WithMany("Modules")
                        .HasForeignKey("CategoryId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Category");
                });

            modelBuilder.Entity("PushDashboard.Models.ModuleUsageLog", b =>
                {
                    b.HasOne("PushDashboard.Models.Company", "Company")
                        .WithMany()
                        .HasForeignKey("CompanyId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("PushDashboard.Models.Module", "Module")
                        .WithMany()
                        .HasForeignKey("ModuleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("PushDashboard.Models.ApplicationUser", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Company");

                    b.Navigation("Module");

                    b.Navigation("User");
                });

            modelBuilder.Entity("PushDashboard.Models.NotificationPreferences", b =>
                {
                    b.HasOne("PushDashboard.Models.ApplicationUser", "User")
                        .WithOne("NotificationPreferences")
                        .HasForeignKey("PushDashboard.Models.NotificationPreferences", "UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("User");
                });

            modelBuilder.Entity("PushDashboard.Models.OrderStatusChangeLog", b =>
                {
                    b.HasOne("PushDashboard.Models.Company", "Company")
                        .WithMany()
                        .HasForeignKey("CompanyId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Company");
                });

            modelBuilder.Entity("PushDashboard.Models.OrderStatusMapping", b =>
                {
                    b.HasOne("PushDashboard.Models.Company", "Company")
                        .WithMany()
                        .HasForeignKey("CompanyId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Company");
                });

            modelBuilder.Entity("PushDashboard.Models.OrderStatusNotification", b =>
                {
                    b.HasOne("PushDashboard.Models.Company", "Company")
                        .WithMany()
                        .HasForeignKey("CompanyId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Company");
                });

            modelBuilder.Entity("PushDashboard.Models.ProductSlider", b =>
                {
                    b.HasOne("PushDashboard.Models.Company", "Company")
                        .WithMany()
                        .HasForeignKey("CompanyId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("PushDashboard.Models.ApplicationUser", "CreatedByUser")
                        .WithMany()
                        .HasForeignKey("CreatedByUserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Company");

                    b.Navigation("CreatedByUser");
                });

            modelBuilder.Entity("PushDashboard.Models.ProductSliderItem", b =>
                {
                    b.HasOne("PushDashboard.Models.ProductSlider", "Slider")
                        .WithMany("Items")
                        .HasForeignKey("SliderId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Slider");
                });

            modelBuilder.Entity("PushDashboard.Models.ProductSliderSettings", b =>
                {
                    b.HasOne("PushDashboard.Models.ProductSlider", "Slider")
                        .WithOne("Settings")
                        .HasForeignKey("PushDashboard.Models.ProductSliderSettings", "SliderId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Slider");
                });

            modelBuilder.Entity("PushDashboard.Models.SocialProofSettings", b =>
                {
                    b.HasOne("PushDashboard.Models.Company", "Company")
                        .WithMany()
                        .HasForeignKey("CompanyId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("PushDashboard.Models.ApplicationUser", "UpdatedByUser")
                        .WithMany()
                        .HasForeignKey("UpdatedByUserId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Company");

                    b.Navigation("UpdatedByUser");
                });

            modelBuilder.Entity("PushDashboard.Models.SyncLog", b =>
                {
                    b.HasOne("PushDashboard.Models.Company", "Company")
                        .WithMany()
                        .HasForeignKey("CompanyId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Company");
                });

            modelBuilder.Entity("PushDashboard.Models.TrendyolProduct", b =>
                {
                    b.HasOne("PushDashboard.Models.TrendyolStore", "Store")
                        .WithMany("Products")
                        .HasForeignKey("StoreId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Store");
                });

            modelBuilder.Entity("PushDashboard.Models.TrendyolStore", b =>
                {
                    b.HasOne("PushDashboard.Models.Company", "Company")
                        .WithMany()
                        .HasForeignKey("CompanyId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Company");
                });

            modelBuilder.Entity("PushDashboard.Models.UserInvitation", b =>
                {
                    b.HasOne("PushDashboard.Models.Company", "Company")
                        .WithMany()
                        .HasForeignKey("CompanyId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("PushDashboard.Models.ApplicationUser", "CreatedByUser")
                        .WithMany()
                        .HasForeignKey("CreatedBy")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Company");

                    b.Navigation("CreatedByUser");
                });

            modelBuilder.Entity("PushDashboard.Models.UserSession", b =>
                {
                    b.HasOne("PushDashboard.Models.ApplicationUser", "User")
                        .WithMany("UserSessions")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("User");
                });

            modelBuilder.Entity("PushDashboard.Models.VideoHosting", b =>
                {
                    b.HasOne("PushDashboard.Models.Company", "Company")
                        .WithMany()
                        .HasForeignKey("CompanyId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("PushDashboard.Models.ApplicationUser", "UpdatedByUser")
                        .WithMany()
                        .HasForeignKey("UpdatedByUserId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("PushDashboard.Models.ApplicationUser", "UploadedByUser")
                        .WithMany()
                        .HasForeignKey("UploadedByUserId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Company");

                    b.Navigation("UpdatedByUser");

                    b.Navigation("UploadedByUser");
                });

            modelBuilder.Entity("PushDashboard.Models.VideoHostingSettings", b =>
                {
                    b.HasOne("PushDashboard.Models.Company", "Company")
                        .WithMany()
                        .HasForeignKey("CompanyId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("PushDashboard.Models.ApplicationUser", "UpdatedByUser")
                        .WithMany()
                        .HasForeignKey("UpdatedByUserId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Company");

                    b.Navigation("UpdatedByUser");
                });

            modelBuilder.Entity("PushDashboard.Models.VideoHostingView", b =>
                {
                    b.HasOne("PushDashboard.Models.Company", "Company")
                        .WithMany()
                        .HasForeignKey("CompanyId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("PushDashboard.Models.VideoHosting", "VideoHosting")
                        .WithMany("Views")
                        .HasForeignKey("VideoHostingId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Company");

                    b.Navigation("VideoHosting");
                });

            modelBuilder.Entity("PushDashboard.Models.ApplicationUser", b =>
                {
                    b.Navigation("NotificationPreferences");

                    b.Navigation("UserSessions");
                });

            modelBuilder.Entity("PushDashboard.Models.Basket", b =>
                {
                    b.Navigation("BasketItems");
                });

            modelBuilder.Entity("PushDashboard.Models.BulkMessage", b =>
                {
                    b.Navigation("Recipients");
                });

            modelBuilder.Entity("PushDashboard.Models.CommentTransferJob", b =>
                {
                    b.Navigation("JobProducts");
                });

            modelBuilder.Entity("PushDashboard.Models.Company", b =>
                {
                    b.Navigation("CompanyModules");

                    b.Navigation("Users");
                });

            modelBuilder.Entity("PushDashboard.Models.CompanyModule", b =>
                {
                    b.Navigation("Settings");
                });

            modelBuilder.Entity("PushDashboard.Models.EmailTemplate", b =>
                {
                    b.Navigation("CompanyTemplates");
                });

            modelBuilder.Entity("PushDashboard.Models.ExitSurvey", b =>
                {
                    b.Navigation("Questions");

                    b.Navigation("Responses");
                });

            modelBuilder.Entity("PushDashboard.Models.ExitSurveyQuestion", b =>
                {
                    b.Navigation("Responses");
                });

            modelBuilder.Entity("PushDashboard.Models.GiftWheel", b =>
                {
                    b.Navigation("Prizes");

                    b.Navigation("Settings");

                    b.Navigation("Spins");
                });

            modelBuilder.Entity("PushDashboard.Models.GiftWheelPrize", b =>
                {
                    b.Navigation("Spins");
                });

            modelBuilder.Entity("PushDashboard.Models.Integration", b =>
                {
                    b.Navigation("CompanyIntegrations");
                });

            modelBuilder.Entity("PushDashboard.Models.Module", b =>
                {
                    b.Navigation("CompanyModules");
                });

            modelBuilder.Entity("PushDashboard.Models.ModuleCategory", b =>
                {
                    b.Navigation("Modules");
                });

            modelBuilder.Entity("PushDashboard.Models.ProductSlider", b =>
                {
                    b.Navigation("Items");

                    b.Navigation("Settings");
                });

            modelBuilder.Entity("PushDashboard.Models.TrendyolProduct", b =>
                {
                    b.Navigation("JobProducts");
                });

            modelBuilder.Entity("PushDashboard.Models.TrendyolStore", b =>
                {
                    b.Navigation("Products");

                    b.Navigation("TransferJobs");
                });

            modelBuilder.Entity("PushDashboard.Models.VideoHosting", b =>
                {
                    b.Navigation("Views");
                });
#pragma warning restore 612, 618
        }
    }
}
