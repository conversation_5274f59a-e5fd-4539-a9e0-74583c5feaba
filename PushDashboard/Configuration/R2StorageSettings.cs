namespace PushDashboard.Configuration;

public class R2StorageSettings
{
    public const string SectionName = "R2Storage";
    
    public string AccountId { get; set; } = string.Empty;
    public string AccessKeyId { get; set; } = string.Empty;
    public string SecretAccessKey { get; set; } = string.Empty;
    public string BucketName { get; set; } = string.Empty;
    public string Region { get; set; } = "auto";
    public string PublicUrl { get; set; } = string.Empty;
}
