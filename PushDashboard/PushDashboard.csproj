<?xml version="1.0" encoding="utf-8"?>
<Project Sdk="Microsoft.NET.Sdk.Web">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="AWSSDK.S3" Version="4.0.6" />
    <PackageReference Include="EPPlus" Version="7.7.2" />
    <PackageReference Include="FFMpegCore" Version="5.2.0" />
    <PackageReference Include="Microsoft.AspNetCore.Identity.EntityFrameworkCore" Version="8.0.5" />
    <PackageReference Include="Microsoft.AspNetCore.SignalR" Version="1.2.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="8.0.5">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
    <PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="8.0.5" />
    <PackageReference Include="Otp.NET" Version="1.4.0" />
    <PackageReference Include="QRCoder" Version="1.6.0" />
    <PackageReference Include="UAParser" Version="3.1.47" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="System.ServiceModel.Duplex" Version="4.10.*" />
    <PackageReference Include="System.ServiceModel.Http" Version="4.10.*" />
    <PackageReference Include="System.ServiceModel.NetTcp" Version="4.10.*" />
    <PackageReference Include="System.ServiceModel.Security" Version="4.10.*" />
    <PackageReference Include="System.ServiceModel.Federation" Version="4.10.*" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="wwwroot\uploads\whatsapp\" />
  </ItemGroup>
  <ItemGroup>
    <Compile Remove="Models\WhatsAppTemplate.cs" />
  </ItemGroup>
  <ItemGroup>
    <None Remove="Dockerfile" />
    <None Include="wwwroot\js\modules\product-slider-module.js" />
  </ItemGroup>
  <ItemGroup>
    <Content Remove="wwwroot\images\create-placeholder.html" />
    <Content Remove="wwwroot\images\generate-png.js" />
  </ItemGroup>
</Project>