using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using PushDashboard.DTOs;
using PushDashboard.Services;
using PushDashboard.Services.Modules.GiftWheel;
using PushDashboard.ViewModels;
using System.Security.Claims;

namespace PushDashboard.Controllers;

/// <summary>
/// Admin controller for Gift Wheel management
/// </summary>
[Authorize]
public class GiftWheelController : BaseController
{
    private readonly IGiftWheelService _giftWheelService;
    private readonly IGiftWheelModuleService _moduleService;
    private readonly ILogger<GiftWheelController> _logger;

    public GiftWheelController(
        IGiftWheelService giftWheelService,
        IGiftWheelModuleService moduleService,
        ILogger<GiftWheelController> logger,
        IUserContextService userContextService) : base(userContextService)
    {
        _giftWheelService = giftWheelService;
        _moduleService = moduleService;
        _logger = logger;
    }

    /// <summary>
    /// Main gift wheel management page
    /// </summary>
    [HttpGet]
    public async Task<IActionResult> Index()
    {
        try
        {
            var companyId = GetCurrentUserCompanyId();
            if (!companyId.HasValue)
            {
                return RedirectToAction("Login", "Account");
            }

            // Check if company has active module
            if (!await _moduleService.HasActiveModuleAsync(companyId.Value))
            {
                ViewBag.ErrorMessage = "Hediye çarkı modülü aktif değil. Lütfen modülü satın alın.";
                return View("ModuleNotActive");
            }

            // Get wheel data
            var wheel = await _giftWheelService.GetCompanyWheelAsync(companyId.Value);
            var settings = wheel != null ? await _giftWheelService.GetWheelSettingsAsync(wheel.Id) : null;
            var prizes = wheel != null ? await _giftWheelService.GetWheelPrizesAsync(wheel.Id) : new List<GiftWheelPrizeDto>();
            var stats = await _giftWheelService.GetWheelStatsAsync(companyId.Value);
            var integrationStatus = await _moduleService.CheckRequiredIntegrationsAsync(companyId.Value);

            var viewModel = new GiftWheelViewModel
            {
                Wheel = wheel,
                Settings = settings,
                Prizes = prizes,
                Stats = stats,
                IntegrationStatus = integrationStatus,
                CompanyId = companyId.Value
            };

            return View(viewModel);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading gift wheel page");
            ViewBag.ErrorMessage = "Sayfa yüklenirken hata oluştu.";
            return View("Error");
        }
    }

    #region Wheel Management

    /// <summary>
    /// Create or update wheel
    /// </summary>
    [HttpPost]
    public async Task<IActionResult> CreateOrUpdateWheel([FromBody] CreateWheelRequest request)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return Json(new { success = false, message = "Geçersiz veri." });
            }

            var companyId = GetCurrentUserCompanyId();
            var userId = GetCurrentUserId();

            if (!companyId.HasValue || string.IsNullOrEmpty(userId))
            {
                return Json(new { success = false, message = "Oturum bilgisi bulunamadı." });
            }

            var (success, message) = await _giftWheelService.CreateOrUpdateWheelAsync(companyId.Value, request.Name, userId);

            return Json(new { success, message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating/updating wheel");
            return Json(new { success = false, message = "İşlem sırasında hata oluştu." });
        }
    }

    /// <summary>
    /// Toggle wheel status (active/inactive)
    /// </summary>
    [HttpPost]
    public async Task<IActionResult> ToggleWheelStatus([FromBody] ToggleWheelStatusRequest request)
    {
        try
        {
            var companyId = GetCurrentUserCompanyId();
            var userId = GetCurrentUserId();

            if (!companyId.HasValue || string.IsNullOrEmpty(userId))
            {
                return Json(new { success = false, message = "Oturum bilgisi bulunamadı." });
            }

            var (success, message) = await _giftWheelService.ToggleWheelStatusAsync(companyId.Value, request.IsActive, userId);

            return Json(new { success, message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error toggling wheel status");
            return Json(new { success = false, message = "Durum değiştirilirken hata oluştu." });
        }
    }

    #endregion

    #region Prize Management

    /// <summary>
    /// Get wheel prizes
    /// </summary>
    [HttpGet]
    public async Task<IActionResult> GetPrizes()
    {
        try
        {
            var companyId = GetCurrentUserCompanyId();
            if (!companyId.HasValue)
            {
                return Json(new { success = false, message = "Oturum bilgisi bulunamadı." });
            }

            var wheel = await _giftWheelService.GetCompanyWheelAsync(companyId.Value);
            if (wheel == null)
            {
                return Json(new { success = false, message = "Çark bulunamadı." });
            }

            var prizes = await _giftWheelService.GetWheelPrizesAsync(wheel.Id);

            return Json(new { success = true, data = prizes });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting prizes");
            return Json(new { success = false, message = "Ödüller alınırken hata oluştu." });
        }
    }

    /// <summary>
    /// Add new prize
    /// </summary>
    [HttpPost]
    public async Task<IActionResult> AddPrize([FromBody] GiftWheelPrizeRequest request)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                var errors = ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage);
                return Json(new { success = false, message = string.Join(", ", errors) });
            }

            var companyId = GetCurrentUserCompanyId();
            if (!companyId.HasValue)
            {
                return Json(new { success = false, message = "Oturum bilgisi bulunamadı." });
            }

            var wheel = await _giftWheelService.GetCompanyWheelAsync(companyId.Value);
            if (wheel == null)
            {
                return Json(new { success = false, message = "Çark bulunamadı." });
            }

            var (success, message, prize) = await _giftWheelService.AddPrizeAsync(wheel.Id, request);

            // Auto-export to R2 on successful prize addition
            if (success)
            {
                try
                {
                    await _giftWheelService.ExportGiftWheelToR2Async(companyId.Value);
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Failed to auto-export to R2 after adding prize");
                }
            }

            return Json(new { success, message, data = prize });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error adding prize");
            return Json(new { success = false, message = "Ödül eklenirken hata oluştu." });
        }
    }

    /// <summary>
    /// Update existing prize
    /// </summary>
    [HttpPut]
    public async Task<IActionResult> UpdatePrize(int id, [FromBody] GiftWheelPrizeRequest request)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                var errors = ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage);
                return Json(new { success = false, message = string.Join(", ", errors) });
            }

            var (success, message) = await _giftWheelService.UpdatePrizeAsync(id, request);

            // Auto-export to R2 on successful prize update
            if (success)
            {
                try
                {
                    var companyId = GetCurrentUserCompanyId();
                    if (companyId.HasValue)
                    {
                        await _giftWheelService.ExportGiftWheelToR2Async(companyId.Value);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Failed to auto-export to R2 after updating prize");
                }
            }

            return Json(new { success, message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating prize {PrizeId}", id);
            return Json(new { success = false, message = "Ödül güncellenirken hata oluştu." });
        }
    }

    /// <summary>
    /// Delete prize
    /// </summary>
    [HttpDelete]
    public async Task<IActionResult> DeletePrize(int id)
    {
        try
        {
            var (success, message) = await _giftWheelService.DeletePrizeAsync(id);

            return Json(new { success, message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting prize {PrizeId}", id);
            return Json(new { success = false, message = "Ödül silinirken hata oluştu." });
        }
    }

    /// <summary>
    /// Toggle prize status
    /// </summary>
    [HttpPost]
    public async Task<IActionResult> TogglePrizeStatus(int id, [FromBody] TogglePrizeStatusRequest request)
    {
        try
        {
            var (success, message) = await _giftWheelService.TogglePrizeStatusAsync(id, request.IsActive);

            return Json(new { success, message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error toggling prize status {PrizeId}", id);
            return Json(new { success = false, message = "Ödül durumu değiştirilirken hata oluştu." });
        }
    }

    /// <summary>
    /// Toggle all prizes status
    /// </summary>
    [HttpPost]
    public async Task<IActionResult> ToggleAllPrizes([FromBody] ToggleAllPrizesRequest request)
    {
        try
        {
            var companyId = GetCurrentUserCompanyId();
            if (!companyId.HasValue)
            {
                return Json(new { success = false, message = "Oturum bilgisi bulunamadı." });
            }

            var wheel = await _giftWheelService.GetCompanyWheelAsync(companyId.Value);
            if (wheel == null)
            {
                return Json(new { success = false, message = "Çark bulunamadı." });
            }

            var (success, message) = await _giftWheelService.ToggleAllPrizesAsync(wheel.Id, request.IsActive);

            return Json(new { success, message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error toggling all prizes status");
            return Json(new { success = false, message = "Ödül durumları değiştirilirken hata oluştu." });
        }
    }

    /// <summary>
    /// Reorder prizes
    /// </summary>
    [HttpPost]
    public async Task<IActionResult> ReorderPrizes([FromBody] ReorderPrizesRequest request)
    {
        try
        {
            var companyId = GetCurrentUserCompanyId();
            if (!companyId.HasValue)
            {
                return Json(new { success = false, message = "Oturum bilgisi bulunamadı." });
            }

            var wheel = await _giftWheelService.GetCompanyWheelAsync(companyId.Value);
            if (wheel == null)
            {
                return Json(new { success = false, message = "Çark bulunamadı." });
            }

            var (success, message) = await _giftWheelService.ReorderPrizesAsync(wheel.Id, request.PrizeOrders);

            return Json(new { success, message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error reordering prizes");
            return Json(new { success = false, message = "Sıralama güncellenirken hata oluştu." });
        }
    }

    #endregion

    #region Settings Management

    /// <summary>
    /// Get wheel settings
    /// </summary>
    [HttpGet]
    public async Task<IActionResult> GetSettings()
    {
        try
        {
            var companyId = GetCurrentUserCompanyId();
            if (!companyId.HasValue)
            {
                return Json(new { success = false, message = "Oturum bilgisi bulunamadı." });
            }

            var wheel = await _giftWheelService.GetCompanyWheelAsync(companyId.Value);
            if (wheel == null)
            {
                return Json(new { success = false, message = "Çark bulunamadı." });
            }

            var settings = await _giftWheelService.GetWheelSettingsAsync(wheel.Id);

            return Json(new { success = true, data = settings });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting settings");
            return Json(new { success = false, message = "Ayarlar alınırken hata oluştu." });
        }
    }

    /// <summary>
    /// Update wheel settings
    /// </summary>
    [HttpPost]
    public async Task<IActionResult> UpdateSettings([FromBody] UpdateGiftWheelSettingsRequest request)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                var errors = ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage);
                return Json(new { success = false, message = string.Join(", ", errors) });
            }

            var companyId = GetCurrentUserCompanyId();
            var userId = GetCurrentUserId();

            if (!companyId.HasValue || string.IsNullOrEmpty(userId))
            {
                return Json(new { success = false, message = "Oturum bilgisi bulunamadı." });
            }

            var wheel = await _giftWheelService.GetCompanyWheelAsync(companyId.Value);
            if (wheel == null)
            {
                return Json(new { success = false, message = "Çark bulunamadı." });
            }

            var (success, message) = await _giftWheelService.UpdateWheelSettingsAsync(wheel.Id, request, userId);

            // Auto-export to R2 on successful settings update
            if (success)
            {
                try
                {
                    await _giftWheelService.ExportGiftWheelToR2Async(companyId.Value);
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Failed to auto-export to R2 after updating settings");
                }
            }

            return Json(new { success, message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating settings");
            return Json(new { success = false, message = "Ayarlar güncellenirken hata oluştu." });
        }
    }

    #endregion

    #region Statistics and Reporting

    /// <summary>
    /// Get wheel statistics
    /// </summary>
    [HttpGet]
    public async Task<IActionResult> GetStats(DateTime? startDate = null, DateTime? endDate = null)
    {
        try
        {
            var companyId = GetCurrentUserCompanyId();
            if (!companyId.HasValue)
            {
                return Json(new { success = false, message = "Oturum bilgisi bulunamadı." });
            }

            var stats = await _giftWheelService.GetWheelStatsAsync(companyId.Value, startDate, endDate);

            return Json(new { success = true, data = stats });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting statistics");
            return Json(new { success = false, message = "İstatistikler alınırken hata oluştu." });
        }
    }

    /// <summary>
    /// Get recent spins
    /// </summary>
    [HttpGet]
    public async Task<IActionResult> GetRecentSpins(int limit = 50)
    {
        try
        {
            var companyId = GetCurrentUserCompanyId();
            if (!companyId.HasValue)
            {
                return Json(new { success = false, message = "Oturum bilgisi bulunamadı." });
            }

            var recentSpins = await _giftWheelService.GetRecentSpinsAsync(companyId.Value, limit);

            return Json(new { success = true, data = recentSpins });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting recent spins");
            return Json(new { success = false, message = "Son çevirmeler alınırken hata oluştu." });
        }
    }

    /// <summary>
    /// Get daily statistics for charts
    /// </summary>
    [HttpGet]
    public async Task<IActionResult> GetDailyStats(int days = 30)
    {
        try
        {
            var companyId = GetCurrentUserCompanyId();
            if (!companyId.HasValue)
            {
                return Json(new { success = false, message = "Oturum bilgisi bulunamadı." });
            }

            var dailyStats = await _giftWheelService.GetDailyStatsAsync(companyId.Value, days);

            return Json(new { success = true, data = dailyStats });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting daily statistics");
            return Json(new { success = false, message = "Günlük istatistikler alınırken hata oluştu." });
        }
    }

    #endregion

    #region Script Generation

    /// <summary>
    /// Get JavaScript embed script
    /// </summary>
    [HttpGet]
    public async Task<IActionResult> GetEmbedScript()
    {
        try
        {
            var companyId = GetCurrentUserCompanyId();
            if (!companyId.HasValue)
            {
                return Content("// Oturum bilgisi bulunamadı.", "application/javascript");
            }

            var script = await _giftWheelService.GenerateEmbedScriptAsync(companyId.Value);

            return Content(script, "application/javascript");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating embed script");
            return Content("// Script oluşturulurken hata oluştu.", "application/javascript");
        }
    }

    /// <summary>
    /// Get wheel configuration for preview
    /// </summary>
    [HttpGet]
    public async Task<IActionResult> GetWheelConfig()
    {
        try
        {
            var companyId = GetCurrentUserCompanyId();
            if (!companyId.HasValue)
            {
                return Json(new { success = false, message = "Oturum bilgisi bulunamadı." });
            }

            var config = await _giftWheelService.GetWheelConfigAsync(companyId.Value);

            return Json(new { success = true, data = config });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting wheel configuration");
            return Json(new { success = false, message = "Konfigürasyon alınırken hata oluştu." });
        }
    }

    #endregion

    #region Module Management

    /// <summary>
    /// Get module settings
    /// </summary>
    [HttpGet]
    public async Task<IActionResult> GetModuleSettings()
    {
        try
        {
            var companyId = GetCurrentUserCompanyId();
            if (!companyId.HasValue)
            {
                return Json(new { success = false, message = "Oturum bilgisi bulunamadı." });
            }

            var settings = await _moduleService.GetModuleSettingsAsync(companyId.Value);

            return Json(new { success = true, data = settings });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting module settings");
            return Json(new { success = false, message = "Modül ayarları alınırken hata oluştu." });
        }
    }

    /// <summary>
    /// Update module settings
    /// </summary>
    [HttpPost]
    public async Task<IActionResult> UpdateModuleSettings([FromBody] GiftWheelModuleSettingsDto settings)
    {
        try
        {
            var companyId = GetCurrentUserCompanyId();
            var userId = GetCurrentUserId();

            if (!companyId.HasValue || string.IsNullOrEmpty(userId))
            {
                return Json(new { success = false, message = "Oturum bilgisi bulunamadı." });
            }

            var (success, message) = await _moduleService.UpdateModuleSettingsAsync(companyId.Value, settings, userId);

            return Json(new { success, message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating module settings");
            return Json(new { success = false, message = "Modül ayarları güncellenirken hata oluştu." });
        }
    }

    /// <summary>
    /// Get integration status
    /// </summary>
    [HttpGet]
    public async Task<IActionResult> GetIntegrationStatus()
    {
        try
        {
            var companyId = GetCurrentUserCompanyId();
            if (!companyId.HasValue)
            {
                return Json(new { success = false, message = "Oturum bilgisi bulunamadı." });
            }

            var status = await _moduleService.CheckRequiredIntegrationsAsync(companyId.Value);

            return Json(new { success = true, data = status });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting integration status");
            return Json(new { success = false, message = "Entegrasyon durumu alınırken hata oluştu." });
        }
    }

    #endregion

    #region R2 Export

    /// <summary>
    /// Export gift wheel configuration to R2 storage
    /// </summary>
    [HttpPost]
    public async Task<IActionResult> ExportToR2()
    {
        try
        {
            var companyId = GetCurrentUserCompanyId();
            if (!companyId.HasValue)
            {
                TempData["ErrorMessage"] = "Şirket bilgisi bulunamadı.";
                return RedirectToAction(nameof(Index));
            }

            var result = await _giftWheelService.ExportGiftWheelToR2Async(companyId.Value);

            if (result.Contains("successfully"))
            {
                TempData["SuccessMessage"] = "Hediye çarkı başarıyla R2'ye aktarıldı.";
            }
            else
            {
                TempData["ErrorMessage"] = $"R2 aktarımı başarısız: {result}";
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error exporting gift wheel to R2");
            TempData["ErrorMessage"] = "R2 aktarımı sırasında hata oluştu.";
        }

        return RedirectToAction(nameof(Index));
    }

    #endregion
}


