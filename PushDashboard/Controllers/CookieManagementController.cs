using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using PushDashboard.Models;
using PushDashboard.Services;
using System.Text.Json;

namespace PushDashboard.Controllers;

[Authorize]
public class CookieManagementController : BaseController
{
    private readonly ICookieManagementService _cookieManagementService;
    private readonly ILogger<CookieManagementController> _logger;

    public CookieManagementController(
        ICookieManagementService cookieManagementService,
        ILogger<CookieManagementController> logger,
        IUserContextService userContextService) : base(userContextService)
    {
        _cookieManagementService = cookieManagementService;
        _logger = logger;
    }

    public async Task<IActionResult> Index()
    {
        var companyId = GetCurrentUserCompanyId();
        if (!companyId.HasValue)
        {
            return RedirectToAction("Login", "Account");
        }

        // Modül kontrolü
        var hasModule = await _cookieManagementService.HasCookieManagementModuleAsync(companyId.Value);
        if (!hasModule)
        {
            TempData["ErrorMessage"] = "Çerez Yönetimi modülüne sahip değilsiniz. Lütfen önce modülü satın alın.";
            return RedirectToAction("Index", "Store");
        }

        // Mevcut ayarları getir veya varsayılan oluştur
        var cookieManagement = await _cookieManagementService.GetCookieManagementAsync(companyId.Value);
        if (cookieManagement == null)
        {
            var userId = GetCurrentUserId();
            if (string.IsNullOrEmpty(userId))
            {
                return RedirectToAction("Login", "Account");
            }

            cookieManagement = await _cookieManagementService.CreateDefaultSettingsAsync(companyId.Value, userId);
        }

        ViewData["Title"] = "Çerez Yönetimi";
        ViewData["ScriptUrl"] = $"{Request.Scheme}://{Request.Host}/api/cookie-management/script/{companyId.Value}";
        
        return View(cookieManagement);
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> SaveSettings([FromBody] CookieManagementSaveModel model)
    {
        try
        {
            var companyId = GetCurrentUserCompanyId();
            var userId = GetCurrentUserId();

            if (!companyId.HasValue || string.IsNullOrEmpty(userId))
            {
                return Json(new { success = false, message = "Kullanıcı bilgileri bulunamadı." });
            }

            // Modül kontrolü
            var hasModule = await _cookieManagementService.HasCookieManagementModuleAsync(companyId.Value);
            if (!hasModule)
            {
                return Json(new { success = false, message = "Çerez Yönetimi modülüne sahip değilsiniz." });
            }

            var cookieManagement = new CookieManagement
            {
                CompanyId = companyId.Value,
                BannerTitle = model.BannerTitle,
                BannerDescription = model.BannerDescription,
                AcceptButtonText = model.AcceptButtonText,
                RejectButtonText = model.RejectButtonText,
                SettingsButtonText = model.SettingsButtonText,
                SaveButtonText = model.SaveButtonText,
                BannerPosition = model.BannerPosition,
                BannerBackgroundColor = model.BannerBackgroundColor,
                BannerTextColor = model.BannerTextColor,
                AcceptButtonColor = model.AcceptButtonColor,
                RejectButtonColor = model.RejectButtonColor,
                SettingsButtonColor = model.SettingsButtonColor,
                BorderRadius = model.BorderRadius,
                ShowSettingsButton = model.ShowSettingsButton,
                EnableAnimation = model.EnableAnimation,
                IsActive = true, // Modüle sahipse her zaman aktif
                CookieExpiryDays = model.CookieExpiryDays,
                UpdatedByUserId = userId
            };

            // Kategorileri ayarla
            cookieManagement.Categories = model.Categories ?? new List<CookieCategory>();

            var success = await _cookieManagementService.SaveCookieManagementAsync(cookieManagement);

            if (success)
            {
                return Json(new { success = true, message = "Ayarlar başarıyla kaydedildi." });
            }
            else
            {
                return Json(new { success = false, message = "Ayarlar kaydedilirken bir hata oluştu." });
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error saving cookie management settings");
            return Json(new { success = false, message = "Bir hata oluştu. Lütfen tekrar deneyin." });
        }
    }

    [HttpGet]
    public async Task<IActionResult> GetPreviewData()
    {
        try
        {
            var companyId = GetCurrentUserCompanyId();
            if (!companyId.HasValue)
            {
                return Json(new { success = false, message = "Şirket bilgisi bulunamadı." });
            }

            var config = await _cookieManagementService.GetScriptConfigAsync(companyId.Value);
            return Json(new { success = true, data = config });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting preview data");
            return Json(new { success = false, message = "Önizleme verileri alınırken hata oluştu." });
        }
    }
}

/// <summary>
/// Çerez yönetimi ayarlarını kaydetmek için kullanılan model
/// </summary>
public class CookieManagementSaveModel
{
    public string BannerTitle { get; set; } = string.Empty;
    public string BannerDescription { get; set; } = string.Empty;
    public string AcceptButtonText { get; set; } = string.Empty;
    public string RejectButtonText { get; set; } = string.Empty;
    public string SettingsButtonText { get; set; } = string.Empty;
    public string SaveButtonText { get; set; } = string.Empty;
    public string BannerPosition { get; set; } = string.Empty;
    public string BannerBackgroundColor { get; set; } = string.Empty;
    public string BannerTextColor { get; set; } = string.Empty;
    public string AcceptButtonColor { get; set; } = string.Empty;
    public string RejectButtonColor { get; set; } = string.Empty;
    public string SettingsButtonColor { get; set; } = string.Empty;
    public string BorderRadius { get; set; } = string.Empty;
    public bool ShowSettingsButton { get; set; }
    public bool EnableAnimation { get; set; }
    public int CookieExpiryDays { get; set; }
    public List<CookieCategory>? Categories { get; set; }
}
