using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using PushDashboard.Services;
using PushDashboard.ViewModels;
using PushDashboard.Data;

namespace PushDashboard.Controllers;

[Authorize]
public class CustomerController : BaseController
{
    private readonly ICustomerService _customerService;
    private readonly ApplicationDbContext _context;

    public CustomerController(ICustomerService customerService, ApplicationDbContext context, IUserContextService userContextService)
        : base(userContextService)
    {
        _customerService = customerService;
        _context = context;
    }

    public async Task<IActionResult> Index()
    {
        // Customer tablolarının varlığını kontrol et ve gerekirse oluştur
        try
        {
            await _context.Database.EnsureCreatedAsync();
        }
        catch (Exception ex)
        {
            // Tablo zaten varsa hata vermez, diğer hatalar için log
            Console.WriteLine($"Database ensure error: {ex.Message}");
        }

        var companyId = GetCurrentUserCompanyId();
        if (companyId == null)
        {
            return RedirectToAction("Index", "Home");
        }

        // İlk yüklemede sadece boş bir view model döndür, veriler AJAX ile yüklenecek
        var emptyViewModel = new CustomerIndexViewModel
        {
            Customers = new List<CustomerIndexViewModel.CustomerViewModel>(),
            Stats = new CustomerIndexViewModel.CustomerStatsViewModel(),
            Pagination = new CustomerIndexViewModel.PaginationViewModel(),
            RecentSyncLogs = new List<CustomerIndexViewModel.CustomerSyncLogViewModel>()
        };

        return View(emptyViewModel);
    }

    [HttpGet]
    public async Task<IActionResult> GetCustomers(int page = 1, int pageSize = 50, string? searchTerm = null, string? status = null, string? sortBy = null, string? sortDirection = null)
    {
        var companyId = GetCurrentUserCompanyId();
        if (companyId == null)
        {
            return Json(new { success = false, message = "Şirket bilgisi bulunamadı." });
        }

        try
        {
            var viewModel = await _customerService.GetCustomersAsync(companyId.Value, page, pageSize, searchTerm, status, sortBy, sortDirection);

            return Json(new {
                success = true,
                data = new {
                    customers = viewModel.Customers,
                    stats = viewModel.Stats,
                    pagination = viewModel.Pagination
                }
            });
        }
        catch (Exception ex)
        {
            return Json(new { success = false, message = "Müşteriler yüklenirken hata oluştu: " + ex.Message });
        }
    }



    [HttpPost]
    public async Task<IActionResult> SyncCustomers()
    {
        var companyId = GetCurrentUserCompanyId();
        if (companyId == null)
        {
            return Json(new { success = false, message = "Şirket bilgisi bulunamadı." });
        }

        try
        {
            var result = await _customerService.SyncCustomersAsync(companyId.Value);
            return Json(new { success = result.Success, message = result.Message });
        }
        catch (Exception ex)
        {
            return Json(new { success = false, message = ex.Message });
        }
    }

    [HttpGet]
    public async Task<IActionResult> GetCustomerDetails(int id)
    {
        var companyId = GetCurrentUserCompanyId();
        if (companyId == null)
        {
            return Json(new { success = false, message = "Şirket bilgisi bulunamadı." });
        }

        try
        {
            var customer = await _customerService.GetCustomerByIdAsync(id, companyId.Value);
            if (customer == null)
            {
                return Json(new { success = false, message = "Müşteri bulunamadı." });
            }

            return Json(new { success = true, data = customer });
        }
        catch (Exception ex)
        {
            return Json(new { success = false, message = ex.Message });
        }
    }

    [HttpPost]
    public async Task<IActionResult> CreateCustomer([FromBody] CreateCustomerViewModel model)
    {
        var companyId = GetCurrentUserCompanyId();
        if (companyId == null)
        {
            return Json(new { success = false, message = "Şirket bilgisi bulunamadı." });
        }

        if (!ModelState.IsValid)
        {
            var errors = ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage);
            return Json(new { success = false, message = string.Join(", ", errors) });
        }

        try
        {
            var result = await _customerService.CreateCustomerAsync(companyId.Value, model);
            return Json(new { success = result.Success, message = result.Message, customerId = result.CustomerId });
        }
        catch (Exception ex)
        {
            return Json(new { success = false, message = ex.Message });
        }
    }

    [HttpPost]
    public async Task<IActionResult> BulkImport(IFormFile file)
    {
        var companyId = GetCurrentUserCompanyId();
        var userId = GetCurrentUserId();

        if (companyId == null || userId == null)
        {
            return Json(new { success = false, message = "Kullanıcı bilgileri bulunamadı." });
        }

        try
        {
            var result = await _customerService.StartBulkImportAsync(companyId.Value, userId, file);
            return Json(new { success = result.Success, message = result.Message, jobId = result.JobId });
        }
        catch (Exception ex)
        {
            return Json(new { success = false, message = ex.Message });
        }
    }

    [HttpGet]
    public async Task<IActionResult> GetImportProgress(int jobId)
    {
        var companyId = GetCurrentUserCompanyId();
        if (companyId == null)
        {
            return Json(new { success = false, message = "Şirket bilgisi bulunamadı." });
        }

        try
        {
            var progress = await _customerService.GetImportProgressAsync(jobId, companyId.Value);
            if (progress == null)
            {
                return Json(new { success = false, message = "Import işlemi bulunamadı." });
            }

            return Json(new { success = true, data = progress });
        }
        catch (Exception ex)
        {
            return Json(new { success = false, message = ex.Message });
        }
    }

    [HttpGet]
    public async Task<IActionResult> GetImportHistory()
    {
        var companyId = GetCurrentUserCompanyId();
        if (companyId == null)
        {
            return Json(new { success = false, message = "Şirket bilgisi bulunamadı." });
        }

        try
        {
            var history = await _customerService.GetImportHistoryAsync(companyId.Value);
            return Json(new { success = true, data = history });
        }
        catch (Exception ex)
        {
            return Json(new { success = false, message = ex.Message });
        }
    }

    [HttpGet]
    public IActionResult DownloadExcelTemplate()
    {
        try
        {
            var templateBytes = _customerService.GenerateExcelTemplate();
            return File(templateBytes, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "musteri-sablonu.xlsx");
        }
        catch (Exception ex)
        {
            return Json(new { success = false, message = ex.Message });
        }
    }
}