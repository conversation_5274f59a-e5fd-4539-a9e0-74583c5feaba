using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using PushDashboard.Data;
using PushDashboard.Models;
using PushDashboard.Services;
using PushDashboard.ViewModels;
using PushDashboard.Services.Integrations;

namespace PushDashboard.Controllers;

[Authorize]
public class StoreController : BaseController
{
    private readonly IStoreService _storeService;
    private readonly UserManager<ApplicationUser> _userManager;
    private readonly IModuleUsageService _moduleUsageService;
    private readonly IOrderStatusMappingService _orderStatusMappingService;
    private readonly ApplicationDbContext _context;
    private readonly ILogger<StoreController> _logger;
    private readonly EcommerceGiftVoucherFactory _giftVoucherFactory;

    public StoreController(
        IStoreService storeService,
        UserManager<ApplicationUser> userManager,
        IModuleUsageService moduleUsageService,
        IOrderStatusMappingService orderStatusMappingService,
        ApplicationDbContext context,
        ILogger<StoreController> logger,
        EcommerceGiftVoucherFactory giftVoucherFactory,
        IUserContextService userContextService)
        : base(userContextService)
    {
        _storeService = storeService;
        _userManager = userManager;
        _moduleUsageService = moduleUsageService;
        _orderStatusMappingService = orderStatusMappingService;
        _context = context;
        _logger = logger;
        _giftVoucherFactory = giftVoucherFactory;
    }

    public async Task<IActionResult> Index(int? categoryId = null)
    {
        var user = await _userManager.GetUserAsync(User);
        if (user == null)
        {
            return RedirectToAction("Login", "Account");
        }

        var storeData = await _storeService.GetStoreDataAsync(user.Id);

        // Filter by category if specified
        if (categoryId.HasValue)
        {
            storeData.Modules = storeData.Modules.Where(m =>
                storeData.Categories.Any(c => c.Id == categoryId.Value && c.Name == m.CategoryName)).ToList();
            storeData.AvailableModules = storeData.AvailableModules.Where(m =>
                storeData.Categories.Any(c => c.Id == categoryId.Value && c.Name == m.CategoryName)).ToList();
        }

        ViewData["SelectedCategoryId"] = categoryId;
        return View(storeData);
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> PurchaseModule([FromBody] StoreIndexViewModel.PurchaseModuleViewModel model)
    {
        if (!ModelState.IsValid)
        {
            var errors = ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage);
            return Json(new { success = false, message = string.Join(" ", errors) });
        }

        try
        {
            var user = await _userManager.GetUserAsync(User);
            if (user == null)
            {
                return Json(new { success = false, message = "Kullanıcı bulunamadı." });
            }

            // Check if user already owns the module
            var isOwned = await _storeService.IsModuleOwnedAsync(model.ModuleId, user.Id);
            if (isOwned)
            {
                return Json(new { success = false, message = "Bu modüle zaten sahipsiniz." });
            }

            // Get current user credit balance for pre-check
            var currentBalance = await _storeService.GetUserCreditBalanceAsync(user.Id);
            if (currentBalance < model.Price)
            {
                return Json(new {
                    success = false,
                    message = $"Yetersiz kredi bakiyesi. Gerekli: ₺{model.Price:N2}, Mevcut: ₺{currentBalance:N2}",
                    errorType = "insufficient_balance",
                    requiredAmount = model.Price,
                    currentBalance = currentBalance,
                    missingAmount = model.Price - currentBalance
                });
            }

            // Generate transaction ID
            var transactionId = $"TXN_{DateTime.UtcNow:yyyyMMddHHmmss}_{user.Id}_{model.ModuleId}";

            // Attempt purchase
            var (success, message) = await _storeService.PurchaseModuleAsync(
                model.ModuleId,
                user.Id,
                model.Price,
                transactionId);

            if (success)
            {
                _logger.LogInformation("User {UserId} successfully purchased module {ModuleId} for {Price}",
                    user.Id, model.ModuleId, model.Price);

                // Get updated balance for response
                var newBalance = await _storeService.GetUserCreditBalanceAsync(user.Id);

                return Json(new {
                    success = true,
                    message = message,
                    transactionId = transactionId,
                    newBalance = newBalance,
                    purchasedAmount = model.Price
                });
            }
            else
            {
                return Json(new { success = false, message = message });
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error purchasing module {ModuleId}", model.ModuleId);
            return Json(new { success = false, message = "Satın alma işlemi sırasında bir hata oluştu." });
        }
    }
    [HttpGet]
    public async Task<IActionResult> GetModuleSettings(int moduleId)
    {
        try
        {
            var user = await _userManager.GetUserAsync(User);
            if (user == null)
            {
                return Json(new { success = false, message = "Kullanıcı bulunamadı." });
            }

            var (settings, moduleInfo) = await _storeService.GetModuleSettingsAsync(moduleId, user.Id);

            if (moduleInfo == null)
            {
                return Json(new { success = false, message = "Modül bulunamadı veya size ait değil." });
            }

            // Doğum günü modülü için maliyet bilgilerini ve e-ticaret platform bilgisini ekle
            if (moduleInfo.Name.Contains("Doğum Günü") || moduleInfo.Name.Contains("Birthday"))
            {
                try
                {
                    var emailCost = await _moduleUsageService.GetModuleUsageCostAsync(user.Id, moduleInfo.Id, "email");
                    var smsCost = await _moduleUsageService.GetModuleUsageCostAsync(user.Id, moduleInfo.Id, "sms");
                    var whatsappCost = await _moduleUsageService.GetModuleUsageCostAsync(user.Id, moduleInfo.Id, "whatsapp");

                    // Maliyet bilgilerini settings'e ekle
                    if (settings == null)
                        settings = new Dictionary<string, object>();

                    settings["emailCost"] = emailCost;
                    settings["smsCost"] = smsCost;
                    settings["whatsappCost"] = whatsappCost;

                    // E-ticaret platform bilgisini ekle
                    var activePlatformName = await _giftVoucherFactory.GetActivePlatformNameAsync(user.CompanyId ?? Guid.Empty);
                    var hasEcommerceIntegration = await _giftVoucherFactory.HasActiveEcommerceIntegrationAsync(user.CompanyId ?? Guid.Empty);

                    settings["activePlatformName"] = activePlatformName ?? "";
                    settings["hasEcommerceIntegration"] = hasEcommerceIntegration;
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Error getting usage costs for birthday module {ModuleId}", moduleInfo.Id);
                    // Varsayılan maliyetler
                    if (settings == null)
                        settings = new Dictionary<string, object>();

                    settings["emailCost"] = 0.50m;
                    settings["smsCost"] = 2.00m;
                    settings["whatsappCost"] = 1.00m;
                    settings["activePlatformName"] = "";
                    settings["hasEcommerceIntegration"] = false;
                }
            }

            return Json(new
            {
                success = true,
                settings = settings,
                moduleInfo = new
                {
                    id = moduleInfo.Id,
                    name = moduleInfo.Name,
                    description = moduleInfo.Description
                }
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting module settings for module {ModuleId}", moduleId);
            return Json(new { success = false, message = "Modül ayarları yüklenirken bir hata oluştu." });
        }
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> SaveModuleSettings(int moduleId, [FromBody] Dictionary<string, object> settings)
    {
        try
        {
            var user = await _userManager.GetUserAsync(User);
            if (user == null)
            {
                return Json(new { success = false, message = "Kullanıcı bulunamadı." });
            }

            var success = await _storeService.SaveModuleSettingsAsync(moduleId, user.Id, settings);

            if (success)
            {
                _logger.LogInformation("User {UserId} updated settings for module {ModuleId}", user.Id, moduleId);
                return Json(new { success = true, message = "Ayarlar başarıyla kaydedildi." });
            }
            else
            {
                return Json(new { success = false, message = "Modül bulunamadı veya size ait değil." });
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error saving module settings for module {ModuleId}", moduleId);
            return Json(new { success = false, message = "Ayarlar kaydedilirken bir hata oluştu." });
        }
    }


    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> ResetModuleSettings(int moduleId)
    {
        try
        {
            var user = await _userManager.GetUserAsync(User);
            if (user == null)
            {
                return Json(new { success = false, message = "Kullanıcı bulunamadı." });
            }

            var success = await _storeService.ResetModuleSettingsToDefaultAsync(moduleId, user.Id);

            if (success)
            {
                _logger.LogInformation("User {UserId} reset settings to default for module {ModuleId}", user.Id, moduleId);
                return Json(new { success = true, message = "Ayarlar varsayılan değerlere sıfırlandı." });
            }
            else
            {
                return Json(new { success = false, message = "Modül bulunamadı veya size ait değil." });
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error resetting module settings to default for module {ModuleId}", moduleId);
            return Json(new { success = false, message = "Ayarlar sıfırlanırken bir hata oluştu." });
        }
    }

    public async Task<IActionResult> GetTransactionHistory(int page = 1, int pageSize = 10)
    {
        try
        {
            var userId = _userManager.GetUserId(User);
            if (string.IsNullOrEmpty(userId))
            {
                return Json(new { success = false, message = "Kullanıcı bulunamadı." });
            }

            var result = await _storeService.GetTransactionHistoryAsync(userId, page, pageSize);
            return Json(new { success = true, data = result });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting transaction history for user");
            return Json(new { success = false, message = "İşlem geçmişi yüklenirken bir hata oluştu." });
        }
    }

    [HttpGet]
    public async Task<IActionResult> GetModuleDetails(int moduleId)
    {
        try
        {
            var user = await _userManager.GetUserAsync(User);
            if (user == null)
            {
                return Json(new { success = false, message = "Kullanıcı bulunamadı." });
            }

            var moduleDetails = await _storeService.GetModuleDetailsAsync(moduleId, user.Id);

            if (moduleDetails == null)
            {
                return Json(new { success = false, message = "Modül bulunamadı." });
            }

            return Json(new {
                success = true,
                moduleDetails = moduleDetails
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting module details for module {ModuleId}", moduleId);
            return Json(new { success = false, message = "Modül detayları yüklenirken bir hata oluştu." });
        }
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> ToggleModuleStatus(int moduleId)
    {
        try
        {
            var user = await _userManager.GetUserAsync(User);
            if (user == null)
            {
                return Json(new { success = false, message = "Kullanıcı bulunamadı." });
            }

            var (success, message) = await _storeService.ToggleModuleStatusAsync(moduleId, user.Id);

            if (success)
            {
                _logger.LogInformation("User {UserId} toggled status for module {ModuleId}", user.Id, moduleId);
                return Json(new { success = true, message = message });
            }
            else
            {
                return Json(new { success = false, message = message });
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error toggling module status for module {ModuleId}", moduleId);
            return Json(new { success = false, message = "Modül durumu güncellenirken bir hata oluştu." });
        }
    }

    #region Order Status Mapping Endpoints

    [HttpGet]
    public async Task<IActionResult> GetOrderStatusMappingSettings(int moduleId)
    {
        try
        {
            var user = await _userManager.GetUserAsync(User);
            if (user?.Company == null)
            {
                return Json(new { success = false, message = "Kullanıcı veya şirket bulunamadı." });
            }

            // Modülün "Sipariş Durumu Bildirimleri" olduğunu kontrol et
            var moduleInfo = await _storeService.GetModuleInfoAsync(moduleId);
            if (moduleInfo == null || moduleInfo.Name != "Sipariş Durumu Bildirimleri")
            {
                return Json(new { success = false, message = "Bu özellik sadece Sipariş Durumu Bildirimleri modülü için kullanılabilir." });
            }

            // Şirketin aktif e-ticaret entegrasyonunu bul
            var integrationType = await GetActiveIntegrationTypeAsync(user.Company.Id);
            _logger.LogInformation("Integration type for company {CompanyId}: {IntegrationType}", user.Company.Id, integrationType);

            if (string.IsNullOrEmpty(integrationType))
            {
                return Json(new { success = false, message = "Aktif e-ticaret entegrasyonu bulunamadı." });
            }

            // Mevcut mapping'leri al
            var currentMappings = await _orderStatusMappingService.GetMappingsAsync(user.Company.Id, integrationType);
            _logger.LogInformation("Current mappings count: {Count}", currentMappings.Count);

            // E-ticaret platform durumlarını al
            var externalStatuses = await _orderStatusMappingService.GetExternalStatusesAsync(user.Company.Id, integrationType);
            _logger.LogInformation("External statuses count: {Count}", externalStatuses.Count);

            // Sistem durumları
            var internalStatuses = GetInternalOrderStatuses();
            _logger.LogInformation("Internal statuses count: {Count}", internalStatuses.Count);

            var settings = new OrderStatusMappingSettingsViewModel
            {
                IntegrationType = integrationType,
                ExternalStatuses = externalStatuses,
                InternalStatuses = internalStatuses,
                CurrentMappings = currentMappings
            };

            return Json(new { success = true, settings = settings });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting order status mapping settings for module {ModuleId}", moduleId);
            return Json(new { success = false, message = "Sipariş durumu eşleştirme ayarları yüklenirken bir hata oluştu." });
        }
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> SaveOrderStatusMappings(int moduleId, [FromBody] List<OrderStatusMappingViewModel> mappings)
    {
        try
        {
            var user = await _userManager.GetUserAsync(User);
            if (user?.Company == null)
            {
                return Json(new { success = false, message = "Kullanıcı veya şirket bulunamadı." });
            }

            // Modülün "Sipariş Durumu Bildirimleri" olduğunu kontrol et
            var moduleInfo = await _storeService.GetModuleInfoAsync(moduleId);
            if (moduleInfo == null || moduleInfo.Name != "Sipariş Durumu Bildirimleri")
            {
                return Json(new { success = false, message = "Bu özellik sadece Sipariş Durumu Bildirimleri modülü için kullanılabilir." });
            }

            // Şirketin aktif e-ticaret entegrasyonunu bul
            var integrationType = await GetActiveIntegrationTypeAsync(user.Company.Id);
            if (string.IsNullOrEmpty(integrationType))
            {
                return Json(new { success = false, message = "Aktif e-ticaret entegrasyonu bulunamadı." });
            }

            var success = await _orderStatusMappingService.SaveMappingsAsync(user.Company.Id, integrationType, mappings);

            if (success)
            {
                _logger.LogInformation("User {UserId} updated order status mappings for module {ModuleId}", user.Id, moduleId);
                return Json(new { success = true, message = "Sipariş durumu eşleştirmeleri başarıyla kaydedildi." });
            }
            else
            {
                return Json(new { success = false, message = "Eşleştirmeler kaydedilemedi." });
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error saving order status mappings for module {ModuleId}", moduleId);
            return Json(new { success = false, message = "Sipariş durumu eşleştirmeleri kaydedilirken bir hata oluştu." });
        }
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> CreateDefaultOrderStatusMappings(int moduleId)
    {
        try
        {
            var user = await _userManager.GetUserAsync(User);
            if (user?.Company == null)
            {
                return Json(new { success = false, message = "Kullanıcı veya şirket bulunamadı." });
            }

            // Şirketin aktif e-ticaret entegrasyonunu bul
            var integrationType = await GetActiveIntegrationTypeAsync(user.Company.Id);
            if (string.IsNullOrEmpty(integrationType))
            {
                return Json(new { success = false, message = "Aktif e-ticaret entegrasyonu bulunamadı." });
            }

            var success = await _orderStatusMappingService.CreateDefaultMappingsAsync(user.Company.Id, integrationType);

            if (success)
            {
                _logger.LogInformation("User {UserId} created default order status mappings for module {ModuleId}", user.Id, moduleId);
                return Json(new { success = true, message = "Varsayılan eşleştirmeler başarıyla oluşturuldu." });
            }
            else
            {
                return Json(new { success = false, message = "Varsayılan eşleştirmeler oluşturulamadı." });
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating default order status mappings for module {ModuleId}", moduleId);
            return Json(new { success = false, message = "Varsayılan eşleştirmeler oluşturulurken bir hata oluştu." });
        }
    }

    #endregion

    #region Helper Methods

    private async Task<string?> GetActiveIntegrationTypeAsync(Guid companyId)
    {
        try
        {
            _logger.LogInformation("Looking for active ecommerce integration for company {CompanyId}", companyId);

            // Şirketin aktif e-ticaret entegrasyonunu bul
            var integration = await _context.CompanyIntegrations
                .Include(ci => ci.Integration)
                .FirstOrDefaultAsync(ci => ci.CompanyId == companyId &&
                                         ci.Integration.Type == "ecommerce" &&
                                         ci.IsActive);

            if (integration?.Integration != null)
            {
                _logger.LogInformation("Found active integration: {IntegrationName} for company {CompanyId}",
                    integration.Integration.Name, companyId);

                // Integration adına göre platform türünü belirle
                return integration.Integration.Name switch
                {
                    "Ticimax" => "Ticimax",
                    "Shopify" => "Shopify",
                    "WooCommerce" => "WooCommerce",
                    _ => integration.Integration.Name
                };
            }

            _logger.LogWarning("No active ecommerce integration found for company {CompanyId}", companyId);
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting active integration type for company {CompanyId}", companyId);
            return null;
        }
    }

    private List<OrderStatusSelectItem> GetInternalOrderStatuses()
    {
        return new List<OrderStatusSelectItem>
        {
            new() { Value = "1", Text = "Beklemede" },
            new() { Value = "2", Text = "Onaylandı" },
            new() { Value = "3", Text = "Hazırlanıyor" },
            new() { Value = "4", Text = "Kargoya Verildi" },
            new() { Value = "5", Text = "Teslim Edildi" },
            new() { Value = "6", Text = "İptal Edildi" },
            new() { Value = "7", Text = "İade Edildi" }
        };
    }

    #endregion
}
