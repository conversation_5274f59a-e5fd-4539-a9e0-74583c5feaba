using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using PushDashboard.Models;
using PushDashboard.Services;
using PushDashboard.ViewModels.SocialProof;
using System.Text.Json;

namespace PushDashboard.Controllers;

[Authorize]
public class SocialProofController : Controller
{
    private readonly ISocialProofService _socialProofService;
    private readonly UserManager<ApplicationUser> _userManager;
    private readonly ILogger<SocialProofController> _logger;

    public SocialProofController(
        ISocialProofService socialProofService,
        UserManager<ApplicationUser> userManager,
        ILogger<SocialProofController> logger)
    {
        _socialProofService = socialProofService;
        _userManager = userManager;
        _logger = logger;
    }

    public async Task<IActionResult> Index()
    {
        var user = await _userManager.GetUserAsync(User);
        if (user?.CompanyId == null)
        {
            return RedirectToAction("Login", "Account");
        }

        var settings = await _socialProofService.GetOrCreateSettingsAsync(user.CompanyId.Value, user.Id);
        
        var viewModel = new SocialProofIndexViewModel
        {
            Settings = settings,
            ScriptUrl = Url.Action("Script", "SocialProofApi", new { companyId = user.CompanyId }, Request.Scheme)
        };

        return View(viewModel);
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> UpdateSettings([FromBody] UpdateSocialProofSettingsViewModel model)
    {
        if (!ModelState.IsValid)
        {
            var errors = ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage);
            return Json(new { success = false, message = string.Join(" ", errors) });
        }

        try
        {
            var user = await _userManager.GetUserAsync(User);
            if (user?.CompanyId == null)
            {
                return Json(new { success = false, message = "Kullanıcı bulunamadı." });
            }

            var settings = new SocialProofSettings
            {
                CompanyId = user.CompanyId.Value,
                IsActive = model.IsActive,
                ViewersMin = model.ViewersMin,
                ViewersMax = model.ViewersMax,
                FollowersMin = model.FollowersMin,
                FollowersMax = model.FollowersMax,
                BuyersMin = model.BuyersMin,
                BuyersMax = model.BuyersMax,
                UpdateInterval = model.UpdateInterval,
                DisplayDuration = model.DisplayDuration,
                TextTemplates = new SocialProofTextTemplates
                {
                    ViewersTemplate = model.ViewersTemplate,
                    FollowersTemplate = model.FollowersTemplate,
                    BuyersTemplate = model.BuyersTemplate
                },
                DisplaySettings = new SocialProofDisplaySettings
                {
                    Position = model.Position,
                    PrimaryColor = model.PrimaryColor,
                    TextColor = model.TextColor,
                    BackgroundColor = model.BackgroundColor,
                    BorderRadius = model.BorderRadius,
                    FontSize = model.FontSize,
                    EnableAnimation = model.EnableAnimation,
                    EnableShadow = model.EnableShadow
                }
            };

            var success = await _socialProofService.UpdateSettingsAsync(user.CompanyId.Value, settings, user.Id);

            if (success)
            {
                return Json(new { success = true, message = "Ayarlar başarıyla güncellendi." });
            }
            else
            {
                return Json(new { success = false, message = "Ayarlar güncellenirken bir hata oluştu." });
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating social proof settings");
            return Json(new { success = false, message = "Bir hata oluştu. Lütfen tekrar deneyin." });
        }
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> ToggleStatus()
    {
        try
        {
            var user = await _userManager.GetUserAsync(User);
            if (user?.CompanyId == null)
            {
                return Json(new { success = false, message = "Kullanıcı bulunamadı." });
            }

            var success = await _socialProofService.ToggleActiveStatusAsync(user.CompanyId.Value, user.Id);

            if (success)
            {
                return Json(new { success = true, message = "Durum başarıyla güncellendi." });
            }
            else
            {
                return Json(new { success = false, message = "Durum güncellenirken bir hata oluştu." });
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error toggling social proof status");
            return Json(new { success = false, message = "Bir hata oluştu. Lütfen tekrar deneyin." });
        }
    }

    [HttpGet]
    public async Task<IActionResult> Preview()
    {
        var user = await _userManager.GetUserAsync(User);
        if (user?.CompanyId == null)
        {
            return Json(new { success = false, message = "Kullanıcı bulunamadı." });
        }

        var config = await _socialProofService.GetScriptConfigAsync(user.CompanyId.Value);
        
        return Json(new { 
            success = true, 
            config = config,
            scriptUrl = Url.Action("Script", "SocialProofApi", new { companyId = user.CompanyId }, Request.Scheme)
        });
    }
}
