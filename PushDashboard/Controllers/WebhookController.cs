using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using PushDashboard.CustomServis;
using PushDashboard.Data;
using PushDashboard.Models;
using PushDashboard.Services.Integrations;
using PushDashboard.Services.Notifications;
using PushDashboard.Services;
using System.Text.Json;
using PushDashboard.Services.Modules.FirstOrder;
using PushDashboard.DTOs;
namespace PushDashboard.Controllers;

[ApiController]
[Route("webhook")]
public class WebhookController : ControllerBase
{
    private readonly ApplicationDbContext _context;
    private readonly ILogger<WebhookController> _logger;
    private readonly IEcommerceWebhookFactory _webhookFactory;
    private readonly IFirstOrderModuleService _firstOrderModuleService;
    private readonly IOrderStatusNotificationService _orderStatusNotificationService;
    private readonly IOrderStatusMappingService _orderStatusMappingService;
    private readonly ICommentService _commentService;
    private readonly ITrendyolScraperService _trendyolScraperService;

    public WebhookController(
        ApplicationDbContext context,
        ILogger<WebhookController> logger,
        IEcommerceWebhookFactory webhookFactory,
        IFirstOrderModuleService firstOrderModuleService,
        ICommentService commentService,
        IOrderStatusNotificationService orderStatusNotificationService,
        IOrderStatusMappingService orderStatusMappingService,
        ITrendyolScraperService trendyolScraperService)
    {
        _context = context;
        _logger = logger;
        _webhookFactory = webhookFactory;
        _firstOrderModuleService = firstOrderModuleService;
        _orderStatusNotificationService = orderStatusNotificationService;
        _orderStatusMappingService = orderStatusMappingService;
        _commentService = commentService;
        _trendyolScraperService = trendyolScraperService;
    }

    /// <summary>
    /// Genel e-ticaret webhook endpoint'i
    /// </summary>
    [HttpPost("{platform}/{companyId}/{webhookType}")]
    public async Task<IActionResult> HandleEcommerceWebhook(string platform, Guid companyId, string webhookType, [FromBody] JsonElement payload)
    {
        try
        {
            _logger.LogInformation("Received {Platform} webhook {WebhookType} for company {CompanyId}: {Payload}",
                platform, webhookType, companyId, payload.ToString());

            // Platform destekleniyor mu kontrol et
            if (!_webhookFactory.IsPlatformSupported(platform))
            {
                _logger.LogWarning("Unsupported platform {Platform} for webhook", platform);
                return BadRequest("Desteklenmeyen platform");
            }

            // Şirketin bu platform entegrasyonu var mı kontrol et
            var companyIntegration = await _context.CompanyIntegrations
                .Include(ci => ci.Integration)
                .FirstOrDefaultAsync(ci => ci.CompanyId == companyId &&
                                         ci.Integration.Name == platform &&
                                         ci.Integration.Type == "ecommerce" &&
                                         ci.IsActive && ci.IsConfigured);

            if (companyIntegration == null)
            {
                _logger.LogWarning("No active {Platform} integration found for company {CompanyId}", platform, companyId);
                return BadRequest("Entegrasyon bulunamadı");
            }

            // Webhook'u işle
            await ProcessWebhookByType(platform, companyId, webhookType, payload);

            return Ok(new { success = true, message = "Webhook işlendi" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing {Platform} webhook {WebhookType} for company {CompanyId}",
                platform, webhookType, companyId);
            return StatusCode(500, new { success = false, message = "Webhook işlenirken hata oluştu" });
        }
    }

    private async Task ProcessWebhookByType(string platform, Guid companyId, string webhookType, JsonElement payload)
    {
        // Platform ve webhook türüne göre işleme yönlendir
        if (platform.Equals("Ticimax", StringComparison.OrdinalIgnoreCase))
        {
            await ProcessTicimaxWebhook(companyId, webhookType, payload);
        }
        // Gelecekte diğer platformlar buraya eklenecek
        // else if (platform.Equals("IKAS", StringComparison.OrdinalIgnoreCase))
        // {
        //     await ProcessIkasWebhook(companyId, webhookType, payload);
        // }
        else
        {
            _logger.LogWarning("Webhook processing not implemented for platform {Platform}", platform);
            throw new NotImplementedException($"Webhook işleme {platform} platformu için henüz uygulanmadı");
        }
    }

    private async Task ProcessTicimaxWebhook(Guid companyId, string webhookType, JsonElement payload)
    {
        switch (webhookType)
        {
            case "UyeKayitOldu":
                await ProcessCustomerRegistrationWebhook(companyId, payload);
                break;
            case "UyeBilgileriGuncellendi":
                await ProcessCustomerUpdateWebhook(companyId, payload);
                break;
            case "SiparisOlustu":
                await ProcessOrderCreationWebhook(companyId, payload);
                break;
            case "SiparisDurumuDegistirildi":
                await ProcessOrderStatusChangeWebhook(companyId, payload);
                break;
            default:
                _logger.LogWarning("Unknown Ticimax webhook type: {WebhookType}", webhookType);
                throw new ArgumentException($"Bilinmeyen webhook türü: {webhookType}");
        }
    }

    private async Task ProcessCustomerRegistrationWebhook(Guid companyId, JsonElement payload)
    {
        try
        {
            // Ticimax'tan gelen müşteri verilerini parse et
            if (payload.TryGetProperty("UyeID", out var uyeIdElement) && uyeIdElement.TryGetInt32(out var uyeId))
            {
                // Müşteri zaten sistemde var mı kontrol et
                var existingCustomer = await _context.Customers
                    .FirstOrDefaultAsync(c => c.ExternalId == uyeId && c.CompanyId == companyId);

                if (existingCustomer == null)
                {
                    // Yeni müşteri oluştur
                    var customer = new Customer
                    {
                        CompanyId = companyId,
                        ExternalId = uyeId,
                        FirstName = GetStringProperty(payload, "Ad") ?? "",
                        LastName = GetStringProperty(payload, "Soyad") ?? "",
                        Email = GetStringProperty(payload, "Email") ?? "",
                        Phone = GetStringProperty(payload, "Telefon") ?? "",
                        CreatedAt = DateTime.UtcNow,
                        UpdatedAt = DateTime.UtcNow,
                        IsActive = true
                    };

                    // Doğum tarihi varsa ekle
                    if (payload.TryGetProperty("DogumTarihi", out var dogumTarihiElement) &&
                        dogumTarihiElement.TryGetDateTime(out var dogumTarihi))
                    {
                        customer.BirthDate = dogumTarihi;
                    }

                    _context.Customers.Add(customer);
                    await _context.SaveChangesAsync();

                    _logger.LogInformation("New customer created from webhook: {CustomerId} (ExternalId: {ExternalId})",
                        customer.Id, customer.ExternalId);
                }
                else
                {
                    _logger.LogInformation("Customer already exists: {CustomerId} (ExternalId: {ExternalId})",
                        existingCustomer.Id, existingCustomer.ExternalId);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing customer registration webhook for company {CompanyId}", companyId);
            throw;
        }
    }

    private async Task ProcessCustomerUpdateWebhook(Guid companyId, JsonElement payload)
    {
        try
        {
            // Ticimax'tan gelen müşteri güncelleme verilerini parse et
            if (payload.TryGetProperty("UyeID", out var uyeIdElement) && uyeIdElement.TryGetInt32(out var uyeId))
            {
                var existingCustomer = await _context.Customers
                    .FirstOrDefaultAsync(c => c.ExternalId == uyeId && c.CompanyId == companyId);

                if (existingCustomer != null)
                {
                    // Müşteri bilgilerini güncelle
                    existingCustomer.FirstName = GetStringProperty(payload, "Ad") ?? existingCustomer.FirstName;
                    existingCustomer.LastName = GetStringProperty(payload, "Soyad") ?? existingCustomer.LastName;
                    existingCustomer.Email = GetStringProperty(payload, "Email") ?? existingCustomer.Email;
                    existingCustomer.Phone = GetStringProperty(payload, "Telefon") ?? existingCustomer.Phone;
                    existingCustomer.UpdatedAt = DateTime.UtcNow;

                    // Doğum tarihi varsa güncelle
                    if (payload.TryGetProperty("DogumTarihi", out var dogumTarihiElement) &&
                        dogumTarihiElement.TryGetDateTime(out var dogumTarihi))
                    {
                        existingCustomer.BirthDate = dogumTarihi;
                    }

                    await _context.SaveChangesAsync();

                    _logger.LogInformation("Customer updated from webhook: {CustomerId} (ExternalId: {ExternalId})",
                        existingCustomer.Id, existingCustomer.ExternalId);
                }
                else
                {
                    _logger.LogWarning("Customer not found for update webhook: ExternalId {ExternalId}, Company {CompanyId}",
                        uyeId, companyId);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing customer update webhook for company {CompanyId}", companyId);
            throw;
        }
    }

    private async Task ProcessOrderCreationWebhook(Guid companyId, JsonElement payload)
    {
        try
        {
            _logger.LogInformation("Processing order creation webhook for company {CompanyId}: {Payload}",
                companyId, payload.ToString());

            // Sipariş bilgilerini parse et
            if (payload.TryGetProperty("UyeID", out var uyeIdElement) && uyeIdElement.TryGetInt32(out var uyeId))
            {
                // Müşteri sistemde var mı kontrol et
                var customer = await _context.Customers
                    .FirstOrDefaultAsync(c => c.ExternalId == uyeId && c.CompanyId == companyId);

                if (customer != null)
                {
                    _logger.LogInformation("Found customer {CustomerId} (ExternalId: {ExternalId}) for order webhook",
                        customer.Id, customer.ExternalId);

                    // İlk sipariş kontrolü ve bildirim gönderimi
                    var isFirstOrder = await _firstOrderModuleService.IsFirstOrderAsync(companyId, customer.Id);

                    if (isFirstOrder)
                    {
                        _logger.LogInformation("This is the first order for customer {CustomerId}, sending first order notification",
                            customer.Id);

                        // İlk alışveriş mesajı gönder
                        var (success, message, notificationsSent, totalCost) = await _firstOrderModuleService
                            .SendFirstOrderNotificationAsync(companyId, customer.Id, "system");

                        if (success)
                        {
                            _logger.LogInformation("First order notification sent successfully for customer {CustomerId}: {Message}. " +
                                "Notifications sent: {NotificationsSent}, Total cost: {TotalCost:C}",
                                customer.Id, message, notificationsSent, totalCost);
                        }
                        else
                        {
                            _logger.LogWarning("Failed to send first order notification for customer {CustomerId}: {Message}",
                                customer.Id, message);
                        }
                    }
                    else
                    {
                        _logger.LogInformation("Customer {CustomerId} has previous orders, skipping first order notification",
                            customer.Id);
                    }

                    // Müşterinin son alışveriş tarihini güncelle
                    customer.UpdatedAt = DateTime.UtcNow;
                    await _context.SaveChangesAsync();
                }
                else
                {
                    _logger.LogWarning("Customer with ExternalId {ExternalId} not found in company {CompanyId} for order webhook",
                        uyeId, companyId);
                }
            }
            else
            {
                _logger.LogWarning("UyeID not found in order creation webhook payload for company {CompanyId}", companyId);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing order creation webhook for company {CompanyId}", companyId);
            throw;
        }
    }

    private async Task ProcessOrderStatusChangeWebhook(Guid companyId, JsonElement payload)
    {
        try
        {
            _logger.LogInformation("Processing order status change webhook for company {CompanyId}: {Payload}",
                companyId, payload.ToString());

            // Sipariş durumu değişikliği bilgilerini parse et
            var orderData = ParseOrderStatusChangeData(payload);

            if (orderData != null)
            {
                // Sipariş durumu değişikliğini kaydet
                await SaveOrderStatusChange(companyId, orderData);

                // Bildirim gönderme işlemini başlat (arka planda)
                _ = Task.Run(async () =>
                {
                    try
                    {
                        await ProcessOrderStatusNotifications(companyId, orderData);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error processing order status notifications for company {CompanyId}, order {OrderId}",
                            companyId, orderData.OrderId);
                    }
                });
            }

            _logger.LogInformation("Order status change webhook processed successfully for company {CompanyId}", companyId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing order status change webhook for company {CompanyId}", companyId);
            throw;
        }
    }

    private OrderStatusChangeData? ParseOrderStatusChangeData(JsonElement payload)
    {
        try
        {
            var orderId = GetStringProperty(payload, "SiparisID");
            var orderNumber = GetStringProperty(payload, "SiparisNo");
            var newStatus = GetStringProperty(payload, "YeniDurum");
            var oldStatus = GetStringProperty(payload, "EskiDurum");
            var customerEmail = GetStringProperty(payload, "MusteriEmail");
            var customerName = GetStringProperty(payload, "MusteriAdi");
            var customerPhone = GetStringProperty(payload, "MusteriTelefon");

            if (string.IsNullOrEmpty(orderId) || string.IsNullOrEmpty(newStatus) || string.IsNullOrEmpty(customerEmail))
            {
                _logger.LogWarning("Required fields missing in order status change webhook payload");
                return null;
            }

            var orderData = new OrderStatusChangeData
            {
                OrderId = orderId,
                OrderNumber = orderNumber,
                NewStatus = newStatus,
                OldStatus = oldStatus,
                CustomerEmail = customerEmail,
                CustomerName = customerName,
                CustomerPhone = customerPhone,
                StatusChangedAt = DateTime.UtcNow,
                WebhookPayload = payload.ToString()
            };

            // Sipariş tutarı varsa ekle
            if (payload.TryGetProperty("SiparisTutari", out var amountElement) && amountElement.TryGetDecimal(out var amount))
            {
                orderData.OrderAmount = amount;
            }

            // Para birimi varsa ekle
            orderData.OrderCurrency = GetStringProperty(payload, "ParaBirimi") ?? "TRY";

            return orderData;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error parsing order status change data from webhook payload");
            return null;
        }
    }

    private async Task SaveOrderStatusChange(Guid companyId, OrderStatusChangeData orderData)
    {
        try
        {
            // Ticimax durumunu sistem durumuna eşleştir
            var mappedStatus = await GetMappedOrderStatusAsync(companyId, orderData.NewStatus);
            var mappedOldStatus = !string.IsNullOrEmpty(orderData.OldStatus)
                ? await GetMappedOrderStatusAsync(companyId, orderData.OldStatus)
                : null;

            var changeLog = new OrderStatusChangeLog
            {
                CompanyId = companyId,
                OrderId = orderData.OrderId,
                OrderNumber = orderData.OrderNumber,
                CustomerEmail = orderData.CustomerEmail,
                CustomerName = orderData.CustomerName,
                CustomerPhone = orderData.CustomerPhone,
                OldStatus = mappedOldStatus,
                NewStatus = mappedStatus,
                NewStatusDisplayName = GetOrderStatusDisplayName(mappedStatus),
                OrderAmount = orderData.OrderAmount,
                OrderCurrency = orderData.OrderCurrency,
                StatusChangedAt = orderData.StatusChangedAt,
                WebhookPayload = orderData.WebhookPayload,
                CreatedAt = DateTime.UtcNow
            };

            _context.OrderStatusChangeLogs.Add(changeLog);
            await _context.SaveChangesAsync();

            _logger.LogInformation("Order status change saved: Order {OrderId}, External Status {ExternalOldStatus} -> {ExternalNewStatus}, Mapped Status {MappedOldStatus} -> {MappedNewStatus}",
                orderData.OrderId, orderData.OldStatus, orderData.NewStatus, mappedOldStatus, mappedStatus);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error saving order status change for company {CompanyId}, order {OrderId}",
                companyId, orderData.OrderId);
            throw;
        }
    }

    private async Task ProcessOrderStatusNotifications(Guid companyId, OrderStatusChangeData orderData)
    {
        try
        {
            // 1. Müşterinin "Sipariş Durumu Bildirimleri" modülü var mı kontrol et
            var hasOrderStatusModule = await _context.CompanyModules
                .Include(cm => cm.Module)
                .AnyAsync(cm => cm.CompanyId == companyId &&
                              cm.Module.Name == "Sipariş Durumu Bildirimleri" &&
                              cm.IsActive &&
                              (!cm.ExpiresAt.HasValue || cm.ExpiresAt.Value > DateTime.UtcNow));

            if (!hasOrderStatusModule)
            {
                _logger.LogInformation("Company {CompanyId} does not have active Order Status Notifications module, skipping notification for order {OrderId}",
                    companyId, orderData.OrderId);
                return;
            }

            // 2. Ticimax durumunu sistem durumuna eşleştir
            var mappedStatus = await GetMappedOrderStatusAsync(companyId, orderData.NewStatus);

            if (string.IsNullOrEmpty(mappedStatus))
            {
                _logger.LogWarning("No status mapping found for external status {ExternalStatus} in company {CompanyId}, order {OrderId}",
                    orderData.NewStatus, companyId, orderData.OrderId);
                return;
            }

            // 3. Sipariş durumları sayfasında bu durum aktif mi kontrol et
            var orderStatusNotification = await _context.OrderStatusNotifications
                .FirstOrDefaultAsync(osn => osn.CompanyId == companyId &&
                                          osn.OrderStatus == mappedStatus &&
                                          osn.IsActive);

            if (orderStatusNotification == null)
            {
                _logger.LogInformation("Order status {MappedStatus} is not active for notifications in company {CompanyId}, skipping notification for order {OrderId}",
                    mappedStatus, companyId, orderData.OrderId);
                return;
            }

            // 4. OrderStatusChangeLog'u bul (eşleştirilmiş durumla)
            var orderStatusChangeLog = await _context.OrderStatusChangeLogs
                .FirstOrDefaultAsync(oscl => oscl.CompanyId == companyId &&
                                           oscl.OrderId == orderData.OrderId &&
                                           oscl.NewStatus == mappedStatus &&
                                           oscl.StatusChangedAt >= DateTime.UtcNow.AddMinutes(-5)); // Son 5 dakika içinde oluşturulan

            if (orderStatusChangeLog == null)
            {
                _logger.LogWarning("OrderStatusChangeLog not found for order {OrderId}, external status {ExternalStatus}, mapped status {MappedStatus} in company {CompanyId}",
                    orderData.OrderId, orderData.NewStatus, mappedStatus, companyId);
                return;
            }

            // 5. Tüm kontroller geçti, bildirim gönderme servisini çağır
            _logger.LogInformation("All checks passed, processing order status notification for company {CompanyId}, order {OrderId}, status {MappedStatus}",
                companyId, orderData.OrderId, mappedStatus);

            await _orderStatusNotificationService.ProcessOrderStatusNotificationAsync(companyId, orderStatusChangeLog);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing order status notifications for company {CompanyId}, order {OrderId}",
                companyId, orderData.OrderId);
        }
    }

    private async Task<string?> GetMappedOrderStatusAsync(Guid companyId, string externalStatus)
    {
        try
        {
            // Şirketin Ticimax entegrasyonunu bul
            var integration = await _context.CompanyIntegrations
                .Include(ci => ci.Integration)
                .FirstOrDefaultAsync(ci => ci.CompanyId == companyId &&
                                         ci.Integration.Name == "Ticimax" &&
                                         ci.Integration.Type == "ecommerce" &&
                                         ci.IsActive);

            if (integration == null)
            {
                _logger.LogWarning("No active Ticimax integration found for company {CompanyId}", companyId);
                return null;
            }

            // OrderStatusMappingService kullanarak eşleştirme yap
            var mappedStatus = await _orderStatusMappingService.MapExternalToInternalStatusAsync(companyId, "Ticimax", externalStatus);

            _logger.LogInformation("Status mapping for company {CompanyId}: {ExternalStatus} -> {MappedStatus}",
                companyId, externalStatus, mappedStatus);

            return mappedStatus;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting mapped order status for company {CompanyId}, external status {ExternalStatus}",
                companyId, externalStatus);
            return null;
        }
    }

    private string GetDefaultTicimaxMapping(string externalStatus)
    {
        // Ticimax durumlarını bizim 7 sistem durumuna eşleştir
        return externalStatus switch
        {
            "0" => "1",  // Yeni Sipariş -> Beklemede
            "1" => "1",  // Beklemede -> Beklemede
            "2" => "2",  // Onaylandı -> Onaylandı
            "3" => "3",  // Hazırlanıyor -> Hazırlanıyor
            "4" => "4",  // Kargoya Verildi -> Kargoya Verildi
            "5" => "5",  // Teslim Edildi -> Teslim Edildi
            "6" => "6",  // İptal Edildi -> İptal Edildi
            "7" => "7",  // İade Edildi -> İade Edildi
            "8" => "5",  // Kısmi Teslim -> Teslim Edildi
            "9" => "1",  // Ödeme Bekliyor -> Beklemede
            "10" => "1", // Stok Bekliyor -> Beklemede
            "11" => "1", // Tedarikçi Onayı Bekliyor -> Beklemede
            "12" => "3", // Kargo Hazırlığı -> Hazırlanıyor
            "13" => "4", // Kargoda -> Kargoya Verildi
            "14" => "4", // Dağıtımda -> Kargoya Verildi
            "15" => "4", // Teslim Edilemedi -> Kargoya Verildi
            "16" => "6", // Müşteri İptali -> İptal Edildi
            "17" => "6", // Sistem İptali -> İptal Edildi
            "18" => "6", // Ödeme İptali -> İptal Edildi
            "19" => "6", // Stok İptali -> İptal Edildi
            "20" => "6", // Kısmi İptal -> İptal Edildi
            "21" => "7", // İade Talebi -> İade Edildi
            "22" => "7", // İade Onaylandı -> İade Edildi
            "23" => "2", // İade Reddedildi -> Onaylandı
            "24" => "7", // İade Kargoda -> İade Edildi
            "25" => "7", // İade Teslim Alındı -> İade Edildi
            "26" => "7", // İade Tamamlandı -> İade Edildi
            "27" => "3", // Değişim Talebi -> Hazırlanıyor
            "28" => "3", // Değişim Onaylandı -> Hazırlanıyor
            "29" => "4", // Değişim Kargoda -> Kargoya Verildi
            "30" => "5", // Değişim Tamamlandı -> Teslim Edildi
            _ => "1"     // Bilinmeyen durumlar için Beklemede
        };
    }

    private string GetOrderStatusDisplayName(string status)
    {
        // Bizim 7 sistem durumunun Türkçe karşılıkları
        return status switch
        {
            "1" => "Beklemede",
            "2" => "Onaylandı",
            "3" => "Hazırlanıyor",
            "4" => "Kargoya Verildi",
            "5" => "Teslim Edildi",
            "6" => "İptal Edildi",
            "7" => "İade Edildi",
            _ => status
        };
    }

    private string? GetStringProperty(JsonElement element, string propertyName)
    {
        if (element.TryGetProperty(propertyName, out var property) && property.ValueKind == JsonValueKind.String)
        {
            return property.GetString();
        }
        return null;
    }

    // Helper class for order status change data
    private class OrderStatusChangeData
    {
        public string OrderId { get; set; } = string.Empty;
        public string? OrderNumber { get; set; }
        public string NewStatus { get; set; } = string.Empty;
        public string? OldStatus { get; set; }
        public string CustomerEmail { get; set; } = string.Empty;
        public string? CustomerName { get; set; }
        public string? CustomerPhone { get; set; }
        public decimal? OrderAmount { get; set; }
        public string? OrderCurrency { get; set; }
        public DateTime StatusChangedAt { get; set; }
        public string? WebhookPayload { get; set; }
    }

    /// <summary>
    /// Comment API'den gelen webhook'ları işler
    /// </summary>
    [HttpPost("comment-completed")]
    public async Task<IActionResult> CommentCompleted([FromBody] CommentWebhookRequestDto request)
    {
        try
        {
            _logger.LogInformation("Received comment webhook for job {JobId} with status {Status}",
                request.job_id, request.status);

            if (!ModelState.IsValid)
            {
                _logger.LogWarning("Invalid comment webhook request received: {Errors}",
                    string.Join(", ", ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage)));
                return BadRequest(ModelState);
            }

            var result = await _commentService.ProcessWebhookAsync(request);

            if (result)
            {
                _logger.LogInformation("Successfully processed comment webhook for job {JobId}", request.job_id);
                return Ok(new { success = true, message = "Webhook processed successfully" });
            }
            else
            {
                _logger.LogWarning("Failed to process comment webhook for job {JobId}", request.job_id);
                return BadRequest(new { success = false, message = "Failed to process webhook" });
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing comment webhook for job {JobId}", request.job_id);
            return StatusCode(500, new { success = false, message = "Internal server error" });
        }
    }

    /// <summary>
    /// Trendyol ürün çekme API'sinden gelen webhook'ları işler
    /// </summary>
    [HttpPost("product-scrape-completed")]
    public async Task<IActionResult> ProductScrapeCompleted([FromBody] ProductScrapeWebhookDto request)
    {
        try
        {
            _logger.LogInformation("Received product scrape webhook for job {JobId} with status {Status}",
                request.job_id, request.status);

            if (!ModelState.IsValid)
            {
                _logger.LogWarning("Invalid product scrape webhook request received: {Errors}",
                    string.Join(", ", ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage)));
                return BadRequest(ModelState);
            }

            var result = await _trendyolScraperService.ProcessProductScrapeWebhookAsync(request);

            if (result)
            {
                _logger.LogInformation("Successfully processed product scrape webhook for job {JobId}", request.job_id);
                return Ok(new { success = true, message = "Webhook processed successfully" });
            }
            else
            {
                _logger.LogWarning("Failed to process product scrape webhook for job {JobId}", request.job_id);
                return BadRequest(new { success = false, message = "Failed to process webhook" });
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing product scrape webhook for job {JobId}", request.job_id);
            return StatusCode(500, new { success = false, message = "Internal server error" });
        }
    }

    /// <summary>
    /// Trendyol yorum çekme API'sinden gelen webhook'ları işler
    /// </summary>
    [HttpPost("comment-scrape-completed")]
    public async Task<IActionResult> CommentScrapeCompleted([FromBody] CommentScrapeWebhookDto request)
    {
        try
        {
            _logger.LogInformation("Received comment scrape webhook for job {JobId} with status {Status}",
                request.job_id, request.status);

            if (!ModelState.IsValid)
            {
                _logger.LogWarning("Invalid comment scrape webhook request received: {Errors}",
                    string.Join(", ", ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage)));
                return BadRequest(ModelState);
            }

            var result = await _trendyolScraperService.ProcessCommentScrapeWebhookAsync(request);

            if (result)
            {
                _logger.LogInformation("Successfully processed comment scrape webhook for job {JobId}", request.job_id);
                return Ok(new { success = true, message = "Webhook processed successfully" });
            }
            else
            {
                _logger.LogWarning("Failed to process comment scrape webhook for job {JobId}", request.job_id);
                return BadRequest(new { success = false, message = "Failed to process webhook" });
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing comment scrape webhook for job {JobId}", request.job_id);
            return StatusCode(500, new { success = false, message = "Internal server error" });
        }
    }
}
