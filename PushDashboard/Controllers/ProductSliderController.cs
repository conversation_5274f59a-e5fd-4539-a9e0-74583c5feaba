using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using PushDashboard.Configuration;
using PushDashboard.DTOs;
using PushDashboard.Services;
using PushDashboard.Services.Integrations;
using PushDashboard.Services.Integrations.Common.Models;

namespace PushDashboard.Controllers;

public class ProductSliderController : BaseController
{
    private readonly IProductSliderService _productSliderService;
    private readonly IEcommerceServiceFactory _ecommerceServiceFactory;
    private readonly ILogger<ProductSliderController> _logger;
    private readonly R2StorageSettings _r2Settings;

    public ProductSliderController(
        IProductSliderService productSliderService,
        IEcommerceServiceFactory ecommerceServiceFactory,
        ILogger<ProductSliderController> logger,
        IUserContextService userContextService,
        IOptions<R2StorageSettings> r2Settings) : base(userContextService)
    {
        _productSliderService = productSliderService;
        _ecommerceServiceFactory = ecommerceServiceFactory;
        _logger = logger;
        _r2Settings = r2Settings.Value;
    }

    #region Views

    /// <summary>
    /// Ana slider yönetim sayfası
    /// </summary>
    public async Task<IActionResult> Index()
    {
        var companyId = GetCurrentUserCompanyId();
        if (!companyId.HasValue)
        {
            return RedirectToAction("Login", "Account");
        }

        var sliders = await _productSliderService.GetSlidersAsync(companyId.Value);
        var stats = await _productSliderService.GetSliderStatsAsync(companyId.Value);

        ViewBag.Stats = stats;
        return View(sliders);
    }

    /// <summary>
    /// Test sayfası - Script'in çalışmasını test etmek için
    /// </summary>
    [HttpGet]
    public async Task<IActionResult> Test()
    {
        var companyId = GetCurrentUserCompanyId();
        if (!companyId.HasValue)
        {
            return RedirectToAction("Login", "Account");
        }

        try
        {
            // Test için gerekli bilgileri hazırla
            var scriptUrl = string.Empty;
            if (!string.IsNullOrEmpty(_r2Settings.PublicUrl))
            {
                scriptUrl = $"{_r2Settings.PublicUrl.TrimEnd('/')}/{companyId}/modules.js";
            }

            // Slider'ları R2'ye export et (test için)
            try
            {
                await _productSliderService.ExportAllSlidersToR2Async(companyId.Value);
                _logger.LogInformation("Successfully exported sliders to R2 for test page, company {CompanyId}", companyId);
            }
            catch (Exception r2Ex)
            {
                _logger.LogWarning(r2Ex, "Failed to export sliders to R2 for test page, company {CompanyId}", companyId);
            }

            var testData = new
            {
                CompanyId = companyId,
                ScriptUrl = scriptUrl,
                R2Settings = new
                {
                    PublicUrl = _r2Settings.PublicUrl,
                    BucketName = _r2Settings.BucketName,
                    AccountId = _r2Settings.AccountId
                },
                TestInfo = new
                {
                    PageType = "test",
                    Timestamp = DateTime.UtcNow,
                    UserAgent = Request.Headers["User-Agent"].ToString()
                }
            };

            ViewBag.TestData = testData;
            return View();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading test page for company {CompanyId}", companyId);
            ViewBag.Error = "Test sayfası yüklenirken hata oluştu: " + ex.Message;
            return View();
        }
    }

    /// <summary>
    /// Standalone Product Slider Script - Test için
    /// </summary>
    [HttpGet]
    public IActionResult StandaloneScript()
    {
        var companyId = GetCurrentUserCompanyId();
        if (!companyId.HasValue)
        {
            return Content("// Company ID not found", "application/javascript");
        }

        try
        {
            var script = _productSliderService.GenerateStandaloneProductSliderScript(companyId.Value);
            return Content(script, "application/javascript");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating standalone script for company {CompanyId}", companyId);
            return Content($"// Error generating script: {ex.Message}", "application/javascript");
        }
    }

    /// <summary>
    /// Yeni slider oluşturma sayfası
    /// </summary>
    public IActionResult Create()
    {
        var companyId = GetCurrentUserCompanyId();
        if (!companyId.HasValue)
        {
            return RedirectToAction("Login", "Account");
        }

        return View();
    }

    /// <summary>
    /// Slider düzenleme sayfası
    /// </summary>
    public async Task<IActionResult> Edit(int id)
    {
        var companyId = GetCurrentUserCompanyId();
        if (!companyId.HasValue)
        {
            return RedirectToAction("Login", "Account");
        }

        var slider = await _productSliderService.GetSliderDetailAsync(companyId.Value, id);
        if (slider == null)
        {
            TempData["ErrorMessage"] = "Slider bulunamadı.";
            return RedirectToAction("Index");
        }

        return View(slider);
    }

    /// <summary>
    /// Slider detay sayfası
    /// </summary>
    public async Task<IActionResult> Details(int id)
    {
        var companyId = GetCurrentUserCompanyId();
        if (!companyId.HasValue)
        {
            return RedirectToAction("Login", "Account");
        }

        var slider = await _productSliderService.GetSliderDetailAsync(companyId.Value, id);
        if (slider == null)
        {
            TempData["ErrorMessage"] = "Slider bulunamadı.";
            return RedirectToAction("Index");
        }

        return View(slider);
    }

    #endregion

    #region Slider Management APIs

    /// <summary>
    /// Yeni slider oluşturur
    /// </summary>
    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> CreateSlider([FromBody] CreateProductSliderRequestDto request)
    {
        var companyId = GetCurrentUserCompanyId();
        var userId = GetCurrentUserId();

        if (!companyId.HasValue || string.IsNullOrEmpty(userId))
        {
            return Json(new { success = false, message = "Oturum bilgisi bulunamadı." });
        }

        if (!ModelState.IsValid)
        {
            var errors = ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage);
            return Json(new { success = false, message = string.Join(", ", errors) });
        }

        try
        {
            var result = await _productSliderService.CreateSliderAsync(companyId.Value, userId, request);

            if (result.Success && result.SliderId.HasValue)
            {
                // Export to R2 after successful creation
                try
                {
                    await _productSliderService.ExportAllSlidersToR2Async(companyId.Value);
                    _logger.LogInformation("Successfully exported new slider {SliderId} to R2", result.SliderId.Value);
                }
                catch (Exception r2Ex)
                {
                    _logger.LogError(r2Ex, "Error exporting new slider {SliderId} to R2", result.SliderId.Value);
                    // Don't fail the main operation if R2 export fails
                }
            }

            return Json(new {
                success = result.Success,
                message = result.Message,
                sliderId = result.SliderId
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating slider for company {CompanyId}", companyId);
            return Json(new { success = false, message = "Slider oluşturulurken bir hata oluştu." });
        }
    }

    /// <summary>
    /// Slider'ı günceller
    /// </summary>
    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> UpdateSlider([FromBody] UpdateProductSliderRequestDto request)
    {
        var companyId = GetCurrentUserCompanyId();
        if (!companyId.HasValue)
        {
            return Json(new { success = false, message = "Oturum bilgisi bulunamadı." });
        }

        if (!ModelState.IsValid)
        {
            var errors = ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage);
            return Json(new { success = false, message = string.Join(", ", errors) });
        }

        try
        {
            var result = await _productSliderService.UpdateSliderAsync(companyId.Value, request);

            if (result.Success)
            {
                // Export to R2 after successful update
                try
                {
                    await _productSliderService.ExportAllSlidersToR2Async(companyId.Value);
                    _logger.LogInformation("Successfully exported slider {SliderId} to R2 after update", request.Id);
                }
                catch (Exception r2Ex)
                {
                    _logger.LogError(r2Ex, "Error exporting slider {SliderId} to R2 after update", request.Id);
                    // Don't fail the main operation if R2 export fails
                }
            }

            return Json(new { success = result.Success, message = result.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating slider {SliderId} for company {CompanyId}", request.Id, companyId);
            return Json(new { success = false, message = "Slider güncellenirken bir hata oluştu." });
        }
    }

    /// <summary>
    /// Slider'ı siler
    /// </summary>
    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> DeleteSlider(int id)
    {
        var companyId = GetCurrentUserCompanyId();
        if (!companyId.HasValue)
        {
            return Json(new { success = false, message = "Oturum bilgisi bulunamadı." });
        }

        try
        {
            var result = await _productSliderService.DeleteSliderAsync(companyId.Value, id);

            if (result.Success)
            {
                // Export to R2 after successful deletion to update the list
                try
                {
                    await _productSliderService.ExportAllSlidersToR2Async(companyId.Value);
                    _logger.LogInformation("Successfully updated R2 after deleting slider {SliderId}", id);
                }
                catch (Exception r2Ex)
                {
                    _logger.LogError(r2Ex, "Error updating R2 after deleting slider {SliderId}", id);
                    // Don't fail the main operation if R2 export fails
                }
            }

            return Json(new { success = result.Success, message = result.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting slider {SliderId} for company {CompanyId}", id, companyId);
            return Json(new { success = false, message = "Slider silinirken bir hata oluştu." });
        }
    }

    /// <summary>
    /// Slider durumunu değiştirir
    /// </summary>
    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> ToggleSliderStatus(int id)
    {
        var companyId = GetCurrentUserCompanyId();
        if (!companyId.HasValue)
        {
            return Json(new { success = false, message = "Oturum bilgisi bulunamadı." });
        }

        try
        {
            var result = await _productSliderService.ToggleSliderStatusAsync(companyId.Value, id);
            return Json(new { success = result.Success, message = result.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error toggling slider {SliderId} status for company {CompanyId}", id, companyId);
            return Json(new { success = false, message = "Slider durumu değiştirilirken bir hata oluştu." });
        }
    }

    #endregion

    #region Slider Items Management APIs

    /// <summary>
    /// Slider'a yeni ürün ekler
    /// </summary>
    [HttpPost]
    public async Task<IActionResult> AddSliderItem([FromBody] CreateProductSliderItemRequestDto request)
    {
        var companyId = GetCurrentUserCompanyId();
        if (!companyId.HasValue)
        {
            return Json(new { success = false, message = "Oturum bilgisi bulunamadı." });
        }

        if (!ModelState.IsValid)
        {
            var errors = ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage);
            return Json(new { success = false, message = string.Join(", ", errors) });
        }

        try
        {
            var result = await _productSliderService.AddSliderItemAsync(companyId.Value, request);
            return Json(new { 
                success = result.Success, 
                message = result.Message,
                itemId = result.ItemId
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error adding item to slider {SliderId} for company {CompanyId}", request.SliderId, companyId);
            return Json(new { success = false, message = "Ürün eklenirken bir hata oluştu." });
        }
    }

    /// <summary>
    /// Slider ürününü günceller
    /// </summary>
    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> UpdateSliderItem([FromBody] UpdateProductSliderItemRequestDto request)
    {
        var companyId = GetCurrentUserCompanyId();
        if (!companyId.HasValue)
        {
            return Json(new { success = false, message = "Oturum bilgisi bulunamadı." });
        }

        if (!ModelState.IsValid)
        {
            var errors = ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage);
            return Json(new { success = false, message = string.Join(", ", errors) });
        }

        try
        {
            var result = await _productSliderService.UpdateSliderItemAsync(companyId.Value, request);
            return Json(new { success = result.Success, message = result.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating item {ItemId} for company {CompanyId}", request.Id, companyId);
            return Json(new { success = false, message = "Ürün güncellenirken bir hata oluştu." });
        }
    }

    /// <summary>
    /// Slider ürününü siler
    /// </summary>
    [HttpPost]
    public async Task<IActionResult> DeleteSliderItem(int id)
    {
        var companyId = GetCurrentUserCompanyId();
        if (!companyId.HasValue)
        {
            return Json(new { success = false, message = "Oturum bilgisi bulunamadı." });
        }

        try
        {
            var result = await _productSliderService.DeleteSliderItemAsync(companyId.Value, id);
            return Json(new { success = result.Success, message = result.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting item {ItemId} for company {CompanyId}", id, companyId);
            return Json(new { success = false, message = "Ürün silinirken bir hata oluştu." });
        }
    }

    /// <summary>
    /// Ürün sırasını günceller
    /// </summary>
    [HttpPost]
    public async Task<IActionResult> UpdateItemsOrder([FromBody] UpdateItemsOrderRequestDto request)
    {
        var companyId = GetCurrentUserCompanyId();
        if (!companyId.HasValue)
        {
            return Json(new { success = false, message = "Oturum bilgisi bulunamadı." });
        }

        try
        {
            var result = await _productSliderService.UpdateItemsOrderAsync(companyId.Value, request.SliderId, request.ItemIds);
            return Json(new { success = result.Success, message = result.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating items order for slider {SliderId} for company {CompanyId}", request.SliderId, companyId);
            return Json(new { success = false, message = "Ürün sırası güncellenirken bir hata oluştu." });
        }
    }

    /// <summary>
    /// Ürün durumunu değiştirir
    /// </summary>
    [HttpPost]
    public async Task<IActionResult> ToggleSliderItemStatus(int id)
    {
        var companyId = GetCurrentUserCompanyId();
        if (!companyId.HasValue)
        {
            return Json(new { success = false, message = "Oturum bilgisi bulunamadı." });
        }

        try
        {
            var result = await _productSliderService.ToggleSliderItemStatusAsync(companyId.Value, id);
            return Json(new { success = result.Success, message = result.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error toggling item {ItemId} status for company {CompanyId}", id, companyId);
            return Json(new { success = false, message = "Ürün durumu değiştirilirken bir hata oluştu." });
        }
    }

    /// <summary>
    /// Slider ayarlarını günceller
    /// </summary>
    [HttpPost]
    public async Task<IActionResult> UpdateSliderSettings([FromBody] UpdateProductSliderSettingsRequestDto request)
    {
        var companyId = GetCurrentUserCompanyId();
        if (!companyId.HasValue)
        {
            return Json(new { success = false, message = "Oturum bilgisi bulunamadı." });
        }

        if (!ModelState.IsValid)
        {
            var errors = ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage);
            return Json(new { success = false, message = string.Join(", ", errors) });
        }

        try
        {
            var result = await _productSliderService.UpdateSliderSettingsAsync(companyId.Value, request);

            if (result.Success)
            {
                // Export to R2 after successful update
                try
                {
                    await _productSliderService.ExportAllSlidersToR2Async(companyId.Value);
                    _logger.LogInformation("Successfully exported slider {SliderId} to R2 after settings update", request.SliderId);
                }
                catch (Exception r2Ex)
                {
                    _logger.LogError(r2Ex, "Error exporting slider {SliderId} to R2 after settings update", request.SliderId);
                    // Don't fail the main operation if R2 export fails
                }
            }

            return Json(new { success = result.Success, message = result.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating slider settings for slider {SliderId} for company {CompanyId}", request.SliderId, companyId);
            return Json(new { success = false, message = "Ayarlar güncellenirken bir hata oluştu." });
        }
    }

    /// <summary>
    /// E-ticaret entegrasyonundan ürünleri getirir
    /// </summary>
    [HttpGet]
    public async Task<IActionResult> GetEcommerceProducts(
        int page = 1,
        int pageSize = 20,
        string? searchTerm = null,
        string? categoryId = null,
        string? brandId = null,
        decimal? minPrice = null,
        decimal? maxPrice = null,
        bool? inStock = null)
    {
        var companyId = GetCurrentUserCompanyId();
        if (!companyId.HasValue)
        {
            return Json(new { success = false, message = "Oturum bilgisi bulunamadı." });
        }

        try
        {
            var ecommerceService = await _ecommerceServiceFactory.GetEcommerceServiceAsync(companyId.Value);
            if (ecommerceService == null)
            {
                return Json(new { success = false, message = "E-ticaret entegrasyonu bulunamadı." });
            }

            var filter = new EcommerceProductFilter
            {
                SearchTerm = searchTerm,
                CategoryId = categoryId,
                BrandId = brandId,
                MinPrice = minPrice,
                MaxPrice = maxPrice,
                InStock = inStock,
                IsActive = true
            };

            var pagination = new EcommercePagination
            {
                PageNumber = page,
                PageSize = pageSize,
                SortField = "Name",
                SortDirection = "ASC"
            };

            var products = await ecommerceService.GetProductsAsync(companyId.Value, filter, pagination);

            return Json(new {
                success = true,
                data = products.Select(p => new {
                    id = p.ExternalId,
                    name = p.Name,
                    code = p.ProductCode,
                    price = p.Price,
                    discountPrice = p.DiscountPrice,
                    currency = p.Currency,
                    image = p.MainImage,
                    categoryName = p.CategoryName,
                    brandName = p.BrandName,
                    inStock = p.InStock,
                    stockQuantity = p.StockQuantity,
                    productUrl = p.ProductUrl
                })
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting ecommerce products for company {CompanyId}", companyId);
            return Json(new { success = false, message = "Ürünler yüklenirken hata oluştu: " + ex.Message });
        }
    }

    /// <summary>
    /// Yeni embed kod formatını döner (R2 tabanlı)
    /// </summary>
    [HttpGet]
    public async Task<IActionResult> GetR2EmbedCode()
    {
        var companyId = GetCurrentUserCompanyId();
        if (!companyId.HasValue)
        {
            return Json(new { success = false, message = "Oturum bilgisi bulunamadı." });
        }

        try
        {
            // R2 ayarlarından dinamik URL oluştur
            if (string.IsNullOrEmpty(_r2Settings.PublicUrl))
            {
                _logger.LogError("R2 PublicUrl not configured in appsettings.json");
                return Json(new { success = false, message = "R2 ayarları yapılandırılmamış. Lütfen sistem yöneticisine başvurun." });
            }

            var scriptUrl = $"{_r2Settings.PublicUrl.TrimEnd('/')}/{companyId}/productSlider.js";

            var embedCode = $@"<!-- Pushonica Product Slider -->
<script
  src=""{scriptUrl}""
  async>
</script>
<!-- End Pushonica Product Slider -->";

            // R2'ye export edilmiş olup olmadığını kontrol et
            try
            {
                await _productSliderService.ExportAllSlidersToR2Async(companyId.Value);
                _logger.LogInformation("Successfully ensured R2 export for company {CompanyId}", companyId);
            }
            catch (Exception r2Ex)
            {
                _logger.LogWarning(r2Ex, "Failed to ensure R2 export for company {CompanyId}, but continuing", companyId);
            }

            return Json(new {
                success = true,
                embedCode = embedCode,
                scriptUrl = scriptUrl,
                companyId = companyId,
                r2Settings = new {
                    bucketName = _r2Settings.BucketName,
                    publicUrl = _r2Settings.PublicUrl,
                    accountId = _r2Settings.AccountId
                },
                instructions = new {
                    title = "Kurulum Talimatları",
                    steps = new[] {
                        "Yukarıdaki embed kodunu kopyalayın",
                        "E-ticaret sitenizin admin panelinde script ekleme bölümüne gidin",
                        "Kodu 'Header' veya 'Footer' bölümüne yapıştırın",
                        "Değişiklikleri kaydedin",
                        "Sitenizi ziyaret edin - sağda floating button görünecek",
                        "Floating button'a tıklayarak slider'ı test edin"
                    },
                    features = new[] {
                        "Standalone script - hiçbir bağımlılık yok",
                        "Floating button + popup slider",
                        "Otomatik company ID algılama",
                        "CDN hızıyla yükleme",
                        "Responsive tasarım",
                        "Tüm e-ticaret platformları uyumlu"
                    }
                }
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating R2 embed code for company {CompanyId}", companyId);
            return Json(new { success = false, message = "Embed kodu oluşturulurken hata oluştu." });
        }
    }

    /// <summary>
    /// Slider'ları R2'ye manuel export eder
    /// </summary>
    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> ExportToR2()
    {
        var companyId = GetCurrentUserCompanyId();
        if (!companyId.HasValue)
        {
            return Json(new { success = false, message = "Oturum bilgisi bulunamadı." });
        }

        try
        {
            // Export all sliders to R2
            var jsonUrl = await _productSliderService.ExportAllSlidersToR2Async(companyId.Value);

            return Json(new {
                success = true,
                message = "Slider'lar başarıyla R2'ye export edildi.",
                jsonUrl = jsonUrl
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error exporting sliders to R2 for company {CompanyId}", companyId);
            return Json(new { success = false, message = "R2 export işlemi sırasında hata oluştu." });
        }
    }

    #endregion
}

/// <summary>
/// Ürün sırası güncelleme için DTO
/// </summary>
public class UpdateItemsOrderRequestDto
{
    public int SliderId { get; set; }
    public List<int> ItemIds { get; set; } = new();
}
