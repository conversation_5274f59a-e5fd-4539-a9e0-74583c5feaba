using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using PushDashboard.DTOs;
using PushDashboard.Services;
using PushDashboard.Services.Modules.VideoHosting;

namespace PushDashboard.Controllers;

[Authorize]
public class VideoHostingController : BaseController
{
    private readonly IVideoHostingService _videoHostingService;
    private readonly IVideoHostingModuleService _videoHostingModuleService;
    private readonly ILogger<VideoHostingController> _logger;

    public VideoHostingController(
        IVideoHostingService videoHostingService,
        IVideoHostingModuleService videoHostingModuleService,
        ILogger<VideoHostingController> logger,
        IUserContextService userContextService)
        : base(userContextService)
    {
        _videoHostingService = videoHostingService;
        _videoHostingModuleService = videoHostingModuleService;
        _logger = logger;
    }

    #region Main Views

    public async Task<IActionResult> Index()
    {
        try
        {
            var companyId = GetCurrentUserCompanyId();
            if (companyId == null)
            {
                TempData["ErrorMessage"] = "Şirket bilgisi bulunamadı.";
                return RedirectToAction("Index", "Home");
            }

            // Check if module is active
            var hasActiveModule = await _videoHostingModuleService.HasActiveModuleAsync(companyId.Value);
            if (!hasActiveModule)
            {
                return RedirectToAction("Index", "Store");
            }

            var videos = await _videoHostingService.GetVideosAsync(companyId.Value);
            var stats = await _videoHostingService.GetVideoStatsAsync(companyId.Value);

            ViewBag.Stats = stats;
            return View(videos);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading video hosting index for company {CompanyId}", GetCurrentUserCompanyId());
            TempData["ErrorMessage"] = "Video listesi yüklenirken hata oluştu.";
            return View(new List<VideoListDto>());
        }
    }

    public async Task<IActionResult> Upload()
    {
        try
        {
            var companyId = GetCurrentUserCompanyId();
            if (companyId == null)
            {
                TempData["ErrorMessage"] = "Şirket bilgisi bulunamadı.";
                return RedirectToAction("Index", "Home");
            }

            // Check if module is active
            var hasActiveModule = await _videoHostingModuleService.HasActiveModuleAsync(companyId.Value);
            if (!hasActiveModule)
            {
                return RedirectToAction("Index", "Store");
            }

            var settings = await _videoHostingService.GetSettingsAsync(companyId.Value);
            ViewBag.Settings = settings;

            return View();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading video upload page for company {CompanyId}", GetCurrentUserCompanyId());
            TempData["ErrorMessage"] = "Video yükleme sayfası yüklenirken hata oluştu.";
            return RedirectToAction("Index");
        }
    }

    public async Task<IActionResult> Settings()
    {
        try
        {
            var companyId = GetCurrentUserCompanyId();
            if (companyId == null)
            {
                TempData["ErrorMessage"] = "Şirket bilgisi bulunamadı.";
                return RedirectToAction("Index", "Home");
            }

            // Check if module is active
            var hasActiveModule = await _videoHostingModuleService.HasActiveModuleAsync(companyId.Value);
            if (!hasActiveModule)
            {
                return RedirectToAction("Index", "Store");
            }

            var settings = await _videoHostingService.GetSettingsAsync(companyId.Value);
            return View(settings);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading video hosting settings for company {CompanyId}", GetCurrentUserCompanyId());
            TempData["ErrorMessage"] = "Ayarlar yüklenirken hata oluştu.";
            return RedirectToAction("Index");
        }
    }

    #endregion

    #region AJAX Actions

    [HttpPost]
    public async Task<IActionResult> UploadVideo(VideoUploadRequestDto request)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return Json(new { success = false, message = "Geçersiz veri gönderildi." });
            }

            var companyId = GetCurrentUserCompanyId();
            var userId = GetCurrentUserId();

            if (companyId == null || userId == null)
            {
                return Json(new { success = false, message = "Kullanıcı bilgileri bulunamadı." });
            }

            var result = await _videoHostingService.UploadVideoAsync(companyId.Value, request, userId);

            return Json(new { 
                success = result.Success, 
                message = result.Message,
                videoId = result.VideoId
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error uploading video for company {CompanyId}", GetCurrentUserCompanyId());
            return Json(new { success = false, message = "Video yükleme sırasında hata oluştu." });
        }
    }

    [HttpGet]
    public async Task<IActionResult> GetVideo(int id)
    {
        try
        {
            var companyId = GetCurrentUserCompanyId();
            if (companyId == null)
            {
                return Json(new { success = false, message = "Şirket bilgisi bulunamadı." });
            }

            var video = await _videoHostingService.GetVideoByIdAsync(companyId.Value, id);

            if (video == null)
            {
                return Json(new { success = false, message = "Video bulunamadı." });
            }

            return Json(new { success = true, data = video });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting video {VideoId} for company {CompanyId}", id, GetCurrentUserCompanyId());
            return Json(new { success = false, message = "Video bilgileri alınırken hata oluştu." });
        }
    }

    [HttpPost]
    public async Task<IActionResult> UpdateVideo(int id, VideoUpdateRequestDto request)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return Json(new { success = false, message = "Geçersiz veri gönderildi." });
            }

            var companyId = GetCurrentUserCompanyId();
            var userId = GetCurrentUserId();

            if (companyId == null || userId == null)
            {
                return Json(new { success = false, message = "Kullanıcı bilgileri bulunamadı." });
            }

            var result = await _videoHostingService.UpdateVideoAsync(companyId.Value, id, request, userId);

            return Json(new { success = result.Success, message = result.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating video {VideoId} for company {CompanyId}", id, GetCurrentUserCompanyId());
            return Json(new { success = false, message = "Video güncelleme sırasında hata oluştu." });
        }
    }

    [HttpPost]
    public async Task<IActionResult> DeleteVideo(int id)
    {
        try
        {
            var companyId = GetCurrentUserCompanyId();
            var userId = GetCurrentUserId();

            if (companyId == null || userId == null)
            {
                return Json(new { success = false, message = "Kullanıcı bilgileri bulunamadı." });
            }

            var result = await _videoHostingService.DeleteVideoAsync(companyId.Value, id, userId);

            return Json(new { success = result.Success, message = result.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting video {VideoId} for company {CompanyId}", id, GetCurrentUserCompanyId());
            return Json(new { success = false, message = "Video silme sırasında hata oluştu." });
        }
    }

    [HttpPost]
    public async Task<IActionResult> UpdateSettings(VideoSettingsRequestDto request)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return Json(new { success = false, message = "Geçersiz veri gönderildi." });
            }

            var companyId = GetCurrentUserCompanyId();
            var userId = GetCurrentUserId();

            if (companyId == null || userId == null)
            {
                return Json(new { success = false, message = "Kullanıcı bilgileri bulunamadı." });
            }

            var result = await _videoHostingService.UpdateSettingsAsync(companyId.Value, request, userId);

            return Json(new { success = result.Success, message = result.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating video hosting settings for company {CompanyId}", GetCurrentUserCompanyId());
            return Json(new { success = false, message = "Ayarlar güncelleme sırasında hata oluştu." });
        }
    }

    [HttpGet]
    public async Task<IActionResult> GenerateEmbedCode(int id, int width = 640, int height = 360)
    {
        try
        {
            var companyId = GetCurrentUserCompanyId();
            if (companyId == null)
            {
                return Json(new { success = false, message = "Şirket bilgisi bulunamadı." });
            }

            var embedData = await _videoHostingService.GenerateEmbedCodeAsync(companyId.Value, id, width, height);

            if (embedData == null)
            {
                return Json(new { success = false, message = "Embed kodu oluşturulamadı." });
            }

            return Json(new { success = true, data = embedData });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating embed code for video {VideoId}, company {CompanyId}", id, GetCurrentUserCompanyId());
            return Json(new { success = false, message = "Embed kodu oluşturma sırasında hata oluştu." });
        }
    }

    [HttpPost]
    public async Task<IActionResult> ValidateUpload(IFormFile videoFile)
    {
        try
        {
            var companyId = GetCurrentUserCompanyId();
            if (companyId == null)
            {
                return Json(new { success = false, message = "Şirket bilgisi bulunamadı." });
            }

            var result = await _videoHostingService.ValidateVideoUploadAsync(companyId.Value, videoFile);

            return Json(new { 
                success = result.CanUpload, 
                message = result.Message,
                requiredCredits = result.RequiredCredits
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating video upload for company {CompanyId}", GetCurrentUserCompanyId());
            return Json(new { success = false, message = "Video doğrulama sırasında hata oluştu." });
        }
    }

    #endregion

    #region Public Player Actions

    [AllowAnonymous]
    public async Task<IActionResult> Player(int id, string? token = null)
    {
        try
        {
            var playerData = await _videoHostingService.GetPublicVideoPlayerDataAsync(id, token);

            if (playerData == null)
            {
                return NotFound("Video bulunamadı veya erişim izniniz yok.");
            }

            // Record view
            var ipAddress = HttpContext.Connection.RemoteIpAddress?.ToString();
            var userAgent = HttpContext.Request.Headers["User-Agent"].ToString();
            var referrer = HttpContext.Request.Headers["Referer"].ToString();

            await _videoHostingService.RecordVideoViewAsync(id, ipAddress, userAgent, referrer);

            return View(playerData);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading video player for video {VideoId}", id);
            return NotFound("Video yüklenirken hata oluştu.");
        }
    }

    [AllowAnonymous]
    public async Task<IActionResult> Embed(int id, string? domain = null)
    {
        try
        {
            var playerData = await _videoHostingService.GetEmbedPlayerDataAsync(id, domain);

            if (playerData == null)
            {
                return NotFound("Video bulunamadı veya embed edilemez.");
            }

            // Record view
            var ipAddress = HttpContext.Connection.RemoteIpAddress?.ToString();
            var userAgent = HttpContext.Request.Headers["User-Agent"].ToString();
            var referrer = HttpContext.Request.Headers["Referer"].ToString();

            await _videoHostingService.RecordVideoViewAsync(id, ipAddress, userAgent, referrer, domain);

            return View(playerData);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading embed player for video {VideoId}", id);
            return NotFound("Video yüklenirken hata oluştu.");
        }
    }

    #endregion
}
