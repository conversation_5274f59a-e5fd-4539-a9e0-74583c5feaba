using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using PushDashboard.DTOs;
using PushDashboard.Services;
using PushDashboard.ViewModels;

namespace PushDashboard.Controllers;

[Authorize]
public class CommentController : BaseController
{
    private readonly ICommentService _commentService;
    private readonly ITrendyolScraperService _trendyolScraperService;
    private readonly ILogger<CommentController> _logger;

    public CommentController(ICommentService commentService,
        ITrendyolScraperService trendyolScraperService,
        ILogger<CommentController> logger,
        IUserContextService userContextService)
        : base(userContextService)
    {
        _commentService = commentService;
        _trendyolScraperService = trendyolScraperService;
        _logger = logger;
    }

    public async Task<IActionResult> Index()
    {
        var companyId = GetCurrentUserCompanyId();
        if (companyId == null)
        {
            TempData["ErrorMessage"] = "Şirket bilgisi bulunamadı.";
            return RedirectToAction("Index", "Home");
        }

        // Yeni Trendyol scraper sistemini kullan
        return View();
    }

    #region Store Management

    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> CreateStore([FromBody] CreateStoreRequestDto request)
    {
        var companyId = GetCurrentUserCompanyId();
        if (companyId == null)
        {
            return Json(new { success = false, message = "Şirket bilgisi bulunamadı." });
        }

        if (!ModelState.IsValid)
        {
            var errors = ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage);
            return Json(new { success = false, message = string.Join(", ", errors) });
        }

        try
        {
            var result = await _trendyolScraperService.CreateStoreAsync(companyId.Value, request);
            return Json(new { success = result.Success, message = result.Message, storeId = result.StoreId });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating store for company {CompanyId}", companyId);
            return Json(new { success = false, message = "Mağaza oluşturulurken hata oluştu." });
        }
    }

    [HttpGet]
    public async Task<IActionResult> GetStores()
    {
        var companyId = GetCurrentUserCompanyId();
        if (companyId == null)
        {
            return Json(new { success = false, message = "Şirket bilgisi bulunamadı." });
        }

        try
        {
            var stores = await _trendyolScraperService.GetStoresAsync(companyId.Value);
            return Json(new { success = true, data = stores });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting stores for company {CompanyId}", companyId);
            return Json(new { success = false, message = "Mağazalar yüklenirken hata oluştu." });
        }
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> SyncStoreProducts([FromBody] SyncStoreProductsRequestDto request)
    {
        var companyId = GetCurrentUserCompanyId();
        if (companyId == null)
        {
            return Json(new { success = false, message = "Şirket bilgisi bulunamadı." });
        }

        if (!ModelState.IsValid)
        {
            var errors = ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage);
            return Json(new { success = false, message = string.Join(", ", errors) });
        }

        try
        {
            var result = await _trendyolScraperService.SyncStoreProductsAsync(companyId.Value, request);
            return Json(new { success = result.Success, message = result.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error syncing store products for company {CompanyId}", companyId);
            return Json(new { success = false, message = "Ürün senkronizasyonu başlatılırken hata oluştu." });
        }
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> DeleteStore([FromBody] DeleteStoreRequest request)
    {
        var companyId = GetCurrentUserCompanyId();
        if (companyId == null)
        {
            return Json(new { success = false, message = "Şirket bilgisi bulunamadı." });
        }

        try
        {
            var result = await _trendyolScraperService.DeleteStoreAsync(companyId.Value, request.StoreId);
            return Json(new { success = result, message = result ? "Mağaza silindi." : "Mağaza bulunamadı." });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting store {StoreId} for company {CompanyId}", request.StoreId, companyId);
            return Json(new { success = false, message = "Mağaza silinirken hata oluştu." });
        }
    }

    #endregion

    #region Product Management

    [HttpGet]
    public async Task<IActionResult> GetStoreProducts(int storeId, int page = 1, int pageSize = 50)
    {
        var companyId = GetCurrentUserCompanyId();
        if (companyId == null)
        {
            return Json(new { success = false, message = "Şirket bilgisi bulunamadı." });
        }

        try
        {
            var products = await _trendyolScraperService.GetStoreProductsAsync(companyId.Value, storeId, page, pageSize);
            var stats = await _trendyolScraperService.GetProductStatsAsync(companyId.Value, storeId);

            return Json(new {
                success = true,
                data = new {
                    products = products,
                    stats = new {
                        totalProducts = stats.TotalProducts,
                        selectedProducts = stats.SelectedProducts
                    }
                }
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting store products for store {StoreId}", storeId);
            return Json(new { success = false, message = "Ürünler yüklenirken hata oluştu." });
        }
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> TransferSelectedProducts([FromBody] TransferSelectedProductsRequestDto request)
    {
        var companyId = GetCurrentUserCompanyId();
        if (companyId == null)
        {
            return Json(new { success = false, message = "Şirket bilgisi bulunamadı." });
        }

        if (!ModelState.IsValid)
        {
            var errors = ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage);
            return Json(new { success = false, message = string.Join(", ", errors) });
        }

        try
        {
            var result = await _trendyolScraperService.TransferSelectedProductsAsync(companyId.Value, request);
            return Json(new { success = result.Success, message = result.Message, jobId = result.JobId });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error transferring selected products for company {CompanyId}", companyId);
            return Json(new { success = false, message = "Transfer işlemi başlatılırken hata oluştu." });
        }
    }

    #endregion

    #region Transfer Job Management

    [HttpGet]
    public async Task<IActionResult> GetTransferJobs(int page = 1, int pageSize = 20)
    {
        var companyId = GetCurrentUserCompanyId();
        if (companyId == null)
        {
            return Json(new { success = false, message = "Şirket bilgisi bulunamadı." });
        }

        try
        {
            var jobs = await _trendyolScraperService.GetTransferJobsAsync(companyId.Value, page, pageSize);
            return Json(new { success = true, data = jobs });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting transfer jobs for company {CompanyId}", companyId);
            return Json(new { success = false, message = "Transfer işleri yüklenirken hata oluştu." });
        }
    }

    [HttpGet]
    public async Task<IActionResult> GetTransferJob(int jobId)
    {
        var companyId = GetCurrentUserCompanyId();
        if (companyId == null)
        {
            return Json(new { success = false, message = "Şirket bilgisi bulunamadı." });
        }

        try
        {
            var job = await _trendyolScraperService.GetTransferJobAsync(companyId.Value, jobId);
            if (job == null)
            {
                return Json(new { success = false, message = "Transfer işi bulunamadı." });
            }

            return Json(new { success = true, data = job });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting transfer job {JobId} for company {CompanyId}", jobId, companyId);
            return Json(new { success = false, message = "Transfer işi yüklenirken hata oluştu." });
        }
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> CancelTransferJob([FromBody] CancelTransferJobRequest request)
    {
        var companyId = GetCurrentUserCompanyId();
        if (companyId == null)
        {
            return Json(new { success = false, message = "Şirket bilgisi bulunamadı." });
        }

        try
        {
            var result = await _trendyolScraperService.CancelTransferJobAsync(companyId.Value, request.JobId);
            return Json(new { success = result.Success, message = result.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error cancelling transfer job {JobId} for company {CompanyId}", request.JobId, companyId);
            return Json(new { success = false, message = "Transfer işi iptal edilirken hata oluştu." });
        }
    }

    #endregion

    #region Legacy Support

    [HttpGet]
    public async Task<IActionResult> GetCommentRequests(int page = 1, int pageSize = 20, string? searchTerm = null, string? status = null, string? sortBy = null, string? sortDirection = null)
    {
        var companyId = GetCurrentUserCompanyId();
        if (companyId == null)
        {
            return Json(new { success = false, message = "Şirket bilgisi bulunamadı." });
        }

        try
        {
            // Önce bekleyen istekleri kontrol et ve güncelle
            await _commentService.CheckAndUpdatePendingRequestsAsync(companyId.Value);

            var viewModel = await _commentService.GetCommentRequestsAsync(companyId.Value, page, pageSize, searchTerm, status, sortBy, sortDirection);

            return Json(new {
                success = true,
                data = new {
                    commentRequests = viewModel.CommentRequests,
                    stats = viewModel.Stats,
                    pagination = viewModel.Pagination
                }
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading comment requests for company {CompanyId}", companyId);
            return Json(new { success = false, message = "Yorum istekleri yüklenirken hata oluştu: " + ex.Message });
        }
    }

    [HttpGet]
    public IActionResult Create()
    {
        return View(new CommentRequestCreateViewModel());
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> Create(CommentRequestCreateViewModel model)
    {
        var companyId = GetCurrentUserCompanyId();
        if (companyId == null)
        {
            TempData["ErrorMessage"] = "Şirket bilgisi bulunamadı.";
            return RedirectToAction("Index");
        }

        if (!ModelState.IsValid)
        {
            return View(model);
        }

        try
        {
            var userId = GetCurrentUserId();
            if (string.IsNullOrEmpty(userId))
            {
                ModelState.AddModelError("", "Kullanıcı bilgisi bulunamadı.");
                return View(model);
            }

            // Use new Trendyol scraper service for legacy support
            var result = await _trendyolScraperService.CreateLegacyCommentRequestAsync(
                companyId.Value,
                model.Request.ProductUrl,
                model.Request.RequestedCommentsCount,
                userId);

            if (result.Success)
            {
                TempData["SuccessMessage"] = "Yorum isteği başarıyla oluşturuldu ve işleme alındı.";
                return RedirectToAction("Index");
            }
            else
            {
                ModelState.AddModelError("", result.Message);
                return View(model);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating comment request for company {CompanyId}", companyId);
            ModelState.AddModelError("", "Yorum isteği oluşturulurken hata oluştu: " + ex.Message);
            return View(model);
        }
    }



    #endregion

    [HttpGet]
    public async Task<IActionResult> Details(int id, int page = 1, int pageSize = 20)
    {
        var companyId = GetCurrentUserCompanyId();
        if (companyId == null)
        {
            TempData["ErrorMessage"] = "Şirket bilgisi bulunamadı.";
            return RedirectToAction("Index");
        }

        try
        {
            var request = await _commentService.GetCommentRequestAsync(id, companyId.Value);
            if (request == null)
            {
                TempData["ErrorMessage"] = "Yorum isteği bulunamadı.";
                return RedirectToAction("Index");
            }

            var details = await _commentService.GetCommentDetailsAsync(id, companyId.Value, page, pageSize);
            if (details == null)
            {
                TempData["ErrorMessage"] = "Yorumlar henüz hazır değil veya yüklenemiyor.";
                return RedirectToAction("Index");
            }

            var viewModel = new CommentRequestDetailsViewModel
            {
                Request = request,
                Details = details
            };

            return View(viewModel);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading comment details for request {RequestId}", id);
            TempData["ErrorMessage"] = "Yorum detayları yüklenirken hata oluştu.";
            return RedirectToAction("Index");
        }
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> Delete(int id)
    {
        var companyId = GetCurrentUserCompanyId();
        if (companyId == null)
        {
            return Json(new { success = false, message = "Şirket bilgisi bulunamadı." });
        }

        try
        {
            var result = await _commentService.DeleteCommentRequestAsync(id, companyId.Value);
            if (result)
            {
                return Json(new { success = true, message = "Yorum isteği başarıyla silindi." });
            }
            else
            {
                return Json(new { success = false, message = "Yorum isteği bulunamadı." });
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting comment request {RequestId}", id);
            return Json(new { success = false, message = "Yorum isteği silinirken hata oluştu." });
        }
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> Retry(int id)
    {
        var companyId = GetCurrentUserCompanyId();
        if (companyId == null)
        {
            return Json(new { success = false, message = "Şirket bilgisi bulunamadı." });
        }

        try
        {
            var result = await _commentService.ProcessCommentRequestAsync(id, companyId.Value);
            if (result)
            {
                return Json(new { success = true, message = "Yorum isteği yeniden işleme alındı." });
            }
            else
            {
                return Json(new { success = false, message = "Yorum isteği bulunamadı." });
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrying comment request {RequestId}", id);
            return Json(new { success = false, message = "Yorum isteği yeniden işlenirken hata oluştu." });
        }
    }

    [HttpGet]
    public async Task<IActionResult> ExportHtml(int id)
    {
        var companyId = GetCurrentUserCompanyId();
        if (companyId == null)
        {
            return BadRequest("Şirket bilgisi bulunamadı.");
        }

        try
        {
            var html = await _commentService.ExportCommentsAsHtmlAsync(id, companyId.Value);
            if (html == null)
            {
                return NotFound("Yorumlar bulunamadı veya henüz hazır değil.");
            }

            return Content(html, "text/html");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error exporting HTML for comment request {RequestId}", id);
            return BadRequest("HTML export edilirken hata oluştu.");
        }
    }

    [HttpGet]
    public async Task<IActionResult> ExportUrl(int id)
    {
        var companyId = GetCurrentUserCompanyId();
        if (companyId == null)
        {
            return Json(new { success = false, message = "Şirket bilgisi bulunamadı." });
        }

        try
        {
            var request = await _commentService.GetCommentRequestAsync(id, companyId.Value);
            if (request == null || !request.HasComments)
            {
                return Json(new { success = false, message = "Yorum isteği bulunamadı veya yorumlar henüz hazır değil." });
            }

            var exportUrl = Url.Action("PublicComments", "Comment", new { token = request.ExportToken }, Request.Scheme);
            return Json(new { success = true, url = exportUrl });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating export URL for comment request {RequestId}", id);
            return Json(new { success = false, message = "Export URL oluşturulurken hata oluştu." });
        }
    }

    [HttpGet]
    [AllowAnonymous]
    public async Task<IActionResult> PublicComments(string token)
    {
        if (string.IsNullOrEmpty(token))
        {
            return NotFound();
        }

        try
        {
            var comments = await _commentService.GetCommentsByTokenAsync(token);
            if (comments == null)
            {
                return NotFound();
            }

            return View(comments);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading public comments for token {Token}", token);
            return NotFound();
        }
    }

    [HttpGet]
    public async Task<IActionResult> CheckBalance(int commentCount)
    {
        var companyId = GetCurrentUserCompanyId();
        if (companyId == null)
        {
            return Json(new { success = false, message = "Şirket bilgisi bulunamadı." });
        }

        try
        {
            var balanceCheck = await _commentService.CheckBalanceForCommentsAsync(companyId.Value, commentCount);

            return Json(new
            {
                success = true,
                hasBalance = balanceCheck.HasBalance,
                requiredAmount = balanceCheck.RequiredAmount,
                currentBalance = balanceCheck.CurrentBalance,
                pricePerComment = balanceCheck.CommentPrice,
                missingAmount = balanceCheck.HasBalance ? 0 : balanceCheck.RequiredAmount - balanceCheck.CurrentBalance
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking balance for company {CompanyId}", companyId);
            return Json(new { success = false, message = "Bakiye kontrolü yapılırken hata oluştu." });
        }
    }
}
