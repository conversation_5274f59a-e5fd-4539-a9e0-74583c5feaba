using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using PushDashboard.Data;
using PushDashboard.Services;
using PushDashboard.Models.ViewModels;
using System.Security.Claims;

namespace PushDashboard.Controllers;

[Authorize]
public class UsageHistoryController : BaseController
{
    private readonly ApplicationDbContext _context;
    private readonly IModuleUsageService _moduleUsageService;
    private readonly ILogger<UsageHistoryController> _logger;

    public UsageHistoryController(
        ApplicationDbContext context,
        IModuleUsageService moduleUsageService,
        ILogger<UsageHistoryController> logger,
        IUserContextService userContextService)
        : base(userContextService)
    {
        _context = context;
        _moduleUsageService = moduleUsageService;
        _logger = logger;
    }

    public async Task<IActionResult> Index()
    {
        try
        {
            var companyId = GetCurrentUserCompanyId();
            if (companyId == null)
            {
                return RedirectToAction("Index", "Home");
            }

            // Son 30 günün harcama geçmişini al
            var startDate = DateTime.UtcNow.AddDays(-30);
            var usageHistory = await _moduleUsageService.GetUsageHistoryAsync(companyId.Value, startDate: startDate, limit: 50);
            var usageStats = await _moduleUsageService.GetUsageStatsAsync(companyId.Value, startDate: startDate);

            // Company bilgisini al
            var company = await _context.Companies.FindAsync(companyId.Value);

            var viewModel = new UsageHistoryViewModel
            {
                UsageHistory = usageHistory,
                UsageStats = usageStats,
                CurrentBalance = company?.CreditBalance ?? 0,
                CompanyName = company?.Name ?? "Bilinmeyen Şirket",
                StartDate = startDate,
                EndDate = DateTime.UtcNow
            };

            return View(viewModel);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading usage history");
            TempData["ErrorMessage"] = "Harcama geçmişi yüklenirken hata oluştu.";
            return RedirectToAction("Index", "Home");
        }
    }

    [HttpGet]
    public async Task<IActionResult> GetUsageHistory(DateTime? startDate = null, DateTime? endDate = null, string? moduleName = null, int page = 1, int pageSize = 20)
    {
        try
        {
            var companyId = GetCurrentUserCompanyId();
            if (companyId == null)
            {
                return Json(new { success = false, message = "Şirket bilgisi bulunamadı." });
            }

            // Modül adından ID'yi bul
            int? moduleId = null;
            if (!string.IsNullOrEmpty(moduleName))
            {
                var module = await _context.Modules.FirstOrDefaultAsync(m => m.Name == moduleName);
                moduleId = module?.Id;
            }

            // Sayfalama için toplam kayıt sayısını al
            var totalRecords = await _moduleUsageService.GetUsageHistoryCountAsync(companyId.Value, moduleId, startDate, endDate);

            // Sayfa numarasını doğrula
            var totalPages = (int)Math.Ceiling((double)totalRecords / pageSize);
            page = Math.Max(1, Math.Min(page, totalPages));

            var usageHistory = await _moduleUsageService.GetUsageHistoryPagedAsync(companyId.Value, moduleId, startDate, endDate, page, pageSize);

            var result = usageHistory.Select(uh => new
            {
                id = uh.Id,
                moduleName = uh.Module.Name,
                usageType = uh.UsageType,
                description = uh.Description,
                cost = uh.Cost,
                balanceBefore = uh.BalanceBefore,
                balanceAfter = uh.BalanceAfter,
                channel = uh.Channel,
                isSuccessful = uh.IsSuccessful,
                errorMessage = uh.ErrorMessage,
                createdAt = uh.CreatedAt,
                userName = uh.User.FirstName + " " + uh.User.LastName
            }).ToList();

            return Json(new {
                success = true,
                data = result,
                pagination = new {
                    currentPage = page,
                    pageSize = pageSize,
                    totalRecords = totalRecords,
                    totalPages = totalPages,
                    hasNextPage = page < totalPages,
                    hasPreviousPage = page > 1
                }
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting usage history");
            return Json(new { success = false, message = "Harcama geçmişi alınırken hata oluştu." });
        }
    }

    [HttpGet]
    public async Task<IActionResult> GetUsageStats(DateTime? startDate = null, DateTime? endDate = null)
    {
        try
        {
            var companyId = GetCurrentUserCompanyId();
            if (companyId == null)
            {
                return Json(new { success = false, message = "Şirket bilgisi bulunamadı." });
            }

            var usageStats = await _moduleUsageService.GetUsageStatsAsync(companyId.Value, startDate, endDate);

            return Json(new { success = true, data = usageStats });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting usage stats");
            return Json(new { success = false, message = "İstatistikler alınırken hata oluştu." });
        }
    }


}
