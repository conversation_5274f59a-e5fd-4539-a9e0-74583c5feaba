using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using PushDashboard.Models;
using PushDashboard.Models.ViewModels;
using PushDashboard.Services;

namespace PushDashboard.Controllers;

[Authorize]
public class ExitSurveyController : Controller
{
    private readonly IExitSurveyService _exitSurveyService;
    private readonly UserManager<ApplicationUser> _userManager;
    private readonly ILogger<ExitSurveyController> _logger;

    public ExitSurveyController(
        IExitSurveyService exitSurveyService,
        UserManager<ApplicationUser> userManager,
        ILogger<ExitSurveyController> logger)
    {
        _exitSurveyService = exitSurveyService;
        _userManager = userManager;
        _logger = logger;
    }

    private Guid? GetCurrentUserCompanyId()
    {
        var companyIdClaim = User.FindFirst("CompanyId")?.Value;
        if (Guid.TryParse(companyIdClaim, out var companyId))
        {
            return companyId;
        }
        return null;
    }

    public async Task<IActionResult> Index()
    {
        var companyId = GetCurrentUserCompanyId();
        if (!companyId.HasValue)
        {
            return RedirectToAction("Login", "Account");
        }

        // Modül kontrolü
        var hasModule = await _exitSurveyService.HasExitSurveyModuleAsync(companyId.Value);
        if (!hasModule)
        {
            TempData["ErrorMessage"] = "Çıkış Anketi modülüne sahip değilsiniz. Lütfen önce modülü satın alın.";
            return RedirectToAction("Index", "Store");
        }

        var exitSurvey = await _exitSurveyService.GetExitSurveyAsync(companyId.Value);
        var questions = exitSurvey != null ? await _exitSurveyService.GetQuestionsAsync(exitSurvey.Id) : new List<ExitSurveyQuestion>();
        var stats = await _exitSurveyService.GetStatsAsync(companyId.Value);
        var recentResponses = await _exitSurveyService.GetResponsesAsync(companyId.Value, 1, 10);

        var viewModel = new ExitSurveyViewModel
        {
            ExitSurvey = exitSurvey,
            Questions = questions,
            Stats = stats,
            RecentResponses = recentResponses
        };

        return View(viewModel);
    }

    [HttpPost]
    public async Task<IActionResult> SaveSettings([FromBody] SaveExitSurveyViewModel model)
    {
        try
        {
            var companyId = GetCurrentUserCompanyId();
            if (!companyId.HasValue)
            {
                return Json(new { success = false, message = "Kullanıcı oturumu bulunamadı." });
            }

            var user = await _userManager.GetUserAsync(User);
            if (user == null)
            {
                return Json(new { success = false, message = "Kullanıcı bulunamadı." });
            }

            var userId = user.Id;

            var exitSurvey = new ExitSurvey
            {
                CompanyId = companyId.Value,
                Title = model.Title,
                Description = model.Description,
                SubmitButtonText = model.SubmitButtonText,
                CancelButtonText = model.CancelButtonText,
                ThankYouMessage = model.ThankYouMessage,
                BackgroundColor = model.BackgroundColor,
                TextColor = model.TextColor,
                SubmitButtonColor = model.SubmitButtonColor,
                CancelButtonColor = model.CancelButtonColor,
                BorderRadius = model.BorderRadius,
                EnableAnimation = model.EnableAnimation,
                ShowOnPageExit = model.ShowOnPageExit,
                ShowOnTabClose = model.ShowOnTabClose,
                DelayBeforeShow = model.DelayBeforeShow,
                ShowFrequencyDays = model.ShowFrequencyDays,
                ShowOnMobile = model.ShowOnMobile,
                ShowOnDesktop = model.ShowOnDesktop,
                IsActive = model.IsActive,
                UpdatedByUserId = userId
            };

            var success = await _exitSurveyService.SaveExitSurveyAsync(exitSurvey);

            if (success)
            {
                return Json(new { success = true, message = "Ayarlar başarıyla kaydedildi." });
            }
            else
            {
                return Json(new { success = false, message = "Ayarlar kaydedilirken bir hata oluştu." });
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error saving exit survey settings");
            return Json(new { success = false, message = "Bir hata oluştu. Lütfen tekrar deneyin." });
        }
    }

    [HttpPost]
    public async Task<IActionResult> SaveQuestion([FromBody] ExitSurveyQuestionViewModel model)
    {
        try
        {
            var companyId = GetCurrentUserCompanyId();
            if (!companyId.HasValue)
            {
                return Json(new { success = false, message = "Kullanıcı oturumu bulunamadı." });
            }

            // Anket ID'sini al
            var exitSurvey = await _exitSurveyService.GetExitSurveyAsync(companyId.Value);
            if (exitSurvey == null)
            {
                // Varsayılan ayarları oluştur
                var user = await _userManager.GetUserAsync(User);
                if (user == null)
                {
                    return Json(new { success = false, message = "Kullanıcı bulunamadı." });
                }
                exitSurvey = await _exitSurveyService.CreateDefaultSettingsAsync(companyId.Value, user.Id);
            }

            var question = new ExitSurveyQuestion
            {
                Id = model.Id,
                ExitSurveyId = exitSurvey.Id,
                QuestionText = model.QuestionText,
                QuestionType = model.QuestionType,
                SortOrder = model.SortOrder,
                IsRequired = model.IsRequired,
                IsActive = model.IsActive,
                Options = model.Options
            };

            var success = await _exitSurveyService.SaveQuestionAsync(question);

            if (success)
            {
                return Json(new { success = true, message = "Soru başarıyla kaydedildi." });
            }
            else
            {
                return Json(new { success = false, message = "Soru kaydedilirken bir hata oluştu." });
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error saving exit survey question");
            return Json(new { success = false, message = "Bir hata oluştu. Lütfen tekrar deneyin." });
        }
    }

    [HttpPost]
    public async Task<IActionResult> DeleteQuestion(int questionId)
    {
        try
        {
            var success = await _exitSurveyService.DeleteQuestionAsync(questionId);

            if (success)
            {
                return Json(new { success = true, message = "Soru başarıyla silindi." });
            }
            else
            {
                return Json(new { success = false, message = "Soru silinirken bir hata oluştu." });
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting exit survey question {QuestionId}", questionId);
            return Json(new { success = false, message = "Bir hata oluştu. Lütfen tekrar deneyin." });
        }
    }

    [HttpPost]
    public async Task<IActionResult> ReorderQuestions([FromBody] List<int> questionIds)
    {
        try
        {
            var success = await _exitSurveyService.ReorderQuestionsAsync(questionIds);

            if (success)
            {
                return Json(new { success = true, message = "Soru sıralaması güncellendi." });
            }
            else
            {
                return Json(new { success = false, message = "Sıralama güncellenirken bir hata oluştu." });
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error reordering exit survey questions");
            return Json(new { success = false, message = "Bir hata oluştu. Lütfen tekrar deneyin." });
        }
    }

    [HttpGet]
    public async Task<IActionResult> GetResponses(int page = 1, int pageSize = 50)
    {
        try
        {
            var companyId = GetCurrentUserCompanyId();
            if (!companyId.HasValue)
            {
                return Json(new { success = false, message = "Kullanıcı oturumu bulunamadı." });
            }

            var responses = await _exitSurveyService.GetResponsesAsync(companyId.Value, page, pageSize);

            return Json(new { success = true, data = responses });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting exit survey responses");
            return Json(new { success = false, message = "Bir hata oluştu. Lütfen tekrar deneyin." });
        }
    }

    [HttpGet]
    public async Task<IActionResult> GetStats()
    {
        try
        {
            var companyId = GetCurrentUserCompanyId();
            if (!companyId.HasValue)
            {
                return Json(new { success = false, message = "Kullanıcı oturumu bulunamadı." });
            }

            var stats = await _exitSurveyService.GetStatsAsync(companyId.Value);

            return Json(new { success = true, data = stats });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting exit survey stats");
            return Json(new { success = false, message = "Bir hata oluştu. Lütfen tekrar deneyin." });
        }
    }

    [HttpGet]
    public async Task<IActionResult> GetScriptUrl()
    {
        try
        {
            var companyId = GetCurrentUserCompanyId();
            if (!companyId.HasValue)
            {
                return Json(new { success = false, message = "Kullanıcı oturumu bulunamadı." });
            }

            var scriptUrl = Url.Action("Script", "ExitSurveyApi", new { companyId = companyId.Value }, Request.Scheme);

            return Json(new { success = true, scriptUrl = scriptUrl });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting script URL");
            return Json(new { success = false, message = "Bir hata oluştu. Lütfen tekrar deneyin." });
        }
    }

    /// <summary>
    /// Test sayfası - Çıkış anketi widget'ını test etmek için
    /// </summary>
    [HttpGet]
    [AllowAnonymous]
    public IActionResult Test()
    {
        return View();
    }

    /// <summary>
    /// Önizleme - Anket widget'ının nasıl görüneceğini gösterir
    /// </summary>
    [HttpGet]
    public async Task<IActionResult> Preview()
    {
        try
        {
            var companyId = GetCurrentUserCompanyId();
            if (!companyId.HasValue)
            {
                return Json(new { success = false, message = "Kullanıcı oturumu bulunamadı." });
            }

            var config = await _exitSurveyService.GetScriptConfigAsync(companyId.Value);

            return Json(new { success = true, config = config });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting preview config");
            return Json(new { success = false, message = "Bir hata oluştu. Lütfen tekrar deneyin." });
        }
    }

    /// <summary>
    /// Soru düzenleme - Mevcut soruyu getirir
    /// </summary>
    [HttpGet]
    public async Task<IActionResult> GetQuestion(int questionId)
    {
        try
        {
            var companyId = GetCurrentUserCompanyId();
            if (!companyId.HasValue)
            {
                return Json(new { success = false, message = "Kullanıcı oturumu bulunamadı." });
            }

            var question = await _exitSurveyService.GetQuestionAsync(questionId, companyId.Value);
            if (question == null)
            {
                return Json(new { success = false, message = "Soru bulunamadı." });
            }

            return Json(new { success = true, question = question });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting question {QuestionId}", questionId);
            return Json(new { success = false, message = "Bir hata oluştu. Lütfen tekrar deneyin." });
        }
    }

    /// <summary>
    /// Soru güncelleme
    /// </summary>
    [HttpPost]
    public async Task<IActionResult> UpdateQuestion([FromBody] ExitSurveyQuestionViewModel model)
    {
        try
        {
            var companyId = GetCurrentUserCompanyId();
            if (!companyId.HasValue)
            {
                return Json(new { success = false, message = "Kullanıcı oturumu bulunamadı." });
            }

            // Mevcut soruyu kontrol et
            var existingQuestion = await _exitSurveyService.GetQuestionAsync(model.Id, companyId.Value);
            if (existingQuestion == null)
            {
                return Json(new { success = false, message = "Güncellenecek soru bulunamadı." });
            }

            var success = await _exitSurveyService.UpdateQuestionAsync(model, companyId.Value);

            if (success)
            {
                return Json(new { success = true, message = "Soru başarıyla güncellendi." });
            }

            return Json(new { success = false, message = "Soru güncellenirken bir hata oluştu." });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating question {QuestionId}", model.Id);
            return Json(new { success = false, message = "Bir hata oluştu. Lütfen tekrar deneyin." });
        }
    }

    /// <summary>
    /// Tüm cevapları görüntüleme
    /// </summary>
    [HttpGet]
    public async Task<IActionResult> AllResponses(int page = 1, int pageSize = 10)
    {
        var companyId = GetCurrentUserCompanyId();
        try
        {
            if (!companyId.HasValue)
            {
                return RedirectToAction("Login", "Account");
            }

            // Sayfa ve sayfa boyutu validasyonu
            if (page < 1) page = 1;
            if (pageSize < 5) pageSize = 5;
            if (pageSize > 50) pageSize = 50;

            var viewModel = await _exitSurveyService.GetAllResponsesViewModelAsync(companyId.Value, page, pageSize);
            return View(viewModel);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading all responses for company {CompanyId}", companyId?.ToString() ?? "Unknown");
            TempData["ErrorMessage"] = "Cevaplar yüklenirken bir hata oluştu.";
            return RedirectToAction("Index");
        }
    }
}
