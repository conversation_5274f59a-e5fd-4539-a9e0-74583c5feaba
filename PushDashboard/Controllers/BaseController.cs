using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using PushDashboard.Models;
using PushDashboard.Services;
using System.Security.Claims;

namespace PushDashboard.Controllers;

[Authorize]
public class BaseController : Controller
{
    private readonly IUserContextService _userContextService;

    public BaseController(IUserContextService userContextService)
    {
        _userContextService = userContextService;
    }

    // Modern methods using UserContextService
    protected Guid? GetCurrentUserCompanyId()
    {
        return _userContextService.CompanyId;
    }

    protected string? GetCurrentUserId()
    {
        return _userContextService.UserId;
    }

    protected ApplicationUser? GetCurrentUser()
    {
        return _userContextService.User;
    }

    protected bool IsUserAuthenticated()
    {
        return _userContextService.IsAuthenticated;
    }

    // Legacy methods for backward compatibility (using HttpContext.User)
    protected Guid? GetCurrentUserCompanyIdLegacy()
    {
        var companyIdClaim = User.FindFirst("CompanyId");
        if (companyIdClaim != null && Guid.TryParse(companyIdClaim.Value, out Guid companyId))
        {
            return companyId;
        }
        return null;
    }

    protected string? GetCurrentUserIdLegacy()
    {
        return User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
    }
}