using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using PushDashboard.Data;
using PushDashboard.Services;
using PushDashboard.Services.WhatsApp;
using PushDashboard.ViewModels;
using System.Security.Claims;
using System.Text.Json;
using System.Text.Json.Serialization;

namespace PushDashboard.Controllers;

[Authorize]
public class WhatsAppTemplateController : BaseController
{
    private readonly ApplicationDbContext _context;
    private readonly IWhatsAppTemplateService _whatsAppTemplateService;
    private readonly IWhatsAppTemplateSampleService _sampleService;
    private readonly ILogger<WhatsAppTemplateController> _logger;

    public WhatsAppTemplateController(
        ApplicationDbContext context,
        IWhatsAppTemplateService whatsAppTemplateService,
        IWhatsAppTemplateSampleService sampleService,
        ILogger<WhatsAppTemplateController> logger,
        IUserContextService userContextService)
        : base(userContextService)
    {
        _context = context;
        _whatsAppTemplateService = whatsAppTemplateService;
        _sampleService = sampleService;
        _logger = logger;
    }

    public async Task<IActionResult> Index()
    {
        var user = GetCurrentUser();

        if (user?.Company == null)
        {
            TempData["ErrorMessage"] = "Şirket bilgisi bulunamadı.";
            return RedirectToAction("Index", "Home");
        }

        // Check if WhatsApp is configured
        var hasWhatsAppConfig = await _whatsAppTemplateService.HasWhatsAppConfigurationAsync(user.Company.Id);
        if (!hasWhatsAppConfig)
        {
            TempData["ErrorMessage"] = "WhatsApp şablonlarını kullanabilmek için önce WhatsApp entegrasyonunu yapılandırmanız gerekiyor.";
            return RedirectToAction("Index", "Integration");
        }

        // Get grouped templates from Facebook
        var groupedTemplates = await _whatsAppTemplateService.GetGroupedFacebookTemplatesAsync(user.Company.Id);

        var viewModel = new WhatsAppTemplateIndexViewModel
        {
            GroupedFacebookTemplates = groupedTemplates,
            CompanyId = user.Company.Id
        };

        return View(viewModel);
    }

    [HttpGet]
    public async Task<IActionResult> GetTemplate(string templateId)
    {
        var user = GetCurrentUser();

        if (user?.Company == null)
        {
            return Json(new { success = false, message = "Şirket bilgisi bulunamadı." });
        }

        try
        {
            var facebookTemplate = await _whatsAppTemplateService.GetFacebookTemplateAsync(user.Company.Id, templateId);
            if (facebookTemplate == null)
            {
                return Json(new { success = false, message = "Şablon bulunamadı." });
            }

            var response = new
            {
                success = true,
                template = new
                {
                    id = facebookTemplate.Id,
                    name = facebookTemplate.Name,
                    category = facebookTemplate.Category,
                    status = facebookTemplate.Status,
                    language = facebookTemplate.Language,
                    components = facebookTemplate.Components,
                }
            };

            return Json(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting WhatsApp template {TemplateId}", templateId);
            return Json(new { success = false, message = "Şablon yüklenirken hata oluştu." });
        }
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> CreateTemplate([FromBody] CreateWhatsAppTemplateRequest request)
    {
        var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);

        var user = await _context.Users
            .Include(u => u.Company)
            .FirstOrDefaultAsync(u => u.Id == userId);

        if (user?.Company == null)
        {
            return Json(new { success = false, message = "Şirket bilgisi bulunamadı." });
        }

        // Debug: Log incoming request
        _logger.LogInformation("CreateTemplate request received: {@Request}", request);
        _logger.LogInformation("Request Name: {Name}, Category: {Category}, Language: {Language}",
            request.Name, request.Category, request.Language);
        _logger.LogInformation("Components count: {Count}", request.Components?.Count ?? 0);

        if (request.Components != null)
        {
            for (int i = 0; i < request.Components.Count; i++)
            {
                var comp = request.Components[i];
                _logger.LogInformation("Component {Index}: Type={Type}, Text={Text}, Format={Format}, ButtonsCount={ButtonsCount}",
                    i, comp.Type, comp.Text, comp.Format, comp.Buttons?.Count ?? 0);
            }
        }

        if (!ModelState.IsValid)
        {
            var errors = ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage);
            _logger.LogWarning("ModelState validation failed: {Errors}", string.Join(", ", errors));
            return Json(new { success = false, message = string.Join(" ", errors) });
        }

        try
        {
            var createRequest = new CreateTemplateRequest
            {
                Name = request.Name,
                Category = request.Category,
                Language = request.Language,
                Components = request.Components
            };

            var createdTemplate = await _whatsAppTemplateService.CreateFacebookTemplateAsync(user.Company.Id, createRequest);

            if (createdTemplate != null)
            {
                return Json(new {
                    success = true,
                    message = "WhatsApp şablonu başarıyla oluşturuldu.",
                    template = createdTemplate
                });
            }
            else
            {
                return Json(new { success = false, message = "Şablon oluşturulurken hata oluştu." });
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating WhatsApp template");
            return Json(new { success = false, message = "Şablon oluşturulurken hata oluştu." });
        }
    }



    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> DeleteTemplate([FromBody] DeleteWhatsAppTemplateRequest request)
    {
        var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);

        var user = await _context.Users
            .Include(u => u.Company)
            .FirstOrDefaultAsync(u => u.Id == userId);

        if (user?.Company == null)
        {
            return Json(new { success = false, message = "Şirket bilgisi bulunamadı." });
        }

        try
        {
            _logger.LogInformation("Attempting to delete WhatsApp template {TemplateName} for company {CompanyId}",
                request.TemplateName, user.Company.Id);
            var success = await _whatsAppTemplateService.DeleteFacebookTemplateAsync(user.Company.Id, request.TemplateName);

            if (success)
            {
                return Json(new { success = true, message = "WhatsApp şablonu başarıyla silindi." });
            }
            else
            {
                return Json(new { success = false, message = "Şablon silinirken hata oluştu." });
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting WhatsApp template {TemplateName}", request.TemplateName);
            return Json(new { success = false, message = "Şablon silinirken hata oluştu." });
        }
    }

    [HttpPost]
    public async Task<IActionResult> UploadMedia(IFormFile file)
    {
        try
        {
            if (file == null || file.Length == 0)
            {
                return Json(new { success = false, message = "Dosya seçilmedi." });
            }

            // Validate file type and size
            var allowedTypes = new[] { "image/jpeg", "image/png", "image/gif", "video/mp4", "application/pdf" };
            if (!allowedTypes.Contains(file.ContentType))
            {
                return Json(new { success = false, message = "Desteklenmeyen dosya türü." });
            }

            // Size limits (WhatsApp requirements)
            var maxSize = file.ContentType.StartsWith("image/") ? 5 * 1024 * 1024 : // 5MB for images
                         file.ContentType.StartsWith("video/") ? 16 * 1024 * 1024 : // 16MB for videos
                         100 * 1024 * 1024; // 100MB for documents

            if (file.Length > maxSize)
            {
                return Json(new { success = false, message = "Dosya boyutu çok büyük." });
            }

            // Create uploads directory if it doesn't exist
            var uploadsPath = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", "uploads", "whatsapp");
            if (!Directory.Exists(uploadsPath))
            {
                Directory.CreateDirectory(uploadsPath);
            }

            // Generate unique filename
            var fileName = $"{Guid.NewGuid()}{Path.GetExtension(file.FileName)}";
            var filePath = Path.Combine(uploadsPath, fileName);

            // Save file
            using (var stream = new FileStream(filePath, FileMode.Create))
            {
                await file.CopyToAsync(stream);
            }

            // Return URL
            var fileUrl = $"/uploads/whatsapp/{fileName}";
            return Json(new {
                success = true,
                url = fileUrl,
                fileName = file.FileName,
                size = file.Length,
                type = file.ContentType
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error uploading media file");
            return Json(new { success = false, message = "Dosya yüklenirken hata oluştu." });
        }
    }

    [HttpGet]
    public IActionResult GetSampleTemplates()
    {
        try
        {
            var samples = _sampleService.GetSampleTemplates();
            return Json(new { success = true, templates = samples });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting sample templates");
            return Json(new { success = false, message = "Hazır şablonlar yüklenirken hata oluştu." });
        }
    }

    [HttpGet]
    public IActionResult GetSampleTemplate(string id)
    {
        try
        {
            var sample = _sampleService.GetSampleTemplate(id);
            if (sample == null)
            {
                return Json(new { success = false, message = "Şablon bulunamadı." });
            }

            return Json(new { success = true, template = sample });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting sample template {TemplateId}", id);
            return Json(new { success = false, message = "Şablon yüklenirken hata oluştu." });
        }
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> CreateFromSample([FromBody] CreateFromSampleRequest request)
    {
        var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);

        var user = await _context.Users
            .Include(u => u.Company)
            .FirstOrDefaultAsync(u => u.Id == userId);

        if (user?.Company == null)
        {
            return Json(new { success = false, message = "Şirket bilgisi bulunamadı." });
        }

        try
        {
            var sample = _sampleService.GetSampleTemplate(request.SampleId);
            if (sample == null)
            {
                return Json(new { success = false, message = "Hazır şablon bulunamadı." });
            }

            var createRequest = new CreateTemplateRequest
            {
                Name = request.TemplateName,
                Category = sample.Category,
                Language = sample.Language,
                Components = sample.Components
            };

            var createdTemplate = await _whatsAppTemplateService.CreateFacebookTemplateAsync(user.Company.Id, createRequest);


            return Json(new { success = true, message = "Şablon başarıyla oluşturuldu ve onay için gönderildi." });

        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating template from sample {SampleId}", request.SampleId);
            return Json(new { success = false, message = "Şablon oluşturulurken hata oluştu." });
        }
    }
}

// Request models
public class CreateWhatsAppTemplateRequest
{
    public string Name { get; set; } = string.Empty;
    public string Category { get; set; } = "MARKETING";
    public string Language { get; set; } = "tr";
    public List<TemplateComponent> Components { get; set; } = new();
}



public class DeleteWhatsAppTemplateRequest
{
    [JsonPropertyName("templateName")]
    public string TemplateName { get; set; }
}

public class CreateFromSampleRequest
{
    public string SampleId { get; set; } = string.Empty;
    public string TemplateName { get; set; } = string.Empty;
}
