using Microsoft.AspNetCore.Mvc;
using PushDashboard.Services;
using PushDashboard.Models.ViewModels;
using System.Text.Json;

namespace PushDashboard.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class ExitSurveyApiController : ControllerBase
    {
        private readonly IExitSurveyService _exitSurveyService;
        private readonly ILogger<ExitSurveyApiController> _logger;

        public ExitSurveyApiController(IExitSurveyService exitSurveyService, ILogger<ExitSurveyApiController> logger)
        {
            _exitSurveyService = exitSurveyService;
            _logger = logger;
        }

        /// <summary>
        /// Widget script'ini döndürür
        /// </summary>
        [HttpGet("widget/{companyId}")]
        public async Task<IActionResult> GetWidget(Guid companyId)
        {
            try
            {
                var config = await _exitSurveyService.GetScriptConfigAsync(companyId);
                if (config == null)
                {
                    return NotFound("Exit survey configuration not found");
                }

                var configJson = JsonSerializer.Serialize(config, new JsonSerializerOptions
                {
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                });

                var scriptContent = GenerateJavaScriptWidget(configJson, companyId);

                Response.Headers["Cache-Control"] = "public, max-age=300"; // 5 dakika cache
                Response.Headers["Content-Type"] = "application/javascript; charset=utf-8";
                Response.Headers["X-Content-Type-Options"] = "nosniff";

                return Content(scriptContent, "application/javascript");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating widget script for company {CompanyId}", companyId);
                return StatusCode(500, "// Error generating widget script");
            }
        }

        /// <summary>
        /// Anket cevaplarını alır
        /// </summary>
        [HttpPost("submit")]
        public async Task<IActionResult> SubmitResponse([FromBody] SubmitExitSurveyResponseViewModel model)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(new { success = false, message = "Geçersiz veri" });
                }

                // IP address ve user agent bilgilerini al
                var ipAddress = HttpContext.Connection.RemoteIpAddress?.ToString();
                var userAgent = Request.Headers["User-Agent"].ToString();

                var success = await _exitSurveyService.SubmitResponseAsync(model, ipAddress, userAgent);

                if (success)
                {
                    return Ok(new { success = true, message = "Cevaplarınız kaydedildi. Teşekkür ederiz!" });
                }
                else
                {
                    return BadRequest(new { success = false, message = "Cevaplar kaydedilirken bir hata oluştu" });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error submitting exit survey response");
                return StatusCode(500, new { success = false, message = "Sunucu hatası" });
            }
        }

        private string GenerateJavaScriptWidget(string configJson, Guid companyId)
        {
            var baseUrl = $"{Request.Scheme}://{Request.Host}";

            var script = $@"
(function() {{
    'use strict';
    
    // CSS dosyasını yükle
    const cssLink = document.createElement('link');
    cssLink.rel = 'stylesheet';
    cssLink.href = '{baseUrl}/css/exit-survey-widget.css';
    document.head.appendChild(cssLink);
    
    // JavaScript dosyasını yükle
    const script = document.createElement('script');
    script.src = '{baseUrl}/js/exit-survey-widget.js';
    script.onload = function() {{
        // Widget'ı başlat
        const config = {configJson};
        if (window.initExitSurvey) {{
            window.initExitSurvey(config);
        }}
    }};
    document.head.appendChild(script);
}})();";

            return script;
        }
    }
}
