using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using PushDashboard.Data;
using PushDashboard.Services;
using PushDashboard.ViewModels;
using System.Security.Claims;
using System.Text.Json;

namespace PushDashboard.Controllers;

[Authorize]
public class EmailTemplateController : BaseController
{
    private readonly ApplicationDbContext _context;
    private readonly IEmailTemplateService _emailTemplateService;
    private readonly ILogger<EmailTemplateController> _logger;

    public EmailTemplateController(
        ApplicationDbContext context,
        IEmailTemplateService emailTemplateService,
        ILogger<EmailTemplateController> logger,
        IUserContextService userContextService)
        : base(userContextService)
    {
        _context = context;
        _emailTemplateService = emailTemplateService;
        _logger = logger;
    }

    public async Task<IActionResult> Index()
    {
        var user = GetCurrentUser();

        if (user?.Company == null)
        {
            TempData["ErrorMessage"] = "Şirket bilgisi bulunamadı.";
            return RedirectToAction("Index", "Home");
        }

        // Check if SMTP is configured
        var hasSmtpConfig = await _emailTemplateService.HasSmtpConfigurationAsync(user.Company.Id);
        if (!hasSmtpConfig)
        {
            TempData["ErrorMessage"] = "Mail şablonlarını kullanabilmek için önce SMTP entegrasyonunu yapılandırmanız gerekiyor.";
            return RedirectToAction("Index", "Integration");
        }

        // Get grouped templates
        var groupedTemplates = await _emailTemplateService.GetGroupedTemplatesAsync(user.Company.Id, GetCurrentUserId());

        var viewModel = new EmailTemplateIndexViewModel
        {
            GroupedTemplates = groupedTemplates,
            CompanyId = user.Company.Id
        };

        return View(viewModel);
    }

    [HttpGet]
    public async Task<IActionResult> GetTemplate(int templateId, int? companyTemplateId = null)
    {
        var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);

        var user = await _context.Users
            .Include(u => u.Company)
            .FirstOrDefaultAsync(u => u.Id == userId);

        if (user?.Company == null)
        {
            return Json(new { success = false, message = "Şirket bilgisi bulunamadı." });
        }

        try
        {
            // If companyTemplateId is provided, this is a custom template
            if (companyTemplateId.HasValue)
            {
                var customTemplate = await _context.CompanyEmailTemplates
                    .Include(cet => cet.LastModifiedByUser)
                    .FirstOrDefaultAsync(cet => cet.Id == companyTemplateId.Value && cet.CompanyId == user.Company.Id);

                if (customTemplate == null)
                {
                    return Json(new { success = false, message = "Özel şablon bulunamadı." });
                }

                // Parse metadata from content
                var (name, category, description, cleanContent) = ParseCustomTemplateMetadata(customTemplate.CustomContent);

                var customResponse = new
                {
                    success = true,
                    template = new
                    {
                        id = 1000 + customTemplate.Id, // Use same ID format as service
                        companyTemplateId = customTemplate.Id,
                        name = name,
                        category = category,
                        description = description,
                        defaultSubject = customTemplate.CustomSubject,
                        defaultContent = cleanContent,
                        customSubject = customTemplate.CustomSubject,
                        customContent = cleanContent,
                        variables = new string[] { "customerName", "customerEmail", "companyName", "companyAddress", "registrationDate", "updateDate", "updatedFields", "itemCount", "basketTotal", "basketUrl", "orderNumber", "orderDate", "orderTotal", "deliveryAddress", "trackingUrl" },
                        isCustomized = true,
                        isCustomTemplate = true,
                        lastModified = customTemplate.UpdatedAt,
                        lastModifiedBy = customTemplate.LastModifiedByUser?.FullName
                    }
                };

                return Json(customResponse);
            }

            // Regular base template
            var baseTemplate = await _context.EmailTemplates.FindAsync(templateId);
            if (baseTemplate == null)
            {
                return Json(new { success = false, message = "Şablon bulunamadı." });
            }

            // Get company customization if exists
            var companyTemplate = await _emailTemplateService.GetCompanyTemplateAsync(user.Company.Id, templateId);

            var response = new
            {
                success = true,
                template = new
                {
                    id = baseTemplate.Id,
                    companyTemplateId = companyTemplate?.Id,
                    name = baseTemplate.Name,
                    category = baseTemplate.Category,
                    description = baseTemplate.Description,
                    defaultSubject = baseTemplate.DefaultSubject,
                    defaultContent = baseTemplate.DefaultContent,
                    customSubject = companyTemplate?.CustomSubject,
                    customContent = companyTemplate?.CustomContent,
                    variables = baseTemplate.Variables != null ? JsonSerializer.Deserialize<string[]>(baseTemplate.Variables) : new string[0],
                    isCustomized = companyTemplate != null,
                    isCustomTemplate = false,
                    lastModified = companyTemplate?.UpdatedAt,
                    lastModifiedBy = companyTemplate?.LastModifiedByUser?.FullName
                }
            };

            return Json(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting template {TemplateId}", templateId);
            return Json(new { success = false, message = "Şablon yüklenirken hata oluştu." });
        }
    }

    private (string name, string category, string description, string cleanContent) ParseCustomTemplateMetadata(string content)
    {
        var name = "Özel Şablon";
        var category = "Özel";
        var description = "";
        var cleanContent = content;

        // Look for metadata comment at the beginning
        if (content.StartsWith("<!-- CUSTOM_TEMPLATE:"))
        {
            var endIndex = content.IndexOf("-->");
            if (endIndex > 0)
            {
                var metadataLine = content.Substring(0, endIndex + 3);
                cleanContent = content.Substring(endIndex + 3).TrimStart('\n', '\r');

                // Parse metadata: <!-- CUSTOM_TEMPLATE: name | category | description -->
                var metadataContent = metadataLine.Replace("<!-- CUSTOM_TEMPLATE:", "").Replace("-->", "").Trim();
                var parts = metadataContent.Split('|');

                if (parts.Length >= 1) name = parts[0].Trim();
                if (parts.Length >= 2) category = parts[1].Trim();
                if (parts.Length >= 3) description = parts[2].Trim();
            }
        }

        return (name, category, description, cleanContent);
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> SaveTemplate([FromBody] SaveTemplateRequest request)
    {
        if (!ModelState.IsValid)
        {
            var errors = ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage);
            return Json(new { success = false, message = string.Join(" ", errors) });
        }

        var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);

        var user = await _context.Users
            .Include(u => u.Company)
            .FirstOrDefaultAsync(u => u.Id == userId);

        if (user?.Company == null)
        {
            return Json(new { success = false, message = "Şirket bilgisi bulunamadı." });
        }

        try
        {
            bool success;

            // If companyTemplateId is provided, update the custom template directly
            if (request.CompanyTemplateId.HasValue)
            {
                var customTemplate = await _context.CompanyEmailTemplates
                    .FirstOrDefaultAsync(cet => cet.Id == request.CompanyTemplateId.Value && cet.CompanyId == user.Company.Id);

                if (customTemplate == null)
                {
                    return Json(new { success = false, message = "Özel şablon bulunamadı." });
                }

                // Parse existing metadata
                var (name, category, description, _) = ParseCustomTemplateMetadata(customTemplate.CustomContent);

                // Rebuild content with metadata
                var templateMetadata = $"<!-- CUSTOM_TEMPLATE: {name} | {category} | {description} -->";
                var fullContent = templateMetadata + "\n" + request.Content;

                customTemplate.CustomContent = fullContent;
                customTemplate.CustomSubject = request.Subject;
                customTemplate.LastModifiedBy = userId;
                customTemplate.UpdatedAt = DateTime.UtcNow;

                await _context.SaveChangesAsync();
                success = true;
            }
            else
            {
                // Regular base template customization
                success = await _emailTemplateService.SaveCompanyTemplateAsync(
                    user.Company.Id,
                    request.TemplateId,
                    request.Content,
                    request.Subject,
                    userId);
            }

            if (success)
            {
                return Json(new { success = true, message = "Şablon başarıyla kaydedildi." });
            }
            else
            {
                return Json(new { success = false, message = "Şablon kaydedilirken hata oluştu." });
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error saving template {TemplateId}", request.TemplateId);
            return Json(new { success = false, message = "Şablon kaydedilirken hata oluştu." });
        }
    }

    [HttpPost]
    public async Task<IActionResult> PreviewTemplate([FromBody] PreviewTemplateRequest request)
    {
        var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);

        var user = await _context.Users
            .Include(u => u.Company)
            .FirstOrDefaultAsync(u => u.Id == userId);

        if (user?.Company == null)
        {
            return Json(new { success = false, message = "Şirket bilgisi bulunamadı." });
        }

        try
        {
            // Sample data for preview
            var sampleData = new Dictionary<string, string>
            {
                ["customerName"] = "Ahmet Yılmaz",
                ["customerEmail"] = "<EMAIL>",
                ["companyName"] = "Örnek Şirket",
                ["companyAddress"] = "İstanbul, Türkiye",
                ["registrationDate"] = DateTime.Now.ToString("dd.MM.yyyy"),
                ["updateDate"] = DateTime.Now.ToString("dd.MM.yyyy HH:mm"),
                ["updatedFields"] = "Ad, Soyad, Telefon",
                ["itemCount"] = "3",
                ["basketTotal"] = "₺299,90",
                ["basketUrl"] = "#",
                ["orderNumber"] = "ORD-2024-001",
                ["orderDate"] = DateTime.Now.ToString("dd.MM.yyyy"),
                ["orderTotal"] = "₺299,90",
                ["deliveryAddress"] = "Kadıköy, İstanbul",
                ["trackingUrl"] = "#"
            };

            string content;

            // If this is a custom template or user provided content, use that
            if (!string.IsNullOrEmpty(request.Content))
            {
                content = request.Content;
            }
            else if (request.CompanyTemplateId.HasValue)
            {
                // Get custom template content
                var customTemplate = await _context.CompanyEmailTemplates
                    .FirstOrDefaultAsync(cet => cet.Id == request.CompanyTemplateId.Value && cet.CompanyId == user.Company.Id);

                if (customTemplate == null)
                {
                    return Json(new { success = false, message = "Özel şablon bulunamadı." });
                }

                // Parse metadata and get clean content
                var (_, _, _, cleanContent) = ParseCustomTemplateMetadata(customTemplate.CustomContent);
                content = cleanContent;
            }
            else
            {
                // Get base template content
                var baseTemplate = await _context.EmailTemplates.FindAsync(request.TemplateId);
                if (baseTemplate == null)
                {
                    return Json(new { success = false, message = "Şablon bulunamadı." });
                }

                // Check if company has customization
                var companyTemplate = await _emailTemplateService.GetCompanyTemplateAsync(user.Company.Id, request.TemplateId);
                content = companyTemplate?.CustomContent ?? baseTemplate.DefaultContent;
            }

            // Replace variables with sample data
            foreach (var kvp in sampleData)
            {
                content = content.Replace($"{{{{{kvp.Key}}}}}", kvp.Value);
            }

            return Json(new { success = true, preview = content });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error previewing template {TemplateId}", request.TemplateId);
            return Json(new { success = false, message = "Önizleme oluşturulurken hata oluştu." });
        }
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> CreateTemplate([FromBody] CreateEmailTemplateRequest request)
    {
        if (!ModelState.IsValid)
        {
            var errors = ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage);
            return Json(new { success = false, message = string.Join(" ", errors) });
        }

        var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);

        var user = await _context.Users
            .Include(u => u.Company)
            .FirstOrDefaultAsync(u => u.Id == userId);

        if (user?.Company == null)
        {
            return Json(new { success = false, message = "Şirket bilgisi bulunamadı." });
        }

        try
        {
            await _emailTemplateService.CreateCustomTemplateAsync(
                user.Company.Id,
                request.Name,
                request.Category,
                request.Description,
                request.Subject,
                request.Content,
                userId);


            return Json(new { success = true, message = "Şablon başarıyla oluşturuldu." });

        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating custom template for company {CompanyId}", user.Company.Id);
            return Json(new { success = false, message = "Şablon oluşturulurken hata oluştu." });
        }
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> ResetTemplate([FromBody] ResetTemplateRequest request)
    {
        var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);

        var user = await _context.Users
            .Include(u => u.Company)
            .FirstOrDefaultAsync(u => u.Id == userId);

        if (user?.Company == null)
        {
            return Json(new { success = false, message = "Şirket bilgisi bulunamadı." });
        }

        try
        {
            var companyTemplate = await _context.CompanyEmailTemplates
                .FirstOrDefaultAsync(cet => cet.CompanyId == user.Company.Id && cet.EmailTemplateId == request.TemplateId);

            if (companyTemplate != null)
            {
                _context.CompanyEmailTemplates.Remove(companyTemplate);
                await _context.SaveChangesAsync();
            }

            return Json(new { success = true, message = "Şablon varsayılan haline sıfırlandı." });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error resetting template {TemplateId}", request.TemplateId);
            return Json(new { success = false, message = "Şablon sıfırlanırken hata oluştu." });
        }
    }
}
