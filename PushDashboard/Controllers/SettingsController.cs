﻿﻿﻿﻿﻿﻿﻿﻿﻿﻿﻿﻿﻿﻿﻿﻿﻿﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using PushDashboard.Data;
using PushDashboard.Models;
using PushDashboard.ViewModels;
using PushDashboard.Services;
using Microsoft.AspNetCore.SignalR;
using PushDashboard.Hubs;
using System.Security.Claims;
namespace PushDashboard.Controllers;

public class SettingsController : BaseController
{
    private readonly UserManager<ApplicationUser> _userManager;
    private readonly ApplicationDbContext _context;
    private readonly ILogger<SettingsController> _logger;
    private readonly ITwoFactorService _twoFactorService;
    private readonly IHubContext<SessionHub> _hubContext;
    private readonly IInvitationService _invitationService;

    public SettingsController(
        UserManager<ApplicationUser> userManager,
        ApplicationDbContext context,
        ITwoFactorService twoFactorService,
        ILogger<SettingsController> logger,
        IInvitationService invitationService,
        IHubContext<SessionHub> hubContext,
        IUserContextService userContextService)
        : base(userContextService)
    {
        _userManager = userManager;
        _context = context;
        _logger = logger;
        _twoFactorService = twoFactorService;
        _invitationService = invitationService;
        _hubContext = hubContext;
    }

    [HttpGet]
    public async Task<IActionResult> Index()
    {
        var user = await _userManager.GetUserAsync(User);
        if (user == null)
        {
            return NotFound();
        }

        // Load user with company
        var userWithCompany = await _context.Users
            .Include(u => u.Company)
            .Include(u => u.NotificationPreferences)
            .FirstOrDefaultAsync(u => u.Id == user.Id);

        if (userWithCompany == null)
        {
            return NotFound();
        }

        // Check if user can edit company (has CompanyOwner role)
        var canEditCompany = await _userManager.IsInRoleAsync(user, "CompanyOwner");

        // Check if user can manage users (has CompanyOwner role)
        var canManageUsers = await _userManager.IsInRoleAsync(user, "CompanyOwner");

        var model = new SettingsViewModel
        {
            UserProfile = new UserProfileViewModel
            {
                FirstName = userWithCompany.FirstName,
                LastName = userWithCompany.LastName,
                Email = userWithCompany.Email,
                PhoneNumber = userWithCompany.PhoneNumber
            },
            CompanyProfile = new CompanyProfileViewModel
            {
                Name = userWithCompany.Company?.Name ?? "",
                Address = userWithCompany.Company?.Address,
                Phone = userWithCompany.Company?.Phone,
                Email = userWithCompany.Company?.Email,
                Website = userWithCompany.Company?.Website
            },
            CanEditCompany = canEditCompany,
            CanManageUsers = canManageUsers,
            TwoFactorStatus = new TwoFactorStatusViewModel
            {
                IsEnabled = userWithCompany.TwoFactorEnabled,
                HasRecoveryCodes = false,
                RecoveryCodesLeft = 0,
                IsMachineRemembered = false
            },
            NotificationPreferences = new NotificationPreferencesViewModel
            {
                EmailInvoiceNotifications = userWithCompany.NotificationPreferences?.EmailInvoiceNotifications ?? true,
                EmailCreditNotifications = userWithCompany.NotificationPreferences?.EmailCreditNotifications ?? true,
                EmailMarketingNotifications = userWithCompany.NotificationPreferences?.EmailMarketingNotifications ?? false,
                SmsSecurityAlerts = userWithCompany.NotificationPreferences?.SmsSecurityAlerts ?? true,
                SmsPaymentNotifications = userWithCompany.NotificationPreferences?.SmsPaymentNotifications ?? false
            },
            CompanyName = userWithCompany.Company?.Name,
        BillingInformation = new BillingInformationViewModel
        {
            BillingType = userWithCompany.Company?.BillingType ?? "Corporate",
            TaxOffice = userWithCompany.Company?.TaxOffice,
            TaxNumber = userWithCompany.Company?.TaxNumber,
            IdentityNumber = userWithCompany.Company?.IdentityNumber,
            BillingAddress = userWithCompany.Company?.BillingAddress ?? ""
            ,
            CompanyName = userWithCompany.Company?.CompanyName,
            FullName = userWithCompany.Company?.FullName        }
        };

        return View(model);
    }

    // AJAX Endpoints
    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> UpdateProfileAjax([FromBody] UserProfileViewModel model)
    {
        try
        {
            _logger.LogInformation("UpdateProfileAjax called with model: FirstName={FirstName}, LastName={LastName}, PhoneNumber={PhoneNumber}",
                model.FirstName, model.LastName, model.PhoneNumber);

            // Manual validation
            var errors = new List<string>();

            if (string.IsNullOrWhiteSpace(model.FirstName) || model.FirstName.Length < 2 || model.FirstName.Length > 100)
            {
                errors.Add("Ad en az 2, en fazla 100 karakter olmalıdır.");
            }

            if (string.IsNullOrWhiteSpace(model.LastName) || model.LastName.Length < 2 || model.LastName.Length > 100)
            {
                errors.Add("Soyad en az 2, en fazla 100 karakter olmalıdır.");
            }

            if (string.IsNullOrWhiteSpace(model.Email) || !IsValidEmail(model.Email))
            {
                errors.Add("Geçerli bir e-posta adresi giriniz.");
            }

            if (errors.Any())
            {
                return Json(new { success = false, message = string.Join(" ", errors) });
            }

            var user = await _userManager.GetUserAsync(User);
            if (user == null)
            {
                return Json(new { success = false, message = "Kullanıcı bulunamadı." });
            }

            user.FirstName = model.FirstName;
            user.LastName = model.LastName;
            user.PhoneNumber = model.PhoneNumber;

            var result = await _userManager.UpdateAsync(user);

            if (result.Succeeded)
            {
                _logger.LogInformation("Profile updated successfully for user {UserId}", user.Id);
                return Json(new { success = true, message = "Profil bilgileriniz başarıyla güncellendi." });
            }
            else
            {
                var errorMessages = string.Join(", ", result.Errors.Select(e => e.Description));
                _logger.LogError("Failed to update profile for user {UserId}: {Errors}", user.Id, errorMessages);
                return Json(new { success = false, message = $"Profil güncellenirken bir hata oluştu: {errorMessages}" });
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in UpdateProfileAjax");
            return Json(new { success = false, message = "Beklenmeyen bir hata oluştu." });
        }
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    [Authorize(Roles = "CompanyOwner")]
    public async Task<IActionResult> UpdateCompanyAjax([FromBody] CompanyProfileViewModel model)
    {
        try
        {
            _logger.LogInformation("UpdateCompanyAjax called with model: Name={Name}", model.Name);

            // Manual validation
            var errors = new List<string>();

            if (string.IsNullOrWhiteSpace(model.Name) || model.Name.Length < 2 || model.Name.Length > 100)
            {
                errors.Add("Firma adı en az 2, en fazla 100 karakter olmalıdır.");
            }

            if (!string.IsNullOrWhiteSpace(model.Email) && !IsValidEmail(model.Email))
            {
                errors.Add("Geçerli bir e-posta adresi giriniz.");
            }

            if (!string.IsNullOrWhiteSpace(model.Website) && !IsValidWebsite(model.Website))
            {
                errors.Add("Geçerli bir website adresi giriniz (http:// veya https:// ile başlamalı).");
            }

            if (errors.Any())
            {
                return Json(new { success = false, message = string.Join(" ", errors) });
            }

            var user = await _userManager.GetUserAsync(User);
            if (user == null)
            {
                return Json(new { success = false, message = "Kullanıcı bulunamadı." });
            }

            var userWithCompany = await _context.Users
                .Include(u => u.Company)
            .Include(u => u.NotificationPreferences)
                .FirstOrDefaultAsync(u => u.Id == user.Id);

            if (userWithCompany?.Company == null)
            {
                return Json(new { success = false, message = "Firma bilgisi bulunamadı." });
            }

            // Check if company name is being changed and if it already exists
            if (userWithCompany.Company.Name != model.Name)
            {
                var existingCompany = await _context.Companies
                    .FirstOrDefaultAsync(c => c.Name.ToLower() == model.Name.ToLower() && c.Id != userWithCompany.Company.Id);

                if (existingCompany != null)
                {
                    return Json(new { success = false, message = "Bu firma adı zaten kullanılmaktadır." });
                }
            }

            // Update company information
            userWithCompany.Company.Name = model.Name;
            userWithCompany.Company.Address = model.Address;
            userWithCompany.Company.Phone = model.Phone;
            userWithCompany.Company.Email = model.Email;
            userWithCompany.Company.Website = model.Website;

            await _context.SaveChangesAsync();
            _logger.LogInformation("Company updated successfully for user {UserId}", user.Id);
            return Json(new { success = true, message = "Firma bilgileri başarıyla güncellendi." });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in UpdateCompanyAjax");
            return Json(new { success = false, message = "Beklenmeyen bir hata oluştu." });
        }
    }

    // Legacy form endpoints (keep for backward compatibility)
    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> UpdateProfile(SettingsViewModel model)
    {
        _logger.LogInformation("UpdateProfile called with model: FirstName={FirstName}, LastName={LastName}, PhoneNumber={PhoneNumber}",
            model.UserProfile.FirstName, model.UserProfile.LastName, model.UserProfile.PhoneNumber);

        // Sadece UserProfile kısmını validate et
        var userProfileErrors = ModelState
            .Where(x => x.Key.StartsWith("UserProfile."))
            .SelectMany(x => x.Value.Errors)
            .Select(x => x.ErrorMessage)
            .ToList();

        if (userProfileErrors.Any())
        {
            var errors = string.Join(", ", userProfileErrors);
            _logger.LogWarning("UserProfile validation failed: {Errors}", errors);
            TempData["ErrorMessage"] = $"Lütfen profil alanlarını doğru şekilde doldurun: {errors}";

            // Rebuild the complete SettingsViewModel with the user's input preserved
            var settingsModel = await BuildSettingsViewModelWithInput(model.UserProfile, null);
            return View("Index", settingsModel);
        }

        var user = await _userManager.GetUserAsync(User);
        if (user == null)
        {
            _logger.LogWarning("User not found");
            return NotFound();
        }

        _logger.LogInformation("Updating user {UserId}: {FirstName} -> {NewFirstName}, {LastName} -> {NewLastName}, {PhoneNumber} -> {NewPhoneNumber}",
            user.Id, user.FirstName, model.UserProfile.FirstName, user.LastName, model.UserProfile.LastName, user.PhoneNumber, model.UserProfile.PhoneNumber);

        user.FirstName = model.UserProfile.FirstName;
        user.LastName = model.UserProfile.LastName;
        user.PhoneNumber = model.UserProfile.PhoneNumber;

        var result = await _userManager.UpdateAsync(user);

        if (result.Succeeded)
        {
            _logger.LogInformation("Profile updated successfully for user {UserId}", user.Id);
            TempData["SuccessMessage"] = "Profil bilgileriniz başarıyla güncellendi.";
        }
        else
        {
            var errors = string.Join(", ", result.Errors.Select(e => e.Description));
            _logger.LogError("Failed to update profile for user {UserId}: {Errors}", user.Id, errors);
            TempData["ErrorMessage"] = $"Profil güncellenirken bir hata oluştu: {errors}";
        }

        return RedirectToAction(nameof(Index));
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    [Authorize(Roles = "CompanyOwner")]
    public async Task<IActionResult> UpdateCompany(SettingsViewModel model)
    {
        // Sadece CompanyProfile kısmını validate et
        var companyProfileErrors = ModelState
            .Where(x => x.Key.StartsWith("CompanyProfile."))
            .SelectMany(x => x.Value.Errors)
            .Select(x => x.ErrorMessage)
            .ToList();

        // Custom validation for optional fields
        var customErrors = new List<string>();

        if (!model.CompanyProfile.IsValidEmail())
        {
            customErrors.Add("Geçerli bir e-posta adresi giriniz.");
        }

        if (!model.CompanyProfile.IsValidWebsite())
        {
            customErrors.Add("Geçerli bir website adresi giriniz (http:// veya https:// ile başlamalı).");
        }

        if (companyProfileErrors.Any() || customErrors.Any())
        {
            var allErrors = companyProfileErrors.Concat(customErrors);
            var errors = string.Join(", ", allErrors);
            _logger.LogWarning("CompanyProfile validation failed: {Errors}", errors);
            TempData["ErrorMessage"] = $"Lütfen firma alanlarını doğru şekilde doldurun: {errors}";

            // Rebuild the complete SettingsViewModel with the company input preserved
            var settingsModel = await BuildSettingsViewModelWithInput(null, model.CompanyProfile);
            return View("Index", settingsModel);
        }

        var user = await _userManager.GetUserAsync(User);
        if (user == null)
        {
            return NotFound();
        }

        var userWithCompany = await _context.Users
            .Include(u => u.Company)
            .Include(u => u.NotificationPreferences)
            .FirstOrDefaultAsync(u => u.Id == user.Id);

        if (userWithCompany?.Company == null)
        {
            TempData["ErrorMessage"] = "Firma bilgisi bulunamadı.";
            var settingsModel = await BuildSettingsViewModelWithInput(null, model.CompanyProfile);
            return View("Index", settingsModel);
        }

        // Check if company name is being changed and if it already exists
        if (userWithCompany.Company.Name != model.CompanyProfile.Name)
        {
            var existingCompany = await _context.Companies
                .FirstOrDefaultAsync(c => c.Name.ToLower() == model.CompanyProfile.Name.ToLower() && c.Id != userWithCompany.Company.Id);

            if (existingCompany != null)
            {
                TempData["ErrorMessage"] = "Bu firma adı zaten kullanılmaktadır.";
                var settingsModel = await BuildSettingsViewModelWithInput(null, model.CompanyProfile);
                return View("Index", settingsModel);
            }
        }

        // Update company information
        userWithCompany.Company.Name = model.CompanyProfile.Name;
        userWithCompany.Company.Address = model.CompanyProfile.Address;
        userWithCompany.Company.Phone = model.CompanyProfile.Phone;
        userWithCompany.Company.Email = model.CompanyProfile.Email;
        userWithCompany.Company.Website = model.CompanyProfile.Website;

        try
        {
            await _context.SaveChangesAsync();
            TempData["SuccessMessage"] = "Firma bilgileri başarıyla güncellendi.";
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating company information");
            TempData["ErrorMessage"] = "Firma bilgileri güncellenirken bir hata oluştu.";
        }

        return RedirectToAction(nameof(Index));
    }

    /// <summary>
    /// Helper method to build SettingsViewModel while preserving user input from failed form submissions
    /// </summary>
    private async Task<SettingsViewModel> BuildSettingsViewModelWithInput(
        UserProfileViewModel? userProfileInput,
        CompanyProfileViewModel? companyProfileInput)
    {
        var user = await _userManager.GetUserAsync(User);
        if (user == null)
        {
            throw new InvalidOperationException("User not found");
        }

        // Load user with company
        var userWithCompany = await _context.Users
            .Include(u => u.Company)
            .Include(u => u.NotificationPreferences)
            .FirstOrDefaultAsync(u => u.Id == user.Id);

        if (userWithCompany == null)
        {
            throw new InvalidOperationException("User not found in database");
        }

        // Check if user can edit company (has CompanyOwner role)
        var canEditCompany = await _userManager.IsInRoleAsync(user, "CompanyOwner");

        // Check if user can manage users (has CompanyOwner role)
        var canManageUsers = await _userManager.IsInRoleAsync(user, "CompanyOwner");

        var model = new SettingsViewModel
        {
            // Use user input if provided, otherwise use database values
            UserProfile = userProfileInput ?? new UserProfileViewModel
            {
                FirstName = userWithCompany.FirstName,
                LastName = userWithCompany.LastName,
                Email = userWithCompany.Email,
                PhoneNumber = userWithCompany.PhoneNumber
            },
            // Use company input if provided, otherwise use database values
            CompanyProfile = companyProfileInput ?? new CompanyProfileViewModel
            {
                Name = userWithCompany.Company?.Name ?? "",
                Address = userWithCompany.Company?.Address,
                Phone = userWithCompany.Company?.Phone,
                Email = userWithCompany.Company?.Email,
                Website = userWithCompany.Company?.Website
            },
            CanEditCompany = canEditCompany,
            CanManageUsers = canManageUsers,
            TwoFactorStatus = new TwoFactorStatusViewModel
            {
                IsEnabled = userWithCompany.TwoFactorEnabled,
                HasRecoveryCodes = false,
                RecoveryCodesLeft = 0,
                IsMachineRemembered = false
            },
            NotificationPreferences = new NotificationPreferencesViewModel
            {
                EmailInvoiceNotifications = userWithCompany.NotificationPreferences?.EmailInvoiceNotifications ?? true,
                EmailMarketingNotifications = userWithCompany.NotificationPreferences?.EmailMarketingNotifications ?? false,
                SmsSecurityAlerts = userWithCompany.NotificationPreferences?.SmsSecurityAlerts ?? true,
                SmsPaymentNotifications = userWithCompany.NotificationPreferences?.SmsPaymentNotifications ?? false
            },
            CompanyName = userWithCompany.Company?.Name,
        BillingInformation = new BillingInformationViewModel
        {
            BillingType = userWithCompany.Company?.BillingType ?? "Corporate",
            TaxOffice = userWithCompany.Company?.TaxOffice,
            TaxNumber = userWithCompany.Company?.TaxNumber,
            IdentityNumber = userWithCompany.Company?.IdentityNumber,
            BillingAddress = userWithCompany.Company?.BillingAddress ?? ""
            ,
            CompanyName = userWithCompany.Company?.CompanyName,
            FullName = userWithCompany.Company?.FullName        }
        };

        return model;
    }

    // Helper methods for validation
    private static bool IsValidEmail(string email)
    {
        try
        {
            var addr = new System.Net.Mail.MailAddress(email);
            return addr.Address == email;
        }
        catch
        {
            return false;
        }
    }

    // Two Factor Authentication Endpoints
    [HttpGet]
    public async Task<IActionResult> GetTwoFactorStatus()
    {
        var user = await _userManager.GetUserAsync(User);
        if (user == null)
        {
            return Json(new { success = false, message = "Kullanıcı bulunamadı." });
        }

        var status = new TwoFactorStatusViewModel
        {
            IsEnabled = user.TwoFactorEnabled,
            HasRecoveryCodes = false, // TODO: Implement recovery codes
            RecoveryCodesLeft = 0,
            IsMachineRemembered = false // TODO: Implement machine remembering
        };

        return Json(new { success = true, data = status });
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> SetupTwoFactor()
    {
        try
        {
            var user = await _userManager.GetUserAsync(User);
            if (user == null)
            {
                return Json(new { success = false, message = "Kullanıcı bulunamadı." });
            }

            if (user.TwoFactorEnabled)
            {
                return Json(new { success = false, message = "İki faktörlü doğrulama zaten etkin." });
            }

            var secretKey = _twoFactorService.GenerateSecretKey();
            var qrCodeUri = _twoFactorService.GenerateQrCodeUri(user.Email!, secretKey);

            var setupModel = new TwoFactorSetupViewModel
            {
                QrCodeUri = qrCodeUri,
                ManualEntryKey = secretKey,
                IsEnabled = false
            };

            // Store the secret key temporarily in TempData
            TempData["TempSecretKey"] = secretKey;

            return Json(new { success = true, data = setupModel });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error setting up two-factor authentication");
            return Json(new { success = false, message = "İki faktörlü doğrulama kurulumu sırasında bir hata oluştu." });
        }
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> EnableTwoFactor([FromBody] TwoFactorSetupViewModel model)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                var errors = ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage);
                return Json(new { success = false, message = string.Join(" ", errors) });
            }

            var user = await _userManager.GetUserAsync(User);
            if (user == null)
            {
                return Json(new { success = false, message = "Kullanıcı bulunamadı." });
            }

            var secretKey = TempData["TempSecretKey"] as string;
            if (string.IsNullOrEmpty(secretKey))
            {
                return Json(new { success = false, message = "Kurulum süresi doldu. Lütfen tekrar deneyin." });
            }

            // Verify the code before enabling
            if (!_twoFactorService.ValidateCode(secretKey, model.VerificationCode))
            {
                TempData["TempSecretKey"] = secretKey; // Keep the secret key for retry
                return Json(new { success = false, message = "Geçersiz doğrulama kodu. Lütfen tekrar deneyin." });
            }

            // Enable 2FA
            var result = await _twoFactorService.EnableTwoFactorAsync(user, secretKey);
            if (!result)
            {
                return Json(new { success = false, message = "İki faktörlü doğrulama etkinleştirilemedi." });
            }

            TempData.Remove("TempSecretKey");
            return Json(new { success = true, message = "İki faktörlü doğrulama başarıyla etkinleştirildi." });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error enabling two-factor authentication");
            return Json(new { success = false, message = "İki faktörlü doğrulama etkinleştirme sırasında bir hata oluştu." });
        }
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> DisableTwoFactor()
    {
        try
        {
            var user = await _userManager.GetUserAsync(User);
            if (user == null)
            {
                return Json(new { success = false, message = "Kullanıcı bulunamadı." });
            }

            if (!user.TwoFactorEnabled)
            {
                return Json(new { success = false, message = "İki faktörlü doğrulama zaten devre dışı." });
            }

            var result = await _twoFactorService.DisableTwoFactorAsync(user);
            if (!result)
            {
                return Json(new { success = false, message = "İki faktörlü doğrulama devre dışı bırakılamadı." });
            }

            return Json(new { success = true, message = "İki faktörlü doğrulama başarıyla devre dışı bırakıldı." });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error disabling two-factor authentication");
            return Json(new { success = false, message = "İki faktörlü doğrulama devre dışı bırakma sırasında bir hata oluştu." });
        }
    }

    [HttpGet]
    public IActionResult GetQrCode(string qrCodeUri)
    {
        try
        {
            if (string.IsNullOrEmpty(qrCodeUri))
            {
                return BadRequest("QR kod URI gerekli.");
            }

            var qrCodeImage = _twoFactorService.GenerateQrCodeImage(qrCodeUri);
            return File(qrCodeImage, "image/png");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating QR code");
            return BadRequest("QR kod oluşturulamadı.");
        }
    }


    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> UpdateNotificationPreferencesAjax([FromBody] NotificationPreferencesViewModel model)
    {
        try
        {
            var user = await _userManager.GetUserAsync(User);
            if (user == null)
            {
                return Json(new { success = false, message = "Kullanıcı bulunamadı." });
            }

            var userWithPreferences = await _context.Users
                .Include(u => u.NotificationPreferences)
                .FirstOrDefaultAsync(u => u.Id == user.Id);

            if (userWithPreferences == null)
            {
                return Json(new { success = false, message = "Kullanıcı bulunamadı." });
            }

            // Create notification preferences if they do not exist
            if (userWithPreferences.NotificationPreferences == null)
            {
                userWithPreferences.NotificationPreferences = new NotificationPreferences
                {
                    UserId = user.Id,
                    CreatedAt = DateTime.UtcNow
                };
                _context.NotificationPreferences.Add(userWithPreferences.NotificationPreferences);
            }

            // Update notification preferences
            userWithPreferences.NotificationPreferences.EmailInvoiceNotifications = model.EmailInvoiceNotifications;
            userWithPreferences.NotificationPreferences.EmailCreditNotifications = model.EmailCreditNotifications;
            userWithPreferences.NotificationPreferences.EmailMarketingNotifications = model.EmailMarketingNotifications;
            userWithPreferences.NotificationPreferences.SmsSecurityAlerts = model.SmsSecurityAlerts;
            userWithPreferences.NotificationPreferences.SmsPaymentNotifications = model.SmsPaymentNotifications;
            userWithPreferences.NotificationPreferences.UpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();
            _logger.LogInformation("Notification preferences updated successfully for user {UserId}", user.Id);
            return Json(new { success = true, message = "Bildirim tercihleri başarıyla güncellendi." });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in UpdateNotificationPreferencesAjax");
            return Json(new { success = false, message = "Beklenmeyen bir hata oluştu." });
        }
    }
    private static bool IsValidWebsite(string website)
    {
        if (string.IsNullOrWhiteSpace(website))
            return true; // Empty is valid for optional field

        return Uri.TryCreate(website, UriKind.Absolute, out var uri) &&
               (uri.Scheme == Uri.UriSchemeHttp || uri.Scheme == Uri.UriSchemeHttps);
    }

    // User Management Methods
    [HttpGet]
    [Authorize(Roles = "CompanyOwner")]
    public async Task<IActionResult> GetCompanyUsersAjax()
    {
        try
        {
            var user = await _userManager.GetUserAsync(User);
            if (user == null)
            {
                return Json(new { success = false, message = "Kullanıcı bulunamadı." });
            }

            var userWithCompany = await _context.Users
                .Include(u => u.Company)
            .Include(u => u.NotificationPreferences)
                .FirstOrDefaultAsync(u => u.Id == user.Id);

            if (userWithCompany?.Company == null)
            {
                return Json(new { success = false, message = "Firma bilgisi bulunamadı." });
            }

            // Get all users from the same company
            var companyUsers = await _context.Users
                .Where(u => u.CompanyId == userWithCompany.CompanyId)
                .OrderBy(u => u.FirstName)
                .ThenBy(u => u.LastName)
                .Select(u => new UserListItemViewModel
                {
                    Id = u.Id,
                    FirstName = u.FirstName ?? "",
                    LastName = u.LastName ?? "",
                    Email = u.Email ?? "",
                    PhoneNumber = u.PhoneNumber,
                    IsActive = !u.LockoutEnd.HasValue || u.LockoutEnd <= DateTimeOffset.UtcNow,
                    CreatedAt = u.CreatedAt,
                    LastLoginAt = u.LastLoginAt
                })
                .ToListAsync();

            var result = new UserManagementViewModel
            {
                Users = companyUsers,
                TotalUsers = companyUsers.Count
            };

            return Json(new { success = true, data = result });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting company users");
            return Json(new { success = false, message = "Kullanıcı listesi alınırken bir hata oluştu." });
        }
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    [Authorize(Roles = "CompanyOwner")]
    public async Task<IActionResult> DeleteUserAjax([FromBody] DeleteUserRequest request)
    {
        if (!ModelState.IsValid)
        {
            var errors = ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage);
            return Json(new { success = false, message = string.Join(" ", errors) });
        }

        try
        {
            var currentUser = await GetCurrentUserWithCompanyAsync();
            if (currentUser == null)
            {
                return Json(new { success = false, message = "Kullanıcı bulunamadı." });
            }

            // Prevent self-deletion
            if (currentUser.Id == request.UserId)
            {
                return Json(new { success = false, message = "Kendi hesabınızı silemezsiniz." });
            }

            var userToDelete = await _context.Users
                .Include(u => u.Company)
            .Include(u => u.NotificationPreferences)
                .FirstOrDefaultAsync(u => u.Id == request.UserId);

            if (userToDelete == null)
            {
                return Json(new { success = false, message = "Silinecek kullanıcı bulunamadı." });
            }

            // Ensure the user belongs to the same company
            if (userToDelete.CompanyId != currentUser.CompanyId)
            {
                return Json(new { success = false, message = "Bu kullanıcıyı silme yetkiniz bulunmamaktadır." });
            }

            var result = await _userManager.DeleteAsync(userToDelete);
            if (result.Succeeded)
            {
                _logger.LogInformation("User {UserId} deleted by {CurrentUserId}", request.UserId, currentUser.Id);
                return Json(new { success = true, message = "Kullanıcı başarıyla silindi." });
            }
            else
            {
                var errors = string.Join(", ", result.Errors.Select(e => e.Description));
                return Json(new { success = false, message = $"Kullanıcı silinirken hata oluştu: {errors}" });
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting user {UserId}", request.UserId);
            return Json(new { success = false, message = "Kullanıcı silinirken bir hata oluştu." });
        }
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    [Authorize(Roles = "CompanyOwner")]
    public async Task<IActionResult> DeactivateUserAjax([FromBody] DeactivateUserRequest request)
    {
        if (!ModelState.IsValid)
        {
            var errors = ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage);
            return Json(new { success = false, message = string.Join(" ", errors) });
        }

        try
        {
            var currentUser = await GetCurrentUserWithCompanyAsync();
            if (currentUser == null)
            {
                return Json(new { success = false, message = "Kullanıcı bulunamadı." });
            }

            // Prevent self-deactivation
            if (currentUser.Id == request.UserId)
            {
                return Json(new { success = false, message = "Kendi hesabınızı deaktive edemezsiniz." });
            }

            var userToUpdate = await _context.Users
                .Include(u => u.Company)
            .Include(u => u.NotificationPreferences)
                .FirstOrDefaultAsync(u => u.Id == request.UserId);

            if (userToUpdate == null)
            {
                return Json(new { success = false, message = "Kullanıcı bulunamadı." });
            }

            // Ensure the user belongs to the same company
            if (userToUpdate.CompanyId != currentUser.CompanyId)
            {
                return Json(new { success = false, message = "Bu kullanıcıyı düzenleme yetkiniz bulunmamaktadır." });
            }

            if (request.Deactivate)
            {
                // Deactivate user by setting lockout end to far future
                userToUpdate.LockoutEnd = DateTimeOffset.MaxValue;
                userToUpdate.LockoutEnabled = true;
            }
            else
            {
                // Reactivate user by removing lockout
                userToUpdate.LockoutEnd = null;
                userToUpdate.LockoutEnabled = false;
            }

            var result = await _userManager.UpdateAsync(userToUpdate);
            if (result.Succeeded)
            {
                var action = request.Deactivate ? "deaktive edildi" : "aktive edildi";
                _logger.LogInformation("User {UserId} {Action} by {CurrentUserId}", request.UserId, action, currentUser.Id);
                return Json(new { success = true, message = $"Kullanıcı başarıyla {action}." });
            }
            else
            {
                var errors = string.Join(", ", result.Errors.Select(e => e.Description));
                return Json(new { success = false, message = $"Kullanıcı durumu güncellenirken hata oluştu: {errors}" });
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating user status {UserId}", request.UserId);
            return Json(new { success = false, message = "Kullanıcı durumu güncellenirken bir hata oluştu." });
        }
    }


    // Active Sessions Management Methods
    [HttpGet]
    public async Task<IActionResult> GetActiveSessionsAjax()
    {
        try
        {
            var user = await _userManager.GetUserAsync(User);
            if (user == null)
            {
                return Json(new { success = false, message = "Kullanıcı bulunamadı." });
            }

            var currentSessionId = $"{HttpContext.Connection.Id}_{HttpContext.Request.Headers["User-Agent"].ToString().GetHashCode()}";
            var sessions = await _context.UserSessions
                .Where(s => s.UserId == user.Id && s.IsActive)
                .OrderByDescending(s => s.LastActivityAt)
                .Select(s => new ActiveSessionViewModel
                {
                    Id = s.Id,
                    SessionId = s.SessionId,
                    DeviceInfo = s.DeviceInfo ?? "",
                    Browser = s.Browser ?? "",
                    OperatingSystem = s.OperatingSystem ?? "",
                    IpAddress = s.IpAddress ?? "",
                    Location = s.Location ?? "",
                    CreatedAt = s.CreatedAt,
                    LastActivityAt = s.LastActivityAt,
                    IsActive = s.IsActive,
                    DeviceType = s.DeviceType ?? "",
                    IsCurrent = s.SessionId == currentSessionId || s.IsCurrent
                })
                .ToListAsync();

            var result = new ActiveSessionsViewModel
            {
                Sessions = sessions,
                TotalSessions = sessions.Count,
                ActiveSessions = sessions.Count(s => s.IsActive)
            };

            return Json(new { success = true, data = result });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting active sessions for user");
            return Json(new { success = false, message = "Aktif oturumlar yüklenirken bir hata oluştu." });
        }
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> TerminateSessionAjax([FromBody] TerminateSessionRequest request)
    {
        if (!ModelState.IsValid)
        {
            var errors = ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage);
            return Json(new { success = false, message = string.Join(" ", errors) });
        }

        try
        {
            var user = await GetCurrentUserWithCompanyAsync();
            if (user == null)
            {
                return Json(new { success = false, message = "Kullanıcı bulunamadı." });
            }

            var session = await _context.UserSessions
                .FirstOrDefaultAsync(s => s.Id == request.SessionId && s.UserId == user.Id && s.IsActive);

            if (session == null)
            {
                return Json(new { success = false, message = "Oturum bulunamadı." });
            }

            // Prevent terminating current session
            var currentSessionId = $"{HttpContext.Connection.Id}_{HttpContext.Request.Headers["User-Agent"].ToString().GetHashCode()}";
            if (session.SessionId == currentSessionId || session.IsCurrent)
            {
                return Json(new { success = false, message = "Mevcut oturumunuzu sonlandıramazsınız." });
            }

            session.IsActive = false;
            session.EndedAt = DateTime.UtcNow;
            session.IsCurrent = false;

            await _context.SaveChangesAsync();
            _logger.LogInformation("Terminated session {SessionId} for user {UserId}", request.SessionId, user.Id);

            return Json(new { success = true, message = "Oturum başarıyla sonlandırıldı." });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error terminating session {SessionId}", request.SessionId);
            return Json(new { success = false, message = "Oturum sonlandırılırken bir hata oluştu." });
        }
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> TerminateAllOtherSessionsAjax()
    {
        try
        {
            var user = await _userManager.GetUserAsync(User);
            if (user == null)
            {
                return Json(new { success = false, message = "Kullanıcı bulunamadı." });
            }

            var currentSessionId = $"{HttpContext.Connection.Id}_{HttpContext.Request.Headers["User-Agent"].ToString().GetHashCode()}";
            var terminatedCount = await _context.UserSessions
                .Where(s => s.UserId == user.Id && s.SessionId != currentSessionId && s.IsActive)
                .ExecuteUpdateAsync(s => s
                    .SetProperty(x => x.IsActive, false)
                    .SetProperty(x => x.EndedAt, DateTime.UtcNow)
                    .SetProperty(x => x.IsCurrent, false));

            _logger.LogInformation("Terminated {Count} other sessions for user {UserId}", terminatedCount, user.Id);

            // Notify the user about all other sessions termination via SignalR
            try
            {
                await _hubContext.Clients.Group($"user_{user.Id}")
                    .SendAsync("AllOtherSessionsTerminated", new { count = terminatedCount, message = $"{terminatedCount} oturum başka bir cihazdan sonlandırıldı." });
            }
            catch (Exception signalREx)
            {
                _logger.LogError(signalREx, "Error sending SignalR notification for all sessions termination");
            }

            return Json(new { success = true, message = $"{terminatedCount} oturum başarıyla sonlandırıldı." });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error terminating other sessions for user");
            return Json(new { success = false, message = "Diğer oturumlar sonlandırılırken bir hata oluştu." });
        }
    }

    // Billing Information Management Methods
    [HttpGet]
    [Authorize(Roles = "CompanyOwner")]
    public async Task<IActionResult> GetBillingInformationAjax()
    {
        try
        {
            var user = await _userManager.GetUserAsync(User);
            if (user == null)
            {
                return Json(new { success = false, message = "Kullanıcı bulunamadı." });
            }

            var userWithCompany = await _context.Users
                .Include(u => u.Company)
                .FirstOrDefaultAsync(u => u.Id == user.Id);

            if (userWithCompany?.Company == null)
            {
                return Json(new { success = false, message = "Firma bilgisi bulunamadı." });
            }

            var billingInfo = new BillingInformationViewModel
            {
                BillingType = userWithCompany.Company.BillingType ?? "Corporate",
                TaxOffice = userWithCompany.Company.TaxOffice,
                TaxNumber = userWithCompany.Company.TaxNumber,
                IdentityNumber = userWithCompany.Company.IdentityNumber,
                BillingAddress = userWithCompany.Company.BillingAddress ?? ""
                ,
                CompanyName = userWithCompany.Company.CompanyName,
                FullName = userWithCompany.Company.FullName            };

            return Json(new { success = true, data = billingInfo });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting billing information");
            return Json(new { success = false, message = "Fatura bilgileri yüklenirken bir hata oluştu." });
        }
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    [Authorize(Roles = "CompanyOwner")]
    public async Task<IActionResult> UpdateBillingInformationAjax(BillingInformationViewModel model)
    {
        if (!ModelState.IsValid)
        {
            var errors = ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage);
            return Json(new { success = false, message = string.Join(" ", errors) });
        }

        try
        {
            _logger.LogInformation("Received billing data: BillingType={BillingType}, BillingAddress={BillingAddress}",
                model.BillingType, model.BillingAddress);

            var user = await _userManager.GetUserAsync(User);
            if (user == null)
            {
                return Json(new { success = false, message = "Kullanıcı bulunamadı." });
            }

            var userWithCompany = await _context.Users
                .Include(u => u.Company)
                .FirstOrDefaultAsync(u => u.Id == user.Id);

            if (userWithCompany?.Company == null)
            {
                return Json(new { success = false, message = "Firma bilgisi bulunamadı." });
            }

            userWithCompany.Company.BillingType = model.BillingType;
            userWithCompany.Company.TaxOffice = model.TaxOffice;
            userWithCompany.Company.TaxNumber = model.TaxNumber;
            userWithCompany.Company.IdentityNumber = model.IdentityNumber;
            userWithCompany.Company.BillingAddress = model.BillingAddress;
            userWithCompany.Company.CompanyName = model.CompanyName;
            userWithCompany.Company.FullName = model.FullName;            userWithCompany.Company.BillingUpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();
            _logger.LogInformation("Billing information updated successfully for company {CompanyId} by user {UserId}",
                userWithCompany.Company.Id, user.Id);

            return Json(new { success = true, message = "Fatura bilgileri başarıyla güncellendi." });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating billing information");
            return Json(new { success = false, message = "Fatura bilgileri güncellenirken bir hata oluştu." });
        }
    }

    // User Invitation Management Methods
    [HttpGet]
    [Authorize(Roles = "CompanyOwner")]
    public async Task<IActionResult> GetCompanyInvitationsAjax()
    {
        try
        {
            var user = await GetCurrentUserWithCompanyAsync();
            if (user?.Company == null)
            {
                return Json(new { success = false, message = "Firma bilgisi bulunamadı." });
            }

            var invitations = await _invitationService.GetCompanyInvitationsAsync(user.Company.Id);
            return Json(new { success = true, data = invitations });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting company invitations");
            return Json(new { success = false, message = "Davetiyeler yüklenirken bir hata oluştu." });
        }
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    [Authorize(Roles = "CompanyOwner")]
    public async Task<IActionResult> SendInvitationAjax([FromBody] SendInvitationRequest request)
    {
        if (!ModelState.IsValid)
        {
            var errors = ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage);
            return Json(new { success = false, message = string.Join(" ", errors) });
        }

        try
        {
            var user = await GetCurrentUserWithCompanyAsync();
            if (user?.Company == null)
            {
                return Json(new { success = false, message = "Firma bilgisi bulunamadı." });
            }

            var (success, message) = await _invitationService.SendInvitationAsync(
                user.Company.Id,
                request.Email,
                user.Id,
                user.FullName);

            return Json(new { success, message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending invitation to {Email}", request.Email);
            return Json(new { success = false, message = "Davetiye gönderilirken bir hata oluştu." });
        }
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    [Authorize(Roles = "CompanyOwner")]
    public async Task<IActionResult> CancelInvitationAjax([FromBody] CancelInvitationRequest request)
    {
        if (!ModelState.IsValid)
        {
            var errors = ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage);
            return Json(new { success = false, message = string.Join(" ", errors) });
        }

        try
        {
            var user = await GetCurrentUserWithCompanyAsync();
            if (user?.Company == null)
            {
                return Json(new { success = false, message = "Firma bilgisi bulunamadı." });
            }

            var (success, message) = await _invitationService.CancelInvitationAsync(request.InvitationId, user.Company.Id);
            return Json(new { success, message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error cancelling invitation {InvitationId}", request.InvitationId);
            return Json(new { success = false, message = "Davetiye iptal edilirken bir hata oluştu." });
        }
    }

    [HttpGet]
    public async Task<IActionResult> GetActiveChannels()
    {
        try
        {
            var companyId = GetCurrentUserCompanyId();
            if (companyId == null)
            {
                return Json(new { success = false, message = "Şirket bilgisi bulunamadı." });
            }

            // SMTP ayarlarını kontrol et
            var smtpIntegration = await _context.CompanyIntegrations
                .Include(ci => ci.Integration)
                .FirstOrDefaultAsync(ci => ci.CompanyId == companyId.Value &&
                                         ci.Integration.Name == "Email SMTP" &&
                                         ci.IsActive);

            bool emailActive = smtpIntegration != null &&
                              smtpIntegration.Settings.ContainsKey("smtpServer") &&
                              !string.IsNullOrEmpty(smtpIntegration.Settings["smtpServer"]?.ToString());

            // SMS ayarlarını kontrol et (gelecekte eklenecek)
            bool smsActive = false;

            // WhatsApp ayarlarını kontrol et (gelecekte eklenecek)
            bool whatsappActive = false;

            return Json(new
            {
                success = true,
                channels = new
                {
                    email = emailActive,
                    sms = smsActive,
                    whatsapp = whatsappActive
                }
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting active channels");
            return Json(new { success = false, message = "Aktif kanallar alınırken hata oluştu." });
        }
    }

    #region Helper Methods

    private async Task<ApplicationUser?> GetCurrentUserWithCompanyAsync()
    {
        var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);
        return await _context.Users
            .Include(u => u.Company)
            .FirstOrDefaultAsync(u => u.Id == userId);
    }

    #endregion
}
