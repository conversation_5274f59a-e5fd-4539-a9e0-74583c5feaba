using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using PushDashboard.Models;
using PushDashboard.Services;
using System.Security.Claims;

namespace PushDashboard.Controllers;

[Authorize]
public class BasketReminderController : BaseController
{
    private readonly IBasketReminderService _basketReminderService;
    private readonly ILogger<BasketReminderController> _logger;
    private readonly UserManager<ApplicationUser> _userManager;
    private readonly IWhatsAppTemplateService _whatsAppTemplateService;

    public BasketReminderController(
        IBasketReminderService basketReminderService,
        ILogger<BasketReminderController> logger,
        UserManager<ApplicationUser> userManager,
        IWhatsAppTemplateService whatsAppTemplateService,
        IUserContextService userContextService) : base(userContextService)
    {
        _basketReminderService = basketReminderService;
        _logger = logger;
        _userManager = userManager;
        _whatsAppTemplateService = whatsAppTemplateService;
    }

    /// <summary>
    /// Ana sayfa - Basit hatırlatma listesi
    /// </summary>
    public async Task<IActionResult> Index()
    {
        var companyId = GetCurrentUserCompanyId();
        if (companyId == null)
        {
            TempData["ErrorMessage"] = "Şirket bilgisi bulunamadı.";
            return RedirectToAction("Index", "Home");
        }

        try
        {
            var reminders = await _basketReminderService.GetAllSchedulesAsync(companyId.Value) ?? new List<BasketReminderSchedule>();
            return View(reminders);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading basket reminders for company {CompanyId}", companyId);
            TempData["ErrorMessage"] = "Hatırlatmalar yüklenirken hata oluştu.";
            return View(new List<BasketReminderSchedule>());
        }
    }

    /// <summary>
    /// Yeni hatırlatma oluştur
    /// </summary>
    [HttpPost]
    public async Task<IActionResult> Create([FromBody] CreateReminderRequest request)
    {
        var companyId = GetCurrentUserCompanyId();
        if (companyId == null)
        {
            return Json(new { success = false, message = "Şirket bilgisi bulunamadı." });
        }

        try
        {
            var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (string.IsNullOrEmpty(userId))
            {
                return Json(new { success = false, message = "Kullanıcı bilgisi bulunamadı." });
            }

            var reminder = new BasketReminderSchedule
            {
                Name = request.Name,
                NotificationContent = request.Message,
                ReminderTimeForHours = request.Hours,
                IsActive = request.IsActive,
                CompanyId = companyId.Value,
                CreatedAt = DateTime.UtcNow,
                CommunicationChannels = request.CommunicationChannels,
                ChannelMessages = request.ChannelMessages
            };

            var (success, message, scheduleId) = await _basketReminderService.CreateScheduleAsync(companyId.Value, reminder, userId);
            return Json(new { success = success, message = message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating basket reminder for company {CompanyId}", companyId);
            return Json(new { success = false, message = "Hatırlatma oluşturulurken hata oluştu: " + ex.Message });
        }
    }

    /// <summary>
    /// Hatırlatma güncelle
    /// </summary>
    [HttpPost]
    public async Task<IActionResult> Update([FromBody] UpdateReminderRequest request)
    {
        var companyId = GetCurrentUserCompanyId();
        if (companyId == null)
        {
            return Json(new { success = false, message = "Şirket bilgisi bulunamadı." });
        }

        try
        {
            var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (string.IsNullOrEmpty(userId))
            {
                return Json(new { success = false, message = "Kullanıcı bilgisi bulunamadı." });
            }

            var reminder = new BasketReminderSchedule
            {
                Id = request.Id,
                Name = request.Name,
                NotificationContent = request.Message,
                ReminderTimeForHours = request.Hours,
                IsActive = request.IsActive,
                UpdatedAt = DateTime.UtcNow,
                CommunicationChannels = request.CommunicationChannels,
                ChannelMessages = request.ChannelMessages
            };

            var (success, message) = await _basketReminderService.UpdateScheduleAsync(companyId.Value, reminder, userId);
            return Json(new { success = success, message = message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating basket reminder {Id} for company {CompanyId}", request.Id, companyId);
            return Json(new { success = false, message = "Hatırlatma güncellenirken hata oluştu: " + ex.Message });
        }
    }

    /// <summary>
    /// Hatırlatma sil
    /// </summary>
    [HttpPost]
    public async Task<IActionResult> Delete([FromBody] DeleteReminderRequest request)
    {
        var companyId = GetCurrentUserCompanyId();
        if (companyId == null)
        {
            return Json(new { success = false, message = "Şirket bilgisi bulunamadı." });
        }

        try
        {
            var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (string.IsNullOrEmpty(userId))
            {
                return Json(new { success = false, message = "Kullanıcı bilgisi bulunamadı." });
            }

            var (success, message) = await _basketReminderService.DeleteScheduleAsync(companyId.Value, request.Id, userId);
            return Json(new { success = success, message = message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting basket reminder {Id} for company {CompanyId}", request.Id, companyId);
            return Json(new { success = false, message = "Hatırlatma silinirken hata oluştu: " + ex.Message });
        }
    }

    /// <summary>
    /// Hatırlatma durumunu değiştir
    /// </summary>
    [HttpPost]
    public async Task<IActionResult> ToggleStatus([FromBody] ToggleStatusRequest request)
    {
        var companyId = GetCurrentUserCompanyId();
        if (companyId == null)
        {
            return Json(new { success = false, message = "Şirket bilgisi bulunamadı." });
        }

        try
        {
            var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (string.IsNullOrEmpty(userId))
            {
                return Json(new { success = false, message = "Kullanıcı bilgisi bulunamadı." });
            }

            var (success, message) = await _basketReminderService.ToggleScheduleStatusAsync(companyId.Value, request.Id, userId);
            return Json(new { success = success, message = message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error toggling basket reminder {Id} status for company {CompanyId}", request.Id, companyId);
            return Json(new { success = false, message = "Hatırlatma durumu değiştirilirken hata oluştu: " + ex.Message });
        }
    }

    // Request Models
    public class CreateReminderRequest
    {
        public string Name { get; set; } = "";
        public string Message { get; set; } = "";
        public int Hours { get; set; }
        public bool IsActive { get; set; } = true;
        public string CommunicationChannels { get; set; } = "[]";
        public string ChannelMessages { get; set; } = "{}";
    }

    public class UpdateReminderRequest
    {
        public int Id { get; set; }
        public string Name { get; set; } = "";
        public string Message { get; set; } = "";
        public int Hours { get; set; }
        public bool IsActive { get; set; }
        public string CommunicationChannels { get; set; } = "[]";
        public string ChannelMessages { get; set; } = "{}";
    }

    public class DeleteReminderRequest
    {
        public int Id { get; set; }
    }

    public class ToggleStatusRequest
    {
        public int Id { get; set; }
    }

    /// <summary>
    /// Aktif iletişim kanallarını getir
    /// </summary>
    [HttpGet]
    public async Task<IActionResult> GetAvailableChannels()
    {
        try
        {
            var user = await _userManager.GetUserAsync(User);
            if (user?.CompanyId == null)
            {
                return Json(new { success = false, message = "Kullanıcı veya şirket bulunamadı." });
            }

            var channels = await _basketReminderService.GetAvailableChannelsAsync(user.CompanyId.Value);
            return Json(new { success = true, channels = channels });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting available communication channels");
            return Json(new { success = false, message = "İletişim kanalları alınırken hata oluştu." });
        }
    }

    /// <summary>
    /// WhatsApp template'lerini getir
    /// </summary>
    [HttpGet]
    public async Task<IActionResult> GetWhatsAppTemplates()
    {
        try
        {
            var user = await _userManager.GetUserAsync(User);
            if (user?.CompanyId == null)
            {
                return Json(new { success = false, message = "Kullanıcı veya şirket bulunamadı." });
            }

            var templates = await _whatsAppTemplateService.GetFacebookTemplatesAsync(user.CompanyId.Value);

            // Sadece onaylanmış template'leri döndür
            var approvedTemplates = templates
                .Where(t => t.Status.Equals("APPROVED", StringComparison.OrdinalIgnoreCase))
                .Select(t => new
                {
                    id = t.Id,
                    name = t.Name,
                    category = t.Category,
                    language = t.Language
                })
                .ToList();

            return Json(new { success = true, templates = approvedTemplates });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting WhatsApp templates");
            return Json(new { success = false, message = "WhatsApp template'leri alınırken hata oluştu." });
        }
    }
}
